// Test script to debug chart issues with date filtering
import fetch from 'node-fetch';

const testWithDateFilter = async () => {
  try {
    console.log('🔍 Testing with date filter...');
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query GetStopDashboardData($filters: StopFilterInput) {
            getStopDashboardData(filters: $filters) {
              sidecards {
                Arret_Totale
                Arret_Totale_nondeclare
              }
              stopComparison {
                Machine_Name
                stops
                totalDuration
              }
              allStops {
                Machine_Name
                Date_Insert
                Debut_Stop
                Fin_Stop_Time
                Code_Stop
                duration_minutes
                Cause
                Raison_Arret
                Regleur_Prenom
                Operateur
              }
            }
          }
        `,
        variables: {
          filters: {
            startDate: "2024-09-30",
            endDate: "2024-10-01"
          }
        }
      })
    });

    const data = await response.json();
    
    console.log('🧪 GraphQL Response with date filter:');
    console.log('- Success:', !data.errors);
    console.log('- Sidecards:', data.data?.getStopDashboardData?.sidecards);
    console.log('- Stop Comparison:', data.data?.getStopDashboardData?.stopComparison);
    console.log('- All Stops count:', data.data?.getStopDashboardData?.allStops?.length);
    
    if (data.data?.getStopDashboardData?.allStops?.length > 0) {
      console.log('- Sample stops:', data.data.getStopDashboardData.allStops.slice(0, 3));
      
      // Test chart data processing
      const stops = data.data.getStopDashboardData.allStops;
      
      // Test hourly distribution (for time patterns chart)
      const hourlyCount = {};
      const hourlyDuration = {};
      let validTimeStops = 0;
      
      stops.forEach(stop => {
        if (stop.Debut_Stop) {
          try {
            const date = new Date(stop.Debut_Stop);
            if (!isNaN(date.getTime())) {
              const hour = date.getHours();
              hourlyCount[hour] = (hourlyCount[hour] || 0) + 1;
              hourlyDuration[hour] = (hourlyDuration[hour] || 0) + (parseFloat(stop.duration_minutes) || 0);
              validTimeStops++;
            }
          } catch (e) {
            console.log('Invalid date:', stop.Debut_Stop);
          }
        }
      });
      
      console.log('🕐 Valid time stops:', validTimeStops, 'out of', stops.length);
      console.log('🕐 Hourly distribution (count):', hourlyCount);
      console.log('🕐 Hourly distribution (duration):', hourlyDuration);
      
      // Test duration distribution
      const durations = stops
        .filter(stop => stop.duration_minutes != null)
        .map(stop => parseFloat(stop.duration_minutes))
        .filter(d => !isNaN(d) && d > 0);
      
      console.log('📊 Duration stats:', {
        total: stops.length,
        validDurations: durations.length,
        avgDuration: durations.length > 0 ? durations.reduce((a,b) => a+b, 0) / durations.length : 0,
        minDuration: durations.length > 0 ? Math.min(...durations) : 0,
        maxDuration: durations.length > 0 ? Math.max(...durations) : 0
      });
      
      // Test machine name distribution
      const machineNames = [...new Set(stops.map(stop => stop.Machine_Name))].filter(name => name && name.trim());
      console.log('🏭 Machine names:', machineNames);
      
      // Test cause distribution
      const causes = [...new Set(stops.map(stop => stop.Cause || stop.Code_Stop))].filter(cause => cause && cause.trim());
      console.log('🔧 Stop causes:', causes.slice(0, 10)); // Show first 10 causes
    }
    
    if (data.errors) {
      console.error('GraphQL Errors:', data.errors);
    }
    
  } catch (error) {
    console.error('Request failed:', error);
  }
};

testWithDateFilter();
