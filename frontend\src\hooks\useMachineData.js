import { useState, useCallback, useEffect } from "react";
import request from 'superagent';
import { extractResponseData } from "../utils/apiUtils";

/**
 * Custom hook for fetching and managing machine data
 * @returns {Object} Machine data and related functions
 */
const useMachineData = () => {
  const [machineModels, setMachineModels] = useState([]);
  const [machineNames, setMachineNames] = useState([]);
  const [filteredMachineNames, setFilteredMachineNames] = useState([]);
  const [selectedMachineModel, setSelectedMachineModel] = useState("IPS"); // Set default to IPS for immediate data loading
  const [selectedMachine, setSelectedMachine] = useState("");
  const [loading, setLoading] = useState(false);

  // Fetch machine models
  const fetchMachineModels = useCallback(async () => {
    try {
      console.log("Fetching machine models...");
      setLoading(true);
      const response = await request.get("/api/machine-models").retry(2);

      if (response.body) {
        const responseData = extractResponseData(response);
        const models = Array.isArray(responseData)
          ? responseData.map((item) => item.model || item)
          : [];

        console.log("Machine models fetched:", models);
        setMachineModels(models.length > 0 ? models : ["IPS", "CCM24"]);
      } else {
        // Set default models if API returns empty
        console.log("No machine models returned from API, using defaults");
        setMachineModels(["IPS", "CCM24"]);
      }
    } catch (error) {
      console.error("Error loading machine models:", error);
      // Set default values if API fails
      setMachineModels(["IPS", "CCM24"]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch machine names
  const fetchMachineNames = useCallback(async () => {
    try {
      console.log("Fetching machine names...");
      setLoading(true);
      const response = await request.get("/api/machine-names").retry(2);

      if (response.body) {
        const responseData = extractResponseData(response);
        console.log("Machine names fetched:", responseData);

        if (Array.isArray(responseData) && responseData.length > 0) {
          setMachineNames(responseData);

          // Find IPS01 in the machine names to confirm default selection
          const ips01Machine = responseData.find((m) => m.Machine_Name === "IPS01");
          if (ips01Machine && selectedMachineModel === "IPS") {
            // Confirm IPS as the default model if it exists in the data
            console.log("Confirmed IPS as default machine model");
          }
        } else {
          // Set default machine names if API returns empty
          console.log("No machine names returned from API, using defaults");
          setMachineNames([
            { Machine_Name: "IPS01" },
            { Machine_Name: "IPS02" },
            { Machine_Name: "IPS03" },
            { Machine_Name: "IPS04" },
          ]);
        }
      } else {
        // Set default machine names if API returns empty
        console.log("No machine names returned from API, using defaults");
        setMachineNames([
          { Machine_Name: "IPS01" },
          { Machine_Name: "IPS02" },
          { Machine_Name: "IPS03" },
          { Machine_Name: "IPS04" },
        ]);
      }
    } catch (error) {
      console.error("Error loading machine names:", error);
      // Set default values if API fails
      setMachineNames([
        { Machine_Name: "IPS01" },
        { Machine_Name: "IPS02" },
        { Machine_Name: "IPS03" },
        { Machine_Name: "IPS04" },
      ]);
    } finally {
      setLoading(false);
    }
  }, [selectedMachineModel]);

  // Handle machine model selection
  const handleMachineModelChange = (value) => {
    setSelectedMachineModel(value);
    // Machine selection will be cleared in the useEffect if it doesn't belong to the new model
  };

  // Handle machine selection
  const handleMachineChange = (value) => {
    setSelectedMachine(value);
  };

  // Effect to filter machines by selected model
  useEffect(() => {
    if (selectedMachineModel) {
      // Filter machines that belong to the selected model
      const filtered = machineNames.filter(
        (machine) => machine.Machine_Name && machine.Machine_Name.startsWith(selectedMachineModel),
      );
      setFilteredMachineNames(filtered);

      // Don't clear machine selection when model changes if the machine still belongs to the model
      if (selectedMachine && !filtered.some((m) => m.Machine_Name === selectedMachine)) {
        setSelectedMachine("");
      }
    } else {
      setFilteredMachineNames([]);
      setSelectedMachine("");
    }
  }, [selectedMachineModel, machineNames, selectedMachine]);

  // Fetch machine data on mount
  useEffect(() => {
    fetchMachineModels();
  }, [fetchMachineModels]);

  useEffect(() => {
    fetchMachineNames();
  }, [fetchMachineNames]);

  return {
    machineModels,
    machineNames,
    filteredMachineNames,
    selectedMachineModel,
    selectedMachine,
    loading,
    handleMachineModelChange,
    handleMachineChange,
    fetchMachineModels,
    fetchMachineNames,
    setSelectedMachineModel,
    setSelectedMachine
  };
};

export default useMachineData;
