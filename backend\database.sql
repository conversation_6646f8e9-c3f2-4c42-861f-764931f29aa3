-- MySQL dump 10.13  Distrib 8.0.41, for Win64 (x86_64)
--
-- Host: localhost    Database: testingarea51
-- ------------------------------------------------------
-- Server version	8.0.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `department_access`
--

CREATE TABLE `department_access` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `department_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_department` (`user_id`,`department_id`),
  KEY `department_id` (`department_id`),
  CONSTRAINT `department_access_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `department_access_ibfk_2` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
) 

CREATE TABLE `departments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) 

CREATE TABLE `login` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(45) DEFAULT NULL,
  `email` varchar(45) DEFAULT NULL,
  `password` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) 

CREATE TABLE `login_attempts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `attempt_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `success` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) 

CREATE TABLE `machine_daily_table_mould` (
  `Machine_Name` text,
  `Date_Insert_Day` text,
  `Run_Hours_Day` text,
  `Down_Hours_Day` text,
  `Good_QTY_Day` text,
  `Rejects_QTY_Day` text,
  `Speed_Day` text,
  `Availability_Rate_Day` text,
  `Performance_Rate_Day` text,
  `Quality_Rate_Day` text,
  `OEE_Day` text,
  `Shift` text,
  `Part_Number` text,
  `Poid_Unitaire` varchar(45) DEFAULT NULL,
  `Cycle_Theorique` varchar(45) DEFAULT NULL,
  `Poid_Purge` varchar(45) DEFAULT NULL
) 

/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `machine_daily_table_mould`
--
CREATE TABLE `machine_sessions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `machine_id` int NOT NULL,
  `session_start` datetime DEFAULT CURRENT_TIMESTAMP,
  `session_end` datetime DEFAULT NULL,
  `Machine_Name` varchar(45) DEFAULT NULL,
  `Ordre_Fabrication` varchar(45) DEFAULT NULL,
  `Article` varchar(45) DEFAULT NULL,
  `Quantite_Planifier` varchar(45) DEFAULT NULL,
  `Quantite_Bon` varchar(45) DEFAULT NULL,
  `Quantite_Rejet` varchar(45) DEFAULT NULL,
  `Poids_Purge` varchar(45) DEFAULT NULL,
  `Stop_Time` varchar(45) DEFAULT NULL,
  `Regleur_Prenom` varchar(45) DEFAULT NULL,
  `Etat` varchar(45) DEFAULT NULL,
  `Code_arret` varchar(45) DEFAULT NULL,
  `TRS` varchar(45) DEFAULT NULL,
  `cycle` varchar(45) DEFAULT NULL,
  `Poid_unitaire` varchar(45) DEFAULT NULL,
  `cycle_theorique` varchar(45) DEFAULT NULL,
  `empreint` varchar(45) DEFAULT NULL,
  `last_updated` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `machine_id` (`machine_id`),
  CONSTRAINT `machine_sessions_ibfk_1` FOREIGN KEY (`machine_id`) REFERENCES `real_time_table` (`id`)
) 

CREATE TABLE `machine_stop_table_mould` (
  `Machine_Name` text,
  `Date_Insert` text,
  `Part_NO` text,
  `Code_Stop` text,
  `Debut_Stop` text,
  `Fin_Stop_Time` text,
  `Regleur_Prenom` text
) 

CREATE TABLE `notifications` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `category` enum('alert','maintenance','update','info','machine_alert','production','quality') NOT NULL,
  `priority` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `severity` enum('info','warning','error','critical') NOT NULL DEFAULT 'info',
  `source` varchar(100) DEFAULT 'system',
  `machine_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read` tinyint(1) NOT NULL DEFAULT '0',
  `acknowledged` tinyint(1) NOT NULL DEFAULT '0',
  `acknowledged_by` int DEFAULT NULL,
  `acknowledged_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_notifications_user_id` (`user_id`),
  KEY `idx_notifications_timestamp` (`timestamp`),
  KEY `idx_notifications_read` (`read`),
  KEY `idx_notifications_priority` (`priority`),
  KEY `idx_notifications_machine_id` (`machine_id`),
  KEY `idx_notifications_category` (`category`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`acknowledged_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
)

CREATE TABLE `password_resets` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_id` (`user_id`),
  CONSTRAINT `password_resets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) 

CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `namespace` varchar(50) DEFAULT NULL,
  `action` varchar(100) DEFAULT NULL,
  `name` varchar(150) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `original_name` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) 

CREATE TABLE `real_time_table` (
  `id` int NOT NULL,
  `Machine_Name` varchar(45) DEFAULT NULL,
  `Ordre_Fabrication` varchar(45) DEFAULT NULL,
  `Article` varchar(45) DEFAULT NULL,
  `Quantite_Planifier` varchar(45) DEFAULT NULL,
  `Quantite_Bon` varchar(45) DEFAULT NULL,
  `Quantite_Rejet` varchar(45) DEFAULT NULL,
  `Poids_Purge` varchar(45) DEFAULT NULL,
  `Stop_Time` varchar(45) DEFAULT NULL,
  `Regleur_Prenom` varchar(45) DEFAULT NULL,
  `Etat` varchar(45) DEFAULT NULL,
  `Code_arret` varchar(45) DEFAULT NULL,
  `TRS` varchar(45) DEFAULT NULL,
  `cycle` varchar(45) DEFAULT NULL,
  `Poid_unitaire` varchar(45) DEFAULT NULL,
  `cycle_theorique` varchar(45) DEFAULT NULL,
  `empreint` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`)
) 

CREATE TABLE `reports` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `date` date NOT NULL,
  `shift` varchar(50) DEFAULT NULL,
  `machine_id` varchar(50) DEFAULT NULL,
  `machine_name` varchar(100) DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'pending',
  `data` json DEFAULT NULL,
  `generated_by` int DEFAULT NULL,
  `generated_at` datetime NOT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `generated_by` (`generated_by`),
  CONSTRAINT `reports_ibfk_1` FOREIGN KEY (`generated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) 

CREATE TABLE `role_inheritance` (
  `id` int NOT NULL AUTO_INCREMENT,
  `parent_role_id` int NOT NULL,
  `child_role_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_inheritance` (`parent_role_id`,`child_role_id`),
  KEY `child_role_id` (`child_role_id`),
  CONSTRAINT `role_inheritance_ibfk_1` FOREIGN KEY (`parent_role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_inheritance_ibfk_2` FOREIGN KEY (`child_role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) 

CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `hierarchy_level` int DEFAULT '0',
  `inherits_from` json DEFAULT NULL,
  `is_department_specific` tinyint(1) DEFAULT '0',
  `permissions` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) 

CREATE TABLE `roles_backup` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `permissions` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) 

CREATE TABLE `roles_backup_rbac_fix` (
  `id` int NOT NULL DEFAULT '0',
  `name` varchar(50) NOT NULL,
  `description` text,
  `hierarchy_level` int DEFAULT '0',
  `inherits_from` json DEFAULT NULL,
  `is_department_specific` tinyint(1) DEFAULT '0',
  `permissions` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) 


CREATE TABLE `sessions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) 


CREATE TABLE `user_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `dark_mode` tinyint(1) DEFAULT '0',
  `notifications_enabled` tinyint(1) DEFAULT '1',
  `email_notifications` tinyint(1) DEFAULT '0',
  `dashboard_refresh_rate` int DEFAULT '60',
  `data_display_mode` varchar(20) DEFAULT 'chart',
  `compact_mode` tinyint(1) DEFAULT '0',
  `animations_enabled` tinyint(1) DEFAULT '1',
  `chart_animations` tinyint(1) DEFAULT '1',
  `default_view` varchar(50) DEFAULT 'dashboard',
  `table_rows_per_page` int DEFAULT '20',
  `default_shift` varchar(20) DEFAULT 'Matin',
  `shift_report_notifications` tinyint(1) DEFAULT '1',
  `shift_report_emails` tinyint(1) DEFAULT '1',
  `shift1_notifications` tinyint(1) DEFAULT '1',
  `shift2_notifications` tinyint(1) DEFAULT '1',
  `shift3_notifications` tinyint(1) DEFAULT '1',
  `shift1_emails` tinyint(1) DEFAULT '1',
  `shift2_emails` tinyint(1) DEFAULT '1',
  `shift3_emails` tinyint(1) DEFAULT '1',
  `email_format` varchar(10) DEFAULT 'html',
  `email_digest` tinyint(1) DEFAULT '0',
  `notify_machine_alerts` tinyint(1) DEFAULT '1',
  `notify_maintenance` tinyint(1) DEFAULT '1',
  `notify_updates` tinyint(1) DEFAULT '1',
  `display_name` varchar(100) DEFAULT NULL,
  `job_title` varchar(100) DEFAULT NULL,
  `contact_email` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `default_report_format` varchar(10) DEFAULT 'pdf',
  `report_auto_download` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_settings` (`user_id`),
  KEY `idx_user_settings_user_id` (`user_id`),
  KEY `idx_user_settings_email_notifications` (`email_notifications`),
  KEY `idx_user_settings_shift_report_emails` (`shift_report_emails`),
  CONSTRAINT `user_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) 

CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` varchar(100) NOT NULL,
  `reset_token` varchar(255) DEFAULT NULL,
  `reset_token_expiry` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `department_id` int DEFAULT NULL,
  `permissions` json DEFAULT NULL,
  `role_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `fk_user_department` (`department_id`),
  KEY `fk_user_role` (`role_id`),
  CONSTRAINT `fk_user_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_user_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE SET NULL
) 

CREATE TABLE `users_backup_rbac_fix` (
  `id` int NOT NULL DEFAULT '0',
  `username` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` varchar(100) NOT NULL,
  `reset_token` varchar(255) DEFAULT NULL,
  `reset_token_expiry` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `department_id` int DEFAULT NULL,
  `permissions` json DEFAULT NULL,
  `role_id` int DEFAULT NULL
) 
--
-- Dumping routines for database 'testingarea51'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-13 14:20:37
