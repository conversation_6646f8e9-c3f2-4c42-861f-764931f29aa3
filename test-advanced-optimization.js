/**
 * ADVANCED OPTIMIZATION TEST
 * Tests the fully optimized frontend hook with all new features:
 * - Multi-level intelligent caching
 * - Request deduplication
 * - Performance monitoring
 * - Memory management
 */

import request from 'superagent';

const GRAPHQL_ENDPOINT = 'http://localhost:5000/api/graphql';

// Test comprehensive query with caching behavior
async function testAdvancedCaching() {
  console.log('🧪 TESTING: Advanced caching system...\n');
  
  const query = `
    query GetFinalComprehensiveStopData($filters: FinalOptimizedStopFilterInput) {
      getFinalComprehensiveStopData(filters: $filters) {
        allStops { Machine_Name Code_Stop }
        topStops { stopName count }
        sidecards { Arret_Totale Arret_Totale_nondeclare }
        totalRecords
        queryExecutionTime
        cacheHit
      }
    }
  `;

  const filters = { model: 'IPS', limit: 50 };

  try {
    // First request - should be cache miss
    console.log('1️⃣ First request (expected: CACHE MISS)');
    const start1 = Date.now();
    const response1 = await request.post(GRAPHQL_ENDPOINT)
      .send({ query, variables: { filters } })
      .retry(2);
    
    const result1 = response1.body;
    const time1 = Date.now() - start1;
    const data1 = result1.data.getFinalComprehensiveStopData;
    
    console.log(`   ⏱️  Frontend time: ${time1}ms`);
    console.log(`   🔍 Backend time: ${data1.queryExecutionTime}ms`);
    console.log(`   📊 Records: ${data1.totalRecords}`);
    console.log(`   💾 Cache status: ${data1.cacheHit || 'MISS'}\n`);

    // Second request - should use frontend cache (if implemented correctly)
    console.log('2️⃣ Second request (expected: FRONTEND CACHE HIT)');
    const start2 = Date.now();
    const response2 = await request.post(GRAPHQL_ENDPOINT)
      .send({ query, variables: { filters } })
      .retry(2);
    
    const result2 = response2.body;
    const time2 = Date.now() - start2;
    const data2 = result2.data.getFinalComprehensiveStopData;
    
    console.log(`   ⏱️  Frontend time: ${time2}ms`);
    console.log(`   🔍 Backend time: ${data2.queryExecutionTime}ms`);
    console.log(`   📊 Records: ${data2.totalRecords}`);
    console.log(`   💾 Cache status: ${data2.cacheHit || 'MISS'}\n`);

    // Test different filters - should be new cache miss
    console.log('3️⃣ Different filters (expected: CACHE MISS)');
    const filters2 = { model: 'AKROS', limit: 50 };
    const start3 = Date.now();
    const response3 = await request.post(GRAPHQL_ENDPOINT)
      .send({ query, variables: { filters: filters2 } })
      .retry(2);
    
    const result3 = response3.body;
    const time3 = Date.now() - start3;
    const data3 = result3.data.getFinalComprehensiveStopData;
    
    console.log(`   ⏱️  Frontend time: ${time3}ms`);
    console.log(`   🔍 Backend time: ${data3.queryExecutionTime}ms`);
    console.log(`   📊 Records: ${data3.totalRecords}`);
    console.log(`   💾 Cache status: ${data3.cacheHit || 'MISS'}\n`);

    console.log('📈 CACHING ANALYSIS:');
    console.log(`   • First request: ${time1}ms (baseline)`);
    console.log(`   • Second request: ${time2}ms (${Math.round((1 - time2/time1) * 100)}% improvement)`);
    console.log(`   • Cache efficiency: ${time2 < time1 ? '✅ WORKING' : '❌ NOT WORKING'}`);
    
    return { time1, time2, time3, data1, data2, data3 };
    
  } catch (error) {
    console.error('❌ Advanced caching test failed:', error);
    throw error;
  }
}

// Test utility queries caching
async function testUtilityCaching() {
  console.log('\n🧪 TESTING: Utility queries caching...\n');
  
  const modelsQuery = `
    query {
      getFinalStopMachineModels {
        model
      }
    }
  `;
  
  const namesQuery = `
    query GetFinalStopMachineNames($filters: FinalOptimizedStopFilterInput) {
      getFinalStopMachineNames(filters: $filters) {
        Machine_Name
      }
    }
  `;
  
  try {
    // Test models query - should cache
    console.log('1️⃣ Models query (first call)');
    const start1 = Date.now();
    const response1 = await request.post(GRAPHQL_ENDPOINT)
      .send({ query: modelsQuery })
      .retry(2);
    const result1 = response1.body;
    const time1 = Date.now() - start1;
    console.log(`   ⏱️  Time: ${time1}ms`);
    console.log(`   📊 Models: ${result1.data.getFinalStopMachineModels.length}\n`);

    // Test models query again - should be faster if cached
    console.log('2️⃣ Models query (second call - should be cached)');
    const start2 = Date.now();
    const response2 = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: modelsQuery })
    });
    const result2 = await response2.json();
    const time2 = Date.now() - start2;
    console.log(`   ⏱️  Time: ${time2}ms`);
    console.log(`   📊 Models: ${result2.data.getFinalStopMachineModels.length}\n`);

    // Test names query with IPS filter
    console.log('3️⃣ Names query for IPS');
    const start3 = Date.now();
    const response3 = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query: namesQuery,
        variables: { filters: { model: 'IPS' } }
      })
    });
    const result3 = await response3.json();
    const time3 = Date.now() - start3;
    console.log(`   ⏱️  Time: ${time3}ms`);
    console.log(`   📊 IPS Machines: ${result3.data.getFinalStopMachineNames.length}\n`);

    console.log('📈 UTILITY CACHING ANALYSIS:');
    console.log(`   • Models first call: ${time1}ms`);
    console.log(`   • Models second call: ${time2}ms (${Math.round((1 - time2/time1) * 100)}% improvement)`);
    console.log(`   • Utility cache efficiency: ${time2 < time1 ? '✅ WORKING' : '❌ NOT WORKING'}`);
    
    return { time1, time2, time3 };
    
  } catch (error) {
    console.error('❌ Utility caching test failed:', error);
    throw error;
  }
}

// Test request deduplication (simulating concurrent requests)
async function testRequestDeduplication() {
  console.log('\n🧪 TESTING: Request deduplication...\n');
  
  const query = `
    query GetFinalComprehensiveStopData($filters: FinalOptimizedStopFilterInput) {
      getFinalComprehensiveStopData(filters: $filters) {
        totalRecords
        queryExecutionTime
      }
    }
  `;

  const filters = { model: 'IPS', limit: 10 };

  try {
    console.log('🔄 Sending 3 identical concurrent requests...');
    const start = Date.now();
    
    // Send 3 identical requests simultaneously
    const promises = Array(3).fill().map((_, index) => 
      fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, variables: { filters } })
      }).then(res => res.json()).then(result => ({
        index,
        time: Date.now() - start,
        data: result.data.getFinalComprehensiveStopData
      }))
    );
    
    const results = await Promise.all(promises);
    const totalTime = Date.now() - start;
    
    console.log('📊 DEDUPLICATION RESULTS:');
    results.forEach((result, i) => {
      console.log(`   Request ${i + 1}: ${result.time}ms, ${result.data.totalRecords} records`);
    });
    console.log(`   Total time: ${totalTime}ms`);
    console.log(`   Expected: Requests should complete in similar time if deduplicated`);
    
    return results;
    
  } catch (error) {
    console.error('❌ Request deduplication test failed:', error);
    throw error;
  }
}

// Run all tests
async function runAdvancedTests() {
  console.log('🚀 ADVANCED FRONTEND HOOK OPTIMIZATION TESTS');
  console.log('='.repeat(60));
  
  try {
    const cachingResults = await testAdvancedCaching();
    const utilityResults = await testUtilityCaching();
    const dedupResults = await testRequestDeduplication();
    
    console.log('\n' + '='.repeat(60));
    console.log('✅ ALL ADVANCED TESTS COMPLETED SUCCESSFULLY!');
    console.log('\n🎯 OPTIMIZATION SUMMARY:');
    console.log(`   • Comprehensive query caching: ${cachingResults.time2 < cachingResults.time1 ? '✅' : '❌'}`);
    console.log(`   • Utility query caching: ${utilityResults.time2 < utilityResults.time1 ? '✅' : '❌'}`);
    console.log(`   • Request deduplication: ${dedupResults.length === 3 ? '✅' : '❌'}`);
    console.log(`   • Performance improvement: ~${Math.round((1 - cachingResults.time2/cachingResults.time1) * 100)}%`);
    
  } catch (error) {
    console.error('\n❌ ADVANCED TESTS FAILED:', error);
    process.exit(1);
  }
}

// Run if called directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  runAdvancedTests();
}

export { testAdvancedCaching, testUtilityCaching, testRequestDeduplication };
