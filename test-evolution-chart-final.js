// Final test to verify the Evolution Arrêts chart fix

// Test the frontend chart data processing logic
function testEvolutionDataProcessing() {
  console.log('=== Testing Evolution Chart Data Processing ===');
  
  // Sample data similar to what comes from backend
  const sampleAllStops = [
    { date: '01/01/2024 10:30', duree: 120 },
    { date: '01/01/2024 14:15', duree: 90 },
    { date: '02/01/2024 09:00', duree: 180 },
    { date: '02/01/2024 16:45', duree: 60 },
    { date: '03/01/2024 11:20', duree: 240 },
    { date: '04/01/2024 08:30', duree: 150 },
    { date: '04/01/2024 13:45', duree: 75 },
    { date: '05/01/2024 10:00', duree: 300 }
  ];

  // Process data as done in ArretContext.jsx
  const evolutionData = {};
  
  sampleAllStops.forEach(stop => {
    const dateOnly = stop.date.split(' ')[0]; // Extract date part
    if (!evolutionData[dateOnly]) {
      evolutionData[dateOnly] = { totalDuration: 0, count: 0 };
    }
    evolutionData[dateOnly].totalDuration += stop.duree;
    evolutionData[dateOnly].count += 1;
  });

  // Transform to chart format
  const chartData = Object.keys(evolutionData)
    .sort((a, b) => {
      const [dayA, monthA, yearA] = a.split('/');
      const [dayB, monthB, yearB] = b.split('/');
      const dateA = new Date(`${monthA}/${dayA}/${yearA}`);
      const dateB = new Date(`${monthB}/${dayB}/${yearB}`);
      return dateA - dateB;
    })
    .map(date => ({
      date: date,
      duration: evolutionData[date].totalDuration,
      count: evolutionData[date].count
    }));

  console.log('Generated chart data:');
  console.log(JSON.stringify(chartData, null, 2));

  // Check if all dates are valid
  const allDatesValid = chartData.every(item => {
    const [day, month, year] = item.date.split('/');
    const jsDate = new Date(`${month}/${day}/${year}`);
    return !isNaN(jsDate.getTime());
  });

  console.log('\n=== Validation Results ===');
  console.log(`Data points: ${chartData.length}`);
  console.log(`All dates valid: ${allDatesValid}`);
  console.log(`Date range: ${chartData[0]?.date} to ${chartData[chartData.length - 1]?.date}`);
  
  return chartData;
}

// Test the chart data
const chartData = testEvolutionDataProcessing();

// Test date formatting for X-axis
console.log('\n=== X-axis Date Formatting Test ===');
chartData.forEach(item => {
  const [day, month, year] = item.date.split('/');
  const jsDate = new Date(`${month}/${day}/${year}`);
  console.log(`${item.date} -> ${jsDate.toLocaleDateString()} (${jsDate.toISOString().split('T')[0]})`);
});

console.log('\n=== Test Complete ===');
console.log('If the Evolution Arrêts chart is still not showing dates on X-axis:');
console.log('1. Check browser console for debug logs');
console.log('2. Verify data is being passed correctly to the chart');
console.log('3. Check if Recharts is handling the date format properly');
