"use client"

import React, { useMemo } from "react";
import { Typography, Calendar, Badge, Empty, Spin, Tooltip } from "antd";
import { ClockCircleOutlined, InfoCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import SOMIPEM_COLORS from '../../styles/brand-colors';

const { Text } = Typography;

// Enhanced chart colors for heat levels with gradients and glow effects - SOMIPEM Brand Colors
const HEAT_COLORS = {
  low: SOMIPEM_COLORS.SECONDARY_BLUE, // SOMIPEM Secondary Blue for good MTTR (low)
  medium: SOMIPEM_COLORS.LIGHT_GRAY, // SOMIPEM Light Gray for medium MTTR
  high: SOMIPEM_COLORS.DARK_GRAY, // SOMIPEM Dark Gray for bad MTTR (high)
  none: "#f5f5f5", // Light gray for no data
  excellent: SOMIPEM_COLORS.PRIMARY_BLUE, // SOMIPEM Primary Blue for excellent MTTR
  critical: "#1a1a1a", // Near black for critical MTTR
  // Add gradient variations using SOMIPEM colors
  excellentGradient: `linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}, #1a365d)`,
  lowGradient: `linear-gradient(135deg, ${SOMIPEM_COLORS.SECONDARY_BLUE}, ${SOMIPEM_COLORS.PRIMARY_BLUE})`,
  mediumGradient: `linear-gradient(135deg, ${SOMIPEM_COLORS.LIGHT_GRAY}, ${SOMIPEM_COLORS.DARK_GRAY})`,
  highGradient: `linear-gradient(135deg, ${SOMIPEM_COLORS.DARK_GRAY}, #1a1a1a)`,
  criticalGradient: "linear-gradient(135deg, #1a1a1a, #000000)",
  noneGradient: "linear-gradient(135deg, #fafafa, #f0f0f0)"
};

// Enhanced status colors with shadow effects - SOMIPEM Brand Colors
const STATUS_EFFECTS = {
  excellent: { 
    boxShadow: `0 4px 20px rgba(30, 58, 138, 0.4), 0 0 20px rgba(30, 58, 138, 0.2)`,
    border: `2px solid rgba(30, 58, 138, 0.3)`
  },
  low: { 
    boxShadow: `0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)`,
    border: `2px solid rgba(59, 130, 246, 0.3)`
  },
  medium: { 
    boxShadow: `0 4px 20px rgba(107, 114, 128, 0.4), 0 0 20px rgba(107, 114, 128, 0.2)`,
    border: `2px solid rgba(107, 114, 128, 0.3)`
  },
  high: { 
    boxShadow: `0 4px 20px rgba(31, 41, 55, 0.4), 0 0 20px rgba(31, 41, 55, 0.2)`,
    border: `2px solid rgba(31, 41, 55, 0.3)`
  },
  critical: { 
    boxShadow: "0 4px 20px rgba(26, 26, 26, 0.5), 0 0 25px rgba(26, 26, 26, 0.3)",
    border: "2px solid rgba(26, 26, 26, 0.4)"
  },
  none: { 
    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
    border: "1px solid rgba(0, 0, 0, 0.1)"
  }
};

// Helper function to format MTTR values with units
const formatMttrValue = (value) => {
  if (value === undefined || value === null) return "N/A";

  let numValue = Number(value);

  if (!isNaN(numValue) && numValue > 0 && numValue < 1) {
    numValue = numValue * 100;
  }

  return numValue.toFixed(1);
};

// Helper function to get MTTR status text
const getMttrStatus = (mttr, thresholds) => {
  if (mttr === undefined || mttr === null) return "Aucune donnée";
  if (mttr <= thresholds.low * 0.5) return "Excellent";
  if (mttr <= thresholds.low) return "Bon";
  if (mttr <= thresholds.medium) return "Moyen";
  if (mttr <= thresholds.medium * 1.5) return "Mauvais";
  return "Critique";
};

const MttrHeatCalendar = ({
  data = [],
  selectedMachine = "",
  selectedDate = null,
  dateRangeType = "day",
  loading = false,
  thresholds = { low: 15, medium: 30 }, // MTTR thresholds in minutes
}) => {
  // Process data into a map for easy lookup by date
  const dataMap = useMemo(() => {
    const map = new Map();

    if (!data || !Array.isArray(data)) {
      console.error("MTTR Calendar data is not an array:", data);
      return map;
    }

    try {
      data.forEach((item) => {
        const processedItem = { ...item };

        if (processedItem.mttr !== undefined && processedItem.mttr !== null) {
          const mttrValue = Number(processedItem.mttr);
          if (!isNaN(mttrValue) && mttrValue > 0 && mttrValue < 1) {
            processedItem.mttr = mttrValue * 100;
          }
        }

        const dateKey = dayjs(processedItem.date).format("YYYY-MM-DD");
        map.set(dateKey, processedItem);
      });
    } catch (error) {
      console.error("Error processing MTTR calendar data:", error);
    }

    return map;
  }, [data]);

  const viewMode = useMemo(() => {
    // When a specific date is selected, always show month view to display daily data
    if (selectedDate) {
      return "month";
    }
    // Default behavior based on date range type
    if (dateRangeType === "day") return "month";
    if (dateRangeType === "week") return "month";
    if (dateRangeType === "month") return "year";
    return "month";
  }, [dateRangeType, selectedDate]);

  const calendarDate = useMemo(() => {
    // Always use the selected date if available, regardless of range type
    if (selectedDate) {
      return selectedDate;
    }
    return dayjs();
  }, [selectedDate]);
  const getMttrColor = (mttr) => {
    if (mttr === undefined || mttr === null) return HEAT_COLORS.noneGradient;
    if (mttr <= thresholds.low * 0.5) return HEAT_COLORS.excellentGradient;
    if (mttr <= thresholds.low) return HEAT_COLORS.lowGradient;
    if (mttr <= thresholds.medium) return HEAT_COLORS.mediumGradient;
    if (mttr <= thresholds.medium * 1.5) return HEAT_COLORS.highGradient;
    return HEAT_COLORS.criticalGradient;
  };

  const getMttrEffects = (mttr) => {
    if (mttr === undefined || mttr === null) return STATUS_EFFECTS.none;
    if (mttr <= thresholds.low * 0.5) return STATUS_EFFECTS.excellent;
    if (mttr <= thresholds.low) return STATUS_EFFECTS.low;
    if (mttr <= thresholds.medium) return STATUS_EFFECTS.medium;
    if (mttr <= thresholds.medium * 1.5) return STATUS_EFFECTS.high;
    return STATUS_EFFECTS.critical;
  };
  const dateCellRender = (date) => {
    const dateKey = date.format("YYYY-MM-DD");
    const dayData = dataMap.get(dateKey);

    if (!dayData) {
      return null;
    }

    const mttr = dayData.mttr;
    const color = getMttrColor(mttr);
    const effects = getMttrEffects(mttr);
    const status = getMttrStatus(mttr, thresholds);
    const stops = dayData.stops || 0;
    const availability = dayData.availability || 0;

    return (
      <div style={{ position: "relative", height: "100%", padding: "2px" }}>
        <Tooltip            title={
            <div style={{ 
              textAlign: 'center',
              background: `linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}, ${SOMIPEM_COLORS.DARK_GRAY})`,
              borderRadius: '8px',
              padding: '12px',
              border: `1px solid rgba(255,255,255,0.1)`
            }}>
              <div style={{ 
                fontWeight: 'bold', 
                marginBottom: '8px',
                fontSize: '14px',
                color: SOMIPEM_COLORS.WHITE,
                textShadow: '0 1px 2px rgba(0,0,0,0.5)'
              }}>
                📅 {date.format("DD/MM/YYYY")}
              </div>
              <div style={{ 
                marginBottom: '4px',
                padding: '4px 8px',
                background: 'rgba(255,255,255,0.1)',
                borderRadius: '4px',
                fontSize: '13px'
              }}>
                ⏱️ MTTR: <span style={{ fontWeight: 'bold', color: SOMIPEM_COLORS.SECONDARY_BLUE }}>{formatMttrValue(mttr)} min</span>
              </div>
              <div style={{ 
                marginBottom: '4px',
                padding: '4px 8px',
                background: 'rgba(255,255,255,0.1)',
                borderRadius: '4px',
                fontSize: '13px'
              }}>
                🔧 Statut: <span style={{ fontWeight: 'bold', color: getMttrColor(mttr).includes('excellent') ? SOMIPEM_COLORS.SECONDARY_BLUE : getMttrColor(mttr).includes('critical') ? SOMIPEM_COLORS.LIGHT_GRAY : SOMIPEM_COLORS.SECONDARY_BLUE }}>{status}</span>
              </div>
              {stops > 0 && (
                <div style={{ 
                  marginBottom: '4px',
                  padding: '4px 8px',
                  background: 'rgba(255,255,255,0.1)',
                  borderRadius: '4px',
                  fontSize: '13px'
                }}>
                  🚨 Arrêts: <span style={{ fontWeight: 'bold', color: SOMIPEM_COLORS.LIGHT_GRAY }}>{stops}</span>
                </div>
              )}
              {availability > 0 && (
                <div style={{
                  padding: '4px 8px',
                  background: 'rgba(255,255,255,0.1)',
                  borderRadius: '4px',
                  fontSize: '13px'
                }}>
                  📊 Disponibilité: <span style={{ fontWeight: 'bold', color: SOMIPEM_COLORS.SECONDARY_BLUE }}>{availability.toFixed(1)}%</span>
                </div>
              )}
            </div>
          }
          placement="auto"
          autoAdjustOverflow={true}
          getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
          overlayStyle={{
            maxWidth: '280px',
            zIndex: 1050
          }}
          overlayInnerStyle={{
            maxWidth: '280px',
            wordWrap: 'break-word'
          }}
          mouseEnterDelay={0.3}
          mouseLeaveDelay={0.1}
        >
          <div            style={{
              position: "relative",
              width: "100%",
              height: "100%",
              minHeight: "50px",  // Increased minimum height
              borderRadius: "12px",  // Slightly larger border radius
              background: color,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "12px",  // Increased font size
              fontWeight: "700",
              color: "white",
              textShadow: "0 1px 3px rgba(0,0,0,0.5)",
              cursor: "pointer",
              transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
              transform: "scale(1)",
              ...effects,
              // Enhanced animations for different statuses
              animation: mttr > thresholds.medium * 1.5 
                ? "pulseGlow 2s infinite, sparkle 3s infinite" 
                : mttr <= thresholds.low * 0.5 
                  ? "excellentGlow 3s infinite" 
                  : "none",
              // Add subtle 3D effect
              backgroundImage: `${color}, linear-gradient(145deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%)`,
              backgroundBlendMode: "normal, overlay",
              // Add a subtle inner shadow for depth
              boxShadow: `${effects.boxShadow}, inset 0 1px 0 rgba(255,255,255,0.2), inset 0 -1px 0 rgba(0,0,0,0.1)`,
            }}            onMouseEnter={(e) => {
              e.target.style.transform = "scale(1.15) rotate(3deg)";  // Reduced rotation
              e.target.style.zIndex = "10";
              e.target.style.filter = "brightness(1.2) saturate(1.2)";
              e.target.style.boxShadow = effects.boxShadow.replace(/0.4/g, '0.8').replace(/0.2/g, '0.6') + ', 0 8px 32px rgba(0,0,0,0.2)';
              // Add sparkle effect for excellent performance
              if (mttr <= thresholds.low * 0.5) {
                e.target.style.animation = "excellentGlow 3s infinite, sparkle 1s infinite";
              }
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = "scale(1) rotate(0deg)";
              e.target.style.zIndex = "1";
              e.target.style.filter = "brightness(1) saturate(1)";
              e.target.style.boxShadow = effects.boxShadow;
              // Reset animation
              if (mttr <= thresholds.low * 0.5) {
                e.target.style.animation = "excellentGlow 3s infinite";
              } else if (mttr > thresholds.medium * 1.5) {
                e.target.style.animation = "pulseGlow 2s infinite, sparkle 3s infinite";
              } else {
                e.target.style.animation = "none";
              }
            }}
          >            {/* Enhanced decorative elements */}
            <div style={{
              position: "absolute",
              top: "2px",
              right: "2px",
              width: "8px",
              height: "8px",
              borderRadius: "50%",
              background: "radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.3) 70%, transparent 100%)",
              animation: "sparkle 3s infinite",
              boxShadow: "0 0 6px rgba(255,255,255,0.6)"
            }} />
            
            {/* Additional sparkles for excellent performance */}
            {mttr <= thresholds.low * 0.5 && (
              <>
                <div style={{
                  position: "absolute",
                  top: "1px",
                  left: "1px",
                  width: "3px",
                  height: "3px",
                  borderRadius: "50%",
                  background: "rgba(255,255,255,0.9)",
                  animation: "sparkle 2s infinite 0.5s"
                }} />
                <div style={{
                  position: "absolute",
                  bottom: "1px",
                  right: "1px",
                  width: "2px",
                  height: "2px",
                  borderRadius: "50%",
                  background: "rgba(255,255,255,0.7)",
                  animation: "sparkle 2.5s infinite 1s"
                }} />
              </>
            )}
            
            {/* Critical alert indicator */}
            {mttr > thresholds.medium * 1.5 && (
              <div style={{
                position: "absolute",
                top: "-2px",
                right: "-2px",
                width: "12px",
                height: "12px",
                borderRadius: "50%",
                background: "linear-gradient(45deg, #ff4d4f, #f5222d)",
                border: "2px solid rgba(255,255,255,0.8)",
                animation: "pulseGlow 1.5s infinite",
                boxShadow: "0 0 12px rgba(245, 34, 45, 0.8)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "8px",
                color: "white",
                fontWeight: "bold"
              }}>
                !
              </div>
            )}
            
            <div style={{
              position: "relative",
              zIndex: 2,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "2px"  // Increased gap
            }}>
              <div style={{ 
                fontSize: "13px",  // Increased font size
                lineHeight: 1.2,
                fontWeight: "bold",
                textShadow: "0 1px 2px rgba(0,0,0,0.8)"  // Stronger text shadow
              }}>
                {formatMttrValue(mttr)}
              </div>
              <div style={{ 
                fontSize: "8px",  // Slightly increased font size
                opacity: 0.9,
                fontWeight: "600",
                textShadow: "0 1px 2px rgba(0,0,0,0.8)"
              }}>
                min
              </div>
            </div>

            {/* Status indicator dot */}
            <div style={{
              position: "absolute",
              bottom: "2px",
              left: "2px",
              width: "4px",
              height: "4px",
              borderRadius: "50%",
              background: "rgba(255,255,255,0.6)",
              boxShadow: "0 0 4px rgba(255,255,255,0.8)"
            }} />
          </div>
        </Tooltip>

        {/* Add CSS animations */}
        <style jsx>{`
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
          @keyframes sparkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
          }
        `}</style>
      </div>
    );
  };
  const monthCellRender = (date) => {
    const startOfMonth = date.startOf("month");
    const endOfMonth = date.endOf("month");
    const daysInMonth = endOfMonth.diff(startOfMonth, "day") + 1;

    let totalMttr = 0;
    let daysWithData = 0;
    let totalStops = 0;

    for (let i = 0; i < daysInMonth; i++) {
      const currentDate = startOfMonth.add(i, "day");
      const dateKey = currentDate.format("YYYY-MM-DD");
      const dayData = dataMap.get(dateKey);

      if (dayData && dayData.mttr !== undefined && dayData.mttr !== null) {
        totalMttr += dayData.mttr;
        totalStops += dayData.stops || 0;
        daysWithData++;
      }
    }

    const avgMttr = daysWithData > 0 ? totalMttr / daysWithData : null;
    const color = getMttrColor(avgMttr);
    const effects = getMttrEffects(avgMttr);
    const status = getMttrStatus(avgMttr, thresholds);

    return (
      <div style={{ 
        position: "relative", 
        height: "100%", 
        display: "flex", 
        alignItems: "center", 
        justifyContent: "center",
        padding: "8px"
      }}>
        {avgMttr !== null && (
          <Tooltip
            title={
              <div style={{ 
                textAlign: 'center',
                background: 'linear-gradient(135deg, #001529, #002766)',
                borderRadius: '12px',
                padding: '16px',
                border: '1px solid rgba(255,255,255,0.1)',
                maxWidth: '300px'
              }}>
                <div style={{ 
                  fontWeight: 'bold', 
                  marginBottom: '12px',
                  fontSize: '16px',
                  color: '#fff',
                  textShadow: '0 1px 2px rgba(0,0,0,0.5)',
                  borderBottom: '1px solid rgba(255,255,255,0.2)',
                  paddingBottom: '8px'
                }}>
                  📅 {date.format("MMMM YYYY")}
                </div>
                
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginBottom: '12px' }}>
                  <div style={{ 
                    padding: '8px',
                    background: 'rgba(255,255,255,0.1)',
                    borderRadius: '8px',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#ffc53d' }}>
                      {formatMttrValue(avgMttr)}
                    </div>
                    <div style={{ fontSize: '11px', color: "rgba(255,255,255,0.8)" }}>MTTR Moyen (min)</div>
                  </div>
                  
                  <div style={{ 
                    padding: '8px',
                    background: 'rgba(255,255,255,0.1)',
                    borderRadius: '8px',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#ff7875' }}>
                      {totalStops}
                    </div>
                    <div style={{ fontSize: '11px', color: "rgba(255,255,255,0.8)" }}>Total Arrêts</div>
                  </div>
                </div>
                
                <div style={{ 
                  padding: '8px',
                  background: 'rgba(255,255,255,0.1)',
                  borderRadius: '8px',
                  marginBottom: '8px'
                }}>
                  <div style={{ fontSize: '13px', marginBottom: '4px' }}>
                    🔧 Statut: <span style={{ 
                      fontWeight: 'bold', 
                      color: color.includes('excellent') ? SOMIPEM_COLORS.SECONDARY_BLUE : color.includes('critical') ? SOMIPEM_COLORS.LIGHT_GRAY : SOMIPEM_COLORS.SECONDARY_BLUE 
                    }}>{status}</span>
                  </div>
                  <div style={{ fontSize: '12px', opacity: 0.9 }}>
                    📊 Jours avec données: <span style={{ fontWeight: 'bold' }}>{daysWithData}</span>
                  </div>
                </div>
              </div>
            }
            placement="top"
          >
            <div
              style={{
                background: color,
                borderRadius: "16px",
                padding: "12px 20px",
                fontSize: "13px",
                fontWeight: "bold",
                color: "white",
                textShadow: "0 1px 3px rgba(0,0,0,0.5)",
                cursor: "pointer",
                transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                gap: "4px",
                position: "relative",
                overflow: "hidden",
                ...effects,
                minWidth: "80px"
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = "scale(1.08) translateY(-2px)";
                e.target.style.boxShadow = effects.boxShadow.replace(/0.4/g, '0.7').replace(/0.2/g, '0.5');
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = "scale(1) translateY(0px)";
                e.target.style.boxShadow = effects.boxShadow;
              }}
            >
              {/* Background decoration */}
              <div style={{
                position: "absolute",
                top: "-50%",
                right: "-50%",
                width: "100%",
                height: "100%",
                background: "radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)",
                borderRadius: "50%",
                animation: "float 6s ease-in-out infinite"
              }} />
              
              <div style={{ 
                display: "flex", 
                alignItems: "center", 
                gap: "6px",
                position: "relative",
                zIndex: 2
              }}>
                <ClockCircleOutlined style={{ fontSize: "12px" }} />
                <span style={{ fontSize: "14px", fontWeight: "800" }}>
                  {formatMttrValue(avgMttr)}
                </span>
                <span style={{ fontSize: "10px", opacity: 0.8 }}>min</span>
              </div>
              
              <div style={{ 
                fontSize: "9px", 
                opacity: 0.9,
                fontWeight: "600",
                position: "relative",
                zIndex: 2
              }}>
                {daysWithData} jours • {totalStops} arrêts
              </div>

              {/* Floating particles for excellent performance */}
              {avgMttr <= thresholds.low * 0.5 && (
                <>
                  <div style={{
                    position: "absolute",
                    top: "10%",
                    left: "20%",
                    width: "3px",
                    height: "3px",
                    borderRadius: "50%",
                    background: "rgba(255,255,255,0.8)",
                    animation: "sparkle 2s infinite 0.5s"
                  }} />
                  <div style={{
                    position: "absolute",
                    top: "70%",
                    right: "15%",
                    width: "2px",
                    height: "2px",
                    borderRadius: "50%",
                    background: "rgba(255,255,255,0.6)",
                    animation: "sparkle 2s infinite 1.2s"
                  }} />
                </>
              )}
            </div>
          </Tooltip>
        )}

        {/* Add CSS animations for month view */}
        <style jsx>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
          }
        `}</style>
      </div>
    );
  };
  const headerRender = ({ value, type, onChange, onTypeChange }) => {
    const title = selectedMachine ? `MTTR pour ${selectedMachine}` : "Calendrier MTTR";
    const subtitle = viewMode === "month" ? "Valeurs quotidiennes" : "Moyennes mensuelles";    return (
      <div style={{
        background: `linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE} 0%, ${SOMIPEM_COLORS.DARK_GRAY} 100%)`,
        borderRadius: "12px",
        padding: "12px",
        marginBottom: "8px",
        border: "none",
        boxShadow: `0 4px 16px rgba(30, 58, 138, 0.15), 0 0 15px rgba(31, 41, 55, 0.08)`,
        position: "relative",
        overflow: "hidden"
      }}>{/* Animated background elements */}
        <div style={{
          position: "absolute",
          top: "-20%",
          right: "-10%",
          width: "120px",
          height: "120px",
          background: "radial-gradient(circle, rgba(255,255,255,0.06) 0%, transparent 70%)",
          borderRadius: "50%",
          animation: "rotate 20s linear infinite"
        }} />
        <div style={{
          position: "absolute",
          bottom: "-15%",
          left: "-5%",
          width: "100px",
          height: "100px",
          background: "radial-gradient(circle, rgba(255,255,255,0.04) 0%, transparent 70%)",
          borderRadius: "50%",
          animation: "rotate 15s linear infinite reverse"
        }} />        {/* Header Title */}
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          marginBottom: "8px",
          position: "relative",
          zIndex: 2
        }}>
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <div style={{
              width: "32px",
              height: "32px",
              borderRadius: "8px",
              background: "rgba(255,255,255,0.2)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255,255,255,0.3)",
              boxShadow: "0 2px 12px rgba(0,0,0,0.1)"
            }}>
              <ClockCircleOutlined style={{ color: "#fff", fontSize: "16px" }} />
            </div>
            <div>
              <Text strong style={{ 
                fontSize: "16px", 
                color: "#fff",
                textShadow: "0 1px 3px rgba(0,0,0,0.3)",
                fontWeight: "700"
              }}>
                {title}
              </Text>
              <div style={{
                fontSize: "11px",
                color: "rgba(255,255,255,0.9)",
                marginTop: "1px",
                fontWeight: "500"
              }}>
                Surveillance temps réel
              </div>
            </div>
          </div>
          <Tooltip title="Le MTTR (Mean Time To Repair) représente le temps moyen nécessaire pour réparer une panne. Un MTTR faible indique une maintenance efficace.">
            <div style={{
              width: "28px",
              height: "28px",
              borderRadius: "8px",
              background: "rgba(255,255,255,0.15)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255,255,255,0.2)",
              cursor: "help",
              transition: "all 0.3s ease"
            }}>
              <InfoCircleOutlined style={{ color: "rgba(255,255,255,0.9)", fontSize: "14px" }} />
            </div>
          </Tooltip>        </div>

        
          
        
        <div style={{
          display: "flex",
          flexWrap: "wrap",
          gap: "8px",
          alignItems: "center",
          justifyContent: "center",
          padding: "12px",
          background: "rgba(255,255,255,0.1)",
          borderRadius: "12px",
          border: "1px solid rgba(255,255,255,0.2)",
          backdropFilter: "blur(10px)",
          position: "relative",
          zIndex: 2
        }}>
          {[
            { key: 'excellent', label: `Excellent (≤ ${(thresholds.low * 0.5).toFixed(0)}min)`, color: HEAT_COLORS.excellent },
            { key: 'low', label: `Bon (≤ ${thresholds.low}min)`, color: HEAT_COLORS.low },
            { key: 'medium', label: `Moyen (≤ ${thresholds.medium}min)`, color: HEAT_COLORS.medium },
            { key: 'high', label: `Mauvais (≤ ${(thresholds.medium * 1.5).toFixed(0)}min)`, color: HEAT_COLORS.high },
            { key: 'critical', label: `Critique (> ${(thresholds.medium * 1.5).toFixed(0)}min)`, color: HEAT_COLORS.critical }
          ].map((item, index) => (
            <div key={item.key} style={{ 
              display: "flex", 
              alignItems: "center", 
              gap: "6px",
              background: "rgba(255,255,255,0.08)",
              borderRadius: "8px",
              padding: "6px 10px",
              border: "1px solid rgba(255,255,255,0.15)",
              animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`
            }}>
              <div style={{
                width: "14px",
                height: "14px",
                background: item.color.includes('gradient') ? item.color : `linear-gradient(135deg, ${item.color}, ${item.color}dd)`,
                borderRadius: "4px",
                boxShadow: "0 1px 4px rgba(0,0,0,0.2)",
                border: "1px solid rgba(255,255,255,0.2)"
              }} />
              <Text style={{ 
                fontSize: "11px", 
                fontWeight: "600",
                color: "#fff",
                textShadow: "0 1px 2px rgba(0,0,0,0.3)"
              }}>
                {item.label}
              </Text>
            </div>
          ))}
        </div>

        {/* Add keyframe animations */}
        <style jsx>{`
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}</style>
      </div>
    );
  };
  if (loading) {
    return (
      <div style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "300px",
        background: "linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",
        borderRadius: "12px",
        border: "1px solid #e6f7ff"
      }}>
        <Spin size="large" />
        <Text style={{ marginTop: "16px", color: "#8c8c8c" }}>Chargement du calendrier MTTR...</Text>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "300px",
        background: "linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)",
        borderRadius: "12px",
        border: "1px solid #e8e8e8"
      }}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div style={{ textAlign: "center" }}>
              <Text style={{ fontSize: "16px", color: "#8c8c8c" }}>Aucune donnée MTTR disponible</Text>
              <br />
              <Text type="secondary" style={{ fontSize: "14px" }}>Sélectionnez une machine et une période pour afficher les données</Text>
            </div>
          }
        />
      </div>
    );
  }  return (
    <div style={{
      height: "100%",
      width: "100%",
      minHeight: "500px",  // Increased minimum height
      background: "linear-gradient(135deg, #f8f9fa 0%, #fff 25%, #f0f2f5 50%, #fff 75%, #fafafa 100%)",
      borderRadius: "16px",
      padding: "20px",  // Increased padding
      border: "1px solid rgba(230,230,230,0.8)",
      boxShadow: "0 4px 20px rgba(0,0,0,0.06), 0 1px 4px rgba(0,0,0,0.02)",
      position: "relative",
      overflow: "visible",  // Changed from hidden to visible for tooltips
      display: "flex",
      flexDirection: "column"
    }}>
      {/* Animated background pattern */}
      <div style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `
          radial-gradient(circle at 20% 20%, rgba(103, 126, 234, 0.02) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.02) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(240, 147, 251, 0.01) 0%, transparent 50%)
        `,
        animation: "floatUp 8s ease-in-out infinite",
        zIndex: 0,
        borderRadius: "16px"  // Match parent border radius
      }} />
      
      <div style={{ position: "relative", zIndex: 1, flex: 1, overflow: "visible" }}>  {/* Changed overflow to visible */}
        <Calendar
          value={calendarDate}
          mode={viewMode}
          fullscreen={false}
          dateCellRender={dateCellRender}
          monthCellRender={monthCellRender}
          headerRender={headerRender}
          style={{
            backgroundColor: "transparent",
            borderRadius: "12px",
            overflow: "visible",  // Changed to visible for tooltips
            height: "100%"
          }}
          className="custom-mttr-calendar"
        />
        
        {/* Add custom CSS for better calendar styling */}
        <style jsx global>{`
          .custom-mttr-calendar .ant-picker-calendar {
            background: transparent !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date-content {
            height: 60px !important;  /* Increased cell height */
            min-height: 60px !important;
          }
          
          .custom-mttr-calendar .ant-picker-cell {
            padding: 4px !important;  /* Add padding to cells */
            position: relative !important;
            overflow: visible !important;
          }
          
          .custom-mttr-calendar .ant-picker-cell-inner {
            min-height: 60px !important;  /* Ensure minimum cell height */
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: visible !important;
          }
          
          /* Ensure tooltips stay within bounds */
          .custom-mttr-calendar .ant-tooltip {
            z-index: 1060 !important;
          }
          
          .custom-mttr-calendar .ant-tooltip-inner {
            max-width: 280px !important;
            word-wrap: break-word !important;
            border-radius: 8px !important;
          }
          
          /* Make calendar container allow overflow for tooltips */
          .custom-mttr-calendar {
            overflow: visible !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-header,
          .custom-mttr-calendar .ant-picker-calendar-body {
            overflow: visible !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date {
            border: 1px solid rgba(0,0,0,0.06) !important;
            border-radius: 8px !important;
            margin: 2px !important;
            min-height: 56px !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date:hover {
            border-color: #40a9ff !important;
            box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2) !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-date-value {
            font-size: 14px !important;
            font-weight: 600 !important;
            color: #262626 !important;
            position: absolute !important;
            top: 4px !important;
            right: 6px !important;
            z-index: 3 !important;
            background: rgba(255,255,255,0.9) !important;
            border-radius: 4px !important;
            padding: 2px 6px !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-header {
            margin-bottom: 16px !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-mode-switch {
            background: linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE}, ${SOMIPEM_COLORS.SECONDARY_BLUE}) !important;
            border: none !important;
            color: white !important;
            border-radius: 8px !important;
            font-weight: 600 !important;
            padding: 6px 16px !important;
            box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3) !important;
          }
          
          .custom-mttr-calendar .ant-picker-calendar-mode-switch:hover {
            background: linear-gradient(135deg, ${SOMIPEM_COLORS.SECONDARY_BLUE}, ${SOMIPEM_COLORS.PRIMARY_BLUE}) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4) !important;
          }
          
          .custom-mttr-calendar .ant-select-selector {
            border: 2px solid #d9d9d9 !important;
            border-radius: 8px !important;
            background: white !important;
            min-height: 40px !important;
            font-weight: 600 !important;
          }
          
          .custom-mttr-calendar .ant-select-focused .ant-select-selector {
            border-color: ${SOMIPEM_COLORS.SECONDARY_BLUE} !important;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
          }
          
          /* Enhanced responsiveness */
          @media (max-width: 768px) {
            .custom-mttr-calendar .ant-picker-calendar-date-content {
              height: 45px !important;
              min-height: 45px !important;
            }
            
            .custom-mttr-calendar .ant-picker-cell-inner {
              min-height: 45px !important;
            }
            
            .custom-mttr-calendar .ant-picker-calendar-date {
              min-height: 41px !important;
            }
          }
        `}</style>
      </div>
    </div>
  );
};

export default React.memo(MttrHeatCalendar);

{/* Enhanced CSS animations and styles */}
<style jsx global>{`
  @keyframes pulseGlow {
    0%, 100% {
      box-shadow: 0 4px 20px rgba(207, 19, 34, 0.5), 0 0 25px rgba(207, 19, 34, 0.3), inset 0 1px 0 rgba(255,255,255,0.2), inset 0 -1px 0 rgba(0,0,0,0.1);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 6px 30px rgba(207, 19, 34, 0.8), 0 0 40px rgba(207, 19, 34, 0.6), inset 0 1px 0 rgba(255,255,255,0.2), inset 0 -1px 0 rgba(0,0,0,0.1);
      transform: scale(1.02);
    }
  }
  
  @keyframes excellentGlow {
    0%, 100% {
      box-shadow: 0 4px 20px rgba(35, 120, 4, 0.4), 0 0 20px rgba(35, 120, 4, 0.2), inset 0 1px 0 rgba(255,255,255,0.2), inset 0 -1px 0 rgba(0,0,0,0.1);
      filter: brightness(1) saturate(1);
    }
    50% {
      box-shadow: 0 6px 30px rgba(35, 120, 4, 0.7), 0 0 35px rgba(35, 120, 4, 0.5), inset 0 1px 0 rgba(255,255,255,0.2), inset 0 -1px 0 rgba(0,0,0,0.1);
      filter: brightness(1.1) saturate(1.2);
    }
  }
  
  @keyframes sparkle {
    0%, 100% {
      opacity: 0.3;
      transform: rotate(0deg) scale(1);
    }
    25% {
      opacity: 1;
      transform: rotate(90deg) scale(1.2);
    }
    50% {
      opacity: 0.5;
      transform: rotate(180deg) scale(0.8);
    }
    75% {
      opacity: 1;
      transform: rotate(270deg) scale(1.1);
    }
  }
  
  @keyframes fadeInScale {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }
    @keyframes floatUp {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-5px);
    }
  }
  
  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
  
  /* Enhanced calendar cell styling */
  .ant-picker-calendar-date-content {
    position: relative;
    height: 100%;
    transition: all 0.3s ease;
  }
  
  .ant-picker-calendar-date:hover .ant-picker-calendar-date-content {
    transform: translateY(-2px);
  }
  
  /* Enhanced month header */
  .ant-picker-calendar-header {
    background: linear-gradient(135deg, ${SOMIPEM_COLORS.PRIMARY_BLUE} 0%, ${SOMIPEM_COLORS.DARK_GRAY} 100%);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    color: white;
    animation: fadeInScale 0.6s ease-out;
  }
  
  .ant-picker-calendar-header .ant-picker-calendar-year-select,
  .ant-picker-calendar-header .ant-picker-calendar-month-select {
    color: white;
    border-color: rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
  }
  
  .ant-picker-calendar-header .ant-picker-calendar-year-select:hover,
  .ant-picker-calendar-header .ant-picker-calendar-month-select:hover {
    border-color: rgba(255,255,255,0.5);
    background: rgba(255,255,255,0.2);
  }
  
  /* Enhanced day cells */
  .ant-picker-calendar .ant-picker-calendar-date {
    border: none;
    margin: 2px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: visible;
  }
  
  .ant-picker-calendar .ant-picker-calendar-date:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
  
  /* Floating animation for performance summary */
  .performance-summary {
    animation: floatUp 3s ease-in-out infinite;
  }
  
  /* Shimmer effect for loading states */
  .shimmer-effect {
    background: linear-gradient(90deg, #f0f0f0 0%, #e0e0e0 50%, #f0f0f0 100%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }
`}</style>