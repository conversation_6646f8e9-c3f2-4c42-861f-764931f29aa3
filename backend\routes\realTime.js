import express from "express"
import db from "../db.js"
import { corsOptions } from "../middleware/cors.js"
import cors from "cors"
import { createHash } from "crypto" // Import only what we need from the built-in crypto module
import { body, query, validationResult } from "express-validator"
import { indexRealTimeData, indexMachineSession } from "../middleware/elasticsearchMiddleware.js"



const router = express.Router()

// Base endpoint to get all real-time data
router.get("/RealTimeTable", cors(corsOptions), indexRealTimeData, async (req, res) => {  try {
    const [results] = await db.execute(`SELECT * FROM real_time_table`)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})

// Endpoint for machine cards
router.get("/MachineCard", cors(corsOptions), indexRealTimeData, async (req, res) => {  try {
    const [results] = await db.execute(`
      SELECT
        *
      FROM real_time_table
    `)
    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})








// Endpoint for side cards data
router.get("/sidecards", cors(corsOptions), async (req, res) => {
  try {
    // Calculate aggregated data from real_time_table
    const [results] = await db.execute(`
      SELECT
        COUNT(*) as total_machines,
        SUM(CASE WHEN Etat = 'on' THEN 1 ELSE 0 END) as active_machines,
        AVG(CAST(TRS AS DECIMAL(10,2))) as avg_trs,
        SUM(CAST(Quantite_Bon AS UNSIGNED)) as total_production,
        SUM(CAST(Quantite_Rejet AS UNSIGNED)) as total_rejects
      FROM real_time_table
    `)

    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})

// Endpoint for daily stats
router.get("/dailyStats", cors(corsOptions), async (req, res) => {
  try {
    // Get hourly stats for the current day
    const [results] = await db.execute(`
      SELECT
        DATE_FORMAT(session_start, '%H:00') as time_bucket,
        AVG(CAST(TRS AS DECIMAL(10,2))) as trs,
        SUM(CAST(Quantite_Bon AS UNSIGNED)) as production
      FROM machine_sessions
      WHERE DATE(session_start) = CURDATE()
      GROUP BY time_bucket
      ORDER BY time_bucket
    `)

    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})

// Endpoint for operator stats
router.get("/operator-stats", cors(corsOptions), async (req, res) => {
  try {
    const [results] = await db.execute(`
      SELECT
        Regleur_Prenom as operator,
        SUM(CAST(Quantite_Bon AS UNSIGNED)) as production,
        AVG(CAST(TRS AS DECIMAL(10,2))) as efficiency
      FROM real_time_table
      WHERE Regleur_Prenom IS NOT NULL AND Regleur_Prenom != ''
      GROUP BY Regleur_Prenom
      ORDER BY production DESC
    `)

    res.json(results)
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})

// Endpoint for production stats
router.get("/production-stats", cors(corsOptions), async (req, res) => {
  try {
    const [results] = await db.execute(`
      SELECT
        SUM(CAST(Quantite_Bon AS UNSIGNED)) as totalProduction,
        SUM(CAST(Quantite_Rejet AS UNSIGNED)) as totalRejects,
        AVG(CAST(TRS AS DECIMAL(10,2))) as avgOEE,
        COUNT(CASE WHEN Etat = 'on' THEN 1 END) / COUNT(*) * 100 as avgAvailability
      FROM real_time_table
    `)

    res.json(results[0])
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database query failed" })
  }
})


// Helper function for error handling
const handleDBError = (res, err, message = "Database operation failed") => {
  console.error("Database Error:", err)
  return res.status(500).json({
    error: message,
    details: err.message,
    stack: process.env.NODE_ENV === "development" ? err.stack : undefined,
  })
}

// Helper function to safely convert values for database
const safeValue = (value) => {
  // If value is undefined or null, return an empty string instead of null
  if (value === undefined || value === null) return ""
  // Otherwise return the value as a string
  return String(value)
}


// SESSION APIS - IMPROVED VERSION

// Helper function for validation
const validateRequest = (req, res, next) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() })
  }
  next()
}

// Check if a machine has an active session
const checkActiveSession = async (machineId) => {
  try {
    const query = `
      SELECT * FROM machine_sessions      WHERE machine_id = ? AND session_end IS NULL
      ORDER BY session_start DESC LIMIT 1
    `
    const [results] = await db.execute(query, [machineId])
    return results.length > 0 ? results[0] : null
  } catch (error) {
    console.error("Error checking active session:", error)
    throw error
  }
}

// Import WebSocket broadcast functions
import { broadcastSessionUpdate, broadcastMachineUpdate } from "./machineDataWebSocket.js"

// Create a new session - with proper varchar handling and default values
router.post(
  "/createSession",
  cors(corsOptions),
  [
    body("machineId").isInt().withMessage("machineId must be an integer"),
    body("machineData").isObject().withMessage("machineData must be an object"),
  ],
  validateRequest,
  indexMachineSession,
  async (req, res) => {
    try {
      const { machineId, machineData } = req.body

      // Log the incoming data for debugging
      console.log("Creating session with data:", {
        machineId,
        Quantite_Planifier: machineData.Quantite_Planifier,
        Quantite_Bon: machineData.Quantite_Bon,
        Quantite_Rejet: machineData.Quantite_Rejet,
        TRS: machineData.TRS,
      })

      // First check if there's already an active session for this machine
      const activeSession = await checkActiveSession(machineId)

      if (activeSession) {
        // If there's already an active session, update it instead of creating a new one
        console.log(`Active session found for machine ${machineId}, updating instead of creating`)

        // Update the existing session with explicit handling for the problematic fields
        const updateQuery = `
          UPDATE machine_sessions
          SET
            Machine_Name = ?,
            Ordre_Fabrication = ?,
            Article = ?,
            Quantite_Planifier = ?,
            Quantite_Bon = ?,
            Quantite_Rejet = ?,
            Poids_Purge = ?,
            Stop_Time = ?,
            Regleur_Prenom = ?,
            Etat = ?,
            Code_arret = ?,
            TRS = ?,
            cycle = ?,
            Poid_unitaire = ?,
            cycle_theorique = ?,
            empreint = ?,
            last_updated = NOW()
          WHERE id = ?
        `

        await db.execute(updateQuery, [
          safeValue(machineData.Machine_Name),
          safeValue(machineData.Ordre_Fabrication),
          safeValue(machineData.Article),
          safeValue(machineData.Quantite_Planifier || "0"), // Ensure a default value
          safeValue(machineData.Quantite_Bon || "0"), // Ensure a default value
          safeValue(machineData.Quantite_Rejet || "0"), // Ensure a default value
          safeValue(machineData.Poids_Purge),
          safeValue(machineData.Stop_Time),
          safeValue(machineData.Regleur_Prenom),
          safeValue(machineData.Etat),
          safeValue(machineData.Code_arret),
          safeValue(machineData.TRS || "0"), // Ensure a default value
          safeValue(machineData.cycle),
          safeValue(machineData.Poid_unitaire),
          safeValue(machineData.cycle_theorique),
          safeValue(machineData.empreint),
          activeSession.id,
        ])

        // Get the updated session data to broadcast
        const [updatedSession] = await db.execute(
          "SELECT * FROM machine_sessions WHERE id = ?",
          [activeSession.id]
        )

        // Broadcast the session update via WebSocket
        if (updatedSession && updatedSession.length > 0) {
          broadcastSessionUpdate(updatedSession[0], "updated")
        }

        return res.status(200).json({
          message: "Existing session updated instead of creating a new one",
          sessionId: activeSession.id,
          action: "updated",
        })
      }

      // Prepare session data with proper string handling and default values
      const query = `
        INSERT INTO machine_sessions (
          machine_id, session_start, Machine_Name, Ordre_Fabrication, Article,
          Quantite_Planifier, Quantite_Bon, Quantite_Rejet, Poids_Purge, Stop_Time,
          Regleur_Prenom, Etat, Code_arret, TRS, cycle, Poid_unitaire, cycle_theorique, empreint, last_updated
        ) VALUES (?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `

      const [result] = await db.execute(query, [
        machineId,
        safeValue(machineData.Machine_Name),
        safeValue(machineData.Ordre_Fabrication),
        safeValue(machineData.Article),
        safeValue(machineData.Quantite_Planifier || "0"), // Ensure a default value
        safeValue(machineData.Quantite_Bon || "0"), // Ensure a default value
        safeValue(machineData.Quantite_Rejet || "0"), // Ensure a default value
        safeValue(machineData.Poids_Purge),
        safeValue(machineData.Stop_Time),
        safeValue(machineData.Regleur_Prenom),
        safeValue(machineData.Etat),
        safeValue(machineData.Code_arret),
        safeValue(machineData.TRS || "0"), // Ensure a default value
        safeValue(machineData.cycle),
        safeValue(machineData.Poid_unitaire),
        safeValue(machineData.cycle_theorique),
        safeValue(machineData.empreint),
      ])

      // Get the newly created session data to broadcast
      const [newSession] = await db.execute(
        "SELECT * FROM machine_sessions WHERE id = ?",
        [result.insertId]
      )

      // Broadcast the new session via WebSocket
      if (newSession && newSession.length > 0) {
        broadcastSessionUpdate(newSession[0], "created")
      }

      res.status(201).json({
        message: "New session created successfully",
        sessionId: result.insertId,
        action: "created",
      })
    } catch (error) {
      handleDBError(res, error)
    }
  },
)

// Update active session - with proper varchar handling and default values
router.post(
  "/updateSession",
  cors(corsOptions),
  [
    body("machineId").isInt().withMessage("machineId must be an integer"),
    body("machineData").isObject().withMessage("machineData must be an object"),
  ],
  validateRequest,
  indexMachineSession,
  async (req, res) => {
    try {
      const { machineId, machineData } = req.body

      // Log the incoming data for debugging
      console.log("Updating session with data:", {
        machineId,
        Quantite_Planifier: machineData.Quantite_Planifier,
        Quantite_Bon: machineData.Quantite_Bon,
        Quantite_Rejet: machineData.Quantite_Rejet,
        TRS: machineData.TRS,
      })

      // First check if there's an active session for this machine
      const activeSession = await checkActiveSession(machineId)

      if (!activeSession) {
        // If no active session exists, create one instead of updating
        console.log(`No active session found for machine ${machineId}, creating a new one`)

        const createQuery = `
          INSERT INTO machine_sessions (
            machine_id, session_start, Machine_Name, Ordre_Fabrication, Article,
            Quantite_Planifier, Quantite_Bon, Quantite_Rejet, Poids_Purge, Stop_Time,
            Regleur_Prenom, Etat, Code_arret, TRS, cycle, Poid_unitaire, cycle_theorique, empreint, last_updated
          ) VALUES (?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        `

        const [result] = await db.execute(createQuery, [
          machineId,
          safeValue(machineData.Machine_Name),
          safeValue(machineData.Ordre_Fabrication),
          safeValue(machineData.Article),
          safeValue(machineData.Quantite_Planifier || "0"), // Ensure a default value
          safeValue(machineData.Quantite_Bon || "0"), // Ensure a default value
          safeValue(machineData.Quantite_Rejet || "0"), // Ensure a default value
          safeValue(machineData.Poids_Purge),
          safeValue(machineData.Stop_Time),
          safeValue(machineData.Regleur_Prenom),
          safeValue(machineData.Etat),
          safeValue(machineData.Code_arret),
          safeValue(machineData.TRS || "0"), // Ensure a default value
          safeValue(machineData.cycle),
          safeValue(machineData.Poid_unitaire),
          safeValue(machineData.cycle_theorique),
          safeValue(machineData.empreint),
        ])

        return res.status(201).json({
          message: "New session created because no active session was found",
          sessionId: result.insertId,
          action: "created",
        })
      }

      const query = `
        UPDATE machine_sessions
        SET
          Machine_Name = ?,
          Ordre_Fabrication = ?,
          Article = ?,
          Quantite_Planifier = ?,
          Quantite_Bon = ?,
          Quantite_Rejet = ?,
          Poids_Purge = ?,
          Stop_Time = ?,
          Regleur_Prenom = ?,
          Etat = ?,
          Code_arret = ?,
          TRS = ?,
          cycle = ?,
          Poid_unitaire = ?,
          cycle_theorique = ?,
          empreint = ?,
          last_updated = NOW()
        WHERE
          id = ?
      `

      const [result] = await db.execute(query, [
        safeValue(machineData.Machine_Name),
        safeValue(machineData.Ordre_Fabrication),
        safeValue(machineData.Article),
        safeValue(machineData.Quantite_Planifier || "0"), // Ensure a default value
        safeValue(machineData.Quantite_Bon || "0"), // Ensure a default value
        safeValue(machineData.Quantite_Rejet || "0"), // Ensure a default value
        safeValue(machineData.Poids_Purge),
        safeValue(machineData.Stop_Time),
        safeValue(machineData.Regleur_Prenom),
        safeValue(machineData.Etat),
        safeValue(machineData.Code_arret),
        safeValue(machineData.TRS || "0"), // Ensure a default value
        safeValue(machineData.cycle),
        safeValue(machineData.Poid_unitaire),
        safeValue(machineData.cycle_theorique),
        safeValue(machineData.empreint),
        activeSession.id,
      ])

      if (result.affectedRows === 0) {
        return res.status(404).json({
          error: "Failed to update session",
        })
      }

      res.status(200).json({
        message: "Session data updated successfully",
        sessionId: activeSession.id,
      })
    } catch (error) {
      handleDBError(res, error)
    }
  },
)

// Stop active session - with improved error handling
router.post(
  "/stopSession",
  cors(corsOptions),
  [body("machineId").isInt().withMessage("machineId must be an integer")],
  validateRequest,
  async (req, res) => {
    try {
      const { machineId } = req.body

      // First check if there's an active session for this machine
      const activeSession = await checkActiveSession(machineId)

      if (!activeSession) {
        return res.status(404).json({
          error: "No active session found for this machine",
          machineId,
        })
      }

      const query = `
        UPDATE machine_sessions
        SET
          session_end = NOW(),
          last_updated = NOW()
        WHERE id = ?
      `

      const [result] = await db.execute(query, [activeSession.id])

      if (result.affectedRows === 0) {
        return res.status(404).json({
          error: "Failed to stop session",
        })
      }

      // Also clear the real-time table row for this machine
      const clearRealTimeQuery = `
        UPDATE real_time_table
        SET
          Quantite_Bon = 0,
          Quantite_Rejet = 0,
          TRS = 0
        WHERE id = ?
      `

      await db.execute(clearRealTimeQuery, [machineId])

      res.status(200).json({
        message: "Session stopped successfully",
        sessionId: activeSession.id,
      })
    } catch (error) {
      handleDBError(res, error)
    }
  },
)

// Get sessions by machine - with improved error handling
router.get("/machineSessions/:machineId", cors(corsOptions), validateRequest, async (req, res) => {
  try {
    const { machineId } = req.params

    const query = `
        SELECT *
        FROM machine_sessions
        WHERE machine_id = ?
        ORDER BY session_start DESC
      `

    const [results] = await db.execute(query, [machineId])

    res.set("Cache-Control", "public, max-age=300")
    res.set("ETag", createHash("md5").update(JSON.stringify(results)).digest("hex"))
    res.json(results)
  } catch (error) {
    handleDBError(res, error)
  }
})

// Get all sessions with pagination
router.get(
  "/allSessions",
  cors(corsOptions),
  [
    query("page").optional().isInt().withMessage("page must be an integer"),
    query("limit").optional().isInt().withMessage("limit must be an integer"),
  ],
  validateRequest,
  async (req, res) => {
    try {
      const page = Number.parseInt(req.query.page) || 1
      const limit = Number.parseInt(req.query.limit) || 20
      const offset = (page - 1) * limit

      const query = `
        SELECT *
        FROM machine_sessions
        ORDER BY session_start DESC
        LIMIT ? OFFSET ?
      `

      const countQuery = `SELECT COUNT(*) AS total FROM machine_sessions`

      const [[countResult], [results]] = await Promise.all([db.execute(countQuery), db.execute(query, [limit, offset])])

      res.set("Cache-Control", "public, max-age=300")
      res.set("ETag", createHash("md5").update(JSON.stringify(results)).digest("hex"))
      res.json({
        data: results,
        pagination: {
          total: countResult.total,
          page,
          limit,
          pages: Math.ceil(countResult.total / limit),
        },
      })
    } catch (error) {
      handleDBError(res, error)
    }
  },
)

// Get active sessions - with improved caching
router.get("/activeSessions", cors(corsOptions), async (req, res) => {
  try {
    const query = `
        SELECT *
        FROM machine_sessions
        WHERE session_end IS NULL
        ORDER BY session_start DESC
      `

    const [results] = await db.execute(query)

    // Shorter cache time for active sessions since they change frequently
    res.set("Cache-Control", "public, max-age=30")
    res.set("ETag", createHash("md5").update(JSON.stringify(results)).digest("hex"))
    res.json(results)
  } catch (error) {
    handleDBError(res, error)
  }
})

// Helper function
function handleError(res, err) {
  console.error("Database Error Details:", err) // Log the full error
  res.status(500).json({
    error: "Database query failed",
    details: err.message, // Include the error message in the response
  })
}

// Update machine data with WebSocket broadcasting
router.post("/updateMachineData", cors(corsOptions), async (req, res) => {
  try {
    const { id, ...updateData } = req.body

    if (!id) {
      return res.status(400).json({ error: "Machine ID is required" })
    }

    // Construct the SQL query dynamically based on the provided fields
    const fields = Object.keys(updateData)
      .map((key) => `${key} = ?`)
      .join(", ")
    const values = Object.values(updateData)

    // Add the ID to the values array for the WHERE clause
    values.push(id)

    const query = `UPDATE real_time_table SET ${fields} WHERE id = ?`
    const [result] = await db.execute(query, values)

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: "Machine not found" })
    }

    // Get the updated machine data to broadcast via WebSocket
    const [updatedMachine] = await db.execute(
      `SELECT * FROM real_time_table WHERE id = ?`,
      [id]
    )

    // Get all machine data for full data update
    const [allMachineData] = await db.execute(
      `SELECT * FROM real_time_table`
    )

    // Broadcast the update via WebSocket
    if (updatedMachine && updatedMachine.length > 0) {
      broadcastMachineUpdate({
        changedMachines: [{
          type: 'updated',
          machine: updatedMachine[0]
        }],
        fullData: allMachineData
      })
    }

    res.json({ message: "Machine data updated successfully" })
  } catch (err) {
    console.error("Database error:", err)
    res.status(500).json({ error: "Database update failed" })
  }
})

export default router
