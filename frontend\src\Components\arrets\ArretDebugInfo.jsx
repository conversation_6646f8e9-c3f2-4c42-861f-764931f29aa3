import React from 'react';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';

const ArretDebugInfo = ({ optimizedData }) => {
  const context = useArretQueuedContext();
  
  return (
    <div style={{ 
      background: '#fff', 
      padding: '16px', 
      margin: '16px 0', 
      border: '1px solid #ddd',
      borderRadius: '8px'
    }}>
      <h3>🔍 ArretsDashboard Debug Information</h3>
      <pre style={{ fontSize: '12px', maxHeight: '400px', overflow: 'auto' }}>
        {JSON.stringify({
          // Context status
          contextExists: !!context,
          loading: context?.loading,
          error: context?.error,
          
          // Data counts
          totalStops: context?.totalStops,
          undeclaredStops: context?.undeclaredStops,
          avgDuration: context?.avgDuration,
          totalDuration: context?.totalDuration,
          
          // Data arrays
          arretStatsLength: context?.arretStats?.length,
          topStopsDataLength: context?.topStopsData?.length,
          arretsByRangeLength: context?.arretsByRange?.length,
          stopReasonsLength: context?.stopReasons?.length,
          stopsDataLength: context?.stopsData?.length,
          sidebarStatsLength: context?.sidebarStats?.length,
          
          // Filter state
          selectedMachineModel: context?.selectedMachineModel,
          selectedMachine: context?.selectedMachine,
          selectedDate: context?.selectedDate?.format?.('YYYY-MM-DD'),
          dateRangeType: context?.dateRangeType,
          
          // Sample data (first few items)
          sampleArretStats: context?.arretStats?.slice(0, 2),
          sampleTopStops: context?.topStopsData?.slice(0, 3),
          sampleStopReasons: context?.stopReasons?.slice(0, 3),
          sampleStopsData: context?.stopsData?.slice(0, 2),
          
          // Optimized data passed from dashboard
          optimizedDataPresent: !!optimizedData,
          totalDataPoints: optimizedData?.totalDataPoints,
          usingAggregatedEndpoint: optimizedData?.usingAggregatedEndpoint
        }, null, 2)}
      </pre>
    </div>
  );
};

export default ArretDebugInfo;
