/**
 * Test Evolution Chart Filtering Logic
 * Verify that date filtering works correctly for the evolution chart
 */

// Test data simulating stops from April 2025
const testStopsData = [
  { Date_Insert: "2025 10:30:00-04-15", Machine_Name: "IPS01", duration_minutes: 30 },
  { Date_Insert: "2025 14:20:00-04-15", Machine_Name: "IPS01", duration_minutes: 45 },
  { Date_Insert: "2025 09:15:00-04-16", Machine_Name: "IPS01", duration_minutes: 20 },
  { Date_Insert: "2025 11:45:00-04-17", Machine_Name: "IPS01", duration_minutes: 60 },
  { Date_Insert: "2025 16:30:00-04-17", Machine_Name: "IPS01", duration_minutes: 15 },
  { Date_Insert: "2025 08:00:00-04-18", Machine_Name: "IPS01", duration_minutes: 90 },
  // Some data from other months to test filtering
  { Date_Insert: "2025 10:30:00-03-15", Machine_Name: "IPS01", duration_minutes: 30 },
  { Date_Insert: "2025 14:20:00-05-16", Machine_Name: "IPS01", duration_minutes: 45 },
];

function parseCustomDateFormat(dateString) {
  const match = dateString.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-(\d{1,2})$/);
  if (match) {
    const [_, year, month, day] = match;
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  return null;
}

function testEvolutionDataGeneration() {
  console.log('🧪 Testing Evolution Chart Data Generation');
  console.log('📊 Test data:', testStopsData.length, 'stops');
  
  // Simulate the filtering logic
  const selectedDate = new Date('2025-04-15'); // April 2025
  const dateRangeType = 'month';
  const selectedMachine = 'IPS01';
  
  console.log('🎯 Filters:', {
    selectedDate: selectedDate.toISOString(),
    dateRangeType,
    selectedMachine
  });
  
  // Step 1: Filter by machine and date (like filteredStopsData)
  const filteredStops = testStopsData.filter(stop => {
    // Filter by machine
    if (selectedMachine && stop.Machine_Name !== selectedMachine) {
      return false;
    }
    
    // Filter by date
    if (selectedDate && stop.Date_Insert) {
      const stopDate = new Date(parseCustomDateFormat(stop.Date_Insert));
      const filterDate = selectedDate;
      
      if (dateRangeType === 'month') {
        return stopDate.getMonth() === filterDate.getMonth() && 
               stopDate.getFullYear() === filterDate.getFullYear();
      }
    }
    
    return true;
  });
  
  console.log('✅ Filtered stops:', filteredStops.length);
  console.log('📅 Filtered dates:', filteredStops.map(s => parseCustomDateFormat(s.Date_Insert)));
  
  // Step 2: Generate daily stats
  const dailyStats = {};
  filteredStops.forEach(stop => {
    const date = parseCustomDateFormat(stop.Date_Insert);
    if (date) {
      if (!dailyStats[date]) {
        dailyStats[date] = { date, stops: 0, duration: 0 };
      }
      dailyStats[date].stops++;
      if (stop.duration_minutes) {
        dailyStats[date].duration += parseFloat(stop.duration_minutes);
      }
    }
  });
  
  console.log('📈 Daily stats:', dailyStats);
  
  // Step 3: Create evolution data
  let evolutionData = Object.values(dailyStats)
    .sort((a, b) => new Date(a.date) - new Date(b.date))
    .map(item => {
      const dateObj = new Date(item.date);
      return {
        ...item,
        displayDate: dateObj.toLocaleDateString('fr-FR', { 
          day: '2-digit', 
          month: '2-digit' 
        })
      };
    });
  
  console.log('🎯 Final evolution data:', evolutionData);
  
  // Verify results
  const expectedResults = {
    shouldHaveData: evolutionData.length > 0,
    shouldBeFromApril2025: evolutionData.every(item => item.date.startsWith('2025-04')),
    shouldHaveCorrectCounts: evolutionData.find(item => item.date === '2025-04-15')?.stops === 2,
    shouldHaveDisplayDates: evolutionData.every(item => item.displayDate && !item.displayDate.includes('Invalid'))
  };
  
  console.log('✅ Verification:', expectedResults);
  
  return evolutionData;
}

testEvolutionDataGeneration();
