import express from "express";
import db from "../db.js";
import auth from "../middleware/auth.js";
import bypassPermission from "../middleware/bypassPermission.js";
import PDFDocument from "pdfkit";
import ExcelJS from "exceljs";
import { Parser } from "json2csv";
import fs from "fs";
import path from "path";
import dayjs from "dayjs";
import ShiftReportService from "../utils/shiftReportService.js";
import { indexReport } from "../middleware/elasticsearchMiddleware.js";

const router = express.Router();

// Récupérer tous les rapports avec filtrage
router.get("/", auth, bypassPermission, async (req, res) => {
  console.log('🔍 [REPORTS API] Fetching reports with params:', req.query);

  try {
    const { type = "all", startDate, endDate, shift, machines, search, page = 1, pageSize = 10 } = req.query

    // Validate and sanitize parameters early
    const pageSizeInt = Math.max(1, Math.min(100, parseInt(pageSize) || 10));
    const pageInt = Math.max(1, parseInt(page) || 1);

    // Validate date parameters if provided
    if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
      return res.status(400).json({
        error: "Invalid startDate format. Expected YYYY-MM-DD",
        code: "INVALID_DATE_FORMAT"
      });
    }

    if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
      return res.status(400).json({
        error: "Invalid endDate format. Expected YYYY-MM-DD",
        code: "INVALID_DATE_FORMAT"
      });
    }

    // Validate machines parameter
    if (machines && typeof machines !== 'string') {
      return res.status(400).json({
        error: "Invalid machines parameter. Expected comma-separated string.",
        code: "INVALID_MACHINES_FORMAT"
      });
    }

    // Validate search parameter
    if (search && (typeof search !== 'string' || search.length > 100)) {
      return res.status(400).json({
        error: "Invalid search parameter. Must be string with max 100 characters.",
        code: "INVALID_SEARCH_FORMAT"
      });
    }

    // Validate shift parameter
    const validShifts = ['matin', 'apres-midi', 'nuit'];
    if (shift && !validShifts.includes(shift)) {
      return res.status(400).json({
        error: `Invalid shift parameter. Must be one of: ${validShifts.join(', ')}`,
        code: "INVALID_SHIFT_VALUE"
      });
    }

    console.log('✅ [REPORTS API] All parameters validated - page:', pageInt, 'pageSize:', pageSizeInt);

    // Construction de la requête SQL de base
    let query = `
      SELECT r.*, u.username as generatedBy
      FROM reports r
      LEFT JOIN users u ON r.generated_by = u.id
      WHERE 1=1
    `

    const queryParams = []

    // Filtrage par type de rapport
    if (type !== "all") {
      query += " AND r.type = ?"
      queryParams.push(type)
    }

    // Filtrage par date
    if (startDate && endDate) {
      query += " AND r.date BETWEEN ? AND ?"
      queryParams.push(startDate, endDate)
    }

    // Filtrage par quart
    if (shift) {
      query += " AND r.shift = ?"
      queryParams.push(shift)
    }

    // Filtrage par machines
    if (machines) {
      const machineIds = machines.split(",")
      query += ` AND r.machine_id IN (${machineIds.map(() => "?").join(",")})`
      queryParams.push(...machineIds)
    }

    // Recherche textuelle
    if (search) {
      query += " AND (r.title LIKE ? OR r.description LIKE ? OR u.username LIKE ?)"
      const searchTerm = `%${search}%`
      queryParams.push(searchTerm, searchTerm, searchTerm)
    }

    // Comptage du nombre total de résultats pour la pagination
    const countQuery = query.replace("r.*, u.username as generatedBy", "COUNT(*) as total")

    // Ajout de l'ordre et de la pagination
    // Note: MySQL2 prepared statements don't support parameterized LIMIT/OFFSET
    // Using string concatenation for LIMIT/OFFSET (safe because values are validated integers)
    const offsetInt = (pageInt - 1) * pageSizeInt;
    query += ` ORDER BY r.date DESC, r.id DESC LIMIT ${pageSizeInt} OFFSET ${offsetInt}`;

    console.log('🔍 [REPORTS API] Pagination params - pageSize:', pageSizeInt, 'page:', pageInt, 'offset:', offsetInt);

    // Don't push LIMIT/OFFSET to queryParams since they're now in the query string

    console.log('🔍 [REPORTS API] Executing count query:', countQuery);
    console.log('🔍 [REPORTS API] Count params:', queryParams);

    // Use promise-based database queries with timeout
    const countPromise = db.execute(countQuery, queryParams);
    const countTimeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Count query timeout after 15 seconds')), 15000)
    );

    const [countResults] = await Promise.race([countPromise, countTimeoutPromise]);
    const total = countResults[0].total;

    console.log('✅ [REPORTS API] Count query successful, total:', total);
    console.log('🔍 [REPORTS API] Executing main query:', query);
    console.log('🔍 [REPORTS API] Main params:', queryParams);

    const mainPromise = db.execute(query, queryParams);
    const mainTimeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Main query timeout after 15 seconds')), 15000)
    );

    const [results] = await Promise.race([mainPromise, mainTimeoutPromise]);

    console.log('✅ [REPORTS API] Main query successful, results:', results.length);

    // Transformation des résultats
    const reports = results.map((report) => {
      // Conversion des champs JSON avec debugging amélioré
      let data = {}
      try {
        if (report.data) {
          // Check if data is already an object (MySQL JSON type)
          if (typeof report.data === 'object') {
            data = report.data;
          } else if (typeof report.data === 'string') {
            // Check for invalid JSON strings like "[object Object]"
            if (report.data === '[object Object]' || report.data.startsWith('[object ')) {
              console.warn(`⚠️ [REPORTS] Invalid JSON data for report ${report.id}: ${report.data}`);
              data = {};
            } else {
              data = JSON.parse(report.data);
            }
          } else {
            console.warn(`⚠️ [REPORTS] Unexpected data type for report ${report.id}:`, typeof report.data);
            data = {};
          }
        }
      } catch (e) {
        console.error(`❌ [REPORTS] Error parsing report data for report ${report.id}:`, e);
        console.error(`❌ [REPORTS] Raw data:`, report.data);
        data = {}
      }

      return {
        id: report.id,
        type: report.type,
        title: report.title,
        description: report.description,
        date: report.date,
        shift: report.shift,
        machineId: report.machine_id,
        machineName: report.machine_name,
        status: report.status,
        generatedAt: report.generated_at,
        generatedBy: report.generatedBy,
        fileSize: report.file_size,
        version: report.version,
        ...data,
      }
    })

    console.log('✅ [REPORTS API] Sending response with', reports.length, 'reports');

    res.json({
      reports,
      total,
      page: pageInt,
      pageSize: pageSizeInt,
      totalPages: Math.ceil(total / pageSizeInt),
    })

  } catch (err) {
    console.error("❌ [REPORTS API] Error fetching reports:", err)
    console.error("❌ [REPORTS API] Error code:", err.code, "errno:", err.errno);

    // Handle specific MySQL errors
    if (err.code === 'ER_WRONG_ARGUMENTS' || err.errno === 1210) {
      console.error("❌ [REPORTS API] MySQL parameter binding error - query params:", queryParams);
      return res.status(400).json({
        error: "Invalid query parameters. Please check your request parameters.",
        code: "INVALID_PARAMETERS",
        details: "MySQL parameter binding failed"
      });
    }

    // Handle malformed packet errors
    if (err.code === 'ER_MALFORMED_PACKET' || err.errno === 1835) {
      console.error("❌ [REPORTS API] MySQL malformed packet error");
      console.error("❌ [REPORTS API] Query:", err.sql);
      console.error("❌ [REPORTS API] Expected params vs actual:", queryParams);
      return res.status(500).json({
        error: "Database communication error. Please try again.",
        code: "DATABASE_COMMUNICATION_ERROR",
        details: "MySQL malformed packet"
      });
    }

    // Handle specific timeout errors
    if (err.message.includes('timeout')) {
      return res.status(408).json({
        error: "Database query timeout. Please try again with a smaller date range.",
        code: "QUERY_TIMEOUT"
      })
    }

    // Handle other MySQL errors
    if (err.code && err.code.startsWith('ER_')) {
      return res.status(500).json({
        error: "Database error occurred. Please try again later.",
        code: "DATABASE_ERROR",
        mysqlCode: err.code
      });
    }

    res.status(500).json({
      error: "Server error while fetching reports",
      code: "SERVER_ERROR"
    })
  }
})

// Récupérer un rapport spécifique
router.get("/:id", auth, bypassPermission, async (req, res) => {
  try {
    const reportId = req.params.id

    db.execute(
      `SELECT r.*, u.username as generatedBy
       FROM reports r
       LEFT JOIN users u ON r.generated_by = u.id
       WHERE r.id = ?`,
      [reportId],
      (err, results) => {
        if (err) {
          console.error("Database error:", err)
          return res.status(500).json({ error: "Server error" })
        }

        if (results.length === 0) {
          return res.status(404).json({ error: "Report not found" })
        }

        const report = results[0]

        // Conversion des champs JSON
        let data = {}
        try {
          data = JSON.parse(report.data)
        } catch (e) {
          console.error("Error parsing report data:", e)
        }

        const formattedReport = {
          id: report.id,
          type: report.type,
          title: report.title,
          description: report.description,
          date: report.date,
          shift: report.shift,
          machineId: report.machine_id,
          machineName: report.machine_name,
          status: report.status,
          generatedAt: report.generated_at,
          generatedBy: report.generatedBy,
          ...data,
        }

        res.json(formattedReport)
      },
    )
  } catch (err) {
    console.error("Error fetching report:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Exporter un rapport
router.get("/:id/export", auth, bypassPermission, async (req, res) => {
  try {
    const reportId = req.params.id
    const format = req.query.format || "pdf"

    // Récupérer le rapport
    db.execute(
      `SELECT r.*, u.username as generatedBy
       FROM reports r
       LEFT JOIN users u ON r.generated_by = u.id
       WHERE r.id = ?`,
      [reportId],
      (err, results) => {
        if (err) {
          console.error("Database error:", err)
          return res.status(500).json({ error: "Server error" })
        }

        if (results.length === 0) {
          return res.status(404).json({ error: "Report not found" })
        }

        const report = results[0]

        // Conversion des champs JSON
        let data = {}
        try {
          data = JSON.parse(report.data)
        } catch (e) {
          console.error("Error parsing report data:", e)
        }

        const formattedReport = {
          id: report.id,
          type: report.type,
          title: report.title,
          description: report.description,
          date: report.date,
          shift: report.shift,
          machineId: report.machine_id,
          machineName: report.machine_name,
          status: report.status,
          generatedAt: report.generated_at,
          generatedBy: report.generatedBy,
          ...data,
        }

        // Exporter selon le format demandé
        switch (format) {
          case "pdf":
            exportToPdf(formattedReport, res)
            break
          case "excel":
            exportToExcel(formattedReport, res)
            break
          case "csv":
            exportToCsv(formattedReport, res)
            break
          default:
            res.status(400).json({ error: "Format non supporté" })
        }
      },
    )
  } catch (err) {
    console.error("Error exporting report:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Fonction pour exporter en PDF
const exportToPdf = (report, res) => {
  try {
    // Créer un nouveau document PDF
    const doc = new PDFDocument({ margin: 50 })

    // Configurer les en-têtes de réponse
    res.setHeader("Content-Type", "application/pdf")
    res.setHeader("Content-Disposition", `attachment; filename=rapport_${report.id}.pdf`)

    // Pipe le PDF directement dans la réponse
    doc.pipe(res)

    // Ajouter le contenu au PDF
    doc.fontSize(25).text(`Rapport #${report.id}`, { align: "center" })
    doc.moveDown()

    doc.fontSize(14).text(`Type: ${getReportTypeName(report.type)}`)
    doc.fontSize(14).text(`Date: ${dayjs(report.date).format("DD/MM/YYYY")}`)

    if (report.shift) {
      doc.fontSize(14).text(`Quart: ${getShiftName(report.shift)}`)
    }

    if (report.machineName) {
      doc.fontSize(14).text(`Machine: ${report.machineName}`)
    }

    doc.moveDown()
    doc.fontSize(12).text(`Généré le: ${dayjs(report.generatedAt).format("DD/MM/YYYY HH:mm")}`)
    doc.fontSize(12).text(`Généré par: ${report.generatedBy}`)

    doc.moveDown()
    doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke()
    doc.moveDown()

    // Ajouter le contenu spécifique au type de rapport
    switch (report.type) {
      case "shift":
        addShiftReportContent(doc, report)
        break
      case "daily":
        addDailyReportContent(doc, report)
        break
      case "weekly":
        addWeeklyReportContent(doc, report)
        break
      case "machine":
        addMachineReportContent(doc, report)
        break
      default:
        doc.fontSize(12).text("Contenu détaillé non disponible pour ce type de rapport.")
    }

    // Ajouter un pied de page
    const pageCount = doc.bufferedPageRange().count
    for (let i = 0; i < pageCount; i++) {
      doc.switchToPage(i)

      // Pied de page
      const bottom = doc.page.height - 50
      doc.fontSize(10).text(`Page ${i + 1} sur ${pageCount}`, 50, bottom, { align: "center" })
    }

    // Finaliser le PDF
    doc.end()
  } catch (error) {
    console.error("Error generating PDF:", error)
    res.status(500).json({ error: "Error generating PDF" })
  }
}

// Fonction pour exporter en Excel
const exportToExcel = (report, res) => {
  try {
    // Créer un nouveau classeur Excel
    const workbook = new ExcelJS.Workbook()
    workbook.creator = "Somipem Dashboard"
    workbook.created = new Date()

    // Ajouter une feuille
    const worksheet = workbook.addWorksheet(`Rapport ${report.id}`)

    // Ajouter les en-têtes et les informations générales
    worksheet.mergeCells("A1:E1")
    worksheet.getCell("A1").value = `Rapport #${report.id}`
    worksheet.getCell("A1").font = { size: 16, bold: true }
    worksheet.getCell("A1").alignment = { horizontal: "center" }

    worksheet.getCell("A3").value = "Type:"
    worksheet.getCell("B3").value = getReportTypeName(report.type)

    worksheet.getCell("A4").value = "Date:"
    worksheet.getCell("B4").value = dayjs(report.date).format("DD/MM/YYYY")

    if (report.shift) {
      worksheet.getCell("A5").value = "Quart:"
      worksheet.getCell("B5").value = getShiftName(report.shift)
    }

    if (report.machineName) {
      worksheet.getCell("A6").value = "Machine:"
      worksheet.getCell("B6").value = report.machineName
    }

    worksheet.getCell("A8").value = "Généré le:"
    worksheet.getCell("B8").value = dayjs(report.generatedAt).format("DD/MM/YYYY HH:mm")

    worksheet.getCell("A9").value = "Généré par:"
    worksheet.getCell("B9").value = report.generatedBy

    // Ajouter le contenu spécifique au type de rapport
    let rowIndex = 11

    switch (report.type) {
      case "shift":
        rowIndex = addShiftReportExcelContent(worksheet, report, rowIndex)
        break
      case "daily":
        rowIndex = addDailyReportExcelContent(worksheet, report, rowIndex)
        break
      case "weekly":
        rowIndex = addWeeklyReportExcelContent(worksheet, report, rowIndex)
        break
      case "machine":
        rowIndex = addMachineReportExcelContent(worksheet, report, rowIndex)
        break
      default:
        worksheet.getCell(`A${rowIndex}`).value = "Contenu détaillé non disponible pour ce type de rapport."
    }

    // Configurer les en-têtes de réponse
    res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    res.setHeader("Content-Disposition", `attachment; filename=rapport_${report.id}.xlsx`)

    // Écrire le classeur dans la réponse
    workbook.xlsx
      .write(res)
      .then(() => {
        res.end()
      })
      .catch((error) => {
        console.error("Error writing Excel:", error)
        res.status(500).json({ error: "Error generating Excel file" })
      })
  } catch (error) {
    console.error("Error generating Excel:", error)
    res.status(500).json({ error: "Error generating Excel file" })
  }
}

// Fonction pour exporter en CSV
const exportToCsv = (report, res) => {
  try {
    // Préparer les données pour le CSV
    let fields = []
    let data = {}

    // Ajouter les champs communs
    fields = ["id", "type", "date", "generatedAt", "generatedBy"]
    data = {
      id: report.id,
      type: getReportTypeName(report.type),
      date: dayjs(report.date).format("DD/MM/YYYY"),
      generatedAt: dayjs(report.generatedAt).format("DD/MM/YYYY HH:mm"),
      generatedBy: report.generatedBy,
    }

    // Ajouter les champs spécifiques au type de rapport
    switch (report.type) {
      case "shift":
        fields = fields.concat([
          "shift",
          "startTime",
          "endTime",
          "productionTotal",
          "productionRate",
          "activeMachines",
          "alertsTotal",
          "machinesWithAlerts",
          "maintenanceTotal",
          "maintenanceDuration",
        ])
        data = {
          ...data,
          shift: getShiftName(report.shift),
          startTime: report.startTime,
          endTime: report.endTime,
          productionTotal: report.production.total,
          productionRate: report.production.rate,
          activeMachines: report.production.activeMachines,
          alertsTotal: report.alerts.total,
          machinesWithAlerts: report.alerts.machinesWithAlerts,
          maintenanceTotal: report.maintenance.total,
          maintenanceDuration: report.maintenance.duration,
        }
        break

      case "daily":
        fields = fields.concat([
          "productionTotal",
          "productionTarget",
          "productionPerformance",
          "qualityRate",
          "rejects",
          "rejectRate",
        ])
        data = {
          ...data,
          productionTotal: report.production.total,
          productionTarget: report.production.target,
          productionPerformance: report.production.performance,
          qualityRate: report.quality.rate,
          rejects: report.quality.rejects,
          rejectRate: report.quality.rejectRate,
        }
        break

      case "machine":
        fields = fields.concat([
          "machineName",
          "status",
          "productionTotal",
          "averageRate",
          "efficiency",
          "uptime",
          "downtime",
          "availability",
        ])
        data = {
          ...data,
          machineName: report.machineName,
          status: report.status === "operational" ? "Opérationnelle" : "En maintenance",
          productionTotal: report.production.total,
          averageRate: report.production.averageRate,
          efficiency: report.efficiency,
          uptime: report.uptime,
          downtime: report.downtime,
          availability: report.availability,
        }
        break
    }

    // Convertir en CSV
    const json2csvParser = new Parser({ fields })
    const csv = json2csvParser.parse(data)

    // Configurer les en-têtes de réponse
    res.setHeader("Content-Type", "text/csv")
    res.setHeader("Content-Disposition", `attachment; filename=rapport_${report.id}.csv`)

    // Envoyer le CSV
    res.send(csv)
  } catch (error) {
    console.error("Error generating CSV:", error)
    res.status(500).json({ error: "Error generating CSV file" })
  }
}

// Fonctions d'aide pour le contenu des rapports PDF
const addShiftReportContent = (doc, report) => {
  doc.fontSize(16).text("Production", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Production totale: ${report.production.total} unités`)
  doc.fontSize(12).text(`Taux de production: ${report.production.rate} unités/heure`)
  doc.fontSize(12).text(`Machines actives: ${report.production.activeMachines}`)

  doc.moveDown()
  doc.fontSize(16).text("Alertes", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Nombre total d'alertes: ${report.alerts.total}`)
  doc.fontSize(12).text(`Machines avec alertes: ${report.alerts.machinesWithAlerts}`)

  doc.moveDown()
  doc.fontSize(16).text("Maintenance", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Événements de maintenance: ${report.maintenance.total}`)
  doc.fontSize(12).text(`Durée totale de maintenance: ${report.maintenance.duration} minutes`)

  if (report.notes) {
    doc.moveDown()
    doc.fontSize(16).text("Notes", { underline: true })
    doc.moveDown(0.5)
    doc.fontSize(12).text(report.notes)
  }
}

const addDailyReportContent = (doc, report) => {
  doc.fontSize(16).text("Résumé de production", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Production totale: ${report.production.total} unités`)
  doc.fontSize(12).text(`Objectif: ${report.production.target} unités`)
  doc.fontSize(12).text(`Performance: ${report.production.performance}%`)

  doc.moveDown()
  doc.fontSize(16).text("Résumé par quart", { underline: true })
  doc.moveDown(0.5)

  // Tableau des quarts
  const shiftTableTop = doc.y
  doc.fontSize(10)

  // En-têtes du tableau
  doc.text("Quart", 50, shiftTableTop)
  doc.text("Production", 150, shiftTableTop)
  doc.text("Performance", 250, shiftTableTop)
  doc.text("Alertes", 350, shiftTableTop)

  doc
    .moveTo(50, shiftTableTop + 15)
    .lineTo(450, shiftTableTop + 15)
    .stroke()

  // Lignes du tableau
  let yPos = shiftTableTop + 25
  report.shifts.forEach((shift) => {
    doc.text(shift.name, 50, yPos)
    doc.text(`${shift.production} unités`, 150, yPos)
    doc.text(`${shift.performance}%`, 250, yPos)
    doc.text(shift.alerts.toString(), 350, yPos)
    yPos += 20
  })

  doc.moveDown()
  doc.fontSize(16).text("Qualité", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Taux de qualité: ${report.quality.rate}%`)
  doc.fontSize(12).text(`Rejets: ${report.quality.rejects} unités`)
  doc.fontSize(12).text(`Taux de rejet: ${report.quality.rejectRate}%`)
}

const addWeeklyReportContent = (doc, report) => {
  doc.fontSize(16).text("Résumé hebdomadaire", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Semaine: ${report.weekNumber}`)
  doc
    .fontSize(12)
    .text(`Période: ${dayjs(report.startDate).format("DD/MM/YYYY")} - ${dayjs(report.endDate).format("DD/MM/YYYY")}`)
  doc.fontSize(12).text(`Production totale: ${report.production.total} unités`)

  doc.moveDown()
  doc.fontSize(16).text("Performance", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Performance moyenne: ${report.performance.average}%`)
  doc
    .fontSize(12)
    .text(
      `Meilleur jour: ${dayjs(report.performance.bestDay.date).format("dddd DD/MM")} (${report.performance.bestDay.performance}%)`,
    )
  doc
    .fontSize(12)
    .text(
      `Jour le moins performant: ${dayjs(report.performance.worstDay.date).format("dddd DD/MM")} (${report.performance.worstDay.performance}%)`,
    )

  doc.moveDown()
  doc.fontSize(16).text("Tendances journalières", { underline: true })
  doc.moveDown(0.5)

  // Tableau des données journalières
  const dailyTableTop = doc.y
  doc.fontSize(10)

  // En-têtes du tableau
  doc.text("Jour", 50, dailyTableTop)
  doc.text("Production", 150, dailyTableTop)
  doc.text("Performance", 250, dailyTableTop)
  doc.text("Qualité", 350, dailyTableTop)
  doc.text("Alertes", 450, dailyTableTop)

  doc
    .moveTo(50, dailyTableTop + 15)
    .lineTo(500, dailyTableTop + 15)
    .stroke()

  // Lignes du tableau
  let yPos = dailyTableTop + 25
  report.dailyData.forEach((day) => {
    doc.text(dayjs(day.date).format("dddd DD/MM"), 50, yPos)
    doc.text(`${day.production} unités`, 150, yPos)
    doc.text(`${day.performance}%`, 250, yPos)
    doc.text(`${day.quality}%`, 350, yPos)
    doc.text(day.alerts.toString(), 450, yPos)
    yPos += 20
  })
}

const addMachineReportContent = (doc, report) => {
  doc.fontSize(16).text("Informations machine", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Machine: ${report.machineName}`)
  doc.fontSize(12).text(`État: ${report.status === "operational" ? "Opérationnelle" : "En maintenance"}`)
  doc
    .fontSize(12)
    .text(`Période: ${dayjs(report.startDate).format("DD/MM/YYYY")} - ${dayjs(report.endDate).format("DD/MM/YYYY")}`)

  doc.moveDown()
  doc.fontSize(16).text("Performance", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Production totale: ${report.production.total} unités`)
  doc.fontSize(12).text(`Taux de production moyen: ${report.production.averageRate} unités/heure`)
  doc.fontSize(12).text(`Efficacité: ${report.efficiency}%`)

  doc.moveDown()
  doc.fontSize(16).text("Maintenance", { underline: true })
  doc.moveDown(0.5)
  doc.fontSize(12).text(`Temps de fonctionnement: ${report.uptime} heures`)
  doc.fontSize(12).text(`Temps d'arrêt: ${report.downtime} heures`)
  doc.fontSize(12).text(`Disponibilité: ${report.availability}%`)

  if (report.maintenanceEvents && report.maintenanceEvents.length > 0) {
    doc.moveDown()
    doc.fontSize(16).text("Événements de maintenance", { underline: true })
    doc.moveDown(0.5)

    // Tableau des événements de maintenance
    const maintenanceTableTop = doc.y
    doc.fontSize(10)

    // En-têtes du tableau
    doc.text("Date", 50, maintenanceTableTop)
    doc.text("Type", 150, maintenanceTableTop)
    doc.text("Durée", 250, maintenanceTableTop)
    doc.text("Technicien", 350, maintenanceTableTop)

    doc
      .moveTo(50, maintenanceTableTop + 15)
      .lineTo(450, maintenanceTableTop + 15)
      .stroke()

    // Lignes du tableau
    let yPos = maintenanceTableTop + 25
    report.maintenanceEvents.forEach((event) => {
      // Vérifier s'il faut ajouter une nouvelle page
      if (yPos > doc.page.height - 100) {
        doc.addPage()
        yPos = 50
      }

      doc.text(dayjs(event.date).format("DD/MM/YYYY"), 50, yPos)
      doc.text(event.type, 150, yPos)
      doc.text(`${event.duration} minutes`, 250, yPos)
      doc.text(event.technician, 350, yPos)
      yPos += 20
    })
  }
}

// Fonctions d'aide pour le contenu des rapports Excel
const addShiftReportExcelContent = (worksheet, report, rowIndex) => {
  // Titre de section
  worksheet.mergeCells(`A${rowIndex}:E${rowIndex}`)
  worksheet.getCell(`A${rowIndex}`).value = "Production"
  worksheet.getCell(`A${rowIndex}`).font = { size: 14, bold: true }
  rowIndex += 1

  // Données de production
  worksheet.getCell(`A${rowIndex}`).value = "Production totale:"
  worksheet.getCell(`B${rowIndex}`).value = `${report.production.total} unités`
  rowIndex += 1

  worksheet.getCell(`A${rowIndex}`).value = "Taux de production:"
  worksheet.getCell(`B${rowIndex}`).value = `${report.production.rate} unités/heure`
  rowIndex += 1

  worksheet.getCell(`A${rowIndex}`).value = "Machines actives:"
  worksheet.getCell(`B${rowIndex}`).value = report.production.activeMachines
  rowIndex += 2

  // Titre de section
  worksheet.mergeCells(`A${rowIndex}:E${rowIndex}`)
  worksheet.getCell(`A${rowIndex}`).value = "Alertes"
  worksheet.getCell(`A${rowIndex}`).font = { size: 14, bold: true }
  rowIndex += 1

  // Données d'alertes
  worksheet.getCell(`A${rowIndex}`).value = "Nombre total d'alertes:"
  worksheet.getCell(`B${rowIndex}`).value = report.alerts.total
  rowIndex += 1

  worksheet.getCell(`A${rowIndex}`).value = "Machines avec alertes:"
  worksheet.getCell(`B${rowIndex}`).value = report.alerts.machinesWithAlerts
  rowIndex += 2

  // Titre de section
  worksheet.mergeCells(`A${rowIndex}:E${rowIndex}`)
  worksheet.getCell(`A${rowIndex}`).value = "Maintenance"
  worksheet.getCell(`A${rowIndex}`).font = { size: 14, bold: true }
  rowIndex += 1

  // Données de maintenance
  worksheet.getCell(`A${rowIndex}`).value = "Événements de maintenance:"
  worksheet.getCell(`B${rowIndex}`).value = report.maintenance.total
  rowIndex += 1

  worksheet.getCell(`A${rowIndex}`).value = "Durée totale de maintenance:"
  worksheet.getCell(`B${rowIndex}`).value = `${report.maintenance.duration} minutes`
  rowIndex += 1

  return rowIndex
}

const addDailyReportExcelContent = (worksheet, report, rowIndex) => {
  // Titre de section
  worksheet.mergeCells(`A${rowIndex}:E${rowIndex}`)
  worksheet.getCell(`A${rowIndex}`).value = "Résumé de production"
  worksheet.getCell(`A${rowIndex}`).font = { size: 14, bold: true }
  rowIndex += 1

  // Données de production
  worksheet.getCell(`A${rowIndex}`).value = "Production totale:"
  worksheet.getCell(`B${rowIndex}`).value = `${report.production.total} unités`
  rowIndex += 1

  worksheet.getCell(`A${rowIndex}`).value = "Objectif:"
  worksheet.getCell(`B${rowIndex}`).value = `${report.production.target} unités`
  rowIndex += 1

  worksheet.getCell(`A${rowIndex}`).value = "Performance:"
  worksheet.getCell(`B${rowIndex}`).value = `${report.production.performance}%`
  rowIndex += 2

  // Titre de section
  worksheet.mergeCells(`A${rowIndex}:E${rowIndex}`)
  worksheet.getCell(`A${rowIndex}`).value = "Résumé par quart"
  worksheet.getCell(`A${rowIndex}`).font = { size: 14, bold: true }
  rowIndex += 1

  // En-têtes du tableau
  worksheet.getCell(`A${rowIndex}`).value = "Quart"
  worksheet.getCell(`B${rowIndex}`).value = "Production"
  worksheet.getCell(`C${rowIndex}`).value = "Performance"
  worksheet.getCell(`D${rowIndex}`).value = "Alertes"

  // Style des en-têtes
  ;["A", "B", "C", "D"].forEach((col) => {
    worksheet.getCell(`${col}${rowIndex}`).font = { bold: true }
    worksheet.getCell(`${col}${rowIndex}`).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    }
  })

  rowIndex += 1

  // Données des quarts
  report.shifts.forEach((shift) => {
    worksheet.getCell(`A${rowIndex}`).value = shift.name
    worksheet.getCell(`B${rowIndex}`).value = `${shift.production} unités`
    worksheet.getCell(`C${rowIndex}`).value = `${shift.performance}%`
    worksheet.getCell(`D${rowIndex}`).value = shift.alerts
    rowIndex += 1
  })

  rowIndex += 1

  // Titre de section
  worksheet.mergeCells(`A${rowIndex}:E${rowIndex}`)
  worksheet.getCell(`A${rowIndex}`).value = "Qualité"
  worksheet.getCell(`A${rowIndex}`).font = { size: 14, bold: true }
  rowIndex += 1

  // Données de qualité
  worksheet.getCell(`A${rowIndex}`).value = "Taux de qualité:"
  worksheet.getCell(`B${rowIndex}`).value = `${report.quality.rate}%`
  rowIndex += 1

  worksheet.getCell(`A${rowIndex}`).value = "Rejets:"
  worksheet.getCell(`B${rowIndex}`).value = `${report.quality.rejects} unités`
  rowIndex += 1

  worksheet.getCell(`A${rowIndex}`).value = "Taux de rejet:"
  worksheet.getCell(`B${rowIndex}`).value = `${report.quality.rejectRate}%`
  rowIndex += 1

  return rowIndex
}

const addWeeklyReportExcelContent = (worksheet, report, rowIndex) => {
  // Implémentation similaire aux autres fonctions
  // ...
  return rowIndex
}

const addMachineReportExcelContent = (worksheet, report, rowIndex) => {
  // Implémentation similaire aux autres fonctions
  // ...
  return rowIndex
}

// Fonctions utilitaires
const getReportTypeName = (type) => {
  const types = {
    shift: "Rapport de quart",
    daily: "Rapport journalier",
    weekly: "Rapport hebdomadaire",
    monthly: "Rapport mensuel",
    machine: "Rapport de machine",
    production: "Rapport de production",
    maintenance: "Rapport de maintenance",
    quality: "Rapport de qualité",
    financial: "Rapport financier",
    custom: "Rapport personnalisé",
  }

  return types[type] || type
}

const getShiftName = (shift) => {
  const shifts = {
    morning: "Matin (06:00 - 14:00)",
    afternoon: "Après-midi (14:00 - 22:00)",
    night: "Nuit (22:00 - 06:00)",
  }

  return shifts[shift] || shift
}

// Créer un rapport de quart
router.post("/shift", auth, async (req, res) => {
  try {
    const { shiftId, date } = req.body

    // Générer le rapport de quart
    const report = await ShiftReportService.generateShiftReport(shiftId, new Date(date))

    // Sauvegarder le rapport dans la base de données
    db.execute(
      `INSERT INTO reports (type, title, description, date, shift, status, data, generated_at, generated_by)
       VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?)`,
      [
        "shift",
        `Rapport de quart: ${report.shiftName}`,
        `Rapport de quart pour le ${dayjs(report.date).format("DD/MM/YYYY")}`,
        report.date,
        report.shiftId === 1 ? "morning" : report.shiftId === 2 ? "afternoon" : "night",
        "completed",
        JSON.stringify(report),
        req.user.id,
      ],
      (err, result) => {
        if (err) {
          console.error("Database error:", err)
          return res.status(500).json({ error: "Server error" })
        }

        res.status(201).json({
          id: result.insertId,
          ...report,
        })
      },
    )
  } catch (err) {
    console.error("Error creating shift report:", err)
    res.status(500).json({ error: "Server error" })
  }
})

export default router;

