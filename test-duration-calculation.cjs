// Test duration calculation from the sample data
const sampleStop = {
  Date_Insert: '30/09/2024 23:47',
  Machine_Name: 'IPS01',
  Part_NO: '1254',
  Code_Stop: 'Machine OFF',
  Debut_Stop: '30/09/2024 23:16',
  Fin_Stop_Time: '30/09/2024 23:47',
  Regleur_Prenom: '',
  duration_minutes: null
};

console.log('🔍 Testing duration calculation...');
console.log('Sample stop:', sampleStop);

// Parse date format: DD/MM/YYYY HH:mm
const parseDateTime = (dateStr) => {
  const [datePart, timePart] = dateStr.split(' ');
  const [day, month, year] = datePart.split('/');
  const [hours, minutes] = timePart.split(':');
  return new Date(year, month - 1, day, hours, minutes);
};

try {
  const startTime = parseDateTime(sampleStop.Debut_Stop);
  const endTime = parseDateTime(sampleStop.Fin_Stop_Time);
  console.log('Start time:', startTime);
  console.log('End time:', endTime);
  
  if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
    const durationMs = endTime - startTime;
    const durationMinutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
    
    console.log('Duration:', {
      milliseconds: durationMs,
      minutes: durationMinutes
    });
    
    if (durationMinutes > 0) {
      console.log('✅ Duration calculation works!');
    } else {
      console.log('⚠️ Duration is 0 or negative');
    }
  } else {
    console.log('❌ Invalid dates after parsing');
  }
} catch (error) {
  console.error('❌ Error calculating duration:', error);
}
