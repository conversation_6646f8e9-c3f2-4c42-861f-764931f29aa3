import pool, { execute } from './db.js';

async function testAllGraphQLQueries() {
  console.log('=== Comprehensive GraphQL Queries Testing ===');
  
  try {
    console.log('\n🚀 Testing getFinalComprehensiveStopData with various filters...\n');
    
    // Test 1: No filters (baseline)
    await testComprehensiveStopData({}, 'No filters (baseline)');
    
    // Test 2: Date range filter
    await testComprehensiveStopData({
      startDate: '2024-09-01',
      endDate: '2024-09-30'
    }, 'Date range filter (September 2024)');
    
    // Test 3: Machine model filter
    await testComprehensiveStopData({
      model: 'IPS'
    }, 'Machine model filter (IPS)');
    
    // Test 4: Specific machine filter
    await testComprehensiveStopData({
      machine: 'IPS01'
    }, 'Specific machine filter (IPS01)');
    
    // Test 5: Combined filters
    await testComprehensiveStopData({
      startDate: '2024-09-01',
      endDate: '2024-09-30',
      model: 'IPS',
      limit: 100
    }, 'Combined filters (Date + Model + Limit)');
    
    // Test 6: Empty result scenario
    await testComprehensiveStopData({
      startDate: '2025-01-01',
      endDate: '2025-01-31'
    }, 'Empty result scenario (Future dates)');
    
    console.log('\n🔧 Testing utility resolvers...\n');
    
    // Test 7: Machine models
    await testMachineModels();
    
    // Test 8: Machine names (no filter)
    await testMachineNames({}, 'All machine names');
    
    // Test 9: Machine names with model filter
    await testMachineNames({ model: 'IPS' }, 'Machine names for IPS model');
    
    // Test 10: Machine names with non-existent model
    await testMachineNames({ model: 'NONEXISTENT' }, 'Machine names for non-existent model');
    
    console.log('\n✅ All GraphQL query tests completed!\n');
    
  } catch (error) {
    console.error('❌ Error in comprehensive testing:', error);
  } finally {
    process.exit(0);
  }
}

async function testComprehensiveStopData(filters, testName) {
  console.log(`📊 Testing: ${testName}`);
  console.log(`   Filters:`, JSON.stringify(filters, null, 2));
  
  const startTime = Date.now();
  const limit = filters.limit || 1000;
  
  try {
    // Build query conditions (same logic as GraphQL resolver)
    const conditions = [];
    const queryParams = [];
    
    // Apply machine filtering
    if (filters.model && !filters.machine) {
      conditions.push(`Machine_Name LIKE ?`);
      queryParams.push(`${filters.model}%`);
    } else if (filters.machine) {
      conditions.push(`Machine_Name = ?`);
      queryParams.push(filters.machine);
    }
    
    // Apply date filtering
    if (filters.startDate && filters.endDate) {
      const parseDateColumn = `
        COALESCE(
          DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
          DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i'), '%Y-%m-%d'),
          DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
          DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i'), '%Y-%m-%d')
        )
      `;
      conditions.push(`${parseDateColumn} BETWEEN ? AND ?`);
      queryParams.push(filters.startDate, filters.endDate);
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // Main data query
    const allStopsQuery = `
      SELECT 
        Machine_Name,
        Date_Insert,
        Part_NO,
        Code_Stop,
        Debut_Stop,
        Fin_Stop_Time,
        Regleur_Prenom,
        COALESCE(
          TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          ), 15
        ) AS duration_minutes
      FROM machine_stop_table_mould
      ${whereClause}
      ORDER BY Date_Insert DESC
      LIMIT ${limit}
    `;
    
    const [allStopsResult] = await execute(allStopsQuery);
    
    // Test all aggregation queries
    const aggregationQueries = [
      {
        name: 'Top stops',
        query: `
          SELECT 
            Code_Stop AS stopName,
            COUNT(*) AS count
          FROM machine_stop_table_mould
          ${whereClause}
          GROUP BY Code_Stop
          ORDER BY count DESC
          LIMIT 5
        `
      },
      {
        name: 'Stop reasons',
        query: `
          SELECT 
            Code_Stop AS reason,
            COUNT(*) AS count
          FROM machine_stop_table_mould
          ${whereClause}
          GROUP BY Code_Stop
          ORDER BY count DESC
        `
      },
      {
        name: 'Machine comparison',
        query: `
          SELECT 
            Machine_Name,
            COUNT(*) AS stops,
            SUM(COALESCE(
              TIMESTAMPDIFF(MINUTE,
                STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
                STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
              ), 15
            )) AS totalDuration
          FROM machine_stop_table_mould
          ${whereClause}
          GROUP BY Machine_Name
          ORDER BY stops DESC
        `
      },
      {
        name: 'Operator stats',
        query: `
          SELECT 
            Regleur_Prenom AS operator,
            COUNT(*) AS interventions,
            SUM(COALESCE(
              TIMESTAMPDIFF(MINUTE,
                STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
                STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
              ), 15
            )) AS totalDuration
          FROM machine_stop_table_mould
          ${whereClause}
          ${whereClause ? 'AND' : 'WHERE'} Regleur_Prenom IS NOT NULL 
          AND Regleur_Prenom != ''
          GROUP BY Regleur_Prenom
          ORDER BY interventions DESC
        `
      },
      {
        name: 'Duration trend',
        query: `
          SELECT 
            HOUR(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')) AS hour,
            AVG(COALESCE(
              TIMESTAMPDIFF(MINUTE,
                STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
                STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
              ), 15
            )) AS avgDuration
          FROM machine_stop_table_mould
          ${whereClause}
          GROUP BY hour
          ORDER BY hour
        `
      },
      {
        name: 'Stop stats',
        query: `
          SELECT 
            DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') AS Stop_Date,
            COUNT(*) AS Total_Stops
          FROM machine_stop_table_mould
          ${whereClause}
          GROUP BY Stop_Date
          ORDER BY Stop_Date DESC
          LIMIT 30
        `
      },
      {
        name: 'Total stops (sidecards)',
        query: `
          SELECT COUNT(*) AS total
          FROM machine_stop_table_mould
          ${whereClause}
        `
      },
      {
        name: 'Non-declared stops (sidecards)',
        query: `
          SELECT COUNT(*) AS non_declared
          FROM machine_stop_table_mould
          ${whereClause}
          ${whereClause ? 'AND' : 'WHERE'} Code_Stop = 'Arrêt non déclaré'
        `
      }
    ];
    
    const aggregationResults = [];
    
    for (const aggQuery of aggregationQueries) {
      try {
        const [result] = await execute(aggQuery.query, queryParams);
        aggregationResults.push({
          name: aggQuery.name,
          success: true,
          data: result,
          count: result.length
        });
      } catch (error) {
        aggregationResults.push({
          name: aggQuery.name,
          success: false,
          error: error.message
        });
      }
    }
    
    const executionTime = Date.now() - startTime;
    
    // Build sidecards
    const sidecards = {
      Arret_Totale: aggregationResults[6].success ? aggregationResults[6].data[0]?.total || 0 : 0,
      Arret_Totale_nondeclare: aggregationResults[7].success ? aggregationResults[7].data[0]?.non_declared || 0 : 0
    };
    
    // Results summary
    console.log(`   ✅ Main query: ${allStopsResult.length} records`);
    console.log(`   📊 Sidecards: Total=${sidecards.Arret_Totale}, Non-declared=${sidecards.Arret_Totale_nondeclare}`);
    
    // Show aggregation results
    aggregationResults.forEach(result => {
      if (result.success) {
        console.log(`   ✅ ${result.name}: ${result.count} records`);
      } else {
        console.log(`   ❌ ${result.name}: ERROR - ${result.error}`);
      }
    });
    
    console.log(`   ⏱️  Execution time: ${executionTime}ms\n`);
    
    // Additional validation
    if (allStopsResult.length > 0) {
      const sampleStop = allStopsResult[0];
      console.log(`   📋 Sample stop: ${sampleStop.Machine_Name} - ${sampleStop.Code_Stop} (${sampleStop.duration_minutes} min)`);
    }
    
  } catch (error) {
    console.log(`   ❌ ERROR: ${error.message}\n`);
  }
}

async function testMachineModels() {
  console.log(`🔧 Testing: Machine Models Resolver`);
  
  try {
    const query = `
      SELECT DISTINCT
        CASE
          WHEN Machine_Name REGEXP '^[A-Za-z]+[0-9]'
          THEN REGEXP_REPLACE(Machine_Name, '[0-9].*$', '')
          ELSE Machine_Name
        END AS model
      FROM machine_stop_table_mould
      WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
      ORDER BY model
    `;
    
    const [result] = await execute(query);
    
    console.log(`   ✅ Found ${result.length} machine models:`);
    result.forEach(row => {
      console.log(`      - ${row.model}`);
    });
    
    if (result.length === 0) {
      console.log(`   ⚠️  No models found, would return default models: IPS, AKROS, ML, FCS`);
    }
    
  } catch (error) {
    console.log(`   ❌ ERROR: ${error.message}`);
  }
  
  console.log('');
}

async function testMachineNames(filters, testName) {
  console.log(`🔧 Testing: ${testName}`);
  
  try {
    let query = `
      SELECT DISTINCT Machine_Name
      FROM machine_stop_table_mould
      WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
    `;
    
    const queryParams = [];
    
    if (filters.model) {
      query += ` AND Machine_Name LIKE ?`;
      queryParams.push(`${filters.model}%`);
    }
    
    query += ` ORDER BY Machine_Name`;
    
    const [result] = await execute(query, queryParams);
    
    console.log(`   ✅ Found ${result.length} machine names:`);
    result.slice(0, 10).forEach(row => {
      console.log(`      - ${row.Machine_Name}`);
    });
    
    if (result.length > 10) {
      console.log(`      ... and ${result.length - 10} more`);
    }
    
    if (result.length === 0) {
      console.log(`   ⚠️  No machines found for model "${filters.model || 'all'}"`);
      if (filters.model) {
        console.log(`   ℹ️  Would return default machines for model "${filters.model}"`);
      }
    }
    
  } catch (error) {
    console.log(`   ❌ ERROR: ${error.message}`);
  }
  
  console.log('');
}

testAllGraphQLQueries();
