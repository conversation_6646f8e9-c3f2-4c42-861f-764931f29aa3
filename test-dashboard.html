<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: #52c41a;
            font-weight: bold;
        }
        .error {
            color: #f5222d;
            font-weight: bold;
        }
        .info {
            color: #1890ff;
            font-weight: bold;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-card {
            background: #e6f7ff;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>🔧 Dashboard Test - Arrêts de Machines</h1>
    
    <div class="test-section">
        <h2>📊 Backend Connection Test</h2>
        <button onclick="testBackendConnection()">Test Backend Connection</button>
        <button onclick="testGraphQLQuery()">Test GraphQL Query</button>
        <button onclick="testDateFilter()">Test Date Filter</button>
        <div id="backend-status"></div>
    </div>

    <div class="test-section">
        <h2>📈 Data Analysis</h2>
        <div class="stats" id="stats-container">
            <div class="stat-card">
                <div class="stat-value" id="total-stops">-</div>
                <div class="stat-label">Total Arrêts</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="unique-machines">-</div>
                <div class="stat-label">Machines Uniques</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avg-duration">-</div>
                <div class="stat-label">Durée Moyenne (min)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="filtered-stops">-</div>
                <div class="stat-label">Arrêts Filtrés</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 Data Samples</h2>
        <button onclick="showSampleData()">Show Sample Data</button>
        <div id="sample-data"></div>
    </div>

    <div class="test-section">
        <h2>🎯 Frontend Dashboard</h2>
        <p>Open the main dashboard at: <a href="http://localhost:5173" target="_blank">http://localhost:5173</a></p>
        <p>The dashboard should show:</p>
        <ul>
            <li>✅ Stats cards with total stops and filtered data</li>
            <li>✅ Charts that respond to date filters</li>
            <li>✅ Proper date formatting (DD/MM/YYYY HH:mm)</li>
            <li>✅ No "Unknown" machine labels</li>
            <li>✅ Consistent chart data across all components</li>
        </ul>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        
        async function testBackendConnection() {
            const statusDiv = document.getElementById('backend-status');
            statusDiv.innerHTML = '<div class="info">Testing backend connection...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                statusDiv.innerHTML = `<div class="success">✅ Backend connected successfully</div>`;
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Backend connection failed: ${error.message}</div>`;
            }
        }

        async function testGraphQLQuery() {
            const statusDiv = document.getElementById('backend-status');
            statusDiv.innerHTML = '<div class="info">Testing GraphQL query...</div>';
            
            try {
                const query = `
                    query {
                        getStopDashboardData {
                            allStops {
                                Machine_Name
                                Code_Stop
                                Debut_Stop
                                Fin_Stop_Time
                                duration_minutes
                            }
                            sidecards {
                                Arret_Totale
                                Arret_Totale_nondeclare
                            }
                        }
                    }
                `;
                
                const response = await fetch(`${API_BASE}/graphql`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });
                
                const data = await response.json();
                
                if (data.data && data.data.getStopDashboardData) {
                    const stops = data.data.getStopDashboardData.allStops;
                    statusDiv.innerHTML = `<div class="success">✅ GraphQL query successful - ${stops.length} stops found</div>`;
                    
                    // Update stats
                    updateStats(stops);
                    
                } else {
                    statusDiv.innerHTML = `<div class="error">❌ GraphQL query failed: ${JSON.stringify(data.errors)}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ GraphQL query failed: ${error.message}</div>`;
            }
        }

        async function testDateFilter() {
            const statusDiv = document.getElementById('backend-status');
            statusDiv.innerHTML = '<div class="info">Testing date filter...</div>';
            
            try {
                const query = `
                    query {
                        getStopDashboardData(filters: { startDate: "2024-09-30", endDate: "2024-10-01" }) {
                            allStops {
                                Machine_Name
                                Code_Stop
                                Debut_Stop
                                Fin_Stop_Time
                                duration_minutes
                            }
                            sidecards {
                                Arret_Totale
                                Arret_Totale_nondeclare
                            }
                        }
                    }
                `;
                
                const response = await fetch(`${API_BASE}/graphql`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });
                
                const data = await response.json();
                
                if (data.data && data.data.getStopDashboardData) {
                    const stops = data.data.getStopDashboardData.allStops;
                    statusDiv.innerHTML = `<div class="success">✅ Date filter successful - ${stops.length} stops found for 2024-09-30 to 2024-10-01</div>`;
                    
                    // Update filtered stats
                    document.getElementById('filtered-stops').textContent = stops.length;
                    
                } else {
                    statusDiv.innerHTML = `<div class="error">❌ Date filter failed: ${JSON.stringify(data.errors)}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Date filter failed: ${error.message}</div>`;
            }
        }

        function updateStats(stops) {
            document.getElementById('total-stops').textContent = stops.length;
            
            const uniqueMachines = new Set(stops.map(s => s.Machine_Name).filter(m => m));
            document.getElementById('unique-machines').textContent = uniqueMachines.size;
            
            const durations = stops.map(s => s.duration_minutes).filter(d => d > 0);
            const avgDuration = durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0;
            document.getElementById('avg-duration').textContent = avgDuration.toFixed(1);
        }

        async function showSampleData() {
            const sampleDiv = document.getElementById('sample-data');
            sampleDiv.innerHTML = '<div class="info">Loading sample data...</div>';
            
            try {
                const query = `
                    query {
                        getStopDashboardData {
                            allStops {
                                Machine_Name
                                Code_Stop
                                Debut_Stop
                                Fin_Stop_Time
                                duration_minutes
                                Cause
                                Raison_Arret
                                Operateur
                            }
                        }
                    }
                `;
                
                const response = await fetch(`${API_BASE}/graphql`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query })
                });
                
                const data = await response.json();
                
                if (data.data && data.data.getStopDashboardData) {
                    const stops = data.data.getStopDashboardData.allStops.slice(0, 5); // First 5 stops
                    sampleDiv.innerHTML = `<h3>Sample Data (first 5 stops):</h3><pre>${JSON.stringify(stops, null, 2)}</pre>`;
                } else {
                    sampleDiv.innerHTML = `<div class="error">❌ Failed to load sample data</div>`;
                }
            } catch (error) {
                sampleDiv.innerHTML = `<div class="error">❌ Failed to load sample data: ${error.message}</div>`;
            }
        }

        // Auto-test on page load
        window.onload = function() {
            testBackendConnection();
            setTimeout(testGraphQLQuery, 1000);
        };
    </script>
</body>
</html>
