import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON>s, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { formatFrenchNumber, formatFrenchDecimal } from '../utils/numberFormatter';

// Test data - exact same structure as production
const testData = [
  {
    Machine_Name: "IPS01",
    Shift: "Shift 1",
    production: 4534069,
    rejects: 2105,
    downtime: 388.91,
    availability: 51,
    performance: 52,
    oee: 48,
    quality: 58
  },
  {
    Machine_Name: "IPS01",
    Shift: "Shift 3",
    production: 4306178,
    rejects: 61351,
    downtime: 125.02,
    availability: 75,
    performance: 76,
    oee: 72,
    quality: 83
  },
  {
    Machine_Name: "IPS01",
    Shift: "Shift 2",
    production: 4069191,
    rejects: 21416,
    downtime: 264.87,
    availability: 59,
    performance: 71,
    oee: 61,
    quality: 70
  }
];

const TestDowntimeChart = () => {
  console.log('TestDowntimeChart rendering with data:', testData);
  console.log('Downtime values:', testData.map(d => ({ shift: d.Shift, downtime: d.downtime })));

  return (
    <div style={{ width: '100%', height: '400px', border: '1px solid #ccc', padding: '20px' }}>
      <h3>Test: Downtime Chart</h3>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={testData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="Shift" 
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => formatFrenchNumber(value)}
            domain={[0, 'dataMax']}
            label={{ 
              value: 'Downtime (hours)', 
              angle: -90, 
              position: 'insideLeft',
              style: { textAnchor: 'middle' }
            }}
          />
          <Tooltip
            formatter={(value) => [formatFrenchNumber(value), 'Downtime (hours)']}
            labelFormatter={(label) => `Shift: ${label}`}
          />
          <Bar 
            dataKey="downtime"
            name="Downtime (hours)"
            fill="#ff4d4f"
          />
        </BarChart>
      </ResponsiveContainer>
      <div style={{ marginTop: '10px', fontSize: '12px' }}>
        <strong>Expected values:</strong>
        <ul>
          {testData.map((item, index) => (
            <li key={index}>
              {item.Shift}: {item.downtime} hours
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default TestDowntimeChart;
