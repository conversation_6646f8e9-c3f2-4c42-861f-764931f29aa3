import pool, { execute } from './db.js';

async function testGraphQLEndpoint() {
  console.log('=== Testing GraphQL Endpoint Response ===');
  
  try {
    // Simulate the exact GraphQL query that the frontend is calling
    const startTime = Date.now();
    const limit = 1000;
    
    // Build the same query as the GraphQL resolver
    const conditions = [];
    const queryParams = [];
    
    // For testing, let's use no filters (same as frontend initial load)
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    console.log('🚀 Simulating getFinalComprehensiveStopData with no filters...');
    
    // Main data query (same as GraphQL resolver)
    const allStopsQuery = `
      SELECT 
        Machine_Name,
        Date_Insert,
        Part_NO,
        Code_Stop,
        Debut_Stop,
        Fin_Stop_Time,
        Regleur_Prenom,
        COALESCE(
          TIMESTAMPDIFF(MINUTE,
            STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
            STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
          ), 15
        ) AS duration_minutes
      FROM machine_stop_table_mould
      ${whereClause}
      ORDER BY Date_Insert DESC
      LIMIT ${limit}
    `;
    
    console.log('📊 Executing main query...');
    const [allStopsResult] = await execute(allStopsQuery);
    console.log(`Retrieved ${allStopsResult.length} stops`);
    
    // Sidecards queries (same as GraphQL resolver)
    const totalStopsQuery = `
      SELECT COUNT(*) AS total
      FROM machine_stop_table_mould
      ${whereClause}
    `;
    
    const nonDeclaredStopsQuery = `
      SELECT COUNT(*) AS non_declared
      FROM machine_stop_table_mould
      ${whereClause}
      ${whereClause ? 'AND' : 'WHERE'} Code_Stop = 'Arrêt non déclaré'
    `;
    
    const [totalStopsResult] = await execute(totalStopsQuery);
    const [nonDeclaredResult] = await execute(nonDeclaredStopsQuery);
    
    const sidecards = {
      Arret_Totale: totalStopsResult[0].total || 0,
      Arret_Totale_nondeclare: nonDeclaredResult[0].non_declared || 0
    };
    
    const executionTime = Date.now() - startTime;
    
    console.log(`✅ Simulation completed in ${executionTime}ms`);
    console.log('📊 Final results:');
    console.log(`  - Total stops retrieved: ${allStopsResult.length}`);
    console.log(`  - Sidecards:`);
    console.log(`    * Arret_Totale: ${sidecards.Arret_Totale}`);
    console.log(`    * Arret_Totale_nondeclare: ${sidecards.Arret_Totale_nondeclare}`);
    
    // Verify sample stop data
    console.log('\nSample stop data:');
    allStopsResult.slice(0, 3).forEach((stop, index) => {
      console.log(`  ${index + 1}. ${stop.Machine_Name} - ${stop.Code_Stop} (${stop.duration_minutes} min)`);
    });
    
    // Count non-declared in the retrieved data
    const nonDeclaredInResults = allStopsResult.filter(stop => stop.Code_Stop === 'Arrêt non déclaré').length;
    console.log(`\nNon-declared stops in retrieved data: ${nonDeclaredInResults}`);
    
    // Return the same structure as GraphQL
    const response = {
      allStops: allStopsResult,
      sidecards: sidecards,
      totalRecords: allStopsResult.length,
      queryExecutionTime: executionTime
    };
    
    console.log('\n=== GraphQL Response Structure ===');
    console.log(JSON.stringify({
      allStops: `[${response.allStops.length} records]`,
      sidecards: response.sidecards,
      totalRecords: response.totalRecords,
      queryExecutionTime: response.queryExecutionTime
    }, null, 2));
    
  } catch (error) {
    console.error('Error testing GraphQL endpoint:', error);
  } finally {
    process.exit(0);
  }
}

testGraphQLEndpoint();
