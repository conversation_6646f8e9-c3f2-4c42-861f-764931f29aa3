/**
 * QUEUED Custom hook for Stop Table GraphQL queries
 * Features: Sequential loading, priority-based loading, progressive UI updates
 * Uses the individual queries from stopTableResolvers.js instead of one comprehensive query
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { handleGraphQLError, isAbortError } from '../utils/error_handler';

const GRAPHQL_ENDPOINT = '/api/graphql';

// Configuration constants
const CACHE_TTL = 30000; // 30 seconds cache TTL
const REQUEST_TIMEOUT = 10000; // 10 seconds timeout
const MAX_CACHE_ENTRIES = 10; // Prevent memory bloat

// Priority levels for different types of data
const PRIORITY = {
  CRITICAL: 0,   // Stats cards & filters - load first
  HIGH: 1,       // Main table data - load second
  MEDIUM: 2,     // Charts & visualizations - load third
  LOW: 3,        // Additional analytics - load last
  BACKGROUND: 4  // Non-essential data - load in background
};

/**
 * Custom hook that implements a queued approach to GraphQL queries
 * Loads data in priority order for progressive UI updates
 */
const useQueuedStopGraphQL = () => {
  // Basic state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [progressStatus, setProgressStatus] = useState({});
  
  // Queue management
  const requestQueue = useRef([]);
  const isProcessingQueue = useRef(false);
  const abortControllers = useRef(new Map());
  
  // Cache system
  const queryCache = useRef(new Map());
  const modelsCache = useRef({ data: null, timestamp: null });
  const machineNamesCache = useRef(new Map());
  
  /**
   * Cache management - check if data exists in cache
   */
  const checkCache = useCallback((queryKey) => {
    const cached = queryCache.current.get(queryKey);
    if (!cached) return null;
    
    const now = Date.now();
    const age = now - cached.timestamp;
    
    // Return cached data if it's not expired
    if (age < CACHE_TTL) {
      console.log(`✅ CACHE HIT: ${queryKey} (age: ${age}ms)`);
      return cached.data;
    }
    
    // Remove expired entry
    console.log(`🕒 CACHE EXPIRED: ${queryKey} (age: ${age}ms)`);
    queryCache.current.delete(queryKey);
    return null;
  }, []);
  
  /**
   * Cache management - store data in cache
   */
  const updateCache = useCallback((queryKey, data) => {
    // Clean up cache if it's too large
    if (queryCache.current.size >= MAX_CACHE_ENTRIES) {
      const oldestKey = Array.from(queryCache.current.keys())[0];
      queryCache.current.delete(oldestKey);
    }
    
    queryCache.current.set(queryKey, {
      data,
      timestamp: Date.now()
    });
  }, []);
  
  /**
   * Execute a GraphQL query with timeout and caching
   */
  const executeGraphQL = useCallback(async (query, variables, queryName, priority) => {
    // Generate a unique key for this query
    const queryKey = `${queryName}-${JSON.stringify(variables)}`;
    
    // Check cache first
    const cachedData = checkCache(queryKey);
    if (cachedData) return cachedData;
    
    // Create an abort controller for this request
    const controller = new AbortController();
    const signal = controller.signal;
    const requestId = Date.now().toString();
    abortControllers.current.set(requestId, controller);
    
    try {
      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Request timeout after ${REQUEST_TIMEOUT}ms`)), REQUEST_TIMEOUT);
      });
      
      // Execute the GraphQL query
      const fetchPromise = fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, variables }),
        signal
      });
      
      // Race between fetch and timeout
      const response = await Promise.race([fetchPromise, timeoutPromise]);
      const result = await response.json();
      
      // Remove the controller reference
      abortControllers.current.delete(requestId);
      
      // Handle GraphQL errors
      if (result.errors) {
        const errorMessage = result.errors.map(e => e.message).join('; ');
        throw new Error(`GraphQL error: ${errorMessage}`);
      }
      
      // Extract the data from the named query
      const data = result.data[queryName];
      
      // Cache the result
      updateCache(queryKey, data);
      
      return data;
    } catch (error) {
      // Remove the controller reference
      abortControllers.current.delete(requestId);
      
      // Handle abort errors differently
      if (isAbortError(error)) {
        console.log(`🛑 Request ${requestId} was aborted`);
        throw error;
      }
      
      // Log and rethrow other errors
      console.error(`❌ Error in GraphQL query ${queryName}:`, error);
      throw error;
    }
  }, [checkCache, updateCache]);
    /**
   * Process the request queue sequentially
   */
  const processQueue = useCallback(async () => {
    // If the queue is empty or already processing, do nothing
    if (requestQueue.current.length === 0 || isProcessingQueue.current) return;
    
    // Mark as processing
    isProcessingQueue.current = true;
    
    try {
      // Get the next request from the queue
      const request = requestQueue.current.shift();
      
      // Update progress status
      setProgressStatus(prev => ({
        ...prev,
        [request.queryName]: 'loading'
      }));
      
      // Execute the request
      const result = await executeGraphQL(
        request.query, 
        request.variables, 
        request.queryName,
        request.priority
      );
      
      // Call the success callback
      if (request.onSuccess) {
        request.onSuccess(result);
      }
      
      // Update progress status
      setProgressStatus(prev => ({
        ...prev,
        [request.queryName]: 'success'
      }));
    } catch (error) {
      // Get the failed request
      const failedRequest = requestQueue.current[0];
      
      // Update progress status
      if (failedRequest) {
        setProgressStatus(prev => ({
          ...prev,
          [failedRequest.queryName]: 'error'
        }));
        
        // Call the error callback
        if (failedRequest.onError) {
          failedRequest.onError(error);
        }
      }
      
      // Log the error
      console.error('❌ Error processing queue:', error);
      
      // Set the general error state
      setError(error.message);
    } finally {
      // Mark as not processing
      isProcessingQueue.current = false;
      
      // Process the next request if there are any
      if (requestQueue.current.length > 0) {
        setTimeout(() => processQueue(), 50); // Small delay to prevent UI blocking
      } else {
        setLoading(false);
      }
    }
  }, [executeGraphQL, setError, setLoading, setProgressStatus]);
  
  /**
   * Add a request to the queue with priority
   */
  const enqueueRequest = useCallback((query, variables, queryName, priority, onSuccess, onError) => {
    // Add the request to the queue
    const request = {
      query,
      variables,
      queryName,
      priority: priority || PRIORITY.MEDIUM,
      onSuccess,
      onError,
      timestamp: Date.now()
    };
    
    requestQueue.current.push(request);
    
    // Sort queue by priority (lowest number = highest priority)
    requestQueue.current.sort((a, b) => a.priority - b.priority);
    
    // Start processing the queue if it's not already running
    if (!isProcessingQueue.current) {
      processQueue();
    }
  }, [processQueue]);
  
  /**
   * Cancel all pending requests
   */
  const cancelAllRequests = useCallback((reason = 'User cancelled') => {
    // Clear the queue
    const queueSize = requestQueue.current.length;
    requestQueue.current = [];
    
    // Abort all active requests
    abortControllers.current.forEach((controller) => {
      try {
        controller.abort(reason);
      } catch (e) {
        // Fallback for browsers not supporting abort with reason
        controller.abort();
      }
    });
    
    // Clear controllers
    abortControllers.current.clear();
    
    // Reset processing state
    isProcessingQueue.current = false;
    
    // Reset loading state
    setLoading(false);
    
    console.log(`🛑 Cancelled ${queueSize} pending requests`);
  }, []);
  
  /**
   * Get machine models with high priority
   */
  const getMachineModels = useCallback(async () => {
    // Check cache first for models
    if (modelsCache.current.data && Date.now() - modelsCache.current.timestamp < CACHE_TTL) {
      return modelsCache.current.data;
    }
    
    return new Promise((resolve, reject) => {
      setLoading(true);
      
      const query = `
        query {
          getStopMachineModels {
            model
          }
        }
      `;
      
      enqueueRequest(
        query,
        {},
        'getStopMachineModels',
        PRIORITY.CRITICAL,
        (result) => {
          // Cache the result
          modelsCache.current = {
            data: result,
            timestamp: Date.now()
          };
          resolve(result);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }, [enqueueRequest]);
  
  /**
   * Get machine names by model with high priority
   */
  const getMachineNames = useCallback(async (model = null) => {
    // Create a cache key for this model
    const cacheKey = model || 'all';
    
    // Debug logging for machine names request
    console.log('🔍 getMachineNames called with model:', {
      model,
      cacheKey
    });
    
    // Check cache first for names
    const cached = machineNamesCache.current.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      console.log('✅ CACHE HIT for machine names:', {
        model,
        count: cached.data?.length || 0
      });
      return cached.data;
    }
    
    return new Promise((resolve, reject) => {
      setLoading(true);
      
      const query = `
        query GetMachineNames($filters: StopFilterInput) {
          getStopMachineNames(filters: $filters) {
            Machine_Name
          }
        }
      `;
      
      const variables = {
        filters: { model }
      };
      
      console.log('📤 GraphQL request for machine names:', {
        model,
        variables
      });
      
      enqueueRequest(
        query,
        variables,
        'getStopMachineNames',
        PRIORITY.CRITICAL,
        (result) => {
          // Cache the result
          machineNamesCache.current.set(cacheKey, {
            data: result,
            timestamp: Date.now()
          });
          resolve(result);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }, [enqueueRequest]);
  
  /**
   * Get stats cards data with highest priority
   */
  const getStopSidecards = useCallback(async (filters = {}) => {
    return new Promise((resolve, reject) => {
      setLoading(true);
      
      const query = `
        query GetStopSidecards($filters: StopFilterInput) {
          getStopSidecards(filters: $filters) {
            Arret_Totale
            Arret_Totale_nondeclare
          }
        }
      `;
      
      enqueueRequest(
        query,
        { filters },
        'getStopSidecards',
        PRIORITY.CRITICAL,
        (result) => {
          resolve(result);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }, [enqueueRequest]);
  
  /**
   * Get all machine stops with medium priority (used for main table)
   */
  const getAllStops = useCallback(async (filters = {}) => {
    return new Promise((resolve, reject) => {
      setLoading(true);
      
      const query = `
        query GetAllMachineStops($filters: StopFilterInput) {
          getAllMachineStops(filters: $filters) {
            Machine_Name
            Date_Insert
            Code_Stop
            Debut_Stop
            Fin_Stop_Time
            Regleur_Prenom
            Part_NO
            duration_minutes
          }
        }
      `;
      
      enqueueRequest(
        query,
        { filters },
        'getAllMachineStops',
        PRIORITY.HIGH,
        (result) => {
          resolve(result);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }, [enqueueRequest]);
  
  /**
   * Get top 5 stops with medium priority (for charts)
   */
  const getTop5Stops = useCallback(async (filters = {}) => {
    return new Promise((resolve, reject) => {
      const query = `
        query GetTop5Stops($filters: StopFilterInput) {
          getTop5Stops(filters: $filters) {
            stopName
            count
          }
        }
      `;
      
      enqueueRequest(
        query,
        { filters },
        'getTop5Stops',
        PRIORITY.MEDIUM,
        (result) => {
          resolve(result);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }, [enqueueRequest]);
  
  /**
   * Get machine comparison with medium priority (for charts)
   */
  const getMachineStopComparison = useCallback(async (filters = {}) => {
    return new Promise((resolve, reject) => {
      const query = `
        query GetMachineStopComparison($filters: StopFilterInput) {
          getMachineStopComparison(filters: $filters) {
            Machine_Name
            stops
            totalDuration
          }
        }
      `;
      
      enqueueRequest(
        query,
        { filters },
        'getMachineStopComparison',
        PRIORITY.MEDIUM,
        (result) => {
          resolve(result);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }, [enqueueRequest]);
  
  /**
   * Get operator stats with low priority
   */
  const getOperatorStopStats = useCallback(async (filters = {}) => {
    return new Promise((resolve, reject) => {
      const query = `
        query GetOperatorStopStats($filters: StopFilterInput) {
          getOperatorStopStats(filters: $filters) {
            operator
            interventions
            totalDuration
          }
        }
      `;
      
      enqueueRequest(
        query,
        { filters },
        'getOperatorStopStats',
        PRIORITY.LOW,
        (result) => {
          resolve(result);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }, [enqueueRequest]);
  
  /**
   * Get duration trend with low priority
   */
  const getStopDurationTrend = useCallback(async (filters = {}) => {
    return new Promise((resolve, reject) => {
      const query = `
        query GetStopDurationTrend($filters: StopFilterInput) {
          getStopDurationTrend(filters: $filters) {
            hour
            avgDuration
          }
        }
      `;
      
      enqueueRequest(
        query,
        { filters },
        'getStopDurationTrend',
        PRIORITY.LOW,
        (result) => {
          resolve(result);
        },
        (error) => {
          reject(error);
        }
      );
    });
  }, [enqueueRequest]);
  
  /**
   * Load dashboard data in priority sequence
   * This is the main entry point that will be used by ArretContext
   */
  const loadDashboardData = useCallback(async (filters = {}) => {
    setLoading(true);
    
    try {
      // Step 1: Load critical data first (stats cards)
      const sidecards = await getStopSidecards(filters);
      
      // Step 2: Start loading table data (high priority)
      getAllStops(filters)
        .then((allStops) => {
          console.log(`✅ Loaded ${allStops?.length || 0} stops`);
        })
        .catch((error) => {
          console.error('❌ Error loading stops:', error);
        });
      
      // Step 3: Start loading chart data (medium priority)
      getTop5Stops(filters)
        .then((topStops) => {
          console.log(`✅ Loaded ${topStops?.length || 0} top stops`);
        })
        .catch((error) => {
          console.error('❌ Error loading top stops:', error);
        });
        
      getMachineStopComparison(filters)
        .then((comparison) => {
          console.log(`✅ Loaded ${comparison?.length || 0} machine comparisons`);
        })
        .catch((error) => {
          console.error('❌ Error loading machine comparison:', error);
        });
        
      // Step 4: Load analytics data (low priority)
      getOperatorStopStats(filters)
        .then((operatorStats) => {
          console.log(`✅ Loaded ${operatorStats?.length || 0} operator stats`);
        })
        .catch((error) => {
          console.error('❌ Error loading operator stats:', error);
        });
        
      getStopDurationTrend(filters)
        .then((durationTrend) => {
          console.log(`✅ Loaded ${durationTrend?.length || 0} duration trend points`);
        })
        .catch((error) => {
          console.error('❌ Error loading duration trend:', error);
        });
      
      // Return the critical data immediately
      return {
        sidecards,
        // The rest will be loaded progressively
      };
    } catch (error) {
      setError(error.message);
      console.error('❌ Error loading dashboard data:', error);
      return {
        sidecards: { Arret_Totale: 0, Arret_Totale_nondeclare: 0 }
      };
    } finally {
      // Loading state will be cleared when the queue is empty
    }
  }, [
    getStopSidecards,
    getAllStops,
    getTop5Stops,
    getMachineStopComparison,
    getOperatorStopStats,
    getStopDurationTrend
  ]);
  
  // Clean up resources on unmount
  useEffect(() => {
    return () => {
      cancelAllRequests('Component unmounted');
    };
  }, [cancelAllRequests]);
  
  return {
    // Basic state
    loading,
    error,
    progressStatus,
    
    // Queue management
    cancelAllRequests,
    
    // Filter utilities (high priority)
    getMachineModels,
    getMachineNames,
    
    // Individual data loaders
    getStopSidecards,
    getAllStops,
    getTop5Stops,
    getMachineStopComparison,
    getOperatorStopStats,
    getStopDurationTrend,
    
    // Main entry point
    loadDashboardData
  };
};

export default useQueuedStopGraphQL;
