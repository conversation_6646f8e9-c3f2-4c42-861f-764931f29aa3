/**
 * Test the exact scenario shown in the screenshot:
 * - Machine filter: IPS01 
 * - Date filter: April 2025 (month filter)
 * This should generate chart data but it's showing "Aucune donnée d'évolution disponible"
 */

// Mock test to simulate the queuedDataManager logic with the actual filters
console.log('🧪 Testing screenshot scenario: IPS01 + April 2025 filter');

// Mock backend response that matches what we know the backend returns
const mockTableData = {
  stopsData: [
    {
      ID_Stop: 1,
      Date_Insert: "16/04/2025 10:30:00",
      Machine_Name: "IPS01",
      duration_minutes: 90
    },
    {
      ID_Stop: 2,
      Date_Insert: "16/04/2025 14:45:00", 
      Machine_Name: "IPS01",
      duration_minutes: 45
    },
    {
      ID_Stop: 3,
      Date_Insert: "17/04/2025 08:15:00",
      Machine_Name: "IPS01", 
      duration_minutes: 135
    },
    {
      ID_Stop: 4,
      Date_Insert: "18/04/2025 11:20:00",
      Machine_Name: "IPS01",
      duration_minutes: 60
    },
    {
      ID_Stop: 5,
      Date_Insert: "20/04/2025 15:30:00",
      Machine_Name: "IPS01",
      duration_minutes: 75
    },
    // Include some data for other machines to test filtering
    {
      ID_Stop: 6,
      Date_Insert: "16/04/2025 09:00:00",
      Machine_Name: "IPS02",
      duration_minutes: 120
    }
  ]
};

// Mock current filter state (from screenshot)
const mockState = {
  selectedMachine: "IPS01",
  selectedDate: "2025-04-15", // April 2025
  dateRangeType: "month"
};

console.log('🔧 Mock data setup:', {
  totalStops: mockTableData.stopsData.length,
  selectedMachine: mockState.selectedMachine,
  selectedDate: mockState.selectedDate,
  dateRangeType: mockState.dateRangeType
});

// Step 1: Filter stops data (like queuedDataManager does)
const filteredStopsData = mockTableData.stopsData.filter(stop => {
  // Filter by machine if selected
  if (mockState.selectedMachine && stop.Machine_Name !== mockState.selectedMachine) {
    console.log('❌ Filtered out by machine:', stop.Machine_Name, '!==', mockState.selectedMachine);
    return false;
  }
  
  // Filter by date if selected
  if (mockState.selectedDate && stop.Date_Insert) {
    const stopDate = new Date(stop.Date_Insert.replace(/(\d{2})\/(\d{2})\/(\d{4})/, '$3-$2-$1'));
    const filterDate = new Date(mockState.selectedDate);
    
    console.log('🗓️ Date comparison:', {
      stopDateString: stop.Date_Insert,
      parsedStopDate: stopDate.toISOString(),
      filterDate: filterDate.toISOString(),
      stopMonth: stopDate.getMonth(),
      filterMonth: filterDate.getMonth(),
      stopYear: stopDate.getFullYear(),
      filterYear: filterDate.getFullYear()
    });
    
    if (mockState.dateRangeType === 'month') {
      const matches = stopDate.getMonth() === filterDate.getMonth() && 
                     stopDate.getFullYear() === filterDate.getFullYear();
      console.log('📅 Month filter result:', matches);
      return matches;
    }
  }
  
  return true;
});

console.log('🔍 Filtering results:', {
  originalCount: mockTableData.stopsData.length,
  filteredCount: filteredStopsData.length,
  filteredStops: filteredStopsData.map(s => ({ id: s.ID_Stop, date: s.Date_Insert, machine: s.Machine_Name }))
});

// Step 2: Generate daily stats (like queuedDataManager does)
const dailyStats = {};

filteredStopsData.forEach(stop => {
  if (stop.Date_Insert && typeof stop.Date_Insert === 'string') {
    let date;
    let originalDate = stop.Date_Insert.toString();
    
    console.log('🔍 Processing date for daily stats:', originalDate);
    
    // Handle DD/MM/YYYY HH:MM:SS format (matching queuedDataManager logic)
    if (stop.Date_Insert.includes('/')) {
      const parts = stop.Date_Insert.split(' ');
      const datePart = parts[0]; // DD/MM/YYYY
      if (datePart && datePart.includes('/')) {
        const dateParts = datePart.split('/');
        if (dateParts.length === 3) {
          const [day, month, year] = dateParts;
          if (day && month && year) {
            date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            console.log('✅ Parsed DD/MM/YYYY format for daily stats:', originalDate, '->', date);
          } else {
            console.warn('❌ Invalid date parts:', dateParts);
            return;
          }
        }
      }
    }
    
    if (date) {
      if (!dailyStats[date]) {
        dailyStats[date] = { date, stops: 0, duration: 0 };
      }
      dailyStats[date].stops++;
      
      if (stop.duration_minutes && stop.duration_minutes > 0) {
        dailyStats[date].duration += parseFloat(stop.duration_minutes);
      }
      
      console.log('📊 Updated daily stats for', date, ':', dailyStats[date]);
    }
  }
});

console.log('📈 Generated daily stats:', dailyStats);

// Step 3: Create evolution data (like queuedDataManager does)
let evolutionData = Object.values(dailyStats)
  .sort((a, b) => new Date(a.date) - new Date(b.date));

console.log('🎯 Final evolution data:', {
  dailyStatsKeys: Object.keys(dailyStats),
  evolutionDataLength: evolutionData.length,
  evolutionData: evolutionData
});

// Step 4: Format display dates
evolutionData = evolutionData.map(item => {
  const dateObj = new Date(item.date);
  const displayDate = dateObj.toLocaleDateString('fr-FR', { 
    day: '2-digit', 
    month: '2-digit' 
  });
  
  return {
    ...item,
    displayDate
  };
});

console.log('✅ Final formatted evolution data:', evolutionData);

if (evolutionData.length > 0) {
  console.log('🎉 SUCCESS: Chart data generated successfully!');
  console.log('📊 Chart will receive:', evolutionData.length, 'data points');
  evolutionData.forEach(item => {
    console.log(`  ${item.displayDate}: ${item.stops} stops, ${item.duration}min`);
  });
} else {
  console.log('❌ FAILURE: No chart data generated');
  console.log('🔍 This explains why chart shows "Aucune donnée d\'évolution disponible"');
}

export { evolutionData };
