/**
 * Utilities for consistent error handling
 * @module errorUtils
 */

import { notification } from 'antd';

/**
 * Error types for categorizing errors
 * @type {Object}
 */
export const ERROR_TYPES = {
  NETWORK: 'network',
  AUTH: 'auth',
  VALIDATION: 'validation',
  SERVER: 'server',
  CLIENT: 'client',
  UNKNOWN: 'unknown',
};

/**
 * Determine error type based on error object
 * @param {Error|Object} error - Error object
 * @returns {string} Error type from ERROR_TYPES
 */
export const getErrorType = (error) => {
  if (!error) return ERROR_TYPES.UNKNOWN;
  
  // Network errors
  if (error.message === 'Network Error' || error.code === 'ECONNABORTED') {
    return ERROR_TYPES.NETWORK;
  }
  
  // Authentication errors
  if (error.response?.status === 401 || error.response?.status === 403) {
    return ERROR_TYPES.AUTH;
  }
  
  // Validation errors
  if (error.response?.status === 400 || error.response?.data?.errors) {
    return ERROR_TYPES.VALIDATION;
  }
  
  // Server errors
  if (error.response?.status >= 500) {
    return ERROR_TYPES.SERVER;
  }
  
  // Client errors
  if (error.response?.status >= 400 && error.response?.status < 500) {
    return ERROR_TYPES.CLIENT;
  }
  
  return ERROR_TYPES.UNKNOWN;
};

/**
 * Get user-friendly error message
 * @param {Error|Object} error - Error object
 * @returns {string} User-friendly error message
 */
export const getUserFriendlyMessage = (error) => {
  if (!error) return 'Une erreur inconnue est survenue';
  
  const errorType = getErrorType(error);
  
  switch (errorType) {
    case ERROR_TYPES.NETWORK:
      return 'Impossible de se connecter au serveur. Veuillez vérifier votre connexion internet.';
      
    case ERROR_TYPES.AUTH:
      if (error.response?.status === 401) {
        return 'Votre session a expiré. Veuillez vous reconnecter.';
      }
      return 'Vous n\'avez pas les autorisations nécessaires pour effectuer cette action.';
      
    case ERROR_TYPES.VALIDATION:
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        return error.response.data.errors.map(err => err.msg || err.message).join(', ');
      }
      return error.response?.data?.message || 'Veuillez vérifier les informations saisies.';
      
    case ERROR_TYPES.SERVER:
      return 'Le serveur a rencontré une erreur. Veuillez réessayer plus tard.';
      
    case ERROR_TYPES.CLIENT:
      return error.response?.data?.message || 'Une erreur est survenue lors de la requête.';
      
    default:
      return error.message || 'Une erreur inconnue est survenue';
  }
};

/**
 * Get technical error details for logging
 * @param {Error|Object} error - Error object
 * @returns {Object} Technical error details
 */
export const getTechnicalDetails = (error) => {
  if (!error) return { message: 'Unknown error', type: ERROR_TYPES.UNKNOWN };
  
  return {
    message: error.message,
    type: getErrorType(error),
    status: error.response?.status,
    statusText: error.response?.statusText,
    data: error.response?.data,
    url: error.config?.url,
    method: error.config?.method,
    stack: error.stack,
  };
};

/**
 * Log error to console with consistent format
 * @param {Error|Object} error - Error object
 * @param {string} [context=''] - Context where error occurred
 */
export const logError = (error, context = '') => {
  if (process.env.NODE_ENV !== 'production') {
    const details = getTechnicalDetails(error);
    console.error(
      `[ERROR]${context ? ` [${context}]` : ''} ${details.message}`,
      details
    );
  }
};

/**
 * Show error notification to user
 * @param {Error|Object} error - Error object
 * @param {Object} [options={}] - Notification options
 * @param {string} [options.title='Erreur'] - Notification title
 * @param {number} [options.duration=4.5] - Notification duration in seconds
 * @param {Function} [options.onClose] - Callback when notification closes
 */
export const showErrorNotification = (error, options = {}) => {
  const {
    title = 'Erreur',
    duration = 4.5,
    onClose,
  } = options;
  
  const message = getUserFriendlyMessage(error);
  
  notification.error({
    message: title,
    description: message,
    duration,
    onClose,
  });
  
  // Also log the error
  logError(error, options.context);
};

/**
 * Handle error with consistent approach
 * @param {Error|Object} error - Error object
 * @param {Object} [options={}] - Handler options
 * @param {boolean} [options.showNotification=true] - Whether to show notification
 * @param {Function} [options.onAuth] - Callback for auth errors
 * @param {Function} [options.onNetwork] - Callback for network errors
 * @param {Function} [options.onValidation] - Callback for validation errors
 * @param {Function} [options.onServer] - Callback for server errors
 * @param {Function} [options.onClient] - Callback for client errors
 * @param {Function} [options.onUnknown] - Callback for unknown errors
 * @returns {Object} Error details
 */
export const handleError = (error, options = {}) => {
  const {
    showNotification = true,
    onAuth,
    onNetwork,
    onValidation,
    onServer,
    onClient,
    onUnknown,
  } = options;
  
  const errorType = getErrorType(error);
  const details = getTechnicalDetails(error);
  
  // Log error
  logError(error, options.context);
  
  // Show notification if enabled
  if (showNotification) {
    showErrorNotification(error, options);
  }
  
  // Call type-specific callback
  switch (errorType) {
    case ERROR_TYPES.NETWORK:
      if (onNetwork) onNetwork(details);
      break;
      
    case ERROR_TYPES.AUTH:
      if (onAuth) onAuth(details);
      break;
      
    case ERROR_TYPES.VALIDATION:
      if (onValidation) onValidation(details);
      break;
      
    case ERROR_TYPES.SERVER:
      if (onServer) onServer(details);
      break;
      
    case ERROR_TYPES.CLIENT:
      if (onClient) onClient(details);
      break;
      
    default:
      if (onUnknown) onUnknown(details);
      break;
  }
  
  return {
    message: getUserFriendlyMessage(error),
    ...details,
  };
};

export default {
  ERROR_TYPES,
  getErrorType,
  getUserFriendlyMessage,
  getTechnicalDetails,
  logError,
  showErrorNotification,
  handleError,
};