"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, memo } from "react"
import ShiftReportButton from "./ShiftReportButton"
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  DatePicker,
  Tag,
  Spin,
  Typography,
  Progress,
  Grid,
  Empty,
  Tabs,
  Button,
  Space,
  Badge,
  Tooltip,
  Select,
  Dropdown,
  Menu,
  Segmented,
  message,
  notification,
} from "antd"
import { extractResponseData, isResponseSuccessful, getErrorMessage } from "../utils/apiUtils"
import { normalizePercentage, transformData } from "../utils/dataUtils"
import {
  RiseOutlined,
  FallOutlined,
  DashboardOutlined,
  LineChartOutlined,
  Bar<PERSON>hartOutlined,
  TableOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined,
  ReloadOutlined,
  SettingOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined,
  DownloadOutlined,
  FilterOutlined,
  InfoCircleOutlined,
  ToolOutlined,
  CalendarOutlined,
  UserOutlined,

  MoreOutlined,
  ClearOutlined,
} from "@ant-design/icons"
import {
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Toolt<PERSON> as RechartsTooltip,
  Legend,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  ComposedChart,
} from "recharts"
import superagent from "superagent"
import dayjs from "dayjs"
import "dayjs/locale/fr"

// Configuration de dayjs en français
dayjs.locale("fr")

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs
const { useBreakpoint } = Grid
const { Option } = Select

// Palette de couleurs améliorée
const COLORS = [
  "#1890ff", // bleu
  "#13c2c2", // cyan
  "#52c41a", // vert
  "#faad14", // jaune
  "#f5222d", // rouge
  "#722ed1", // violet
  "#eb2f96", // rose
  "#fa8c16", // orange
  "#a0d911", // lime
  "#096dd9", // bleu foncé
]

// Composant de graphique à barres mémorisé avec ligne de TRS
const MemoizedBarChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={400}>
    <ComposedChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
      <XAxis
        dataKey="date"
        tick={{ fill: "#666" }}
        tickFormatter={(date) => {
          // Ensure we have a valid date string
          if (!date) return "";
          // Parse the date and format it as DD/MM
          return dayjs(date).format("DD/MM");
        }}
        label={{
          value: "Date",
          position: "bottom",
          offset: 0,
          style: { fill: "#666" },
        }}
      />
      <YAxis
        yAxisId="left"
        tickFormatter={(value) => value.toLocaleString()}
        label={{
          value: "Quantité",
          angle: -90,
          position: "insideLeft",
          style: { fill: "#666" },
        }}
      />
      <YAxis
        yAxisId="right"
        orientation="right"
        tickFormatter={(value) => `${value}%`}
        domain={[0, 100]}
        label={{
          value: "TRS",
          angle: 90,
          position: "insideRight",
          style: { fill: "#666" },
        }}
      />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value, name) => {
          // Vérifier si la valeur est un nombre valide
          const isValidNumber = typeof value === "number" && !isNaN(value)
          const formattedValue = isValidNumber
            ? Number.isInteger(value)
              ? value.toLocaleString()
              : value.toFixed(2)
            : value

          const labels = {
            good: "Quantité bonne",
            reject: "Quantité rejetée (kg)",
            oee: "TRS",
            speed: "Cycle De Temps",
          }

          return [formattedValue, labels[name] || name]
        }}
        labelFormatter={(label) => `Date: ${dayjs(label).format("DD/MM/YYYY")}`}
      />
      <Legend
        wrapperStyle={{ paddingTop: 20 }}
        formatter={(value) => {
          const labels = {
            good: "Quantité bonne",
            reject: "Quantité rejetée (kg)",
            oee: "TRS",
            speed: "Cycle De Temps",
          }
          return <span style={{ color: "#666" }}>{labels[value] || value}</span>
        }}
      />
      <Bar yAxisId="left" dataKey="good" name="good" fill={COLORS[2]} maxBarSize={40} stackId="production" />
      <Bar yAxisId="left" dataKey="reject" name="reject" fill={COLORS[4]} maxBarSize={40} stackId="production" />
      <Line
        yAxisId="right"
        type="monotone"
        dataKey="oee"
        name="oee"
        stroke={COLORS[0]}
        strokeWidth={2}
        dot={{ r: 4, fill: COLORS[0] }}
        activeDot={{ r: 6, fill: "#fff", stroke: COLORS[0], strokeWidth: 2 }}
      />
    </ComposedChart>
  </ResponsiveContainer>
))
// Add a new MemoizedShiftComparisonChart component after your other chart components
// Around line 300-400 (after the other chart components)

// Composant de graphique pour la comparaison des équipes
const MemoizedShiftComparisonChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <BarChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="Shift" tick={{ fill: "#666" }} />
      <YAxis
        yAxisId="left"
        tickFormatter={(value) => value.toLocaleString()}
        label={{
          value: "Production",
          angle: -90,
          position: "insideLeft",
          style: { fill: "#666" },
        }}
      />
      <YAxis
        yAxisId="right"
        orientation="right"
        tickFormatter={(value) => `${value}%`}
        domain={[0, 100]}
        label={{
          value: "Performance",
          angle: 90,
          position: "insideRight",
          style: { fill: "#666" },
        }}
      />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value, name) => {
          // Vérifier si la valeur est un nombre valide
          const isValidNumber = typeof value === "number" && !isNaN(value)

          if (name === "production") return [isValidNumber ? value.toLocaleString() : value, "Production"]
          if (name === "downtime") return [isValidNumber ? value.toLocaleString() : value, "Temps d'arrêt"]
          if (name === "oee") return [isValidNumber ? `${value.toFixed(1)}%` : `${value}%`, "TRS"]
          if (name === "performance") return [isValidNumber ? `${value.toFixed(1)}%` : `${value}%`, "Performance"]
          return [value, name]
        }}
        labelFormatter={(label) => `Équipe: ${label}`}
      />

      <Bar yAxisId="left" dataKey="production" name="Production" fill={COLORS[2]} maxBarSize={40} />
      <Bar yAxisId="left" dataKey="downtime" name="Temps d'arrêt" fill={COLORS[4]} maxBarSize={40} />
      <Line
        yAxisId="right"
        type="monotone"
        dataKey="oee"
        name="TRS"
        stroke={COLORS[0]}
        strokeWidth={2}
        dot={{ r: 4, fill: COLORS[0] }}
      />
      <Line
        yAxisId="right"
        type="monotone"
        dataKey="performance"
        name="Performance"
        stroke={COLORS[5]}
        strokeWidth={2}
        dot={{ r: 4, fill: COLORS[5] }}
      />
    </BarChart>
  </ResponsiveContainer>
))

// Composant de graphique de performance des machines mémorisé
// Composant de graphique de performance des machines mémorisé
const MemoizedMachinePerformance = memo(({ data }) => {
  // Aggregate data by machine name
  const aggregatedData = data.reduce((acc, item) => {
    const machineName = item.Machine_Name
    if (!acc[machineName]) {
      acc[machineName] = {
          Machine_Name: machineName,
        good: 0,
        reject: 0,
      }
    }

    acc[machineName].good += Number(item.good) || Number(item.Good_QTY_Day) || 0
    acc[machineName].reject += Number(item.reject) || Number(item.Rejects_QTY_Day) || 0

    return acc
  }, {})

  // Convert to array for the chart
  const chartData = Object.values(aggregatedData)

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={chartData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="Machine_Name" tick={{ fill: "#666" }} interval={0} angle={-45} textAnchor="end" height={80} />
        <YAxis
          tickFormatter={(value) => value.toLocaleString()}
          label={{
            value: "Quantité",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666" },
          }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          }}
          formatter={(value, name) => {
            // Check if value is a number before using toFixed
            const isNumber = typeof value === "number" && !isNaN(value)
            const formattedValue = isNumber
              ? Number.isInteger(value)
                ? value.toLocaleString()
                : value.toFixed(2)
              : value

            const labels = {
              good: "Production",
              reject: "Rejets (kg)",
            }

            return [formattedValue, labels[name] || name]
          }}
        />

        <Bar dataKey="good" name="good" fill={COLORS[0]} stackId="a" />
        <Bar dataKey="reject" name="reject" fill={COLORS[4]} stackId="a" />
      </BarChart>
    </ResponsiveContainer>
  )
})
// Composant de graphique en secteurs mémorisé
const MemoizedPieChart = memo(({ data, dataKey = "value", nameKey = "name", colors = COLORS }) => (
  <ResponsiveContainer width="100%" height={300}>
    <PieChart margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <Pie
        data={data}
        dataKey={dataKey}
        nameKey={nameKey}
        cx="50%"
        cy="50%"
        innerRadius={60}
        outerRadius={80}
        paddingAngle={5}
        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
      >
        {data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
        ))}
      </Pie>
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
      />
      <Legend
        layout="vertical"
        verticalAlign="middle"
        align="right"
        wrapperStyle={{
          paddingLeft: 24,
          fontSize: 14,
          color: "#666",
        }}
      />
    </PieChart>
  </ResponsiveContainer>
))

// Composant de graphique en ligne mémorisé
const MemoizedLineChart = memo(({ data, dataKeys = ["oee", "speed"], colors = [COLORS[0], COLORS[1]] }) => (
  <ResponsiveContainer width="100%" height={300}>
    <LineChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis
        dataKey="date"
        tick={{ fill: "#666" }}
        tickFormatter={(date) => {
          // Ensure we have a valid date string
          if (!date) return "";
          // Parse the date and format it as DD/MM
          return dayjs(date).format("DD/MM");
        }}
      />
      <YAxis tickFormatter={(value) => `${value}%`} domain={[0, 100]} />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value, name) => {
          // Vérifier si la valeur est un nombre valide
          const isValidNumber = typeof value === "number" && !isNaN(value)
          const formattedValue = isValidNumber
            ? Number.isInteger(value)
              ? value.toLocaleString()
              : value.toFixed(2)
            : value

          const labels = {
            oee: "TRS",
            speed: "Cycle De Temps",
          }

          return [isValidNumber ? formattedValue + "%" : value, labels[name] || name]
        }}
        labelFormatter={(label) => `Date: ${dayjs(label).format("DD/MM/YYYY")}`}
      />

      {dataKeys.map((key, index) => (
        <Line
          key={key}
          type="monotone"
          dataKey={key}
          name={key}
          stroke={colors[index]}
          strokeWidth={2}
          dot={{ r: 4, fill: colors[index] }}
          activeDot={{ r: 6, fill: "#fff", stroke: colors[index], strokeWidth: 2 }}
        />
      ))}
    </LineChart>
  </ResponsiveContainer>
))

// Composant de graphique en aires mémorisé
const MemoizedAreaChart = memo(({ data, dataKey = "average_speed", color = COLORS[2] }) => (
  <ResponsiveContainer width="100%" height={300}>
    <AreaChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis
        dataKey="hour"
        tick={{ fill: "#666" }}
        tickFormatter={(hour) => {
          if (!hour) return ""
          const parts = hour.split(" ")
          if (parts.length >= 2) {
            return `${parts[1]}h`
          }
          return hour
        }}
      />
      <YAxis tickFormatter={(value) => `${value} u/h`} />
      <RechartsTooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value) => {
          // Vérifier si la valeur est un nombre valide
          const isValidNumber = typeof value === "number" && !isNaN(value)
          return [
            isValidNumber ? `${value.toFixed(2)} unités/heure` : `${value} unités/heure`,
            "Cycle De Temps Moyenne",
          ]
        }}
        labelFormatter={(hour) => {
          if (!hour) return "Heure inconnue"
          const parts = hour.split(" ")
          if (parts.length >= 2) {
            return `Date: ${parts[0]}, Heure: ${parts[1]}h`
          }
          return hour
        }}
      />

      <Area
        type="monotone"
        dataKey={dataKey}
        name="Cycle De Temps Moyenne"
        stroke={color}
        fill={color}
        fillOpacity={0.3}
      />
    </AreaChart>
  </ResponsiveContainer>
))

// Composant de graphique à barres horizontales mémorisé pour le TRS par machine
const MemoizedBulletChart = memo(({ data, dataKey = "oee", nameKey = "Machine_Name", color = COLORS[5] }) => {
  // Process data to get unique machines with their average OEE
  const processedData = data.reduce((acc, item) => {
    if (!acc[item.Machine_Name]) {
      acc[item.Machine_Name] = {
        Machine_Name: item.Machine_Name,
        oee: 0,
        count: 0,
      }
    }
    // Handle decimal percentages (0-1 range)
    let oeeValue = Number.parseFloat(item.oee) || 0;
    // Convert to percentage if in 0-1 range
    oeeValue = oeeValue <= 1 && oeeValue > 0 ? oeeValue * 100 : oeeValue;
    acc[item.Machine_Name].oee += oeeValue;
    acc[item.Machine_Name].count += 1
    return acc
  }, {})

  // Calculate averages and convert to array
  const chartData = Object.values(processedData)
    .map((item) => ({
      Machine_Name: item.Machine_Name,
      oee: item.count > 0 ? item.oee / item.count : 0,
      target: 85, // Valeur cible de TRS
      minimum: 70, // Seuil minimum acceptable
    }))
    .sort((a, b) => b.oee - a.oee) // Tri par TRS décroissant

  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart layout="vertical" data={chartData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
        <CartesianGrid strokeDasharray="3 3" horizontal={false} />
        <XAxis type="number" domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
        <YAxis dataKey={nameKey} type="category" width={120} tick={{ fontSize: 12 }} />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 8,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          }}
          formatter={(value, name) => {
            // Vérifier si la valeur est un nombre valide
            let displayValue = value;
            const isValidNumber = typeof value === "number" && !isNaN(value);

            // Handle decimal percentages (0-1 range)
            if (isValidNumber && name === "oee" && value <= 1 && value > 0) {
              displayValue = value * 100;
            }

            if (name === "oee") return [isValidNumber ? `${displayValue.toFixed(2)}%` : `${value}%`, "TRS Actuel"]
            if (name === "target") return [`${value}%`, "Objectif"]
            if (name === "minimum") return [`${value}%`, "Minimum"]
            return [value, name]
          }}
        />
        <Legend
          formatter={(value) => {
            const labels = {
              oee: "TRS Actuel",
              target: "Objectif",
              minimum: "Minimum",
            }

            if (value === "oee") {
              // Utiliser une seule couleur (COLORS[0]) pour correspondre à la barre
              return (
                <span>
                  <span>TRS Actuel</span>
                </span>
              )
            }

            return labels[value] || value
          }}
        />
        <Bar dataKey={dataKey} name="oee" fill={COLORS[0]} radius={[0, 4, 4, 0]} barSize={20} />
      </BarChart>
    </ResponsiveContainer>
  )
})

// Composant principal
const Production = () => {
  const [chartData, setChartData] = useState([])
  const [selectedDateData, setSelectedDateData] = useState([])
  const [uniqueDates, setUniqueDates] = useState([])
  const [goodQty, setGoodQty] = useState(0)
  const [rejetQty, setRejetQty] = useState(0)
  const [loading, setLoading] = useState(false)
  const [dateFilter, setDateFilter] = useState(null)
  const [performanceData, setPerformanceData] = useState([])
  const [machinePerformance, setMachinePerformance] = useState([])
  const [hourlyTrends, setHourlyTrends] = useState([])

  const [oeeTrends, setOeeTrends] = useState([])
  const [speedTrends, setSpeedTrends] = useState([])
  const [shiftComparison, setShiftComparison] = useState([])
  const [mergedData, setMergedData] = useState([])
  const [activeTab, setActiveTab] = useState("1")

  const screens = useBreakpoint()

  // Update the state variables for machine filtering
  const [machineModels, setMachineModels] = useState([]) // Will be populated from API
  const [machineNames, setMachineNames] = useState([])
  const [selectedMachineModel, setSelectedMachineModel] = useState("")
  const [selectedMachine, setSelectedMachine] = useState("")
  const [filteredMachineNames, setFilteredMachineNames] = useState([])

  // Enhanced date filtering state
  const [dateRangeType, setDateRangeType] = useState("day") // Options: "day", "week", "month"
  const [dateRangeDescription, setDateRangeDescription] = useState("")
  const [dateFilterActive, setDateFilterActive] = useState(false)

  // URL de base pour les requêtes API
  const baseURL =
    process.env.NODE_ENV === "production" ? "https://charming-hermit-intense.ngrok-free.app" : "http://localhost:5000"

  // Configuration d'Axios
  // No global config needed for SuperAgent; set baseURL and headers per request

  // Helper function to format date range for display
  const formatDateRange = useCallback((date, rangeType) => {
    if (!date) return { short: "", full: "" }

    const formattedDate = dayjs(date)

    if (rangeType === "day") {
      return {
        short: formattedDate.format("DD/MM/YYYY"),
        full: `le ${formattedDate.format("DD MMMM YYYY")}`,
      }
    } else if (rangeType === "week") {
      const startOfWeek = formattedDate.startOf("isoWeek")
      const endOfWeek = formattedDate.endOf("isoWeek")
      return {
        short: `S${formattedDate.isoWeek()} ${formattedDate.format("YYYY")}`,
        full: `du ${startOfWeek.format("DD MMMM")} au ${endOfWeek.format("DD MMMM YYYY")}`,
      }
    } else if (rangeType === "month") {
      return {
        short: formattedDate.format("MMMM YYYY"),
        full: `${formattedDate.format("MMMM YYYY")}`,
      }
    }

    return { short: "", full: "" }
  }, [])

  // We're now importing normalizePercentage and transformData from utils


  // Fetch machine models
  const fetchMachineModels = useCallback(async () => {
    try {
      console.log("Fetching machine models...")
      const response = await superagent.get(baseURL + "/api/machine-models").withCredentials()

      if (response.data && response.data.length > 0) {
        const models = response.data.map((item) => item.model || item)
        console.log("Machine models fetched:", models)
        setMachineModels(models)
      } else {
        // Set default models if API returns empty
        console.log("No machine models returned from API, using defaults")
        setMachineModels(["IPS", "CCM24"])
      }
    } catch (error) {
      console.error("Error loading machine models:", error)
      // Set default values if API fails
      setMachineModels(["IPS", "CCM24"])
    }
  }, [])

  // Fetch machine names
  const fetchMachineNames = useCallback(async () => {
    try {
      console.log("Fetching machine names...")
      const response = await superagent.get(baseURL + "/api/machine-names").withCredentials()

      if (response.data && response.data.length > 0) {
        console.log("Machine names fetched:", response.data)
        setMachineNames(response.data)

        // Find IPS01 in the machine names to set as default display value
        const ips01Machine = response.data.find((m) => m.Machine_Name === "IPS01")
        if (ips01Machine && !selectedMachineModel) {
          // Set IPS as the default model if not already selected
          setSelectedMachineModel("IPS")
        }
      } else {
        // Set default machine names if API returns empty
        console.log("No machine names returned from API, using defaults")
        setMachineNames([
          { Machine_Name: "IPS01" },
          { Machine_Name: "IPS02" },
          { Machine_Name: "IPS03" },
          { Machine_Name: "IPS04" },
        ])
      }
    } catch (error) {
      console.error("Error loading machine names:", error)
      // Set default values if API fails
      setMachineNames([
        { Machine_Name: "IPS01" },
        { Machine_Name: "IPS02" },
        { Machine_Name: "IPS03" },
        { Machine_Name: "IPS04" },
      ])
    }
  }, [selectedMachineModel])

  // Function to build query parameters for API requests
  const buildQueryParams = useCallback(() => {
    const queryParams = new URLSearchParams()

    // Add machine filtering
    if (selectedMachineModel && !selectedMachine) {
      queryParams.append("model", selectedMachineModel)
    } else if (selectedMachine) {
      queryParams.append("machine", selectedMachine)
    }

    // Add date range type and date if a date is selected
    if (dateFilter) {
      const formattedDate = dayjs(dateFilter).format("YYYY-MM-DD")
      queryParams.append("date", formattedDate)
      queryParams.append("dateRangeType", dateRangeType)
    } else {
      // If no date is selected, we want to show all data
      // No need to add any date parameters
      console.log("No date filter applied, showing all data for the selected machine model")
    }

    return queryParams.toString() ? `?${queryParams.toString()}` : ""
  }, [selectedMachineModel, selectedMachine, dateFilter, dateRangeType])

  // Update fetchData to use the buildQueryParams function
  const fetchData = useCallback(async () => {
    setLoading(true)
    try {
      // Build query parameters
      const queryString = buildQueryParams()
      console.log("API query string:", queryString)

      // Use Promise.allSettled instead of Promise.all to handle individual request failures
      const results = await Promise.allSettled([
        superagent.get(baseURL + `/api/testing-chart-production${queryString}`).withCredentials(),
        superagent.get(baseURL + "/api/unique-dates-production").withCredentials(),
        superagent.get(baseURL + `/api/sidecards-prod${queryString}`).withCredentials(),
        superagent.get(baseURL + `/api/sidecards-prod-rejet${queryString}`).withCredentials(),
        superagent.get(baseURL + `/api/machine-performance${queryString}`).withCredentials(),
        superagent.get(baseURL + `/api/hourly-trends${queryString}`).withCredentials(),
        superagent.get(baseURL + `/api/machine-oee-trends${queryString}`).withCredentials(),
        superagent.get(baseURL + `/api/speed-trends${queryString}`).withCredentials(),
        superagent.get(baseURL + `/api/shift-comparison${queryString}`).withCredentials(),
        superagent.get(baseURL + `/api/machine-daily-mould${queryString}`).withCredentials(),
      ])

      console.log(
        "API responses:",
        results.map((r) => r.status),
      )

      // Process results safely
      const [
        chartRes,
        datesRes,
        prodRes,
        rejetRes,
        machinesRes,
        hourlyRes,
        oeeTrendsRes,
        speedTrendsRes,
        shiftRes,
        mouldDataRes,
      ] = results

      // Update state with data from successful requests
      if (chartRes.status === "fulfilled" && chartRes.value.body) {
        // Extract data using our utility function
        const responseData = extractResponseData(chartRes.value);

        // Ensure responseData is an array before calling map
        const dataArray = Array.isArray(responseData) ? responseData : [];

        const transformedData = dataArray.map(transformData);
        setChartData(transformedData)

        // Calculate average availability with normalization
        let avgAvailability = 0;
        if (transformedData.length > 0) {
          const sum = transformedData.reduce((acc, item) => {
            // Get the availability value and ensure it's a number
            let availValue = parseFloat(item.availability || 0);
            // Normalize it (convert from 0-1 to 0-100 if needed)
            availValue = normalizePercentage(availValue);
            return acc + availValue;
          }, 0);
          avgAvailability = sum / transformedData.length;
        }

        // Calculate average performance with normalization
        let avgPerformance = 0;
        if (transformedData.length > 0) {
          const sum = transformedData.reduce((acc, item) => {
            // Get the performance value and ensure it's a number
            let perfValue = parseFloat(item.performance || 0);
            // Normalize it (convert from 0-1 to 0-100 if needed)
            perfValue = normalizePercentage(perfValue);
            return acc + perfValue;
          }, 0);
          avgPerformance = sum / transformedData.length;
        }

        // Calculate average quality with normalization
        let avgQuality = 0;
        if (transformedData.length > 0) {
          const sum = transformedData.reduce((acc, item) => {
            // Get the quality value and ensure it's a number
            let qualValue = parseFloat(item.quality || 0);
            // Normalize it (convert from 0-1 to 0-100 if needed)
            qualValue = normalizePercentage(qualValue);
            return acc + qualValue;
          }, 0);
          avgQuality = sum / transformedData.length;
        }

        console.log(" the avgAvailability is " + avgAvailability.toFixed(2) + "%")
        console.log(" the performance is " + avgPerformance.toFixed(2) + "%")
        console.log(" the quality is " + avgQuality.toFixed(2) + "%")
      } else {
        console.log("No chart data available")
        setChartData([])
      }

      if (datesRes.status === "fulfilled") {
        // Extract data using our utility function
        const datesData = extractResponseData(datesRes.value);
        setUniqueDates(datesData || [])
      }

      if (prodRes.status === "fulfilled") {
        // Extract data using our utility function
        const prodData = extractResponseData(prodRes.value);
        setGoodQty(prodData[0]?.goodqty || 0)
      } else {
        setGoodQty(0)
      }

      if (rejetRes.status === "fulfilled") {
        // Extract data using our utility function
        const rejetData = extractResponseData(rejetRes.value);
        setRejetQty(rejetData[0]?.rejetqty || 0)
      } else {
        setRejetQty(0)
      }

      if (machinesRes.status === "fulfilled" && machinesRes.value.data) {
        // Extract data using our utility function
        const machinesData = extractResponseData(machinesRes.value);
        console.log("Machine performance data:", machinesData)
        setMachinePerformance(machinesData || [])
      } else {
        console.log("No machine performance data available")
        setMachinePerformance([])
      }

      if (hourlyRes.status === "fulfilled") {
        // Extract data using our utility function
        const hourlyData = extractResponseData(hourlyRes.value);
        setHourlyTrends(hourlyData || [])
      }



      // Process OEE and speed trends
      const oeeMap =
        oeeTrendsRes.status === "fulfilled" && oeeTrendsRes.value.data
          ? extractResponseData(oeeTrendsRes.value).reduce((acc, item) => {
              acc[item.date] = parseFloat(item.oee) || 0
              return acc
            }, {})
          : {}

      const speedMap =
        speedTrendsRes.status === "fulfilled" && speedTrendsRes.value.data
          ? extractResponseData(speedTrendsRes.value).reduce((acc, item) => {
              // Ensure speed is a valid number
              const speedValue = parseFloat(item.speed)
              if (!isNaN(speedValue) && speedValue > 0) {
                acc[item.date] = speedValue
              }
              return acc
            }, {})
          : {}

      const allDates = [...new Set([...Object.keys(oeeMap), ...Object.keys(speedMap)])]

      // Process dates to ensure they're in chronological order
      const sortedDates = [...allDates].sort((a, b) => dayjs(a).diff(dayjs(b)));

      // Filter out dates that are too far apart (more than 30 days from the most recent date)
      let filteredDates = sortedDates;
      if (sortedDates.length > 0) {
        const mostRecentDate = dayjs(sortedDates[sortedDates.length - 1]);
        filteredDates = sortedDates.filter(date => {
          const diff = mostRecentDate.diff(dayjs(date), 'day');
          return diff <= 60; // Only show data from the last 60 days
        });
      }

      const mergedRes = filteredDates
        .map((date) => ({
          date,
          oee: oeeMap[date] || 0,
          speed: speedMap[date] || null, // Use null for missing values to avoid connecting lines
        }))
        .sort((a, b) => dayjs(a.date).diff(dayjs(b.date)))

      // Use machine_daily_table_mould data if available, otherwise fallback to merged OEE/speed data
      if (mouldDataRes && mouldDataRes.status === "fulfilled" && mouldDataRes.value.data) {
        // Extract data using our utility function
        const mouldData = extractResponseData(mouldDataRes.value);

        if (mouldData.length > 0) {
          console.log("Machine daily mould data available:", mouldData.length, "records")
          console.log("Sample record:", mouldData[0])

          try {
            // Process the mould data to ensure it has the correct format for charts
            const processedMouldData = mouldData.map(item => {
              // Ensure all numeric values are properly parsed
              const goodQty = parseFloat(item.Good_QTY_Day || item.good || 0);
              const rejectQty = parseFloat(item.Rejects_QTY_Day || item.reject || 0);
              const oeeValue = parseFloat(item.OEE_Day || item.oee || 0);
              const speedValue = parseFloat(item.Speed_Day || item.speed || 0);
              const availabilityValue = parseFloat(item.Availability_Rate_Day || item.availability || 0);
              const performanceValue = parseFloat(item.Performance_Rate_Day || item.performance || 0);
              const qualityValue = parseFloat(item.Quality_Rate_Day || item.quality || 0);

              // Ensure date is in a valid format (YYYY-MM-DD)
              let formattedDate = null;
              try {
                // Try to parse the date from the API
                const rawDate = item.Date_Insert_Day || item.date;
                if (rawDate) {
                  // Check if it's already a valid date string
                  if (dayjs(rawDate).isValid()) {
                    formattedDate = dayjs(rawDate).format('YYYY-MM-DD');
                  } else {
                    // Try to parse different date formats
                    const formats = ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD', 'YYYY/MM/DD', 'DD-MM-YYYY'];
                    for (const format of formats) {
                      const parsed = dayjs(rawDate, format);
                      if (parsed.isValid()) {
                        formattedDate = parsed.format('YYYY-MM-DD');
                        break;
                      }
                    }
                  }
                }

                // If we still don't have a valid date, use today's date
                if (!formattedDate) {
                  console.warn(`Invalid date found: ${item.Date_Insert_Day || item.date}, using today's date instead`);
                  formattedDate = dayjs().format('YYYY-MM-DD');
                }
              } catch (error) {
                console.error("Error parsing date:", error);
                formattedDate = dayjs().format('YYYY-MM-DD');
              }

              // Handle decimal percentages (convert 0-1 range to 0-100)
              let normalizedOee = 0;
              if (!isNaN(oeeValue)) {
                // If oee is between 0-1, multiply by 100 to get percentage
                normalizedOee = oeeValue <= 1 && oeeValue > 0 ? oeeValue * 100 : oeeValue;
              }

              // Handle decimal percentages (convert 0-1 range to 0-100)
              let normalizedAvailability = 0;
              if (!isNaN(availabilityValue)) {
                normalizedAvailability = availabilityValue <= 1 && availabilityValue > 0 ? availabilityValue * 100 : availabilityValue;
              }

              let normalizedPerformance = 0;
              if (!isNaN(performanceValue)) {
                normalizedPerformance = performanceValue <= 1 && performanceValue > 0 ? performanceValue * 100 : performanceValue;
              }

              let normalizedQuality = 0;
              if (!isNaN(qualityValue)) {
                normalizedQuality = qualityValue <= 1 && qualityValue > 0 ? qualityValue * 100 : qualityValue;
              }

              return {
                date: formattedDate,
                oee: normalizedOee,
                speed: !isNaN(speedValue) ? speedValue : 0,
                good: !isNaN(goodQty) ? goodQty : 0,
                reject: !isNaN(rejectQty) ? rejectQty : 0,
                Machine_Name: item.Machine_Name || 'N/A',
                Shift: item.Shift || 'N/A',
                availability: normalizedAvailability,
                performance: normalizedPerformance,
                quality: normalizedQuality
              };
            }).sort((a, b) => dayjs(a.date).diff(dayjs(b.date)));

            // Log the processed data for debugging
            console.log("Processed mould data for charts:", processedMouldData.length, "records");
            console.log("First record:", processedMouldData[0]);
            console.log("Last record:", processedMouldData[processedMouldData.length - 1]);

            // Check if we have any valid data points with non-zero values
            const hasValidData = processedMouldData.some(item =>
              item.good > 0 || item.reject > 0 || item.oee > 0 || item.speed > 0
            );

            if (hasValidData) {
              setMergedData(processedMouldData);
            } else {
              console.warn("No valid data points found in processed mould data");
              // Use sample data for demonstration if needed
              const sampleData = generateSampleData();
              setMergedData(sampleData);

              // Show notification about using sample data
              notification.info({
                message: "Données de démonstration",
                description: "Aucune donnée valide n'a été trouvée. Des données de démonstration sont affichées.",
                duration: 5,
              });
            }
          } catch (error) {
            console.error("Error processing mould data:", error);
            setMergedData(mergedRes);
          }
        } else {
          console.log("Machine daily mould API returned empty array, using merged OEE/speed data as fallback");

          // Check if mergedRes has any data
          if (mergedRes.length > 0) {
            setMergedData(mergedRes);
          } else {
            // Generate sample data for demonstration
            const sampleData = generateSampleData();
            setMergedData(sampleData);

            // Show notification about using sample data
            notification.info({
              message: "Données de démonstration",
              description: "Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",
              duration: 5,
            });
          }

          // Show notification to user if filters are applied
          if (selectedMachineModel || selectedMachine || dateFilter) {
            notification.info({
              message: "Aucune donnée disponible",
              description: "Aucune donnée n'a été trouvée pour les filtres sélectionnés. Essayez de modifier vos critères de recherche.",
              duration: 5,
            });
          }
        }
      } else {
        // Handle API error or rejected promise
        console.log("Machine daily mould API request failed or returned invalid data");
        if (mouldDataRes && mouldDataRes.status === "rejected") {
          console.error("API error:", mouldDataRes.reason);
        }

        // Check if mergedRes has any data
        if (mergedRes.length > 0) {
          setMergedData(mergedRes);
        } else {
          // Generate sample data for demonstration
          const sampleData = generateSampleData();
          setMergedData(sampleData);

          // Show notification about using sample data
          notification.info({
            message: "Données de démonstration",
            description: "Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",
            duration: 5,
          });
        }
      }

      // Helper function to generate sample data for demonstration
      function generateSampleData() {
        // Ensure we're using a valid date object
        const today = dayjs();
        const sampleData = [];

        console.log("Generating sample data starting from:", today.format('YYYY-MM-DD'));

        // Generate 10 days of sample data
        for (let i = 9; i >= 0; i--) {
          // Create a proper date string in YYYY-MM-DD format
          const date = today.subtract(i, 'day').format('YYYY-MM-DD');

          // Validate the date is correct
          if (!dayjs(date).isValid()) {
            console.error("Invalid date generated:", date);
            continue; // Skip this iteration
          }

          // Create sample data point with valid date
          sampleData.push({
            date,
            good: Math.floor(Math.random() * 1000) + 500,
            reject: Math.floor(Math.random() * 100) + 10,
            oee: Math.floor(Math.random() * 30) + 70, // 70-100% (already in 0-100 range)
            speed: Math.floor(Math.random() * 5) + 5, // 5-10
            Machine_Name: selectedMachine || (selectedMachineModel ? `${selectedMachineModel}01` : 'IPS01'),
            Shift: ['Matin', 'Après-midi', 'Nuit'][Math.floor(Math.random() * 3)]
          });
        }

        console.log("Generated sample data:", sampleData.length, "records");
        console.log("Sample data first record:", sampleData[0]);
        console.log("Sample data last record:", sampleData[sampleData.length - 1]);

        return sampleData;
      }

      // Log the current state of the data (this will show the previous state, not the updated one)
      console.log("Current table data state:", mergedData)
      // Log the data that will be set in the next render
      console.log("New table data being set:", mouldDataRes?.status === "fulfilled" && mouldDataRes.value.data?.length > 0 ?
        mouldDataRes.value.data : mergedRes)

      // Log the data in a readable format instead of concatenating objects
      if (oeeTrendsRes.status === "fulfilled") {
        setOeeTrends(extractResponseData(oeeTrendsRes.value) || [])
      }

      if (speedTrendsRes.status === "fulfilled") {
        setSpeedTrends(extractResponseData(speedTrendsRes.value) || [])
      }

      if (shiftRes.status === "fulfilled") {
        setShiftComparison(extractResponseData(shiftRes.value) || [])
      }
    } catch (error) {
      console.error("Error loading data:", error)
      // Set default values for critical components
      setGoodQty(0)
      setRejetQty(0)
      setChartData([])
      setMachinePerformance([])
    } finally {
      setLoading(false)
    }
  }, [selectedMachineModel, selectedMachine, dateRangeType, dateFilter])
  // Update the date handling functions to match arrets2.jsx
  const handleDateChange = (date) => {
    if (!date) {
      resetDateFilter()
      return
    }

    setDateFilter(date)
    const { full } = formatDateRange(date, dateRangeType)
    setDateRangeDescription(full)
    setDateFilterActive(true)
  }

  const handleDateRangeTypeChange = (type) => {
    setDateRangeType(type)

    // If a date is already selected, update the description
    if (dateFilter) {
      const { full } = formatDateRange(dateFilter, type)
      setDateRangeDescription(full)
    }
    // Don't automatically set today's date when no date is selected
    // This allows viewing all data when no date filter is applied
  }
  const resetDateFilter = () => {
    setDateFilter(null)
    setDateRangeDescription("")
    setDateFilterActive(false)
  }

  // Reset clears all filters
  const resetFilters = () => {
    resetDateFilter()
    setDateRangeType("day")
    setSelectedMachineModel("")
    setSelectedMachine("")
    setFilteredMachineNames([])
  }


  // Gestionnaire de rafraîchissement des données
  const handleRefresh = () => {
    fetchData()

    // Calculate average availability with normalization
    let avgAvailabilityValue = 0;
    if (chartData.length > 0) {
      const sum = chartData.reduce((acc, item) => {
        // Get the availability value and ensure it's a number
        let availValue = parseFloat(item.availability || 0);
        // Normalize it (convert from 0-1 to 0-100 if needed)
        availValue = normalizePercentage(availValue);
        return acc + availValue;
      }, 0);
      avgAvailabilityValue = sum / chartData.length;
    }

    // Calculate average performance with normalization
    let avgPerformanceValue = 0;
    if (chartData.length > 0) {
      const sum = chartData.reduce((acc, item) => {
        // Get the performance value and ensure it's a number
        let perfValue = parseFloat(item.performance || 0);
        // Normalize it (convert from 0-1 to 0-100 if needed)
        perfValue = normalizePercentage(perfValue);
        return acc + perfValue;
      }, 0);
      avgPerformanceValue = sum / chartData.length;
    }

    // Calculate average quality with normalization
    let avgQualityValue = 0;
    if (chartData.length > 0) {
      const sum = chartData.reduce((acc, item) => {
        // Get the quality value and ensure it's a number
        let qualValue = parseFloat(item.quality || 0);
        // Normalize it (convert from 0-1 to 0-100 if needed)
        qualValue = normalizePercentage(qualValue);
        return acc + qualValue;
      }, 0);
      avgQualityValue = sum / chartData.length;
    }

    console.log(" the Availability is " + avgAvailabilityValue.toFixed(2) + "%")
    console.log(" the performance is " + avgPerformanceValue.toFixed(2) + "%")
    console.log(" the quality is " + avgQualityValue.toFixed(2) + "%")
  }

  // Handle machine model selection
  const handleMachineModelChange = (value) => {
    setSelectedMachineModel(value)
    // Machine selection will be cleared in the useEffect if it doesn't belong to the new model
  }

  const handleMachineChange = (value) => {
    setSelectedMachine(value)
    // No direct fetchData call here, fetch happens via useEffect
  }

  // Add useEffect hooks for machine data fetching
  useEffect(() => {
    fetchMachineModels()
  }, [fetchMachineModels])

  useEffect(() => {
    fetchMachineNames()
  }, [fetchMachineNames])

  // Fetch general data for good quantity and rejected quantity on component mount
  useEffect(() => {
    const fetchGeneralData = async () => {
      try {
        // Always fetch general data, regardless of machine model selection
        setLoading(true)

        // Use Promise.allSettled to handle individual request failures
        const results = await Promise.allSettled([
          superagent.get(baseURL + `/api/sidecards-prod`).withCredentials(),
          superagent.get(baseURL + `/api/sidecards-prod-rejet`).withCredentials(),
        ])

        const [prodRes, rejetRes] = results

        // Update good quantity
        if (prodRes.status === "fulfilled") {
          const prodData = extractResponseData(prodRes.value);
          console.log("Good quantity response:", prodData)
          setGoodQty(prodData[0]?.goodqty || 15000)
        } else {
          console.error("Failed to fetch good quantity:", prodRes.reason)
          setGoodQty(15000) // Use sample data if the request fails
        }

        // Update rejected quantity
        if (rejetRes.status === "fulfilled") {
          const rejetData = extractResponseData(rejetRes.value);
          console.log("Rejected quantity response:", rejetData)
          setRejetQty(rejetData[0]?.rejetqty || 750)
        } else {
          console.error("Failed to fetch rejected quantity:", rejetRes.reason)
          setRejetQty(750) // Use sample data if the request fails
        }
      } catch (error) {
        console.error("Error loading general data:", error)
        // Use sample data if an error occurs
        setGoodQty(15000)
        setRejetQty(750)
      } finally {
        setLoading(false)
      }
    }

    fetchGeneralData()
  }, [selectedMachineModel])

  // Effect to filter machines by selected model
  useEffect(() => {
    if (selectedMachineModel) {
      // Filter machines that belong to the selected model
      const filtered = machineNames.filter(
        (machine) => machine.Machine_Name && machine.Machine_Name.startsWith(selectedMachineModel),
      )
      setFilteredMachineNames(filtered)

      // Don't clear machine selection when model changes if the machine still belongs to the model
      if (selectedMachine && !filtered.some((m) => m.Machine_Name === selectedMachine)) {
        setSelectedMachine("")
      }
    } else {
      setFilteredMachineNames([])
      setSelectedMachine("")
    }
  }, [selectedMachineModel, machineNames, selectedMachine])

  // Attach main useEffect for data fetching - fires whenever model/machine/date changes
  useEffect(() => {
    if (selectedMachineModel) {
      fetchData()

      // Show notification when no date filter is applied
      if (!dateFilter) {
        notification.info({
          message: "Affichage de toutes les données",
          description: "Aucun filtre de date n'est appliqué. Toutes les données disponibles pour le modèle sélectionné sont affichées.",
          icon: <InfoCircleOutlined style={{ color: "#1890ff" }} />,
          placement: "bottomRight",
          duration: 3
        })
      }
    } else {
      // Clear only chart and performance data if no model selected
      // Don't clear operator stats, good quantity, and rejected quantity
      // as they should be shown even when no machine model is selected
      setChartData([])
      setMachinePerformance([])
      setMergedData([])
      setOeeTrends([])
      setSpeedTrends([])
      setShiftComparison([])
    }
  }, [selectedMachineModel, selectedMachine, dateFilter, dateRangeType, fetchData])

  // Calcul des statistiques

  // Calculate average TRS (OEE) with normalization
  let avgTRS = 0;
  if (chartData.length > 0) {
    const sum = chartData.reduce((acc, item) => {
      // Get the OEE value and ensure it's a number
      let oeeValue = parseFloat(item.oee || 0);
      // Normalize it (convert from 0-1 to 0-100 if needed)
      oeeValue = normalizePercentage(oeeValue);
      return acc + oeeValue;
    }, 0);
    avgTRS = sum / chartData.length;
  }

  const rejectRate = goodQty + rejetQty > 0 ? (rejetQty / (goodQty + rejetQty)) * 100 : 0;
  const qualityRate = goodQty + rejetQty > 0 ? (goodQty / (goodQty + rejetQty)) * 100 : 0;

  // Calculate average availability with normalization
  let avgAvailability = 0;
  if (chartData.length > 0) {
    const sum = chartData.reduce((acc, item) => {
      // Get the availability value and ensure it's a number
      let availValue = parseFloat(item.availability || 0);
      // Normalize it (convert from 0-1 to 0-100 if needed)
      availValue = normalizePercentage(availValue);
      return acc + availValue;
    }, 0);
    avgAvailability = sum / chartData.length;
  }

  // Calculate average performance with normalization
  let avgPerformance = 0;
  if (chartData.length > 0) {
    const sum = chartData.reduce((acc, item) => {
      // Get the performance value and ensure it's a number
      let perfValue = parseFloat(item.performance || 0);
      // Normalize it (convert from 0-1 to 0-100 if needed)
      perfValue = normalizePercentage(perfValue);
      return acc + perfValue;
    }, 0);
    avgPerformance = sum / chartData.length;
  }

  // Calculate average quality with normalization
  let avgQuality = 0;
  if (chartData.length > 0) {
    const sum = chartData.reduce((acc, item) => {
      // Get the quality value and ensure it's a number
      let qualValue = parseFloat(item.quality || 0);
      // Normalize it (convert from 0-1 to 0-100 if needed)
      qualValue = normalizePercentage(qualValue);
      return acc + qualValue;
    }, 0);
    avgQuality = sum / chartData.length;
  }

  console.log(" the Availability is " + avgAvailability.toFixed(2) + "%");
  console.log(" the performance is " + avgPerformance.toFixed(2) + "%");
  console.log(" the quality is " + avgQuality.toFixed(2) + "%");

  // Statistiques principales
  const stats = [
    {
      title: "Production Totale",
      value: goodQty,
      suffix: "Pcs",
      icon: <RiseOutlined />,
      color: "#52c41a",
      description: "Nombre total de pièces bonnes produites",
    },
    {
      title: "Rejet Total",
      value: rejetQty,
      suffix: "Kg",
      icon: <FallOutlined />,
      color: "#f5222d",
      description: "Nombre total de pièces rejetées",
    },
    {
      title: "TRS Moyen",
      value: avgTRS,
      suffix: "%",
      icon: <DashboardOutlined />,
      color: "#1890ff",
      description: "Taux de Rendement Synthétique moyen (OEE_Day)",
    },
    {
      title: "Disponibilité",
      value: avgAvailability,
      suffix: "%",
      icon: <ClockCircleOutlined />,
      color: "#722ed1",
      description: "Taux de disponibilité moyen (Availability_Rate_Day)",
    },
    {
      title: "Performance",
      value: avgPerformance,
      suffix: "%",
      icon: <ThunderboltOutlined />,
      color: "#eb2f96",
      description: "Taux de performance moyen (Performance_Rate_Day)",
    },

    {
      title: "Taux de Rejet",
      value: rejectRate,
      suffix: "%",
      icon: <CloseCircleOutlined />,
      color: "#fa8c16",
      description: "Pourcentage de pièces rejetées sur la production totale",
    },
    {
      title: "Taux de Qualité",
      value: qualityRate,
      suffix: "%",
      icon: <CheckCircleOutlined />,
      color: "#52c41a",
      description: "Pourcentage de pièces bonnes sur la production totale",
    },
  ]



  // Colonnes pour le tableau de performance des machines
  const machinePerformanceColumns = [
    {
      title: "Machine",
      dataIndex: "Machine_Name",
      key: "machine",
      fixed: "left",
      render: (text) => (
        <Space>
          <ToolOutlined style={{ color: COLORS[0] }} />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: "Moule",
      dataIndex: "mould_number",
      key: "mould",
      render: (text) => <Tag color="cyan">{text || "N/A"}</Tag>,
    },
    {
      title: "Équipe",
      dataIndex: "Shift",
      key: "shift",
      render: (text) => <Tag color="blue">{text || "N/A"}</Tag>,
    },
    {
      title: "Production",
      dataIndex: "good",
      key: "good",
      render: (text) => <Tag color="green">{(text || 0).toLocaleString()} pcs</Tag>,
      sorter: (a, b) => (a.good || 0) - (b.good || 0),
    },
    {
      title: "Rejets",
      dataIndex: "reject",
      key: "reject",
      render: (text) => <Tag color="red">{(text || 0).toLocaleString()} kg</Tag>,
      sorter: (a, b) => (a.reject || 0) - (b.reject || 0),
    },
    {
      title: "Heures Prod.",
      dataIndex: "run_hours",
      key: "run_hours",
      render: (text) => <Tag color="green">{(text || 0).toFixed(2)} h</Tag>,
      sorter: (a, b) => (a.run_hours || 0) - (b.run_hours || 0),
    },
    {
      title: "Heures Arrêt",
      dataIndex: "down_hours",
      key: "down_hours",
      render: (text) => <Tag color="orange">{(text || 0).toFixed(2)} h</Tag>,
      sorter: (a, b) => (a.down_hours || 0) - (b.down_hours || 0),
    },
    {
      title: "Cycle Théorique",
      dataIndex: "cycle_theorique",
      key: "cycle_theorique",
      render: (text) => <Tag color="purple">{text || "N/A"}</Tag>,
    },
    {
      title: "Disponibilité",
      dataIndex: "availability",
      key: "availability",
      render: (value) => {
        // Ensure value is a valid number and handle values between 0-1 or 0-100
        let numValue = 0;
        if (typeof value === "number" && !isNaN(value)) {
          // Check if value is between 0-1 (decimal percentage)
          numValue = value <= 1 && value > 0 ? value * 100 : value;
        } else if (typeof value === "string") {
          // Try to parse string value
          const parsed = parseFloat(value);
          if (!isNaN(parsed)) {
            numValue = parsed <= 1 && parsed > 0 ? parsed * 100 : parsed;
          }
        }

        // Ensure value is within valid percentage range (0-100)
        numValue = Math.max(0, Math.min(100, numValue));

        return (
          <Tooltip title={`${numValue.toFixed(1)}% de disponibilité`}>
            <Progress
              percent={numValue}
              size="small"
              status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
              format={(percent) => {
                // Check if percent is a number before using toFixed
                return typeof percent === "number" && !isNaN(percent)
                  ? `${percent.toFixed(1)}%`
                  : "0.0%"
              }}
            />
          </Tooltip>
        );
      },
      sorter: (a, b) => {
        // Get values and normalize them (handle 0-1 range)
        let availA = 0;
        if (typeof a.availability === "number" && !isNaN(a.availability)) {
          availA = a.availability <= 1 && a.availability > 0 ? a.availability * 100 : a.availability;
        }

        let availB = 0;
        if (typeof b.availability === "number" && !isNaN(b.availability)) {
          availB = b.availability <= 1 && b.availability > 0 ? b.availability * 100 : b.availability;
        }

        return availA - availB;
      },
    },
    {
      title: "Performance",
      dataIndex: "performance",
      key: "performance",
      render: (value) => {
        // Ensure value is a valid number and handle values between 0-1 or 0-100
        let numValue = 0;
        if (typeof value === "number" && !isNaN(value)) {
          // Check if value is between 0-1 (decimal percentage)
          numValue = value <= 1 && value > 0 ? value * 100 : value;
        } else if (typeof value === "string") {
          // Try to parse string value
          const parsed = parseFloat(value);
          if (!isNaN(parsed)) {
            numValue = parsed <= 1 && parsed > 0 ? parsed * 100 : parsed;
          }
        }

        // Ensure value is within valid percentage range (0-100)
        numValue = Math.max(0, Math.min(100, numValue));

        return (
          <Tooltip title={`${numValue.toFixed(1)}% de performance`}>
            <Progress
              percent={numValue}
              size="small"
              status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
              format={(percent) => {
                // Check if percent is a number before using toFixed
                return typeof percent === "number" && !isNaN(percent)
                  ? `${percent.toFixed(1)}%`
                  : "0.0%"
              }}
            />
          </Tooltip>
        );
      },
      sorter: (a, b) => {
        // Get values and normalize them (handle 0-1 range)
        let perfA = 0;
        if (typeof a.performance === "number" && !isNaN(a.performance)) {
          perfA = a.performance <= 1 && a.performance > 0 ? a.performance * 100 : a.performance;
        }

        let perfB = 0;
        if (typeof b.performance === "number" && !isNaN(b.performance)) {
          perfB = b.performance <= 1 && b.performance > 0 ? b.performance * 100 : b.performance;
        }

        return perfA - perfB;
      },
    },
    {
      title: "TRS",
      dataIndex: "oee",
      key: "oee",
      render: (value) => {
        // Ensure value is a valid number and handle values between 0-1 or 0-100
        let numValue = 0;
        if (typeof value === "number" && !isNaN(value)) {
          // Check if value is between 0-1 (decimal percentage)
          numValue = value <= 1 && value > 0 ? value * 100 : value;
        } else if (typeof value === "string") {
          // Try to parse string value
          const parsed = parseFloat(value);
          if (!isNaN(parsed)) {
            numValue = parsed <= 1 && parsed > 0 ? parsed * 100 : parsed;
          }
        }

        // Ensure value is within valid percentage range (0-100)
        numValue = Math.max(0, Math.min(100, numValue));

        return (
          <Tooltip title={`${numValue.toFixed(1)}% de TRS`}>
            <Progress
              percent={numValue}
              size="small"
              status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
              format={(percent) => {
                // Check if percent is a number before using toFixed
                return typeof percent === "number" && !isNaN(percent)
                  ? `${percent.toFixed(1)}%`
                  : "0.0%"
              }}
            />
          </Tooltip>
        );
      },
      sorter: (a, b) => {
        // Get values and normalize them (handle 0-1 range)
        let oeeA = 0;
        if (typeof a.oee === "number" && !isNaN(a.oee)) {
          oeeA = a.oee <= 1 && a.oee > 0 ? a.oee * 100 : a.oee;
        }

        let oeeB = 0;
        if (typeof b.oee === "number" && !isNaN(b.oee)) {
          oeeB = b.oee <= 1 && b.oee > 0 ? b.oee * 100 : b.oee;
        }

        return oeeA - oeeB;
      },
      defaultSortOrder: "descend",
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <Dropdown
          overlay={
            <Menu>
              <Menu.Item key="1" icon={<LineChartOutlined />}>
                Voir tendances
              </Menu.Item>
              <Menu.Item key="2" icon={<SettingOutlined />}>
                Paramètres
              </Menu.Item>
              <Menu.Item key="3" icon={<DownloadOutlined />}>
                Exporter données
              </Menu.Item>
            </Menu>
          }
          trigger={["click"]}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ]



  // Fonction pour rendre un graphique avec gestion des données vides
  const renderChart = (data, Component, props = {}) => {
    if (!data || data.length === 0) {
      return (
        <div style={{ height: 300, display: "flex", alignItems: "center", justifyContent: "center" }}>
          <Empty description="Aucune donnée disponible" />
        </div>
      )
    }
    return <Component data={data} {...props} />
  }

  // Fonction pour déterminer le quart actuel en fonction de l'heure
  const getCurrentShift = () => {
    const now = new Date();
    const hour = now.getHours();

    if (hour >= 6 && hour < 14) {
      return "Matin";
    } else if (hour >= 14 && hour < 22) {
      return "Après-midi";
    } else {
      return "Nuit";
    }
  };

  // Rendu du composant
  return (
    <div style={{ padding: screens.md ? 24 : 16 }}>
      <Spin spinning={loading} tip="Chargement des données..." size="large">
        <Row gutter={[24, 24]}>
          {/* En-tête */}
          <Col span={24}>
            <Card bordered={false} bodyStyle={{ padding: screens.md ? 24 : 16 }}>
              <Row gutter={[24, 24]} align="middle">
                <Col xs={24} md={12}>
                  <Title level={3} style={{ marginBottom: 8 }}>
                    <DashboardOutlined style={{ marginRight: 12, color: COLORS[0] }} />
                    Tableau de Bord de Production
                  </Title>
                </Col>
                <Col xs={24} md={12} style={{ textAlign: screens.md ? "right" : "left" }}>
                  <Space direction="vertical" style={{ width: "100%" }}>
                    <Space wrap>
                      {/* Dropdown for machine model selection */}
                      <Select
                        placeholder="Sélectionner un modèle"
                        style={{ width: 150 }}
                        value={selectedMachineModel || undefined}
                        onChange={handleMachineModelChange}
                        allowClear
                      >
                        {machineModels.map((model) => (
                          <Option key={model} value={model}>
                            {model}
                          </Option>
                        ))}
                      </Select>

                      {/* Dropdown for specific machine selection */}
                      {selectedMachineModel && (
                        <Select
                          placeholder="Sélectionner une machine"
                          style={{ width: 150 }}
                          value={selectedMachine || undefined}
                          onChange={handleMachineChange}
                          allowClear
                        >
                          {filteredMachineNames.map((machine) => (
                            <Option key={machine.Machine_Name} value={machine.Machine_Name}>
                              {machine.Machine_Name}
                            </Option>
                          ))}
                        </Select>
                      )}

                      {/* Date range type selector */}
                      <Segmented
                        options={[
                          { label: "Jour", value: "day" },
                          { label: "Semaine", value: "week" },
                          { label: "Mois", value: "month" },
                        ]}
                        value={dateRangeType}
                        onChange={handleDateRangeTypeChange}
                      />

                      {/* Date picker for filtering by date */}
                      <DatePicker
                        placeholder="Filtrer par date"
                        value={dateFilter}
                        onChange={handleDateChange}
                        format="DD/MM/YYYY"
                        picker={dateRangeType === "month" ? "month" : dateRangeType === "week" ? "week" : "date"}
                        allowClear
                        disabledDate={(current) => current > dayjs().endOf("day")}
                      />

                      {/* Reset button */}
                      <Button icon={<ClearOutlined />} onClick={resetFilters} type="primary" ghost>
                        Réinitialiser
                      </Button>

                      <Button
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={handleRefresh}
                        disabled={!selectedMachineModel} // Disable refresh if no model selected
                      >
                        Actualiser
                      </Button>
                    </Space>

                    {/* Display active filters */}
                    {(selectedMachineModel || dateFilterActive) && (
                      <Space wrap style={{ marginTop: 8 }}>
                        {selectedMachineModel && (
                          <Tag color="blue" closable onClose={() => setSelectedMachineModel("")}>
                            Modèle: {selectedMachineModel}
                          </Tag>
                        )}
                        {selectedMachine && (
                          <Tag color="green" closable onClose={() => setSelectedMachine("")}>
                            Machine: {selectedMachine}
                          </Tag>
                        )}
                        {dateFilterActive && (
                          <Tag color="purple" closable onClose={resetDateFilter}>
                            Période: {dateRangeDescription}
                          </Tag>
                        )}
                      </Space>
                    )}
                  </Space>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* First row of stats cards */}
          {stats.slice(0, 4).map((stat) => (
            <Col key={stat.title} xs={24} sm={12} md={6}>
              <Card
                hoverable
                style={{
                  borderTop: `2px solid ${stat.color}`,
                  height: "100%",
                }}
              >
                <Statistic
                  title={
                    <Tooltip title={stat.description}>
                      <Space>
                        {React.cloneElement(stat.icon, {
                          style: { color: stat.color, fontSize: 20 },
                        })}
                        <span>{stat.title}</span>
                        <InfoCircleOutlined style={{ color: "#8c8c8c", fontSize: 14 }} />
                      </Space>
                    </Tooltip>
                  }
                  value={stat.value}
                  precision={stat.title.includes("TRS") || stat.title.includes("Taux") ||
                            stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                            stat.title.includes("Qualité") ? 1 : 0}
                  suffix={stat.suffix}
                  valueStyle={{
                    fontSize: 24,
                    color: stat.color,
                  }}
                />
                {(stat.title.includes("TRS") || stat.title.includes("Taux") ||
                  stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                  stat.title.includes("Qualité")) && (
                  <Progress
                    percent={stat.value}
                    strokeColor={stat.color}
                    showInfo={false}
                    status={stat.value > 85 ? "success" : stat.value > 70 ? "normal" : "exception"}
                    style={{ marginTop: 12 }}
                  />
                )}
              </Card>
            </Col>
          ))}

          {/* Second row of stats cards */}
          {stats.slice(4).map((stat) => (
            <Col key={stat.title} xs={24} sm={12} md={6}>
              <Card
                hoverable
                style={{
                  borderTop: `2px solid ${stat.color}`,
                  height: "100%",
                }}
              >
                <Statistic
                  title={
                    <Tooltip title={stat.description}>
                      <Space>
                        {React.cloneElement(stat.icon, {
                          style: { color: stat.color, fontSize: 20 },
                        })}
                        <span>{stat.title}</span>
                        <InfoCircleOutlined style={{ color: "#8c8c8c", fontSize: 14 }} />
                      </Space>
                    </Tooltip>
                  }
                  value={stat.value}
                  precision={stat.title.includes("TRS") || stat.title.includes("Taux") ||
                            stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                            stat.title.includes("Qualité") ? 1 : 0}
                  suffix={stat.suffix}
                  valueStyle={{
                    fontSize: 24,
                    color: stat.color,
                  }}
                />
                {(stat.title.includes("TRS") || stat.title.includes("Taux") ||
                  stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                  stat.title.includes("Qualité")) && (
                  <Progress
                    percent={stat.value}
                    strokeColor={stat.color}
                    showInfo={false}
                    status={stat.value > 85 ? "success" : stat.value > 70 ? "normal" : "exception"}
                    style={{ marginTop: 12 }}
                  />
                )}
              </Card>
            </Col>
          ))}

          {selectedMachineModel ? (
            <>
              {/* Onglets pour les graphiques et tableaux */}
              <Col span={24}>
                <Card bordered={false}>
                  <Tabs
                    defaultActiveKey="1"
                    onChange={setActiveTab}
                    tabBarExtraContent={
                      <Space>
                        <Button type="link" icon={<DownloadOutlined />} disabled>
                          Exporter
                        </Button>
                        {selectedMachine && (
                          <ShiftReportButton
                            machineId={selectedMachine}
                            machineName={selectedMachine}
                            shift={getCurrentShift()}
                          />
                        )}
                      </Space>
                    }
                  >
                    <TabPane
                      tab={
                        <span>
                          <LineChartOutlined />
                          Tendances
                        </span>
                      }
                      key="1"
                    >
                      <Row gutter={[24, 24]}>
                        {/* Graphique de performance hebdomadaire */}
                        <Col span={24}>
                          <Card
                            title={
                              <Space>
                                <LineChartOutlined style={{ fontSize: 20, color: COLORS[0] }} />
                                <Text strong>Performance {dateRangeType === "day" ? "Journalière" : dateRangeType === "week" ? "Hebdomadaire" : "Mensuelle"}</Text>
                              </Space>
                            }
                            bordered={false}
                            extra={
                              <Tag color={dateFilter ? "blue" : "green"}>
                                {dateFilter ? (
                                  dateRangeType === "day"
                                    ? `${dayjs(dateFilter).format("DD/MM/YYYY")}`
                                    : dateRangeType === "week"
                                      ? `Semaine du ${dayjs(dateFilter).startOf("week").format("DD/MM/YYYY")}`
                                      : `${dayjs(dateFilter).format("MMMM YYYY")}`
                                ) : "Toutes les données"}
                              </Tag>
                            }
                          >
                            <Row gutter={[24, 24]}>
                              <Col xs={24} md={12}>
                                <Card title="Quantité Bonne" type="inner">
                                  {mergedData && mergedData.length > 0 ? (
                                    <ResponsiveContainer width="100%" height={300}>
                                      <BarChart data={mergedData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
                                        <XAxis
                                          dataKey="date"
                                          tick={{ fill: "#666" }}
                                          tickFormatter={(date) => {
                                            // Safely format the date, handling invalid dates
                                            try {
                                              if (date && dayjs(date).isValid()) {
                                                return dayjs(date).format("DD/MM");
                                              }
                                              return "N/A";
                                            } catch (e) {
                                              console.error("Error formatting date:", date, e);
                                              return "N/A";
                                            }
                                          }}
                                        />
                                        <YAxis
                                          tickFormatter={(value) => value.toLocaleString()}
                                          label={{
                                            value: "Quantité",
                                            angle: -90,
                                            position: "insideLeft",
                                            style: { fill: "#666" },
                                          }}
                                        />
                                        <RechartsTooltip
                                          contentStyle={{
                                            backgroundColor: "#fff",
                                            border: "1px solid #f0f0f0",
                                            borderRadius: 8,
                                            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                          }}
                                          formatter={(value) => {
                                            // Ensure value is a number and properly formatted
                                            const numValue = parseFloat(value);
                                            return [!isNaN(numValue) ? numValue.toLocaleString() : "N/A", "Quantité bonne"];
                                          }}
                                          labelFormatter={(label) => {
                                            try {
                                              if (label && dayjs(label).isValid()) {
                                                return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
                                              }
                                              return "Date: N/A";
                                            } catch (e) {
                                              console.error("Error formatting tooltip date:", label, e);
                                              return "Date: N/A";
                                            }
                                          }}
                                        />

                                        <Bar dataKey="good" name="Quantité bonne" fill={COLORS[2]} maxBarSize={40} />
                                      </BarChart>
                                    </ResponsiveContainer>
                                  ) : (
                                    <div style={{ height: 300, display: "flex", alignItems: "center", justifyContent: "center" }}>
                                      <Empty description="Aucune donnée disponible" />
                                    </div>
                                  )}
                                </Card>
                              </Col>
                              <Col xs={24} md={12}>
                                <Card title="Quantité Rejetée" type="inner">
                                  {mergedData && mergedData.length > 0 ? (
                                    <ResponsiveContainer width="100%" height={300}>
                                      <BarChart data={mergedData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
                                        <XAxis
                                          dataKey="date"
                                          tick={{ fill: "#666" }}
                                          tickFormatter={(date) => {
                                            // Safely format the date, handling invalid dates
                                            try {
                                              if (date && dayjs(date).isValid()) {
                                                return dayjs(date).format("DD/MM");
                                              }
                                              return "N/A";
                                            } catch (e) {
                                              console.error("Error formatting date:", date, e);
                                              return "N/A";
                                            }
                                          }}
                                        />
                                        <YAxis
                                          tickFormatter={(value) => value.toLocaleString()}
                                          label={{
                                            value: "Quantité (kg)",
                                            angle: -90,
                                            position: "insideLeft",
                                            style: { fill: "#666" },
                                          }}
                                        />
                                        <RechartsTooltip
                                          contentStyle={{
                                            backgroundColor: "#fff",
                                            border: "1px solid #f0f0f0",
                                            borderRadius: 8,
                                            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                          }}
                                          formatter={(value) => {
                                            // Ensure value is a number and properly formatted
                                            const numValue = parseFloat(value);
                                            return [!isNaN(numValue) ? numValue.toLocaleString() : "N/A", "Quantité rejetée (kg)"];
                                          }}
                                          labelFormatter={(label) => {
                                            try {
                                              if (label && dayjs(label).isValid()) {
                                                return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
                                              }
                                              return "Date: N/A";
                                            } catch (e) {
                                              console.error("Error formatting tooltip date:", label, e);
                                              return "Date: N/A";
                                            }
                                          }}
                                        />

                                        <Bar dataKey="reject" name="Quantité rejetée" fill={COLORS[4]} maxBarSize={40} />
                                      </BarChart>
                                    </ResponsiveContainer>
                                  ) : (
                                    <div style={{ height: 300, display: "flex", alignItems: "center", justifyContent: "center" }}>
                                      <Empty description="Aucune donnée disponible" />
                                    </div>
                                  )}
                                </Card>
                              </Col>
                            </Row>
                          </Card>
                        </Col>

                        <Col xs={24} md={24}>
                          <Card
                            title={
                              <Space>
                                <LineChartOutlined style={{ fontSize: 20, color: COLORS[0] }} />
                                <Text strong>Tendances TRS et Cycle De Temps</Text>
                              </Space>
                            }
                            bordered={false}
                            extra={<Tag color="cyan">Évolution</Tag>}
                          >
                            <Row gutter={[24, 24]}>
                              <Col xs={24} md={12}>
                                <Card title="TRS" type="inner">
                                  <ResponsiveContainer width="100%" height={300}>
                                    <LineChart data={mergedData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis
                                        dataKey="date"
                                        tick={{ fill: "#666" }}
                                        tickFormatter={(date) => {
                                          // Safely format the date, handling invalid dates
                                          try {
                                            if (date && dayjs(date).isValid()) {
                                              return dayjs(date).format("DD/MM");
                                            }
                                            return "N/A";
                                          } catch (e) {
                                            console.error("Error formatting date:", date, e);
                                            return "N/A";
                                          }
                                        }}
                                      />
                                      <YAxis tickFormatter={(value) => `${value}%`} domain={[0, 100]} />
                                      <RechartsTooltip
                                        contentStyle={{
                                          backgroundColor: "#fff",
                                          border: "1px solid #f0f0f0",
                                          borderRadius: 8,
                                          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                        }}
                                        formatter={(value) => {
                                          let parsed = parseFloat(value);
                                          const isValidNumber = !isNaN(parsed);

                                          // Handle decimal percentages (0-1 range)
                                          if (isValidNumber && parsed <= 1 && parsed > 0) {
                                            parsed = parsed * 100;
                                          }

                                          return [isValidNumber ? `${parsed.toFixed(2)}%` : `${value}%`, "TRS"]
                                        }}
                                        labelFormatter={(label) => {
                                          try {
                                            if (label && dayjs(label).isValid()) {
                                              return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
                                            }
                                            return "Date: N/A";
                                          } catch (e) {
                                            console.error("Error formatting tooltip date:", label, e);
                                            return "Date: N/A";
                                          }
                                        }}
                                      />

                                      <Line
                                        type="monotone"
                                        dataKey="oee"
                                        name="TRS"
                                        stroke={COLORS[0]}
                                        strokeWidth={2}
                                        dot={{ r: 4, fill: COLORS[0] }}
                                        activeDot={{ r: 6, fill: "#fff", stroke: COLORS[0], strokeWidth: 2 }}
                                      />
                                    </LineChart>
                                  </ResponsiveContainer>
                                </Card>
                              </Col>
                              <Col xs={24} md={12}>
                                <Card title="Cycle De Temps" type="inner">
                                  <ResponsiveContainer width="100%" height={300}>
                                    <LineChart data={mergedData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis
                                        dataKey="date"
                                        tick={{ fill: "#666" }}
                                        tickFormatter={(date) => {
                                          // Safely format the date, handling invalid dates
                                          try {
                                            if (date && dayjs(date).isValid()) {
                                              return dayjs(date).format("DD/MM");
                                            }
                                            return "N/A";
                                          } catch (e) {
                                            console.error("Error formatting date:", date, e);
                                            return "N/A";
                                          }
                                        }}
                                        type="category"
                                        allowDuplicatedCategory={false}
                                        interval="preserveStartEnd"
                                      />
                                      <YAxis
                                        domain={[0, 'dataMax + 1']}
                                        tickFormatter={(value) => `${value.toFixed(2)}`}
                                      />
                                      <RechartsTooltip
                                        contentStyle={{
                                          backgroundColor: "#fff",
                                          border: "1px solid #f0f0f0",
                                          borderRadius: 8,
                                          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                        }}
                                        formatter={(value) => {
                                          const parsed = parseFloat(value)
                                          const isValidNumber = !isNaN(parsed)
                                          return [
                                            isValidNumber ? parsed.toFixed(2) : value,
                                            "Cycle De Temps"
                                          ]
                                        }}
                                        labelFormatter={(label) => {
                                          try {
                                            if (label && dayjs(label).isValid()) {
                                              return `Date: ${dayjs(label).format("DD/MM/YYYY")}`;
                                            }
                                            return "Date: N/A";
                                          } catch (e) {
                                            console.error("Error formatting tooltip date:", label, e);
                                            return "Date: N/A";
                                          }
                                        }}
                                      />

                                      <Line
                                        type="monotone"
                                        dataKey="speed"
                                        name="Cycle De Temps"
                                        stroke={COLORS[1]}
                                        strokeWidth={2}
                                        dot={{ r: 4, fill: COLORS[1] }}
                                        activeDot={{ r: 6, fill: "#fff", stroke: COLORS[1], strokeWidth: 2 }}
                                        connectNulls={true}
                                      />
                                    </LineChart>
                                  </ResponsiveContainer>
                                </Card>
                              </Col>
                            </Row>
                          </Card>
                        </Col>

                        {/* Tendances horaires */}
                      </Row>
                    </TabPane>

                    <TabPane
                      tab={
                        <span>
                          <BarChartOutlined />
                          Performance
                        </span>
                      }
                      key="2"
                    >
                      <Row gutter={[24, 24]}>
                        {/* Performance des Machines */}
                        <Col xs={24} md={24}>
                          <Card
                            title={
                              <Space>
                                <BarChartOutlined style={{ fontSize: 20, color: COLORS[1] }} />
                                <Text strong>Performance des Machines</Text>
                              </Space>
                            }
                            bordered={false}
                            extra={<Badge count={machinePerformance.length} style={{ backgroundColor: COLORS[1] }} />}
                          >
                            <Row gutter={[24, 24]}>
                              <Col xs={24} md={12}>
                                <Card title="Production par Machine" bordered={false} type="inner">
                                  <ResponsiveContainer width="100%" height={300}>
                                    <BarChart
                                      data={Object.values(
                                        machinePerformance.reduce((acc, item) => {
                                          const machineName = item.Machine_Name
                                          if (!acc[machineName]) {
                                            acc[machineName] = {
                                              Machine_Name: machineName,
                                              production: 0,
                                            }
                                          }
                                          acc[machineName].production += Number(item.production) || 0
                                          return acc
                                        }, {}),
                                      )}
                                      margin={{ top: 16, right: 24, left: 24, bottom: 16 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis
                                        dataKey="Machine_Name"
                                        tick={{ fill: "#666" }}
                                        interval={0}
                                        angle={-45}
                                        textAnchor="end"
                                        height={80}
                                      />
                                      <YAxis
                                        tickFormatter={(value) => value.toLocaleString()}
                                        label={{
                                          value: "Quantité",
                                          angle: -90,
                                          position: "insideLeft",
                                          style: { fill: "#666" },
                                        }}
                                      />
                                      <RechartsTooltip
                                        contentStyle={{
                                          backgroundColor: "#fff",
                                          border: "1px solid #f0f0f0",
                                          borderRadius: 8,
                                          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                        }}
                                        formatter={(value) => [value.toLocaleString(), "Production"]}
                                      />

                                      <Bar dataKey="production" name="Production" fill={COLORS[0]} />
                                    </BarChart>
                                  </ResponsiveContainer>
                                </Card>
                              </Col>
                              <Col xs={24} md={12}>
                                <Card title="Rejets par Machine" bordered={false} type="inner">
                                  <ResponsiveContainer width="100%" height={300}>
                                    <BarChart
                                      data={Object.values(
                                        machinePerformance.reduce((acc, item) => {
                                          const machineName = item.Machine_Name
                                          if (!acc[machineName]) {
                                            acc[machineName] = {
                                              Machine_Name: machineName,
                                              rejects: 0,
                                            }
                                          }
                                          acc[machineName].rejects += Number(item.rejects) || 0
                                          return acc
                                        }, {}),
                                      )}
                                      margin={{ top: 16, right: 24, left: 24, bottom: 16 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis
                                        dataKey="Machine_Name"
                                        tick={{ fill: "#666" }}
                                        interval={0}
                                        angle={-45}
                                        textAnchor="end"
                                        height={80}
                                      />
                                      <YAxis
                                        tickFormatter={(value) => value.toLocaleString()}
                                        label={{
                                          value: "Quantité (kg)",
                                          angle: -90,
                                          position: "insideLeft",
                                          style: { fill: "#666" },
                                        }}
                                      />
                                      <RechartsTooltip
                                        contentStyle={{
                                          backgroundColor: "#fff",
                                          border: "1px solid #f0f0f0",
                                          borderRadius: 8,
                                          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                        }}
                                        formatter={(value) => [value.toLocaleString(), "Rejets (kg)"]}
                                      />

                                      <Bar dataKey="rejects" name="Rejets" fill={COLORS[4]} />
                                    </BarChart>
                                  </ResponsiveContainer>
                                </Card>
                              </Col>
                            </Row>
                          </Card>
                        </Col>

                        {/* TRS par Machine */}
                        <Col xs={24} md={12}>
                          <Card
                            title={
                              <Space>
                                <BarChartOutlined style={{ fontSize: 20, color: COLORS[5] }} />
                                <Text strong>TRS par Machine</Text>
                              </Space>
                            }
                            bordered={false}
                            extra={<Tag color="purple">Performance</Tag>}
                          >
                            {renderChart(machinePerformance, MemoizedBulletChart, {
                              dataKey: "oee",
                              nameKey: "Machine_Name",
                              color: COLORS[5],
                            })}
                          </Card>
                        </Col>

                        {/* Comparaison des équipes */}
                        <Col xs={24} md={12}>
                          <Card
                            title={
                              <Space>
                                <BarChartOutlined style={{ fontSize: 20, color: COLORS[3] }} />
                                <Text strong>Répartition Production</Text>
                              </Space>
                            }
                            bordered={false}
                            extra={<Tag color="red">Qualité</Tag>}
                          >
                            {renderChart(
                              [
                                { name: "Bonnes Pièces", value: Number(goodQty) || 0 },
                                { name: "Rejets", value: Number(rejetQty) || 0 },
                              ].filter((item) => item.value > 0),
                              MemoizedPieChart,
                              {
                                colors: [COLORS[2], COLORS[4]],
                              },
                            )}
                          </Card>
                        </Col>

                        {/* Comparaison des équipes */}
                        <Col xs={24} md={24}>
                          <Card
                            title={
                              <Space>
                                <BarChartOutlined style={{ fontSize: 20, color: COLORS[3] }} />
                                <Text strong>Comparaison des Équipes</Text>
                              </Space>
                            }
                            bordered={false}
                            extra={<Tag color="orange">Par équipe</Tag>}
                          >
                            <Row gutter={[24, 24]}>
                              <Col xs={24} md={12}>
                                <Card title="Production par Équipe" bordered={false} type="inner">
                                  <ResponsiveContainer width="100%" height={300}>
                                    <BarChart
                                      data={shiftComparison}
                                      margin={{ top: 16, right: 24, left: 24, bottom: 16 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="Shift" tick={{ fill: "#666" }} />
                                      <YAxis
                                        tickFormatter={(value) => value.toLocaleString()}
                                        label={{
                                          value: "Production",
                                          angle: -90,
                                          position: "insideLeft",
                                          style: { fill: "#666" },
                                        }}
                                      />
                                      <RechartsTooltip
                                        contentStyle={{
                                          backgroundColor: "#fff",
                                          border: "1px solid #f0f0f0",
                                          borderRadius: 8,
                                          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                        }}
                                        formatter={(value) => [value.toLocaleString(), "Production"]}
                                        labelFormatter={(label) => `Équipe: ${label}`}
                                      />

                                      <Bar dataKey="production" name="Production" fill={COLORS[2]} maxBarSize={40} />
                                    </BarChart>
                                  </ResponsiveContainer>
                                </Card>
                              </Col>
                              <Col xs={24} md={12}>
                                <Card title="Temps d'arrêt par Équipe" bordered={false} type="inner">
                                  <ResponsiveContainer width="100%" height={300}>
                                    <BarChart
                                      data={shiftComparison}
                                      margin={{ top: 16, right: 24, left: 24, bottom: 16 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="Shift" tick={{ fill: "#666" }} />
                                      <YAxis
                                        tickFormatter={(value) => value.toLocaleString()}
                                        label={{
                                          value: "Temps d'arrêt",
                                          angle: -90,
                                          position: "insideLeft",
                                          style: { fill: "#666" },
                                        }}
                                      />
                                      <RechartsTooltip
                                        contentStyle={{
                                          backgroundColor: "#fff",
                                          border: "1px solid #f0f0f0",
                                          borderRadius: 8,
                                          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                        }}
                                        formatter={(value) => [value.toLocaleString(), "Temps d'arrêt"]}
                                        labelFormatter={(label) => `Équipe: ${label}`}
                                      />

                                      <Bar dataKey="downtime" name="Temps d'arrêt" fill={COLORS[4]} maxBarSize={40} />
                                    </BarChart>
                                  </ResponsiveContainer>
                                </Card>
                              </Col>
                              <Col xs={24} md={12}>
                                <Card title="TRS par Équipe" bordered={false} type="inner">
                                  <ResponsiveContainer width="100%" height={300}>
                                    <LineChart
                                      data={shiftComparison}
                                      margin={{ top: 16, right: 24, left: 24, bottom: 16 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="Shift" tick={{ fill: "#666" }} />
                                      <YAxis
                                        tickFormatter={(value) => `${value}%`}
                                        domain={[0, 100]}
                                        label={{
                                          value: "TRS",
                                          angle: -90,
                                          position: "insideLeft",
                                          style: { fill: "#666" },
                                        }}
                                      />
                                      <RechartsTooltip
                                        contentStyle={{
                                          backgroundColor: "#fff",
                                          border: "1px solid #f0f0f0",
                                          borderRadius: 8,
                                          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                        }}
                                        formatter={(value) => {
                                          let parsed = parseFloat(value);
                                          const isValidNumber = !isNaN(parsed);

                                          // Handle decimal percentages (0-1 range)
                                          if (isValidNumber && parsed <= 1 && parsed > 0) {
                                            parsed = parsed * 100;
                                          }

                                          return [isValidNumber ? `${parsed.toFixed(2)}%` : `${value}%`, "TRS"]
                                        }}
                                        labelFormatter={(label) => `Équipe: ${label}`}
                                      />

                                      <Line
                                        type="monotone"
                                        dataKey="oee"
                                        name="TRS"
                                        stroke={COLORS[0]}
                                        strokeWidth={2}
                                        dot={{ r: 4, fill: COLORS[0] }}
                                      />
                                    </LineChart>
                                  </ResponsiveContainer>
                                </Card>
                              </Col>
                              <Col xs={24} md={12}>
                                <Card title="Performance par Équipe" bordered={false} type="inner">
                                  <ResponsiveContainer width="100%" height={300}>
                                    <LineChart
                                      data={shiftComparison}
                                      margin={{ top: 16, right: 24, left: 24, bottom: 16 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="Shift" tick={{ fill: "#666" }} />
                                      <YAxis
                                        tickFormatter={(value) => `${value}%`}
                                        domain={[0, 100]}
                                        label={{
                                          value: "Performance",
                                          angle: -90,
                                          position: "insideLeft",
                                          style: { fill: "#666" },
                                        }}
                                      />
                                      <RechartsTooltip
                                        contentStyle={{
                                          backgroundColor: "#fff",
                                          border: "1px solid #f0f0f0",
                                          borderRadius: 8,
                                          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                                        }}
                                        formatter={(value) => {
                                          let parsed = parseFloat(value);
                                          const isValidNumber = !isNaN(parsed);

                                          // Handle decimal percentages (0-1 range)
                                          if (isValidNumber && parsed <= 1 && parsed > 0) {
                                            parsed = parsed * 100;
                                          }

                                          return [isValidNumber ? `${parsed.toFixed(2)}%` : `${value}%`, "Performance"]
                                        }}

                                        labelFormatter={(label) => `Équipe: ${label}`}
                                      />

                                      <Line
                                        type="monotone"
                                        dataKey="performance"
                                        name="Performance"
                                        stroke={COLORS[5]}
                                        strokeWidth={2}
                                        dot={{ r: 4, fill: COLORS[5] }}
                                      />
                                    </LineChart>
                                  </ResponsiveContainer>
                                </Card>
                              </Col>
                            </Row>
                          </Card>
                        </Col>
                      </Row>
                    </TabPane>

                    <TabPane
                      tab={
                        <span>
                          <TableOutlined />
                          Détails
                        </span>
                      }
                      key="3"
                    >
                      <Row gutter={[24, 24]}>
                        {/* Tableau de performance des machines */}
                        <Col span={24}>
                          <Card
                            title={
                              <Space>
                                <ToolOutlined style={{ fontSize: 20, color: COLORS[1] }} />
                                <Text strong>Performance Totale par Quart</Text>
                              </Space>
                            }
                            bordered={false}
                            extra={
                              <Space>
                                <Badge count={machinePerformance.length} style={{ backgroundColor: COLORS[1] }} />
                                <Button type="link" icon={<DownloadOutlined />} disabled>
                                  Exporter
                                </Button>
                              </Space>
                            }
                          >
                            <Table
                              dataSource={machinePerformance}
                              columns={machinePerformanceColumns}
                              pagination={{
                                pageSize: 5,
                                showSizeChanger: true,
                                pageSizeOptions: ["5", "10", "20"],
                                showTotal: (total) => `Total ${total} machines`,
                              }}
                              scroll={{ x: 1300 }}
                              rowKey="Machine_Name"
                            />
                          </Card>
                        </Col>

                        {/* Tableau détaillé des données de production */}
                        <Col span={24}>
                          <Card
                            title={
                              <Space>
                                <TableOutlined style={{ fontSize: 20, color: COLORS[0] }} />
                                <Text strong>Données Détaillées de Production</Text>
                              </Space>
                            }
                            bordered={false}
                            extra={
                              <Space>
                                <Badge count={mergedData.length} style={{ backgroundColor: COLORS[0] }} />
                                <Button type="link" icon={<DownloadOutlined />} disabled>
                                  Exporter
                                </Button>
                              </Space>
                            }
                          >
                            <Table
                              dataSource={mergedData.map(item => ({
                                ...item,
                                // Ensure all required fields have default values based on machine_daily_table_mould structure
                                date: (() => {
                                  // Ensure date is in a valid format
                                  try {
                                    const rawDate = item.Date_Insert_Day || item.date;
                                    if (rawDate && dayjs(rawDate).isValid()) {
                                      return dayjs(rawDate).format('YYYY-MM-DD');
                                    }
                                    // If date is invalid, use today's date
                                    console.warn(`Invalid date found in table data: ${rawDate}, using today's date`);
                                    return dayjs().format('YYYY-MM-DD');
                                  } catch (e) {
                                    console.error("Error parsing date for table:", e);
                                    return dayjs().format('YYYY-MM-DD');
                                  }
                                })(),
                                Machine_Name: item.Machine_Name || 'N/A',
                                Shift: item.Shift || 'N/A',
                                // Map to the correct database fields
                                good: typeof item.good === 'number' ? item.good :
                                      typeof item.Good_QTY_Day === 'number' ? item.Good_QTY_Day :
                                      parseFloat(item.Good_QTY_Day) || 0,
                                reject: typeof item.reject === 'number' ? item.reject :
                                        typeof item.Rejects_QTY_Day === 'number' ? item.Rejects_QTY_Day :
                                        parseFloat(item.Rejects_QTY_Day) || 0,
                                oee: (() => {
                                  // Handle OEE value which might be a number, string, or null
                                  let oeeValue;

                                  if (typeof item.oee === 'number' && !isNaN(item.oee)) {
                                    oeeValue = item.oee;
                                  } else if (typeof item.OEE_Day === 'number' && !isNaN(item.OEE_Day)) {
                                    oeeValue = item.OEE_Day;
                                  } else if (item.OEE_Day) {
                                    // Try to parse the string value, handling both '.' and ',' as decimal separators
                                    const cleanValue = String(item.OEE_Day).replace(',', '.');
                                    oeeValue = parseFloat(cleanValue);
                                  } else {
                                    oeeValue = 0;
                                  }

                                  // Convert to percentage if in 0-1 range
                                  return !isNaN(oeeValue) && oeeValue > 0 && oeeValue <= 1 ? oeeValue * 100 : oeeValue;
                                })(),
                                speed: typeof item.speed === 'number' ? item.speed :
                                       typeof item.Speed_Day === 'number' ? item.Speed_Day :
                                       parseFloat(item.Speed_Day) || null,
                                // Map Part_Number to mould_number
                                mould_number: item.Part_Number || item.mould_number || 'N/A',
                                // Additional fields specific to machine_daily_table_mould
                                poid_unitaire: item.Poid_Unitaire || item.poid_unitaire || 'N/A',
                                cycle_theorique: item.Cycle_Theorique || item.cycle_theorique || 'N/A',
                                poid_purge: item.Poid_Purge || item.poid_purge || 'N/A',
                                // Map availability, performance, quality rates
                                availability: (() => {
                                  // Handle availability value which might be a number, string, or null
                                  let availValue;

                                  if (typeof item.availability === 'number' && !isNaN(item.availability)) {
                                    availValue = item.availability;
                                  } else if (typeof item.Availability_Rate_Day === 'number' && !isNaN(item.Availability_Rate_Day)) {
                                    availValue = item.Availability_Rate_Day;
                                  } else if (item.Availability_Rate_Day) {
                                    // Try to parse the string value, handling both '.' and ',' as decimal separators
                                    const cleanValue = String(item.Availability_Rate_Day).replace(',', '.');
                                    availValue = parseFloat(cleanValue);
                                  } else {
                                    availValue = 0;
                                  }

                                  // Convert to percentage if in 0-1 range
                                  return !isNaN(availValue) && availValue > 0 && availValue <= 1 ? availValue * 100 : availValue;
                                })(),
                                performance: (() => {
                                  // Handle performance value which might be a number, string, or null
                                  let perfValue;

                                  if (typeof item.performance === 'number' && !isNaN(item.performance)) {
                                    perfValue = item.performance;
                                  } else if (typeof item.Performance_Rate_Day === 'number' && !isNaN(item.Performance_Rate_Day)) {
                                    perfValue = item.Performance_Rate_Day;
                                  } else if (item.Performance_Rate_Day) {
                                    // Try to parse the string value, handling both '.' and ',' as decimal separators
                                    const cleanValue = String(item.Performance_Rate_Day).replace(',', '.');
                                    perfValue = parseFloat(cleanValue);
                                  } else {
                                    perfValue = 0;
                                  }

                                  // Convert to percentage if in 0-1 range
                                  return !isNaN(perfValue) && perfValue > 0 && perfValue <= 1 ? perfValue * 100 : perfValue;
                                })(),
                                quality: (() => {
                                  // Handle quality value which might be a number, string, or null
                                  let qualValue;

                                  if (typeof item.quality === 'number' && !isNaN(item.quality)) {
                                    qualValue = item.quality;
                                  } else if (typeof item.Quality_Rate_Day === 'number' && !isNaN(item.Quality_Rate_Day)) {
                                    qualValue = item.Quality_Rate_Day;
                                  } else if (item.Quality_Rate_Day) {
                                    // Try to parse the string value, handling both '.' and ',' as decimal separators
                                    const cleanValue = String(item.Quality_Rate_Day).replace(',', '.');
                                    qualValue = parseFloat(cleanValue);
                                  } else {
                                    qualValue = 0;
                                  }

                                  // Convert to percentage if in 0-1 range
                                  return !isNaN(qualValue) && qualValue > 0 && qualValue <= 1 ? qualValue * 100 : qualValue;
                                })(),
                                // Map run hours and down hours
                                run_hours: typeof item.run_hours === 'number' ? item.run_hours :
                                          typeof item.Run_Hours_Day === 'number' ? item.Run_Hours_Day :
                                          parseFloat(item.Run_Hours_Day) || 0,
                                down_hours: typeof item.down_hours === 'number' ? item.down_hours :
                                           typeof item.Down_Hours_Day === 'number' ? item.Down_Hours_Day :
                                           parseFloat(item.Down_Hours_Day) || 0
                              }))}
                              rowKey={(record) => `${record.date}-${record.Machine_Name || 'unknown'}-${record.mould_number || 'unknown'}`}
                              scroll={{ x: 2200 }}
                              pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                pageSizeOptions: ["10", "20", "50"],
                                showTotal: (total) => `Total ${total} enregistrements`,
                              }}
                              expandable={{
                                expandedRowRender: record => (
                                  <Card size="small" title="Informations du moule">
                                    <Row gutter={[16, 16]}>
                                      <Col span={8}>
                                        <Statistic
                                          title="Numéro de moule"
                                          value={record.mould_number || 'N/A'}
                                          valueStyle={{ fontSize: 16 }}
                                        />
                                      </Col>
                                      <Col span={8}>
                                        <Statistic
                                          title="Poids unitaire"
                                          value={record.poid_unitaire || 'N/A'}
                                          valueStyle={{ fontSize: 16 }}
                                          suffix="g"
                                        />
                                      </Col>
                                      <Col span={8}>
                                        <Statistic
                                          title="Cycle théorique"
                                          value={record.cycle_theorique || 'N/A'}
                                          valueStyle={{ fontSize: 16 }}
                                          suffix="s"
                                        />
                                      </Col>
                                      <Col span={8}>
                                        <Statistic
                                          title="Poids purge"
                                          value={record.poid_purge || 'N/A'}
                                          valueStyle={{ fontSize: 16 }}
                                          suffix="g"
                                        />
                                      </Col>
                                      <Col span={8}>
                                        <Statistic
                                          title="TRS (OEE)"
                                          value={(() => {
                                            if (record.oee === undefined || record.oee === null) return 'N/A';
                                            // Handle decimal percentages (0-1 range)
                                            let value = parseFloat(record.oee);
                                            if (isNaN(value)) return 'N/A';
                                            // Convert to percentage if in 0-1 range
                                            value = value <= 1 && value > 0 ? value * 100 : value;
                                            return value.toFixed(1);
                                          })()}
                                          valueStyle={{ fontSize: 16, color: (() => {
                                            if (record.oee === undefined || record.oee === null) return '';
                                            let value = parseFloat(record.oee);
                                            if (isNaN(value)) return '';
                                            value = value <= 1 && value > 0 ? value * 100 : value;
                                            return value > 85 ? '#52c41a' : value > 70 ? '#1890ff' : '#f5222d';
                                          })() }}
                                          suffix="%"
                                        />
                                      </Col>
                                      <Col span={8}>
                                        <Statistic
                                          title="Disponibilité"
                                          value={(() => {
                                            if (record.availability === undefined || record.availability === null) return 'N/A';
                                            // Handle decimal percentages (0-1 range)
                                            let value = parseFloat(record.availability);
                                            if (isNaN(value)) return 'N/A';
                                            // Convert to percentage if in 0-1 range
                                            value = value <= 1 && value > 0 ? value * 100 : value;
                                            return value.toFixed(1);
                                          })()}
                                          valueStyle={{ fontSize: 16, color: (() => {
                                            if (record.availability === undefined || record.availability === null) return '';
                                            let value = parseFloat(record.availability);
                                            if (isNaN(value)) return '';
                                            value = value <= 1 && value > 0 ? value * 100 : value;
                                            return value > 85 ? '#52c41a' : value > 70 ? '#1890ff' : '#f5222d';
                                          })() }}
                                          suffix="%"
                                        />
                                      </Col>
                                      <Col span={8}>
                                        <Statistic
                                          title="Performance"
                                          value={(() => {
                                            if (record.performance === undefined || record.performance === null) return 'N/A';
                                            // Handle decimal percentages (0-1 range)
                                            let value = parseFloat(record.performance);
                                            if (isNaN(value)) return 'N/A';
                                            // Convert to percentage if in 0-1 range
                                            value = value <= 1 && value > 0 ? value * 100 : value;
                                            return value.toFixed(1);
                                          })()}
                                          valueStyle={{ fontSize: 16, color: (() => {
                                            if (record.performance === undefined || record.performance === null) return '';
                                            let value = parseFloat(record.performance);
                                            if (isNaN(value)) return '';
                                            value = value <= 1 && value > 0 ? value * 100 : value;
                                            return value > 85 ? '#52c41a' : value > 70 ? '#1890ff' : '#f5222d';
                                          })() }}
                                          suffix="%"
                                        />
                                      </Col>
                                      <Col span={8}>
                                        <Statistic
                                          title="Taux de qualité"
                                          value={(() => {
                                            if (record.quality === undefined || record.quality === null) return 'N/A';
                                            // Handle decimal percentages (0-1 range)
                                            let value = parseFloat(record.quality);
                                            if (isNaN(value)) return 'N/A';
                                            // Convert to percentage if in 0-1 range
                                            value = value <= 1 && value > 0 ? value * 100 : value;
                                            return value.toFixed(1);
                                          })()}
                                          valueStyle={{ fontSize: 16, color: (() => {
                                            if (record.quality === undefined || record.quality === null) return '';
                                            let value = parseFloat(record.quality);
                                            if (isNaN(value)) return '';
                                            value = value <= 1 && value > 0 ? value * 100 : value;
                                            return value > 90 ? '#52c41a' : value > 80 ? '#1890ff' : '#f5222d';
                                          })() }}
                                          suffix="%"
                                        />
                                      </Col>
                                      <Col span={8}>
                                        <Statistic
                                          title="Vitesse"
                                          value={record.speed ? record.speed.toFixed(1) : 'N/A'}
                                          valueStyle={{ fontSize: 16 }}
                                          suffix="u/h"
                                        />
                                      </Col>
                                    </Row>
                                  </Card>
                                ),
                                expandRowByClick: true,
                                rowExpandable: record => record.mould_number && record.mould_number !== 'N/A',
                              }}
                              columns={[
                                {
                                  title: "Date",
                                  dataIndex: "date",
                                  key: "date",
                                  fixed: "left",
                                  width: 120,
                                  render: (date) => {
                                    // Ensure date is valid before formatting
                                    const validDate = date ? dayjs(date).isValid() : false;
                                    return (
                                      <Space>
                                        <CalendarOutlined style={{ color: COLORS[0] }} />
                                        <Text>{validDate ? dayjs(date).format("DD/MM/YYYY") : "N/A"}</Text>
                                      </Space>
                                    );
                                  },
                                  sorter: (a, b) => {
                                    // Handle invalid dates in sorting
                                    const dateA = a.date ? dayjs(a.date) : null;
                                    const dateB = b.date ? dayjs(b.date) : null;

                                    if (!dateA && !dateB) return 0;
                                    if (!dateA) return 1;
                                    if (!dateB) return -1;

                                    return dateA.unix() - dateB.unix();
                                  },
                                  defaultSortOrder: "descend",
                                },
                                {
                                  title: "Machine",
                                  dataIndex: "Machine_Name",
                                  key: "machine",
                                  width: 150,
                                  render: (text) => (
                                    <Space>
                                      <ToolOutlined style={{ color: COLORS[1] }} />
                                      <Text>{text || "N/A"}</Text>
                                    </Space>
                                  ),
                                  filters: Array.from(new Set(mergedData.map(item => item.Machine_Name || "N/A")))
                                    .map(machine => ({ text: machine, value: machine })),
                                  onFilter: (value, record) => record.Machine_Name === value ||
                                    (value === "N/A" && !record.Machine_Name),
                                },
                                {
                                  title: "Moule",
                                  dataIndex: "mould_number",
                                  key: "mould_number",
                                  width: 120,
                                  render: (text) => <Tag color="purple">{text || "N/A"}</Tag>,
                                  filters: Array.from(new Set(mergedData.map(item => item.mould_number || item.Mould_Number || "N/A")))
                                    .map(mould => ({ text: mould, value: mould })),
                                  onFilter: (value, record) => record.mould_number === value ||
                                    (value === "N/A" && !record.mould_number),
                                },
                                {
                                  title: "Part Number",
                                  dataIndex: "Part_Number",
                                  key: "part_number",
                                  width: 100,
                                  render: (text) => <Tag color="cyan">{text || "N/A"}</Tag>,
                                },
                                {
                                  title: "Équipe",
                                  dataIndex: "Shift",
                                  key: "shift",
                                  width: 120,
                                  render: (text) => <Tag color="blue">{text || "N/A"}</Tag>,
                                  filters: [
                                    { text: "Matin", value: "Matin" },
                                    { text: "Après-midi", value: "Après-midi" },
                                    { text: "Nuit", value: "Nuit" },
                                  ],
                                  onFilter: (value, record) => record.Shift === value,
                                },
                                {
                                  title: "Production",
                                  dataIndex: "good",
                                  key: "good",
                                  width: 120,
                                  render: (text) => {
                                    // Ensure value is a valid number before formatting
                                    const numValue = typeof text === "number" && !isNaN(text) ? text : 0;
                                    return <Tag color="green">{numValue.toLocaleString()} pcs</Tag>;
                                  },
                                  sorter: (a, b) => {
                                    const goodA = typeof a.good === "number" && !isNaN(a.good) ? a.good : 0;
                                    const goodB = typeof b.good === "number" && !isNaN(b.good) ? b.good : 0;
                                    return goodA - goodB;
                                  },
                                },
                                {
                                  title: "Rejets",
                                  dataIndex: "reject",
                                  key: "reject",
                                  width: 120,
                                  render: (text) => {
                                    // Ensure value is a valid number before formatting
                                    const numValue = typeof text === "number" && !isNaN(text) ? text : 0;
                                    return <Tag color="red">{numValue.toLocaleString()} kg</Tag>;
                                  },
                                  sorter: (a, b) => {
                                    const rejectA = typeof a.reject === "number" && !isNaN(a.reject) ? a.reject : 0;
                                    const rejectB = typeof b.reject === "number" && !isNaN(b.reject) ? b.reject : 0;
                                    return rejectA - rejectB;
                                  },
                                },
                                {
                                  title: "Cycle Théorique",
                                  dataIndex: "cycle_theorique",
                                  key: "cycle_theorique",
                                  width: 120,
                                  render: (value) => (
                                    <Text>
                                      {value || "N/A"}
                                    </Text>
                                  ),
                                },
                                {
                                  title: "Poids Unitaire",
                                  dataIndex: "poid_unitaire",
                                  key: "poid_unitaire",
                                  width: 120,
                                  render: (value) => (
                                    <Text>
                                      {value || "N/A"}
                                    </Text>
                                  ),
                                },
                                {
                                  title: "Poids Purge",
                                  dataIndex: "poid_purge",
                                  key: "poid_purge",
                                  width: 120,
                                  render: (value) => (
                                    <Text>
                                      {value || "N/A"}
                                    </Text>
                                  ),
                                },
                                {
                                  title: "TRS",
                                  dataIndex: "oee",
                                  key: "oee",
                                  width: 150,
                                  render: (value) => {
                                    // Ensure value is a valid number and handle values between 0-1 or 0-100
                                    let numValue = 0;
                                    if (typeof value === "number" && !isNaN(value)) {
                                      // Check if value is between 0-1 (decimal percentage)
                                      numValue = value <= 1 && value > 0 ? value * 100 : value;
                                    } else if (typeof value === "string") {
                                      // Try to parse string value
                                      const parsed = parseFloat(value);
                                      if (!isNaN(parsed)) {
                                        numValue = parsed <= 1 && parsed > 0 ? parsed * 100 : parsed;
                                      }
                                    }

                                    // Ensure value is within valid percentage range (0-100)
                                    numValue = Math.max(0, Math.min(100, numValue));

                                    return (
                                      <Tooltip title={`${numValue.toFixed(1)}% de TRS`}>
                                        <Progress
                                          percent={numValue}
                                          size="small"
                                          status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
                                          format={(percent) => {
                                            // Check if percent is a number before using toFixed
                                            return typeof percent === "number" && !isNaN(percent)
                                              ? `${percent.toFixed(1)}%`
                                              : "0.0%"
                                          }}
                                        />
                                      </Tooltip>
                                    );
                                  },
                                  sorter: (a, b) => {
                                    // Get values and normalize them (handle 0-1 range)
                                    let oeeA = 0;
                                    if (typeof a.oee === "number" && !isNaN(a.oee)) {
                                      oeeA = a.oee <= 1 && a.oee > 0 ? a.oee * 100 : a.oee;
                                    }

                                    let oeeB = 0;
                                    if (typeof b.oee === "number" && !isNaN(b.oee)) {
                                      oeeB = b.oee <= 1 && b.oee > 0 ? b.oee * 100 : b.oee;
                                    }

                                    return oeeA - oeeB;
                                  },
                                },
                                {
                                  title: "Disponibilité",
                                  dataIndex: "availability",
                                  key: "availability",
                                  width: 150,
                                  render: (value) => {
                                    // Ensure value is a valid number and handle values between 0-1 or 0-100
                                    let numValue = 0;
                                    if (typeof value === "number" && !isNaN(value)) {
                                      // Check if value is between 0-1 (decimal percentage)
                                      numValue = value <= 1 && value > 0 ? value * 100 : value;
                                    } else if (typeof value === "string") {
                                      // Try to parse string value
                                      const parsed = parseFloat(value);
                                      if (!isNaN(parsed)) {
                                        numValue = parsed <= 1 && parsed > 0 ? parsed * 100 : parsed;
                                      }
                                    }

                                    // Ensure value is within valid percentage range (0-100)
                                    numValue = Math.max(0, Math.min(100, numValue));

                                    return (
                                      <Tooltip title={`${numValue.toFixed(1)}% de disponibilité`}>
                                        <Progress
                                          percent={numValue}
                                          size="small"
                                          status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
                                          format={(percent) => {
                                            return typeof percent === "number" && !isNaN(percent)
                                              ? `${percent.toFixed(1)}%`
                                              : "0.0%"
                                          }}
                                        />
                                      </Tooltip>
                                    );
                                  },
                                  sorter: (a, b) => {
                                    // Get values and normalize them (handle 0-1 range)
                                    let availA = 0;
                                    if (typeof a.availability === "number" && !isNaN(a.availability)) {
                                      availA = a.availability <= 1 && a.availability > 0 ? a.availability * 100 : a.availability;
                                    }

                                    let availB = 0;
                                    if (typeof b.availability === "number" && !isNaN(b.availability)) {
                                      availB = b.availability <= 1 && b.availability > 0 ? b.availability * 100 : b.availability;
                                    }

                                    return availA - availB;
                                  },
                                },
                                {
                                  title: "Performance",
                                  dataIndex: "performance",
                                  key: "performance",
                                  width: 150,
                                  render: (value) => {
                                    // Ensure value is a valid number and handle values between 0-1 or 0-100
                                    let numValue = 0;
                                    if (typeof value === "number" && !isNaN(value)) {
                                      // Check if value is between 0-1 (decimal percentage)
                                      numValue = value <= 1 && value > 0 ? value * 100 : value;
                                    } else if (typeof value === "string") {
                                      // Try to parse string value
                                      const parsed = parseFloat(value);
                                      if (!isNaN(parsed)) {
                                        numValue = parsed <= 1 && parsed > 0 ? parsed * 100 : parsed;
                                      }
                                    }

                                    // Ensure value is within valid percentage range (0-100)
                                    numValue = Math.max(0, Math.min(100, numValue));

                                    return (
                                      <Tooltip title={`${numValue.toFixed(1)}% de performance`}>
                                        <Progress
                                          percent={numValue}
                                          size="small"
                                          status={numValue > 85 ? "success" : numValue > 70 ? "normal" : "exception"}
                                          format={(percent) => {
                                            return typeof percent === "number" && !isNaN(percent)
                                              ? `${percent.toFixed(1)}%`
                                              : "0.0%"
                                          }}
                                        />
                                      </Tooltip>
                                    );
                                  },
                                  sorter: (a, b) => {
                                    // Get values and normalize them (handle 0-1 range)
                                    let perfA = 0;
                                    if (typeof a.performance === "number" && !isNaN(a.performance)) {
                                      perfA = a.performance <= 1 && a.performance > 0 ? a.performance * 100 : a.performance;
                                    }

                                    let perfB = 0;
                                    if (typeof b.performance === "number" && !isNaN(b.performance)) {
                                      perfB = b.performance <= 1 && b.performance > 0 ? b.performance * 100 : b.performance;
                                    }

                                    return perfA - perfB;
                                  },
                                },
                                {
                                  title: "Qualité",
                                  dataIndex: "quality",
                                  key: "quality",
                                  width: 150,
                                  render: (value) => {
                                    // Ensure value is a valid number and handle values between 0-1 or 0-100
                                    let numValue = 0;
                                    if (typeof value === "number" && !isNaN(value)) {
                                      // Check if value is between 0-1 (decimal percentage)
                                      numValue = value <= 1 && value > 0 ? value * 100 : value;
                                    } else if (typeof value === "string") {
                                      // Try to parse string value
                                      const parsed = parseFloat(value);
                                      if (!isNaN(parsed)) {
                                        numValue = parsed <= 1 && parsed > 0 ? parsed * 100 : parsed;
                                      }
                                    }

                                    // Ensure value is within valid percentage range (0-100)
                                    numValue = Math.max(0, Math.min(100, numValue));

                                    return (
                                      <Tooltip title={`${numValue.toFixed(1)}% de qualité`}>
                                        <Progress
                                          percent={numValue}
                                          size="small"
                                          status={numValue > 90 ? "success" : numValue > 80 ? "normal" : "exception"}
                                          format={(percent) => {
                                            return typeof percent === "number" && !isNaN(percent)
                                              ? `${percent.toFixed(1)}%`
                                              : "0.0%"
                                          }}
                                        />
                                      </Tooltip>
                                    );
                                  },
                                  sorter: (a, b) => {
                                    // Get values and normalize them (handle 0-1 range)
                                    let qualA = 0;
                                    if (typeof a.quality === "number" && !isNaN(a.quality)) {
                                      qualA = a.quality <= 1 && a.quality > 0 ? a.quality * 100 : a.quality;
                                    }

                                    let qualB = 0;
                                    if (typeof b.quality === "number" && !isNaN(b.quality)) {
                                      qualB = b.quality <= 1 && b.quality > 0 ? b.quality * 100 : b.quality;
                                    }

                                    return qualA - qualB;
                                  },
                                },

                                {
                                  title: "Heures de Fonctionnement",
                                  dataIndex: "run_hours",
                                  key: "run_hours",
                                  width: 150,
                                  render: (value) => {
                                    // Ensure value is a valid number
                                    const numValue = typeof value === "number" && !isNaN(value) ? value :
                                                    parseFloat(value) || 0;
                                    return <Text>{numValue.toFixed(2)}h</Text>;
                                  },
                                  sorter: (a, b) => {
                                    const runA = typeof a.run_hours === "number" && !isNaN(a.run_hours) ? a.run_hours :
                                              parseFloat(a.run_hours) || 0;
                                    const runB = typeof b.run_hours === "number" && !isNaN(b.run_hours) ? b.run_hours :
                                              parseFloat(b.run_hours) || 0;
                                    return runA - runB;
                                  },
                                },
                                {
                                  title: "Heures d'Arrêt",
                                  dataIndex: "down_hours",
                                  key: "down_hours",
                                  width: 150,
                                  render: (value) => {
                                    // Ensure value is a valid number
                                    const numValue = typeof value === "number" && !isNaN(value) ? value :
                                                    parseFloat(value) || 0;
                                    return <Text>{numValue.toFixed(2)}h</Text>;
                                  },
                                  sorter: (a, b) => {
                                    const downA = typeof a.down_hours === "number" && !isNaN(a.down_hours) ? a.down_hours :
                                               parseFloat(a.down_hours) || 0;
                                    const downB = typeof b.down_hours === "number" && !isNaN(b.down_hours) ? b.down_hours :
                                               parseFloat(b.down_hours) || 0;
                                    return downA - downB;
                                  },
                                },

                              ]}
                            />
                          </Card>
                        </Col>
                      </Row>
                    </TabPane>
                  </Tabs>
                </Card>
              </Col>
            </>
          ) : (
            <>


              {/* Message to select a machine model for more data */}
              <Col span={24}>
                <Card>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: "40px 0",
                    }}
                  >
                    <DashboardOutlined style={{ fontSize: 64, color: "#1890ff", marginBottom: 24 }} />
                    <Title level={3}>Veuillez sélectionner un modèle de machine</Title>
                    <Paragraph style={{ fontSize: 16, color: "#666", textAlign: "center", maxWidth: 600 }}>
                      Pour visualiser plus de données du tableau de bord, veuillez sélectionner un modèle de machine
                      (IPS ou CCM24). Les données détaillées seront affichées en fonction de votre sélection.
                    </Paragraph>
                  </div>
                </Card>
              </Col>
            </>
          )}
        </Row>
      </Spin>
    </div>
  )
}

export default Production