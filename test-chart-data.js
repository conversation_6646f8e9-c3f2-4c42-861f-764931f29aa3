// Test to verify <PERSON>rretLineChart can handle the transformed data
const testChartData = [
  { date: '2025-04-30', displayDate: '30/04', stops: 11, duration: 0 },
  { date: '2025-05-01', displayDate: '01/05', stops: 3, duration: 0 },
  { date: '2025-05-02', displayDate: '02/05', stops: 1, duration: 0 }
];

// Test the condition from ArretLineChart
const hasEvolutionData = Array.isArray(testChartData) && testChartData.length > 0 && testChartData[0].date;

console.log('Test data:', testChartData);
console.log('Has evolution data?', hasEvolutionData);
console.log('First item has date?', testChartData[0] && testChartData[0].date);
console.log('Array length:', testChartData.length);

if (hasEvolutionData) {
  console.log('✅ Chart will display data instead of "Aucune donnée d\'évolution disponible"');
} else {
  console.log('❌ Chart will show "Aucune donnée d\'évolution disponible"');
}
