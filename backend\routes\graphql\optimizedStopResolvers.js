/**
 * WORKING Optimized GraphQL resolvers for machine stop data
 * Single comprehensive query approach to minimize database calls and improve performance
 * This replaces multiple separate queries with one consolidated data fetch
 */

import { GraphQLObjectType, GraphQLString, GraphQLInt, GraphQLFloat, GraphQLList, GraphQLInputObjectType } from 'graphql';
import { executeQuery } from '../../utils/dbUtils.js';

// Helper function to handle machine filtering consistently
const addMachineFilters = (conditions, queryParams, filters) => {
  const { model, machine } = filters;

  if (model && !machine) {
    conditions.push(`Machine_Name LIKE ?`);
    queryParams.push(`${model}%`);
  } else if (machine) {
    conditions.push(`Machine_Name = ?`);
    queryParams.push(machine);
  }
  // Note: Removed default filter to allow viewing all data when no filter is specified
};

// Helper function to handle date range filtering
const addDateRangeFilter = (conditions, queryParams, filters) => {
  const { date, startDate, endDate, dateRangeType = "day" } = filters;

  // Robust date parsing that handles various formats in the database
  const parseDateColumn = `
    COALESCE(
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i'), '%Y-%m-%d')
    )
  `;

  // If startDate and endDate are provided, use them directly
  if (startDate && endDate) {
    conditions.push(`${parseDateColumn} BETWEEN ? AND ?`);
    queryParams.push(startDate, endDate);
    return;
  }

  // Fall back to original date logic
  if (date) {
    if (dateRangeType === "day") {
      conditions.push(`${parseDateColumn} = ?`);
      queryParams.push(date);
    } else if (dateRangeType === "week") {
      conditions.push(`YEARWEEK(${parseDateColumn}, 1) = YEARWEEK(?, 1)`);
      queryParams.push(date);
    } else if (dateRangeType === "month") {
      conditions.push(`DATE_FORMAT(${parseDateColumn}, '%Y-%m') = DATE_FORMAT(?, '%Y-%m')`);
      queryParams.push(date);
    
 
    } else if (dateRangeType === "week") {
      conditions.push(`
        ${parseDateColumn} >=
        DATE_SUB(?, INTERVAL WEEKDAY(?) DAY)
      `);
      queryParams.push(date, date);

      conditions.push(`
        ${parseDateColumn} <=
        DATE_ADD(DATE_SUB(?, INTERVAL WEEKDAY(?) DAY), INTERVAL 6 DAY)
      `);
      queryParams.push(date, date);
    } else if (dateRangeType === "month") {
      conditions.push(`
        YEAR(COALESCE(
          STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i'),
          STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i:%s'),
          STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i')
        )) = YEAR(?)
      `);
      queryParams.push(date);

      conditions.push(`
        MONTH(COALESCE(
          STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i:%s'),
          STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i'),
          STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i:%s'),
          STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i')
        )) = MONTH(?)
      `);
      queryParams.push(date);
    }
  }
};

// Define comprehensive data types
const MachineStopType = new GraphQLObjectType({
  name: 'OptimizedMachineStop',
  fields: {
    Machine_Name: { type: GraphQLString },
    Date_Insert: { type: GraphQLString },
    Part_NO: { type: GraphQLString },
    Code_Stop: { type: GraphQLString },
    Debut_Stop: { type: GraphQLString },
    Fin_Stop_Time: { type: GraphQLString },
    Regleur_Prenom: { type: GraphQLString },
    duration_minutes: { type: GraphQLInt },
    // Enhanced fields with database calculations
    stop_date: { type: GraphQLString },
    stop_hour: { type: GraphQLInt },
    // Derived fields for backward compatibility
    Cause: { 
      type: GraphQLString,
      resolve: (parent) => parent.Code_Stop || parent.Cause
    },
    Raison_Arret: { 
      type: GraphQLString,
      resolve: (parent) => parent.Code_Stop || parent.Raison_Arret
    },
    Operateur: { 
      type: GraphQLString,
      resolve: (parent) => parent.Regleur_Prenom || parent.Operateur
    }
  }
});

const StatisticsType = new GraphQLObjectType({
  name: 'OptimizedStatistics',
  fields: {
    totalStops: { type: GraphQLInt },
    nonDeclaredStops: { type: GraphQLInt },
    totalDuration: { type: GraphQLFloat },
    avgDuration: { type: GraphQLFloat },
    uniqueMachines: { type: GraphQLInt },
    uniqueOperators: { type: GraphQLInt },
    dateRange: { type: GraphQLString }
  }
});

const TopStopType = new GraphQLObjectType({
  name: 'OptimizedTopStop',
  fields: {
    stopName: { type: GraphQLString },
    count: { type: GraphQLInt },
    totalDuration: { type: GraphQLFloat },
    percentage: { type: GraphQLFloat },
    avgDuration: { type: GraphQLFloat }
  }
});

const MachineComparisonType = new GraphQLObjectType({
  name: 'OptimizedMachineComparison',
  fields: {
    machine: { type: GraphQLString },
    Machine_Name: { type: GraphQLString },
    stops: { type: GraphQLInt },
    totalDuration: { type: GraphQLFloat },
    avgDuration: { type: GraphQLFloat },
    percentage: { type: GraphQLFloat }
  }
});

const OperatorStatsType = new GraphQLObjectType({
  name: 'OptimizedOperatorStats',
  fields: {
    operator: { type: GraphQLString },
    interventions: { type: GraphQLInt },
    totalDuration: { type: GraphQLFloat },
    avgDuration: { type: GraphQLFloat },
    percentage: { type: GraphQLFloat }
  }
});

const DurationTrendType = new GraphQLObjectType({
  name: 'OptimizedDurationTrend',
  fields: {
    hour: { type: GraphQLInt },
    avgDuration: { type: GraphQLFloat },
    stopCount: { type: GraphQLInt },
    totalDuration: { type: GraphQLFloat }
  }
});

const DateGroupingType = new GraphQLObjectType({
  name: 'OptimizedDateGrouping',
  fields: {
    date: { type: GraphQLString },
    displayDate: { type: GraphQLString },
    stops: { type: GraphQLInt },
    totalDuration: { type: GraphQLFloat },
    avgDuration: { type: GraphQLFloat }
  }
});

const StopReasonType = new GraphQLObjectType({
  name: 'OptimizedStopReason',
  fields: {
    reason: { type: GraphQLString },
    count: { type: GraphQLInt },
    duration: { type: GraphQLFloat },
    percentage: { type: GraphQLFloat }
  }
});

// Main comprehensive data type
const ComprehensiveStopDataType = new GraphQLObjectType({
  name: 'ComprehensiveStopData',
  fields: {
    // Raw data
    rawStops: { type: new GraphQLList(MachineStopType) },
    
    // Calculated statistics
    statistics: { type: StatisticsType },
    
    // Dashboard components
    topStops: { type: new GraphQLList(TopStopType) },
    machineComparison: { type: new GraphQLList(MachineComparisonType) },
    operatorStats: { type: new GraphQLList(OperatorStatsType) },
    durationTrend: { type: new GraphQLList(DurationTrendType) },
    stopReasons: { type: new GraphQLList(StopReasonType) },
    
    // Date-based groupings for charts
    dateGroupings: { type: new GraphQLList(DateGroupingType) },
    
    // Metadata
    queryInfo: { 
      type: new GraphQLObjectType({
        name: 'QueryInfo',
        fields: {
          executionTime: { type: GraphQLInt },
          recordCount: { type: GraphQLInt },
          filterApplied: { type: GraphQLString },
          cacheStatus: { type: GraphQLString }
        }
      })
    }
  }
});

// Input type for filters
const OptimizedStopFilterInputType = new GraphQLInputObjectType({
  name: 'OptimizedStopFilterInput',
  fields: {
    date: { type: GraphQLString },
    startDate: { type: GraphQLString },
    endDate: { type: GraphQLString },
    dateRangeType: { type: GraphQLString },
    model: { type: GraphQLString },
    machine: { type: GraphQLString },
    limit: { type: GraphQLInt }
  }
});

// The main comprehensive resolver
const optimizedStopResolvers = {
  getComprehensiveStopData: {
    type: ComprehensiveStopDataType,
    args: {
      filters: { type: OptimizedStopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const startTime = Date.now();
      
      console.log('🚀 getComprehensiveStopData called with filters:', JSON.stringify(filters, null, 2));
      
      // Performance optimization: Detect complex queries
      const hasAllFilters = filters.model && filters.machine && (filters.startDate || filters.date);
      const isComplexQuery = hasAllFilters || Object.keys(filters).length > 2;
      
      // Adaptive limits based on query complexity
      const getQueryLimit = () => {
        if (hasAllFilters) return 500; // Very restrictive for triple filters
        if (isComplexQuery) return 1000; // Moderate for complex queries
        return 2000; // Normal limit for simple queries
      };
      
      const queryLimit = filters.limit || getQueryLimit();
      console.log(`🎯 Query analysis: hasAllFilters=${hasAllFilters}, isComplex=${isComplexQuery}, limit=${queryLimit}`);
      
      try {
        // Build comprehensive SQL query with all calculations
        const conditions = [];
        const queryParams = [];
        
        // Apply machine filtering
        addMachineFilters(conditions, queryParams, filters);
        
        // Apply date filtering
        if (filters.startDate && filters.endDate) {
          addDateRangeFilter(conditions, queryParams, filters);
        } else if (filters.date) {
          addDateRangeFilter(conditions, queryParams, filters);
        }
        
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        
        // Single comprehensive query that calculates everything
        const comprehensiveQuery = `
          WITH base_data AS (
            SELECT 
              Machine_Name,
              Date_Insert,
              Part_NO,
              Code_Stop,
              Debut_Stop,
              Fin_Stop_Time,
              Regleur_Prenom,
              
              -- Calculate duration with null handling
              COALESCE(
                TIMESTAMPDIFF(MINUTE,
                  STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
                  STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
                ), 15
              ) AS duration_minutes,
              
              -- Parse dates for grouping
              COALESCE(
                DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
                DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i'), '%Y-%m-%d'),
                DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
                DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i'), '%Y-%m-%d')
              ) AS stop_date,
              
              -- Extract hour for trend analysis
              COALESCE(
                HOUR(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i:%s')),
                HOUR(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i')),
                12
              ) AS stop_hour
              
            FROM machine_stop_table_mould
            ${whereClause}
            ORDER BY Date_Insert DESC
            LIMIT ${queryLimit}
          ),
          
          -- Calculate comprehensive statistics
          statistics AS (
            SELECT 
              COUNT(*) as total_stops,
              COUNT(CASE WHEN Code_Stop = 'Arrêt non déclaré' THEN 1 END) as non_declared_stops,
              SUM(duration_minutes) as total_duration,
              AVG(duration_minutes) as avg_duration,
              COUNT(DISTINCT Machine_Name) as unique_machines,
              COUNT(DISTINCT Regleur_Prenom) as unique_operators,
              CONCAT(MIN(stop_date), ' to ', MAX(stop_date)) as date_range
            FROM base_data
          ),
          
          -- Top stops analysis
          top_stops AS (
            SELECT 
              Code_Stop as stop_name,
              COUNT(*) as count,
              SUM(duration_minutes) as total_duration,
              AVG(duration_minutes) as avg_duration,
              (COUNT(*) * 100.0 / (SELECT total_stops FROM statistics)) as percentage
            FROM base_data
            GROUP BY Code_Stop
            ORDER BY count DESC
            LIMIT 5
          ),
          
          -- Machine comparison
          machine_comparison AS (
            SELECT 
              Machine_Name as machine,
              COUNT(*) as stops,
              SUM(duration_minutes) as total_duration,
              AVG(duration_minutes) as avg_duration,
              (COUNT(*) * 100.0 / (SELECT total_stops FROM statistics)) as percentage
            FROM base_data
            GROUP BY Machine_Name
            ORDER BY stops DESC
          ),
          
          -- Operator statistics
          operator_stats AS (
            SELECT 
              Regleur_Prenom as operator,
              COUNT(*) as interventions,
              SUM(duration_minutes) as total_duration,
              AVG(duration_minutes) as avg_duration,
              (COUNT(*) * 100.0 / (SELECT total_stops FROM statistics)) as percentage
            FROM base_data
            WHERE Regleur_Prenom IS NOT NULL AND Regleur_Prenom != ''
            GROUP BY Regleur_Prenom
            ORDER BY interventions DESC
          ),
          
          -- Duration trend by hour
          duration_trend AS (
            SELECT 
              stop_hour as hour,
              AVG(duration_minutes) as avg_duration,
              COUNT(*) as stop_count,
              SUM(duration_minutes) as total_duration
            FROM base_data
            WHERE stop_hour IS NOT NULL
            GROUP BY stop_hour
            ORDER BY stop_hour
          ),
          
          -- Date groupings for evolution charts
          date_groupings AS (
            SELECT 
              stop_date as date,
              CONCAT(DAY(stop_date), '/', MONTH(stop_date)) as display_date,
              COUNT(*) as stops,
              SUM(duration_minutes) as total_duration,
              AVG(duration_minutes) as avg_duration
            FROM base_data
            WHERE stop_date IS NOT NULL
            GROUP BY stop_date
            ORDER BY stop_date DESC
            LIMIT 30
          ),
          
          -- Stop reasons analysis
          stop_reasons AS (
            SELECT 
              Code_Stop as reason,
              COUNT(*) as count,
              SUM(duration_minutes) as duration,
              (COUNT(*) * 100.0 / (SELECT total_stops FROM statistics)) as percentage
            FROM base_data
            GROUP BY Code_Stop
            ORDER BY count DESC
          )
          
          -- Main query that combines all results
          SELECT 
            'raw_data' as data_type,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'Machine_Name', Machine_Name,
                'Date_Insert', Date_Insert,
                'Part_NO', Part_NO,
                'Code_Stop', Code_Stop,
                'Debut_Stop', Debut_Stop,
                'Fin_Stop_Time', Fin_Stop_Time,
                'Regleur_Prenom', Regleur_Prenom,
                'duration_minutes', duration_minutes,
                'stop_date', stop_date,
                'stop_hour', stop_hour
              )
            ) as data
          FROM base_data
          
          UNION ALL
          
          SELECT 'statistics' as data_type,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'totalStops', total_stops,
                'nonDeclaredStops', non_declared_stops,
                'totalDuration', total_duration,
                'avgDuration', avg_duration,
                'uniqueMachines', unique_machines,
                'uniqueOperators', unique_operators,
                'dateRange', date_range
              )
            ) as data
          FROM statistics
          
          UNION ALL
          
          SELECT 'top_stops' as data_type,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'stopName', stop_name,
                'count', count,
                'totalDuration', total_duration,
                'avgDuration', avg_duration,
                'percentage', percentage
              )
            ) as data
          FROM top_stops
          
          UNION ALL
          
          SELECT 'machine_comparison' as data_type,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'machine', machine,
                'Machine_Name', machine,
                'stops', stops,
                'totalDuration', total_duration,
                'avgDuration', avg_duration,
                'percentage', percentage
              )
            ) as data
          FROM machine_comparison
          
          UNION ALL
          
          SELECT 'operator_stats' as data_type,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'operator', operator,
                'interventions', interventions,
                'totalDuration', total_duration,
                'avgDuration', avg_duration,
                'percentage', percentage
              )
            ) as data
          FROM operator_stats
          
          UNION ALL
          
          SELECT 'duration_trend' as data_type,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'hour', hour,
                'avgDuration', avg_duration,
                'stopCount', stop_count,
                'totalDuration', total_duration
              )
            ) as data
          FROM duration_trend
          
          UNION ALL
          
          SELECT 'date_groupings' as data_type,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'date', date,
                'displayDate', display_date,
                'stops', stops,
                'totalDuration', total_duration,
                'avgDuration', avg_duration
              )
            ) as data
          FROM date_groupings
          
          UNION ALL
          
          SELECT 'stop_reasons' as data_type,
            JSON_ARRAYAGG(
              JSON_OBJECT(
                'reason', reason,
                'count', count,
                'duration', duration,
                'percentage', percentage
              )
            ) as data
          FROM stop_reasons
        `;
        
        console.log('📝 Executing comprehensive query...');
        const { success, data, error } = await executeQuery(comprehensiveQuery, queryParams);
        
        const executionTime = Date.now() - startTime;
        console.log(`💾 Comprehensive query completed in ${executionTime}ms`);
        
        if (!success) {
          throw new Error(`Database query failed: ${error}`);
        }
        
        // Parse the results
        const result = {
          rawStops: [],
          statistics: {},
          topStops: [],
          machineComparison: [],
          operatorStats: [],
          durationTrend: [],
          dateGroupings: [],
          stopReasons: [],
          queryInfo: {
            executionTime,
            recordCount: 0,
            filterApplied: JSON.stringify(filters),
            cacheStatus: 'fresh'
          }
        };
        
        // Process the unified results
        data.forEach(row => {
          const parsedData = JSON.parse(row.data);
          
          switch (row.data_type) {
            case 'raw_data':
              result.rawStops = parsedData;
              result.queryInfo.recordCount = parsedData.length;
              break;
            case 'statistics':
              result.statistics = parsedData[0] || {};
              break;
            case 'top_stops':
              result.topStops = parsedData;
              break;
            case 'machine_comparison':
              result.machineComparison = parsedData;
              break;
            case 'operator_stats':
              result.operatorStats = parsedData;
              break;
            case 'duration_trend':
              result.durationTrend = parsedData;
              break;
            case 'date_groupings':
              result.dateGroupings = parsedData;
              break;
            case 'stop_reasons':
              result.stopReasons = parsedData;
              break;
          }
        });
        
        console.log('✅ Comprehensive data processed successfully:', {
          rawStops: result.rawStops.length,
          topStops: result.topStops.length,
          machineComparison: result.machineComparison.length,
          operatorStats: result.operatorStats.length,
          durationTrend: result.durationTrend.length,
          dateGroupings: result.dateGroupings.length,
          stopReasons: result.stopReasons.length,
          executionTime
        });
        
        return result;
        
      } catch (error) {
        console.error('❌ getComprehensiveStopData error:', error);
        
        // Return empty but properly structured data on error
        return {
          rawStops: [],
          statistics: {
            totalStops: 0,
            nonDeclaredStops: 0,
            totalDuration: 0,
            avgDuration: 0,
            uniqueMachines: 0,
            uniqueOperators: 0,
            dateRange: ''
          },
          topStops: [],
          machineComparison: [],
          operatorStats: [],
          durationTrend: [],
          dateGroupings: [],
          stopReasons: [],
          queryInfo: {
            executionTime: Date.now() - startTime,
            recordCount: 0,
            filterApplied: JSON.stringify(filters),
            cacheStatus: 'error'
          }
        };
      }
    }
  },
  
  // Keep essential utility resolvers
  getStopMachineModels: {
    type: new GraphQLList(new GraphQLObjectType({
      name: 'OptimizedMachineModel',
      fields: {
        model: { type: GraphQLString }
      }
    })),
    resolve: async () => {
      const query = `
        SELECT DISTINCT
          CASE
            WHEN Machine_Name REGEXP '^[A-Za-z]+[0-9]'
            THEN REGEXP_REPLACE(Machine_Name, '[0-9].*$', '')
            ELSE Machine_Name
          END AS model
        FROM machine_stop_table_mould
        WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
        ORDER BY model
      `;

      const { success, data, error } = await executeQuery(query);

      if (!success || !data || data.length === 0) {
        return [
          { model: 'IPS' },
          { model: 'ARBURG' },
          { model: 'SUMITOMO' }
        ];
      }

      return data;
    }
  },
  
  getStopMachineNames: {
    type: new GraphQLList(new GraphQLObjectType({
      name: 'OptimizedMachineName',
      fields: {
        Machine_Name: { type: GraphQLString }
      }
    })),
    args: {
      filters: { type: OptimizedStopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const { model } = filters;
      
      let query = `
        SELECT DISTINCT Machine_Name
        FROM machine_stop_table_mould
        WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
      `;
      
      const queryParams = [];

      if (model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      }

      query += ` ORDER BY Machine_Name`;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success || !data || data.length === 0) {
        const defaultMachines = model ? 
          [`${model}01`, `${model}02`, `${model}03`].map(name => ({ Machine_Name: name })) :
          [{ Machine_Name: 'IPS01' }, { Machine_Name: 'IPS02' }, { Machine_Name: 'IPS03' }];
        return defaultMachines;
      }

      return data;
    }
  }
};

// Export types and resolvers
export const optimizedStopTypes = {
  ComprehensiveStopDataType,
  MachineStopType,
  StatisticsType,
  TopStopType,
  MachineComparisonType,
  OperatorStatsType,
  DurationTrendType,
  DateGroupingType,
  StopReasonType,
  OptimizedStopFilterInputType
};

export const optimizedStopQueries = optimizedStopResolvers;

export default optimizedStopResolvers;
