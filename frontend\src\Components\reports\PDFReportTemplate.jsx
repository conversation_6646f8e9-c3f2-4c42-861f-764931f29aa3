import React, { useEffect, useState, useRef } from 'react';
import { Card, Statistic, Table, Progress, Divider } from 'antd';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import dayjs from 'dayjs';
import 'dayjs/locale/fr';
import { formatFrenchNumber, formatFrenchPercentage } from '../../utils/numberFormatter';
import { SOMIPEM_COLORS } from '../../styles/brand-colors';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

dayjs.locale('fr');

/**
 * PDF-optimized React template for shift reports
 * Uses Tailwind CSS for consistent styling with print media queries
 * Designed specifically for Puppeteer PDF generation
 */
const PDFReportTemplate = ({ reportData }) => {
  const [isReady, setIsReady] = useState(false);

  // Chart refs for canvas-based charts
  const gaugeChartRef = useRef(null);
  const qualityChartRef = useRef(null);
  const performanceBarChartRef = useRef(null);

  // Signal when component is ready for PDF generation
  useEffect(() => {
    if (!reportData) {
      // Set ready attribute even for loading state so Puppeteer doesn't timeout
      const timer = setTimeout(() => {
        document.body.setAttribute('data-pdf-ready', 'true');
        console.log('⚠️ PDF template ready (no data)');
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      const timer = setTimeout(() => {
        setIsReady(true);
        // Add data attribute for Puppeteer to detect readiness
        document.body.setAttribute('data-pdf-ready', 'true');
        console.log('✅ PDF template ready for generation');
      }, 2000); // Increased time for charts to render

      return () => {
        clearTimeout(timer);
        // Cleanup on unmount
        document.body.removeAttribute('data-pdf-ready');
      };
    }
  }, [reportData]); // Depend on reportData to re-trigger when data changes

  if (!reportData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg text-gray-600">Chargement des données du rapport...</div>
      </div>
    );
  }

  const { machine, shift, date, performance, sessions, production } = reportData;

  // Provide fallback data to prevent rendering errors
  const safePerformance = performance || {};
  const safeSessions = sessions || [];
  const safeProduction = production || {};

  // Chart configurations optimized for PDF
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: true,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          font: { size: 10 },
          padding: 15
        }
      },
      tooltip: { enabled: false } // Disable tooltips for PDF
    },
    scales: {
      x: {
        ticks: { font: { size: 10 } }
      },
      y: {
        ticks: { font: { size: 10 } }
      }
    },
    animation: false // Disable animations for PDF
  };

  // OEE breakdown chart data with SOMIPEM brand colors
  const oeeData = {
    labels: ['Disponibilité', 'Performance', 'Qualité'],
    datasets: [{
      data: [
        safePerformance.availability || 0,
        safePerformance.performanceRate || 0,
        safePerformance.qualityRate || 0
      ],
      backgroundColor: [
        SOMIPEM_COLORS.CHART_PRIMARY,
        SOMIPEM_COLORS.CHART_SECONDARY,
        SOMIPEM_COLORS.CHART_TERTIARY
      ],
      borderWidth: 2,
      borderColor: SOMIPEM_COLORS.WHITE
    }]
  };

  // Production trend chart data with SOMIPEM brand colors
  const productionTrendData = {
    labels: safeSessions.slice(-12).map(s => dayjs(s.session_start).format('HH:mm')),
    datasets: [{
      label: 'Pièces Bonnes',
      data: safeSessions.slice(-12).map(s => s.Quantite_Bon || 0),
      borderColor: SOMIPEM_COLORS.CHART_PRIMARY,
      backgroundColor: SOMIPEM_COLORS.LIGHT_BLUE_BG,
      fill: true,
      tension: 0.4,
      pointBackgroundColor: SOMIPEM_COLORS.CHART_PRIMARY,
      pointBorderColor: SOMIPEM_COLORS.WHITE,
      pointBorderWidth: 2
    }, {
      label: 'Rejets',
      data: safeSessions.slice(-12).map(s => s.Quantite_Rejet || 0),
      borderColor: SOMIPEM_COLORS.ERROR,
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      fill: false,
      tension: 0.4,
      pointBackgroundColor: SOMIPEM_COLORS.ERROR,
      pointBorderColor: SOMIPEM_COLORS.WHITE,
      pointBorderWidth: 2
    }]
  };

  // Quality chart data
  const qualityData = {
    labels: ['Quantité Bonne', 'Quantité Rejetée'],
    datasets: [{
      data: [
        safeProduction.goodParts || 0,
        safeProduction.rejects || 0
      ],
      backgroundColor: ['#1E40AF', '#DC2626'],
      borderWidth: 0
    }]
  };

  // Performance indicators bar chart data
  const performanceBarData = {
    labels: ['Disponibilité', 'Performance', 'Qualité', 'OEE/TRS'],
    datasets: [{
      data: [
        safePerformance.availability || 0,
        safePerformance.performanceRate || 0,
        safePerformance.qualityRate || 0,
        safePerformance.oee || 0
      ],
      backgroundColor: ['#1E40AF', '#3B82F6', '#10B981', '#F59E0B'],
      borderWidth: 0
    }]
  };

  // Create canvas-based charts for better PDF rendering
  useEffect(() => {
    if (!isReady) return;

    // Gauge Chart for OEE
    if (gaugeChartRef.current) {
      const ctx = gaugeChartRef.current.getContext('2d');
      const oeeValue = safePerformance.oee || 0;

      // Clear canvas
      ctx.clearRect(0, 0, 200, 150);

      // Draw gauge background
      ctx.beginPath();
      ctx.arc(100, 100, 60, Math.PI, 2 * Math.PI);
      ctx.lineWidth = 20;
      ctx.strokeStyle = '#E5E7EB';
      ctx.stroke();

      // Draw gauge value
      const angle = Math.PI + (oeeValue / 100) * Math.PI;
      ctx.beginPath();
      ctx.arc(100, 100, 60, Math.PI, angle);
      ctx.strokeStyle = oeeValue > 0 ? '#3B82F6' : '#E5E7EB';
      ctx.stroke();

      // Draw center text
      ctx.fillStyle = '#1F2937';
      ctx.font = 'bold 16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(`${oeeValue.toFixed(1)}%`, 100, 105);
      ctx.font = '12px Arial';
      ctx.fillText('Performance', 100, 125);
    }

    // Quality Pie Chart
    if (qualityChartRef.current) {
      const ctx = qualityChartRef.current.getContext('2d');
      const goodParts = safeProduction.goodParts || 0;
      const rejects = safeProduction.rejects || 0;
      const total = goodParts + rejects;

      ctx.clearRect(0, 0, 200, 150);

      if (total > 0) {
        const centerX = 100;
        const centerY = 75;
        const radius = 50;

        // Good parts
        const goodAngle = (goodParts / total) * 2 * Math.PI;
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, 0, goodAngle);
        ctx.fillStyle = '#1E40AF';
        ctx.fill();

        // Rejected parts
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, goodAngle, 2 * Math.PI);
        ctx.fillStyle = '#DC2626';
        ctx.fill();
      } else {
        // No data circle
        ctx.beginPath();
        ctx.arc(100, 75, 50, 0, 2 * Math.PI);
        ctx.fillStyle = '#E5E7EB';
        ctx.fill();
      }
    }

    // Performance Bar Chart
    if (performanceBarChartRef.current) {
      const ctx = performanceBarChartRef.current.getContext('2d');
      const values = [
        safePerformance.availability || 0,
        safePerformance.performanceRate || 0,
        safePerformance.qualityRate || 0,
        safePerformance.oee || 0
      ];
      const colors = ['#1E40AF', '#3B82F6', '#10B981', '#F59E0B'];
      const labels = ['Disp.', 'Perf.', 'Qual.', 'OEE'];

      ctx.clearRect(0, 0, 300, 150);

      const barWidth = 60;
      const barSpacing = 15;
      const startX = 20;
      const maxHeight = 100;

      values.forEach((value, index) => {
        const x = startX + index * (barWidth + barSpacing);
        const height = (value / 100) * maxHeight;
        const y = 120 - height;

        // Draw bar
        ctx.fillStyle = colors[index];
        ctx.fillRect(x, y, barWidth, height);

        // Draw label
        ctx.fillStyle = '#1F2937';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(labels[index], x + barWidth/2, 140);

        // Draw value
        ctx.font = 'bold 10px Arial';
        ctx.fillText(`${value.toFixed(1)}%`, x + barWidth/2, y - 5);
      });

      // Draw y-axis labels
      ctx.fillStyle = '#6B7280';
      ctx.font = '8px Arial';
      ctx.textAlign = 'right';
      for (let i = 0; i <= 10; i++) {
        const y = 120 - (i * 10);
        ctx.fillText(`${i * 10}`, 15, y + 3);
      }
    }
  }, [isReady, safePerformance, safeProduction]);

  return (
    <div className="min-h-screen bg-white text-gray-900 print:bg-white">
      {/* Print-specific styles */}
      <style>{`
        @media print {
          .page-break { page-break-before: always; }
          .avoid-break { page-break-inside: avoid; }
          .no-print { display: none !important; }
          
          /* Ensure proper margins and spacing for print */
          body { margin: 0; padding: 0; }
          .print-container { margin: 0; padding: 0; }
          
          /* Chart containers should not break across pages */
          .chart-container { page-break-inside: avoid; margin-bottom: 20px; }
          
          /* Table styling for print */
          table { font-size: 10px; }
          th, td { padding: 4px 8px; }
        }
        
        @page {
          margin: 20mm 15mm;
          size: A4;
        }
      `}</style>

      <div className="print-container max-w-none mx-auto p-6 print:p-0">
        {/* Header Section with SOMIPEM Branding */}
        <div className="avoid-break mb-8">
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1">
              <h1 className="text-4xl font-bold mb-2" style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
                Rapport de Quart
              </h1>
              <div className="text-xl font-semibold mb-1" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                {machine?.name || 'Machine Non Spécifiée'} - Équipe {shift || 'Non Spécifiée'}
              </div>
              <div className="text-base" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                {dayjs(date).format('dddd DD MMMM YYYY')}
              </div>
              <div className="text-sm mt-2" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                Période: {reportData.period?.start ? dayjs(reportData.period.start).format('HH:mm') : '06:00'} - {reportData.period?.end ? dayjs(reportData.period.end).format('HH:mm') : '14:00'}
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold mb-2" style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
                SOMIPEM
              </div>
              <div className="text-sm" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                Système de Performance Industrielle
              </div>
              <div className="text-sm mt-1" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                Généré le {dayjs().format('DD/MM/YYYY à HH:mm')}
              </div>
              <div className="text-xs mt-2 px-3 py-1 rounded-full"
                   style={{
                     backgroundColor: SOMIPEM_COLORS.LIGHT_BLUE_BG,
                     color: SOMIPEM_COLORS.PRIMARY_BLUE,
                     border: `1px solid ${SOMIPEM_COLORS.ACCENT_BORDER}`
                   }}>
                Version: Enhanced React
              </div>
            </div>
          </div>

          <div className="h-2 rounded-full"
               style={{
                 background: `linear-gradient(90deg, ${SOMIPEM_COLORS.PRIMARY_BLUE} 0%, ${SOMIPEM_COLORS.SECONDARY_BLUE} 100%)`
               }}>
          </div>
        </div>

        {/* Information Machine Section */}
        <div className="avoid-break mb-6">
          <h2 className="text-lg font-bold text-white p-3 mb-0"
              style={{ backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE }}>
            Information Machine
          </h2>
          <div className="grid grid-cols-2 gap-0 text-sm"
               style={{ backgroundColor: '#E0F2FE' }}>
            <div className="p-3 border-r border-gray-300">
              <div className="mb-2"><strong>Machine:</strong> {machine?.name || machine?.id || 'N/A'}</div>
              <div className="mb-2"><strong>Article:</strong> {reportData.sessions?.[0]?.Article || 'N/A'}</div>
              <div className="mb-2"><strong>OF:</strong> N/A</div>
              <div className="mb-2"><strong>Empreintes:</strong> N/A</div>
              <div className="mb-2"><strong>Temps de Production:</strong> {formatFrenchNumber((safePerformance.runTime || 0) * 60)} min</div>
              <div><strong>Cycles Totaux:</strong> {formatFrenchNumber(safeProduction.totalProduction || 0)}</div>
            </div>
            <div className="p-3">
              <div className="mb-2"><strong>Quart:</strong> {shift || 'Current'}</div>
              <div className="mb-2"><strong>Date:</strong> {dayjs(date).format('YYYY-MM-DD')}</div>
              <div className="mb-2"><strong>Code arrêt:</strong> N/A</div>
              <div className="mb-2"><strong>Régleur:</strong> N/A</div>
              <div className="mb-2"><strong>État Principal:</strong> N/A</div>
              <div><strong>Code Arrêt Principal:</strong> N/A</div>
            </div>
          </div>
        </div>

        {/* Période Section */}
        <div className="avoid-break mb-6">
          <h2 className="text-lg font-bold text-white p-3 mb-0"
              style={{ backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE }}>
            Période
          </h2>
          <div className="grid grid-cols-2 gap-0 text-sm"
               style={{ backgroundColor: '#E0F2FE' }}>
            <div className="p-3 border-r border-gray-300">
              <div className="mb-2"><strong>Début:</strong> {reportData.period?.start ? dayjs(reportData.period.start).format('YYYY-MM-DD HH:mm:ss') : '2025-07-14 05:34:09'}</div>
              <div><strong>Fin:</strong> {reportData.period?.end ? dayjs(reportData.period.end).format('YYYY-MM-DD HH:mm:ss') : '2025-07-14 13:34:09'}</div>
            </div>
            <div className="p-3">
              <div className="mb-2"><strong>Durée:</strong> 8 heures</div>
              <div><strong>Généré par:</strong> admin</div>
            </div>
          </div>
        </div>

        {/* Métriques de Performance Section */}
        <div className="avoid-break mb-6">
          <h2 className="text-lg font-bold text-white p-3 mb-0"
              style={{ backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE }}>
            Métriques de Performance
          </h2>
          <div className="grid grid-cols-2 gap-0 text-sm"
               style={{ backgroundColor: '#E0F2FE' }}>
            <div className="p-3 border-r border-gray-300">
              <div className="mb-2"><strong>Quantité Bonne:</strong> {formatFrenchNumber(safeProduction.goodParts || 0)}</div>
              <div className="mb-2"><strong>Quantité Rejetée:</strong> {formatFrenchNumber(safeProduction.rejects || 0)}</div>
              <div className="mb-2"><strong>Production Totale:</strong> {formatFrenchNumber(safeProduction.totalProduction || 0)}</div>
              <div><strong>Temps de Cycle Moyen:</strong> {formatFrenchNumber(safePerformance.cycleTime || 0, 2)} sec</div>
            </div>
            <div className="p-3">
              <div className="mb-2"><strong>Taux de Disponibilité:</strong> {formatFrenchPercentage(safePerformance.availability || 0)}%</div>
              <div className="mb-2"><strong>Taux de Performance:</strong> {formatFrenchPercentage(safePerformance.performanceRate || 0)}%</div>
              <div className="mb-2"><strong>Taux de Qualité:</strong> {formatFrenchPercentage(safePerformance.qualityRate || 0)}%</div>
              <div><strong>TRS (OEE):</strong> {formatFrenchPercentage(safePerformance.oee || 0)}%</div>
            </div>
          </div>
        </div>

        {/* Résumé des Sessions Section */}
        <div className="avoid-break mb-8">
          <h2 className="text-lg font-bold text-white p-3 mb-0"
              style={{ backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE }}>
            Résumé des Sessions
          </h2>
          <div className="p-3 text-sm"
               style={{ backgroundColor: '#E0F2FE' }}>
            <div className="mb-2"><strong>Nombre de sessions:</strong> {sessions?.length || 0}</div>
            <div className="mb-2"><strong>Dernière session:</strong> {sessions?.[0] ? `${dayjs(sessions[0].session_start).format('HH:mm:ss')} à ${dayjs(sessions[0].session_end).format('HH:mm:ss')}` : 'N/A'}</div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div><strong>Article:</strong> {sessions?.[0]?.Article || 'N/A'}</div>
                <div><strong>Total Bon:</strong> {formatFrenchNumber(sessions?.reduce((sum, s) => sum + (s.Quantite_Bon || 0), 0) || 0)}</div>
              </div>
              <div>
                <div><strong>OF:</strong> N/A</div>
                <div><strong>Total Rejet:</strong> {formatFrenchNumber(sessions?.reduce((sum, s) => sum + (s.Quantite_Rejet || 0), 0) || 0)}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Primary KPIs */}
        <div className="grid grid-cols-4 gap-6 mb-8">
            <div className="p-6 rounded-xl text-center shadow-sm"
                 style={{
                   backgroundColor: SOMIPEM_COLORS.LIGHT_BLUE_BG,
                   border: `2px solid ${SOMIPEM_COLORS.ACCENT_BORDER}`
                 }}>
              <div className="text-3xl font-bold mb-2" style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
                {formatFrenchNumber(safeProduction.totalProduction || reportData.daily?.goodQty || 0)}
              </div>
              <div className="text-sm font-medium" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                Unités Produites
              </div>
              <div className="text-xs mt-1" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                Objectif: {formatFrenchNumber(safePerformance.theoreticalRate * 8 || 960)}
              </div>
            </div>

            <div className="p-6 rounded-xl text-center shadow-sm"
                 style={{
                   backgroundColor: safePerformance.oee >= 80 ? 'rgba(16, 185, 129, 0.1)' : 'rgba(245, 158, 11, 0.1)',
                   border: `2px solid ${safePerformance.oee >= 80 ? SOMIPEM_COLORS.SUCCESS : SOMIPEM_COLORS.WARNING}`
                 }}>
              <div className="text-3xl font-bold mb-2"
                   style={{ color: safePerformance.oee >= 80 ? SOMIPEM_COLORS.SUCCESS : SOMIPEM_COLORS.WARNING }}>
                {formatFrenchPercentage(safePerformance.oee || reportData.daily?.oee || 0)}%
              </div>
              <div className="text-sm font-medium" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                OEE Global
              </div>
              <div className="text-xs mt-1" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                {safePerformance.oee >= 80 ? '✅ Excellent' : safePerformance.oee >= 60 ? '⚠️ Acceptable' : '❌ Critique'}
              </div>
            </div>

            <div className="p-6 rounded-xl text-center shadow-sm"
                 style={{
                   backgroundColor: 'rgba(245, 158, 11, 0.1)',
                   border: `2px solid ${SOMIPEM_COLORS.WARNING}`
                 }}>
              <div className="text-3xl font-bold mb-2" style={{ color: SOMIPEM_COLORS.WARNING }}>
                {formatFrenchNumber(safeProduction.rejects || reportData.daily?.rejectsQty || 0)}
              </div>
              <div className="text-sm font-medium" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                Pièces Rejetées
              </div>
              <div className="text-xs mt-1" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                Taux: {formatFrenchPercentage(((safeProduction.rejects || 0) / (safeProduction.totalProduction || 1)) * 100)}%
              </div>
            </div>

            <div className="p-6 rounded-xl text-center shadow-sm"
                 style={{
                   backgroundColor: 'rgba(139, 69, 19, 0.1)',
                   border: `2px solid ${SOMIPEM_COLORS.SECONDARY_BLUE}`
                 }}>
              <div className="text-3xl font-bold mb-2" style={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }}>
                {formatFrenchNumber(safePerformance.cycleTime || reportData.daily?.speed || 0)}s
              </div>
              <div className="text-sm font-medium" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                Temps de Cycle Moyen
              </div>
              <div className="text-xs mt-1" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
                Théorique: {formatFrenchNumber(30)}s
              </div>
            </div>
          </div>

          {/* Secondary KPIs */}
          <div className="grid grid-cols-6 gap-4 mb-6">
            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: SOMIPEM_COLORS.LIGHT_BLUE_BG }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
                {formatFrenchNumber(safePerformance.runTime || reportData.daily?.runHours || 0, 1)}h
              </div>
              <div className="text-xs" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Temps Marche</div>
            </div>

            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: 'rgba(239, 68, 68, 0.1)' }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.ERROR }}>
                {formatFrenchNumber(safePerformance.downTime || reportData.daily?.downHours || 0, 1)}h
              </div>
              <div className="text-xs" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Temps Arrêt</div>
            </div>

            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: SOMIPEM_COLORS.LIGHT_BLUE_BG }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.SUCCESS }}>
                {formatFrenchPercentage(safePerformance.availability || reportData.daily?.availabilityRate || 0)}%
              </div>
              <div className="text-xs" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Disponibilité</div>
            </div>

            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: SOMIPEM_COLORS.LIGHT_BLUE_BG }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.SUCCESS }}>
                {formatFrenchPercentage(safePerformance.performanceRate || reportData.daily?.performanceRate || 0)}%
              </div>
              <div className="text-xs" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Performance</div>
            </div>

            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: SOMIPEM_COLORS.LIGHT_BLUE_BG }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.SUCCESS }}>
                {formatFrenchPercentage(safePerformance.qualityRate || reportData.daily?.qualityRate || 0)}%
              </div>
              <div className="text-xs" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Qualité</div>
            </div>

            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: 'rgba(245, 158, 11, 0.1)' }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.WARNING }}>
                {formatFrenchNumber(reportData.daily?.poidPurge || 0, 1)}kg
              </div>
              <div className="text-xs" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Poids Purge</div>
            </div>
        </div>

        {/* Visualisations et Graphiques Section */}
        <div className="page-break">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-white p-4"
                style={{ backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE }}>
              Visualisations et Graphiques
            </h1>
            <div className="text-sm text-gray-600 p-2" style={{ backgroundColor: '#F0F9FF' }}>
              Machine: {machine?.name || machine?.id || 'ALL'} - Date: {dayjs(date).format('YYYY-MM-DD')} - Quart: {shift || 'Current'}
            </div>
          </div>

          {/* Charts Grid Layout - Smaller, More Focused */}
          <div className="grid grid-cols-2 gap-4 mb-6">

            {/* Performance Globale (TRS/OEE) - Gauge Chart */}
            <div className="avoid-break">
              <h3 className="text-base font-semibold mb-2 text-gray-700">
                Performance Globale (TRS/OEE)
              </h3>
              <div className="flex justify-center">
                <div style={{ width: '200px', height: '150px' }}>
                  <canvas ref={gaugeChartRef} width="200" height="150"></canvas>
                </div>
              </div>
              <div className="text-center mt-1">
                <p className="text-lg font-bold text-red-500">
                  {formatFrenchPercentage(safePerformance.oee || 0)}%
                </p>
                <p className="text-xs text-gray-500">
                  {(safePerformance.oee || 0) === 0 ? 'Aucune donnée de performance valide disponible' : ''}
                </p>
              </div>
            </div>

            {/* Répartition de la Qualité - Pie Chart */}
            <div className="avoid-break">
              <h3 className="text-base font-semibold mb-2 text-gray-700">
                Répartition de la Qualité
              </h3>
              <div className="flex justify-center">
                <div style={{ width: '200px', height: '150px' }}>
                  <canvas ref={qualityChartRef} width="200" height="150"></canvas>
                </div>
              </div>
              <div className="flex justify-center mt-1">
                <div className="flex items-center space-x-3 text-xs">
                  <div className="flex items-center">
                    <div className="w-2 h-2 mr-1" style={{ backgroundColor: '#1E40AF' }}></div>
                    <span>Quantité Bonne</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 mr-1" style={{ backgroundColor: '#DC2626' }}></div>
                    <span>Quantité Rejetée</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Second Row of Charts */}
          <div className="grid grid-cols-2 gap-4 mb-6">

            {/* Indicateurs de Performance (%) - Bar Chart */}
            <div className="avoid-break">
              <h3 className="text-base font-semibold mb-2 text-gray-700">
                Indicateurs de Performance (%)
              </h3>
              <div style={{ width: '100%', height: '150px' }}>
                <canvas ref={performanceBarChartRef} width="300" height="150"></canvas>
              </div>
              <div className="text-center mt-1">
                <p className="text-xs text-orange-500">
                  {(safePerformance.oee || 0) === 0 ? 'Pas de données de production suffisantes pour générer un graphique' : ''}
                </p>
              </div>
            </div>

            {/* Décomposition OEE - Doughnut Chart */}
            <div className="avoid-break">
              <h3 className="text-base font-semibold mb-2 text-gray-700">
                Décomposition OEE
              </h3>
              <div className="flex justify-center">
                <div style={{ width: '200px', height: '150px' }}>
                  <Doughnut data={oeeData} options={{
                    ...chartOptions,
                    maintainAspectRatio: false,
                    plugins: {
                      ...chartOptions.plugins,
                      legend: {
                        position: 'bottom',
                        labels: {
                          color: SOMIPEM_COLORS.DARK_GRAY,
                          font: { size: 10 },
                          padding: 8
                        }
                      }
                    }
                  }} />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Performance Metrics with SOMIPEM Branding */}
        <div className="avoid-break mb-8">
          <h2 className="text-2xl font-semibold mb-6 pb-3"
              style={{
                color: SOMIPEM_COLORS.PRIMARY_BLUE,
                borderBottom: `2px solid ${SOMIPEM_COLORS.ACCENT_BORDER}`
              }}>
            📋 Métriques Détaillées
          </h2>

          <div className="grid grid-cols-3 gap-6">
            <div>
              <h3 className="font-medium text-gray-800 mb-3">Disponibilité</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Temps de fonctionnement:</span>
                  <span className="font-medium">{formatFrenchNumber(performance?.runTime || 0)}h</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Temps d'arrêt:</span>
                  <span className="font-medium">{formatFrenchNumber(performance?.downTime || 0)}h</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Taux:</span>
                  <span className="font-medium text-blue-600">
                    {formatFrenchPercentage(performance?.availability || 0)}%
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-medium text-gray-800 mb-3">Performance</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Cadence théorique:</span>
                  <span className="font-medium">{formatFrenchNumber(performance?.theoreticalRate || 0)}/h</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Cadence réelle:</span>
                  <span className="font-medium">{formatFrenchNumber(performance?.actualRate || 0)}/h</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Taux:</span>
                  <span className="font-medium text-green-600">
                    {formatFrenchPercentage(performance?.performanceRate || 0)}%
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-medium text-gray-800 mb-3">Qualité</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Pièces bonnes:</span>
                  <span className="font-medium">{formatFrenchNumber(production?.goodParts || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Rejets:</span>
                  <span className="font-medium">{formatFrenchNumber(production?.rejects || 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Taux:</span>
                  <span className="font-medium text-yellow-600">
                    {formatFrenchPercentage(performance?.qualityRate || 0)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Sessions Table with SOMIPEM Branding */}
        <div className="page-break">
          <h2 className="text-2xl font-semibold mb-6 pb-3"
              style={{
                color: SOMIPEM_COLORS.PRIMARY_BLUE,
                borderBottom: `2px solid ${SOMIPEM_COLORS.ACCENT_BORDER}`
              }}>
            📊 Détail des Sessions de Production
          </h2>

          <div className="overflow-hidden rounded-xl shadow-sm"
               style={{ border: `2px solid ${SOMIPEM_COLORS.ACCENT_BORDER}` }}>
            <table className="w-full text-sm border-collapse">
              <thead>
                <tr style={{ backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE, color: SOMIPEM_COLORS.WHITE }}>
                  <th className="p-3 text-left font-bold">🕐 Début</th>
                  <th className="p-3 text-left font-bold">🕐 Fin</th>
                  <th className="p-3 text-right font-bold">✅ Bonnes</th>
                  <th className="p-3 text-right font-bold">❌ Rejets</th>
                  <th className="p-3 text-right font-bold">⏱️ Cycle (s)</th>
                  <th className="p-3 text-right font-bold">📈 TRS (%)</th>
                  <th className="p-3 text-left font-bold">🔧 Article</th>
                  <th className="p-3 text-right font-bold">📊 Efficacité</th>
                </tr>
              </thead>
              <tbody>
                {sessions?.slice(0, 20).map((session, index) => {
                  const efficiency = ((session.Quantite_Bon || 0) / ((session.Quantite_Bon || 0) + (session.Quantite_Rejet || 0))) * 100;
                  const isGoodPerformance = (session.TRS || 0) >= 80;
                  const rowBg = index % 2 === 0 ? SOMIPEM_COLORS.LIGHT_BLUE_BG : SOMIPEM_COLORS.WHITE;

                  return (
                    <tr key={index} style={{ backgroundColor: rowBg }}>
                      <td className="p-3 font-medium" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                        {dayjs(session.session_start).format('HH:mm')}
                      </td>
                      <td className="p-3 font-medium" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                        {dayjs(session.session_end).format('HH:mm')}
                      </td>
                      <td className="p-3 text-right font-bold" style={{ color: SOMIPEM_COLORS.SUCCESS }}>
                        {formatFrenchNumber(session.Quantite_Bon || 0)}
                      </td>
                      <td className="p-3 text-right font-bold" style={{ color: SOMIPEM_COLORS.ERROR }}>
                        {formatFrenchNumber(session.Quantite_Rejet || 0)}
                      </td>
                      <td className="p-3 text-right font-medium" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                        {formatFrenchNumber(session.cycle || 0, 1)}
                      </td>
                      <td className="p-3 text-right font-bold"
                          style={{ color: isGoodPerformance ? SOMIPEM_COLORS.SUCCESS : SOMIPEM_COLORS.WARNING }}>
                        {formatFrenchPercentage(session.TRS || 0)}
                      </td>
                      <td className="p-3 font-medium" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
                        {session.Article || '-'}
                      </td>
                      <td className="p-3 text-right font-bold"
                          style={{ color: efficiency >= 95 ? SOMIPEM_COLORS.SUCCESS : efficiency >= 90 ? SOMIPEM_COLORS.WARNING : SOMIPEM_COLORS.ERROR }}>
                        {formatFrenchPercentage(efficiency || 0)}%
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Sessions Summary */}
          <div className="mt-6 grid grid-cols-4 gap-4">
            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: SOMIPEM_COLORS.LIGHT_BLUE_BG }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
                {sessions?.length || 0}
              </div>
              <div className="text-sm" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Sessions Total</div>
            </div>

            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: 'rgba(16, 185, 129, 0.1)' }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.SUCCESS }}>
                {sessions?.filter(s => (s.TRS || 0) >= 80).length || 0}
              </div>
              <div className="text-sm" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Sessions Excellentes</div>
            </div>

            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: 'rgba(245, 158, 11, 0.1)' }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.WARNING }}>
                {sessions?.filter(s => (s.TRS || 0) >= 60 && (s.TRS || 0) < 80).length || 0}
              </div>
              <div className="text-sm" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Sessions Moyennes</div>
            </div>

            <div className="p-4 rounded-lg text-center"
                 style={{ backgroundColor: 'rgba(239, 68, 68, 0.1)' }}>
              <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.ERROR }}>
                {sessions?.filter(s => (s.TRS || 0) < 60).length || 0}
              </div>
              <div className="text-sm" style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>Sessions Critiques</div>
            </div>
          </div>
        </div>

        {/* Enhanced Footer with SOMIPEM Branding */}
        <div className="mt-8 pt-6 text-center"
             style={{ borderTop: `2px solid ${SOMIPEM_COLORS.ACCENT_BORDER}` }}>
          <div className="mb-4">
            <div className="text-lg font-bold" style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
              SOMIPEM - Système de Performance Industrielle
            </div>
            <div className="text-sm" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
              Rapport généré automatiquement le {dayjs().format('DD/MM/YYYY à HH:mm:ss')}
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4 text-xs" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
            <div>
              <strong>Version:</strong> Enhanced React PDF
            </div>
            <div>
              <strong>Support:</strong> équipe<EMAIL>
            </div>
            <div>
              <strong>Documentation:</strong> docs.somipem.com
            </div>
          </div>

          <div className="mt-4 text-xs" style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}>
            Ce document est confidentiel et destiné exclusivement à l'usage interne de SOMIPEM
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFReportTemplate;
