import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import cookieParser from "cookie-parser";
import shiftReportRoutes from "./routes/shiftReportRoutes.js";
import auth from "./middleware/auth.js";

// Load environment variables
dotenv.config();

const app = express();

// Middleware
app.use(cors({
  origin: ["http://localhost:3000", "http://localhost:5173"],
  credentials: true
}));
app.use(express.json());
app.use(cookieParser());

// Test endpoint for enhanced PDF generation
app.post("/test-enhanced-pdf", auth, async (req, res) => {
  try {
    console.log("Testing enhanced PDF generation...");
    
    // Mock data for testing
    const testData = {
      machine: {
        name: "TEST_MACHINE_01",
        partNumber: "PART_123",
        poidUnitaire: "25.5",
        cycleTheorique: "8.2",
        shift: "Matin"
      },
      period: {
        startTime: "2025-07-14 06:00:00",
        endTime: "2025-07-14 14:00:00",
        duration: "8 hours"
      },
      daily: {
        date: "2025-07-14",
        runHours: 7.2,
        downHours: 0.8,
        goodQty: 2450,
        rejectsQty: 50,
        speed: 340.5,
        availabilityRate: 90.0,
        performanceRate: 88.5,
        qualityRate: 98.0,
        oee: 77.8,
        shift: "Matin",
        poidPurge: 12.3
      },
      session: {
        totalGoodQty: 2450,
        totalRejectQty: 50,
        totalStopTime: 45,
        totalPurgeWeight: 12.3,
        avgCycleTime: 8.4,
        avgTRS: 77.8,
        qualityRate: 98.0,
        sessionCount: 485,
        operators: ["Jean Dupont", "Marie Martin"],
        sessions: []
      },
      performance: {
        totalProduction: 2500,
        qualityRate: 98.0,
        cycleEfficiency: 97.6,
        productionRate: 5.05,
        rejectionRate: 2.0,
        utilization: 90.6
      },
      generatedAt: new Date().toISOString(),
      shift: "Matin",
      userId: "test_user",
      username: "Test User"
    };

    res.json({
      success: true,
      message: "Enhanced PDF structure validated",
      testData,
      componentsLoaded: {
        PDFReportTemplate: true,
        ReportDataService: true,
        generateEnhancedPdfContent: true
      }
    });

  } catch (error) {
    console.error("Enhanced PDF test error:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Routes
app.use("/api/shift-reports", shiftReportRoutes);

const PORT = process.env.PORT || 5000;

// Start server
app.listen(PORT, () => {
  console.log(`Enhanced PDF Test Server running on port ${PORT}`);
  console.log(`Available endpoints:`);
  console.log(`- POST /test-enhanced-pdf (Test enhanced PDF components)`);
  console.log(`- POST /api/shift-reports/generate (Standard PDF generation)`);
  console.log(`- POST /api/shift-reports/generate-enhanced (Enhanced PDF generation)`);
});

export default app;
