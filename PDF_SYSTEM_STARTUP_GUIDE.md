# PDF Generation System - Startup & Testing Guide

## Quick Start

### 1. Install Dependencies
```bash
# Backend (if not already done)
cd backend
npm install

# Frontend (should already be installed)
cd ../frontend
npm install
```

### 2. Start Services

**Terminal 1 - Frontend:**
```bash
cd frontend
npm run dev
```
*Should start on http://localhost:5173*

**Terminal 2 - Backend:**
```bash
cd backend
npm run dev
```
*Should start on http://localhost:5000*

### 3. Test the System
```bash
# Run comprehensive test
node test-pdf-complete.js
```

## Manual Testing

### Test PDF Template (Browser)
1. Visit: http://localhost:5173/reports/pdf-test
2. You should see a formatted report with charts
3. Use browser Print Preview (Ctrl+P) to see PDF layout
4. Check browser console for "PDF template ready" message

### Test PDF Preview Route (Puppeteer Target)
1. Visit: http://localhost:5173/reports/pdf-preview?data=eyJtYWNoaW5lIjp7ImlkIjoiVEVTVDAxIiwibmFtZSI6Ik1hY2hpbmUgVGVzdCAwMSJ9LCJzaGlmdCI6Ik1hdGluIiwiZGF0ZSI6IjIwMjUtMDEtMTYifQ==
2. Should show the same report template
3. Check that `data-pdf-ready="true"` is set on document.body

## Troubleshooting

### Issue: "PDF ready selector not found"
**Symptoms:** Puppeteer timeout waiting for `[data-pdf-ready="true"]`

**Solutions:**
1. **Check Frontend URL:**
   ```bash
   # Verify in backend/config.env
   FRONTEND_URL=http://localhost:5173
   ```

2. **Test Route Manually:**
   - Visit http://localhost:5173/reports/pdf-test
   - Open browser console
   - Look for "PDF template ready" message
   - Check if `document.body.getAttribute('data-pdf-ready')` returns "true"

3. **Check Dependencies:**
   ```bash
   cd frontend
   npm list react-chartjs-2 chart.js dayjs
   ```

### Issue: "Frontend not accessible"
**Symptoms:** Connection refused or timeout

**Solutions:**
1. **Verify Frontend is Running:**
   ```bash
   curl http://localhost:5173
   ```

2. **Check Port Configuration:**
   - Frontend should be on port 5173 (Vite default)
   - Backend should be on port 5000
   - Update `backend/config.env` if different

3. **Firewall/Network Issues:**
   - Try http://127.0.0.1:5173 instead
   - Check Windows Firewall settings

### Issue: Chart.js Not Rendering
**Symptoms:** Charts appear as empty boxes

**Solutions:**
1. **Check Chart.js Registration:**
   ```javascript
   // In PDFReportTemplate.jsx
   ChartJS.register(/* all required components */);
   ```

2. **Verify Data Format:**
   - Check that chart data arrays are not empty
   - Ensure data types match Chart.js expectations

3. **Console Errors:**
   - Open browser dev tools
   - Look for Chart.js related errors

## Configuration Files

### backend/config.env
```env
FRONTEND_URL=http://localhost:5173
# Other config...
```

### Key Files Modified
- `backend/services/pdfGenerationService.js` - Main PDF service
- `frontend/src/components/reports/PDFReportTemplate.jsx` - PDF template
- `frontend/src/pages/reports/pdf-preview.jsx` - Puppeteer target route
- `backend/routes/shiftReportRoutes.js` - Enhanced endpoint

## Production Deployment

### Environment Variables
```env
# Production example
FRONTEND_URL=https://your-domain.com
```

### Docker Considerations
If using Docker, add Puppeteer args:
```javascript
args: [
  '--no-sandbox',
  '--disable-setuid-sandbox',
  '--disable-dev-shm-usage'
]
```

## Success Indicators

✅ **System Working Correctly:**
- Frontend loads at http://localhost:5173
- Backend responds at http://localhost:5000
- PDF test route shows formatted report
- Browser console shows "PDF template ready"
- `document.body.getAttribute('data-pdf-ready')` returns "true"
- Test script passes all checks

## Support

If issues persist:
1. Check all console logs (browser and Node.js)
2. Verify all dependencies are installed
3. Ensure ports 5173 and 5000 are available
4. Test with sample data first before real report data
