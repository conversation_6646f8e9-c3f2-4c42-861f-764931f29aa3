// Simple test to check what data is available

import fetch from 'node-fetch';

async function testData() {
  const BASE_URL = 'http://localhost:5000';
  
  // Test with a broader date range
  console.log('🔍 Testing with broader date range...');
  try {
    const response = await fetch(`${BASE_URL}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getAllMachineStops(startDate: "2024-01-01", endDate: "2025-07-31", limit: 50) {
              Id_Arret
              Machine_Name
              Debut_Stop
              Fin_Stop_Time
              duration_minutes
              Date_Insert
            }
          }
        `
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Broader date range data received:', data?.data?.getAllMachineStops?.length || 0, 'stops');
    
    if (data?.data?.getAllMachineStops?.length > 0) {
      const sample = data.data.getAllMachineStops[0];
      console.log('📊 Sample data:', {
        Id_Arret: sample.Id_Arret,
        Machine_Name: sample.Machine_Name,
        Debut_Stop: sample.Debut_Stop,
        Fin_Stop_Time: sample.Fin_Stop_Time,
        duration_minutes: sample.duration_minutes,
        Date_Insert: sample.Date_Insert
      });
      
      // Check date distribution
      const dates = data.data.getAllMachineStops.map(stop => {
        const date = new Date(stop.Debut_Stop || stop.Date_Insert);
        return date.toISOString().split('T')[0];
      });
      
      console.log('📅 Date range in data:', {
        earliest: Math.min(...dates.map(d => new Date(d).getTime())),
        latest: Math.max(...dates.map(d => new Date(d).getTime())),
        earliestFormatted: new Date(Math.min(...dates.map(d => new Date(d).getTime()))).toISOString().split('T')[0],
        latestFormatted: new Date(Math.max(...dates.map(d => new Date(d).getTime()))).toISOString().split('T')[0]
      });
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
  
  // Test just getting any data without filters
  console.log('\n🔍 Testing without any filters...');
  try {
    const response = await fetch(`${BASE_URL}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getAllMachineStops(limit: 10) {
              Id_Arret
              Machine_Name
              Debut_Stop
              Fin_Stop_Time
              duration_minutes
              Date_Insert
            }
          }
        `
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ No filter data received:', data?.data?.getAllMachineStops?.length || 0, 'stops');
    
    if (data?.data?.getAllMachineStops?.length > 0) {
      const sample = data.data.getAllMachineStops[0];
      console.log('📊 Sample data:', {
        Id_Arret: sample.Id_Arret,
        Machine_Name: sample.Machine_Name,
        Debut_Stop: sample.Debut_Stop,
        Fin_Stop_Time: sample.Fin_Stop_Time,
        duration_minutes: sample.duration_minutes,
        Date_Insert: sample.Date_Insert
      });
    }
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testData().catch(console.error);
