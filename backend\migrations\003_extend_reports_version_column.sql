-- Migration 003: Extend reports table version column for enhanced PDF generation
-- This migration fixes the "Data too long for column 'version'" error
-- when saving reports with the new "enhanced-react" version identifier

-- Check if the reports table exists
SELECT 'Starting migration 003: Extend reports version column' as status;

-- First, let's check the current column definition
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_DEFAULT,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'reports' 
    AND COLUMN_NAME = 'version';

-- Extend the version column from VARCHAR(10) to VARCHAR(50)
-- This provides plenty of space for future version identifiers
ALTER TABLE reports 
MODIFY COLUMN version VARCHAR(50) DEFAULT 'standard';

-- Verify the change was successful
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_DEFAULT,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'reports' 
    AND COLUMN_NAME = 'version';

-- Update any existing records that might have the old default
-- This ensures backward compatibility
UPDATE reports 
SET version = CASE 
    WHEN version = 'standard' THEN 'standard'
    WHEN version = 'enhanced' THEN 'enhanced'
    WHEN version IS NULL THEN 'standard'
    ELSE version
END;

-- Add an index on the version column for better query performance
-- when filtering reports by version type
ALTER TABLE reports 
ADD INDEX idx_reports_version (version);

-- Verify that we can now insert the new version identifier
-- This is a test insert that we'll immediately roll back
START TRANSACTION;

INSERT INTO reports (
    type, 
    title, 
    description, 
    date, 
    shift, 
    machine_id, 
    machine_name, 
    status, 
    generated_at, 
    version
) VALUES (
    'test',
    'Migration Test Report',
    'Testing extended version column',
    CURDATE(),
    'test',
    'TEST01',
    'Test Machine',
    'completed',
    NOW(),
    'enhanced-react'
);

-- Check if the insert was successful
SELECT 'Test insert successful - version column can now store enhanced-react' as test_result
WHERE ROW_COUNT() > 0;

-- Roll back the test insert
ROLLBACK;

-- Create a view to show version distribution for monitoring
CREATE OR REPLACE VIEW reports_version_stats AS
SELECT 
    version,
    COUNT(*) as report_count,
    MIN(created_at) as first_created,
    MAX(created_at) as last_created,
    AVG(file_size) as avg_file_size
FROM reports 
GROUP BY version
ORDER BY report_count DESC;

-- Log the successful migration
INSERT INTO reports (
    type,
    title,
    description,
    date,
    status,
    generated_at,
    version
) VALUES (
    'migration',
    'Database Migration 003',
    'Extended reports.version column from VARCHAR(10) to VARCHAR(50) to support enhanced-react PDF generation',
    CURDATE(),
    'completed',
    NOW(),
    'migration-003'
);

-- Final verification and summary
SELECT 
    'Migration 003 completed successfully' as status,
    'reports.version column extended to VARCHAR(50)' as change_summary,
    'enhanced-react version identifier now supported' as impact,
    NOW() as completed_at;

-- Show current version statistics
SELECT 'Current version distribution:' as info;
SELECT * FROM reports_version_stats;
