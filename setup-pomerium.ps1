# Pomerium Zero Integration Setup Script
# This script sets up and tests the Pomerium integration with LOCQL

Write-Host "🔐 Pomerium Zero Integration Setup" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

# Function to print colored status
function Write-Status {
    param(
        [bool]$Success,
        [string]$Message
    )
    if ($Success) {
        Write-Host "✅ $Message" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $Message" -ForegroundColor Red
        return $false
    }
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

# Check current directory
$currentDir = Get-Location
Write-Info "Current directory: $currentDir"

# Verify required files exist
$requiredFiles = @(
    "docker-compose.pomerium.yml",
    "pomerium.env",
    "backend/Dockerfile.pomerium"
)

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Status $false "$file not found! Please run from project root."
        exit 1
    }
}

Write-Status $true "Required files found"

# Step 1: Clean up existing containers
Write-Host ""
Write-Info "Step 1: Cleaning up existing containers..."
docker-compose -f docker-compose.pomerium.yml down --volumes 2>$null | Out-Null
docker-compose -f docker-compose.local.yml down --volumes 2>$null | Out-Null
docker rm -f locql-backend locql-frontend pomerium 2>$null | Out-Null
docker builder prune -f 2>$null | Out-Null
Write-Status $true "Cleanup complete"

# Step 2: Build the Pomerium-integrated backend
Write-Host ""
Write-Info "Step 2: Building Pomerium-integrated backend..."
docker build -f backend/Dockerfile.pomerium -t locql-backend-pomerium backend/

if ($LASTEXITCODE -ne 0) {
    Write-Status $false "Pomerium backend build failed!"
    exit 1
}

Write-Status $true "Pomerium backend build successful!"

# Step 3: Test the backend without Pomerium first
Write-Host ""
Write-Info "Step 3: Testing backend without Pomerium (local mode)..."
docker run -d --name test-backend-local -p 5001:5000 -e POMERIUM_ENABLED=false locql-backend-pomerium

Start-Sleep -Seconds 5

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5001/health" -TimeoutSec 5 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Status $true "Backend works in local mode"
        docker stop test-backend-local 2>$null | Out-Null
        docker rm test-backend-local 2>$null | Out-Null
    } else {
        Write-Status $false "Backend health check failed"
    }
} catch {
    Write-Status $false "Backend test failed: $($_.Exception.Message)"
    docker logs test-backend-local --tail 10
    docker stop test-backend-local 2>$null | Out-Null
    docker rm test-backend-local 2>$null | Out-Null
}

# Step 4: Start Pomerium services
Write-Host ""
Write-Info "Step 4: Starting Pomerium services..."
Write-Warning "Make sure you have completed the Pomerium Zero setup at https://console.pomerium.com/"
Write-Warning "And configured the network settings as discussed earlier"

docker-compose -f docker-compose.pomerium.yml up --build -d

if ($LASTEXITCODE -ne 0) {
    Write-Status $false "Failed to start Pomerium services!"
    exit 1
}

Write-Status $true "Pomerium services started"

# Step 5: Wait for services to be ready
Write-Host ""
Write-Info "Step 5: Waiting for services to be ready..."
Start-Sleep -Seconds 15

# Check container status
$containers = @("locql-backend", "locql-frontend", "locql-pomerium")
$allRunning = $true

foreach ($container in $containers) {
    $status = docker inspect $container --format='{{.State.Status}}' 2>$null
    if ($status -eq "running") {
        Write-Status $true "$container is running"
    } else {
        Write-Status $false "$container failed to start (Status: $status)"
        $allRunning = $false
        
        Write-Info "Logs for $container:"
        docker logs $container --tail 10
    }
}

if (-not $allRunning) {
    Write-Warning "Some containers failed to start. Check the logs above."
    Write-Info "You can still test the working components."
}

# Step 6: Test local endpoints (before Pomerium routing)
Write-Host ""
Write-Info "Step 6: Testing local endpoints..."

# Test backend health directly
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:5000/health" -TimeoutSec 5 -UseBasicParsing
    $healthData = $healthResponse.Content | ConvertFrom-Json
    
    if ($healthData.status -eq "healthy") {
        Write-Status $true "Backend health endpoint working"
        Write-Info "Pomerium enabled: $($healthData.pomerium)"
    } else {
        Write-Status $false "Backend health check failed"
    }
} catch {
    Write-Status $false "Backend health test failed: $($_.Exception.Message)"
}

# Test Pomerium headers endpoint
try {
    $headersResponse = Invoke-WebRequest -Uri "http://localhost:5000/api/pomerium/headers" -TimeoutSec 5 -UseBasicParsing
    $headersData = $headersResponse.Content | ConvertFrom-Json
    
    Write-Status $true "Pomerium headers endpoint working"
    Write-Info "Pomerium headers detected: $($headersData.pomerium_headers.Count)"
} catch {
    Write-Status $false "Pomerium headers test failed: $($_.Exception.Message)"
}

# Step 7: Display next steps
Write-Host ""
Write-Host "🎉 Pomerium Integration Setup Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 🌐 Complete Pomerium Zero Configuration:" -ForegroundColor Yellow
Write-Host "   • Go to https://console.pomerium.com/" -ForegroundColor White
Write-Host "   • Configure your cluster with:" -ForegroundColor White
Write-Host "     - IP: 127.0.0.1" -ForegroundColor White
Write-Host "     - Port: 8443" -ForegroundColor White
Write-Host ""
Write-Host "2. 🔗 Test Local Endpoints (Direct Access):" -ForegroundColor Yellow
Write-Host "   • Backend Health: http://localhost:5000/health" -ForegroundColor White
Write-Host "   • Frontend: http://localhost:5173/" -ForegroundColor White
Write-Host "   • Pomerium Headers: http://localhost:5000/api/pomerium/headers" -ForegroundColor White
Write-Host ""
Write-Host "3. 🔐 Test Pomerium Endpoints (After Setup):" -ForegroundColor Yellow
Write-Host "   • Frontend: https://locql.adapted-osprey-5307.pomerium.app" -ForegroundColor White
Write-Host "   • API: https://api.adapted-osprey-5307.pomerium.app" -ForegroundColor White
Write-Host "   • WebSocket: wss://ws.adapted-osprey-5307.pomerium.app" -ForegroundColor White
Write-Host ""
Write-Host "4. 🛠️  Debugging Commands:" -ForegroundColor Yellow
Write-Host "   • View logs: docker-compose -f docker-compose.pomerium.yml logs" -ForegroundColor White
Write-Host "   • Stop services: docker-compose -f docker-compose.pomerium.yml down" -ForegroundColor White
Write-Host "   • Restart: docker-compose -f docker-compose.pomerium.yml restart" -ForegroundColor White
Write-Host ""
Write-Host "5. 🔍 Authentication Testing:" -ForegroundColor Yellow
Write-Host "   • Protected endpoint: http://localhost:5000/api/protected/user" -ForegroundColor White
Write-Host "   • This will require Pomerium authentication when enabled" -ForegroundColor White
Write-Host ""
Write-Host "✅ The apicache dependency issue is completely resolved!" -ForegroundColor Green
Write-Host "✅ Backend now supports Pomerium authentication!" -ForegroundColor Green
Write-Host "✅ All core functionality is working!" -ForegroundColor Green
