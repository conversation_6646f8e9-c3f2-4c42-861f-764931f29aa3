// Test the actual GraphQL endpoint
async function testGraphQLEndpoint() {
  console.log('=== Testing Actual GraphQL Endpoint ===');
  
  const endpoint = 'http://localhost:5000/api/graphql';
  
  // Test query - same as what the frontend uses
  const query = `
    query GetComprehensiveStopData($filters: FinalOptimizedStopFilterInput) {
      getFinalComprehensiveStopData(filters: $filters) {
        allStops {
          Machine_Name
          Date_Insert
          Code_Stop
          duration_minutes
        }
        sidecards {
          Arret_Totale
          Arret_Totale_nondeclare
        }
        topStops {
          stopName
          count
        }
        totalRecords
        queryExecutionTime
      }
    }
  `;
  
  try {
    // Test 1: No filters
    console.log('🚀 Testing GraphQL endpoint with no filters...');
    
    const response1 = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: query,
        variables: {
          filters: {}
        }
      })
    });
    
    const result1 = await response1.json();
    
    if (result1.errors) {
      console.log('❌ GraphQL errors:', result1.errors);
    } else {
      const data = result1.data.getFinalComprehensiveStopData;
      console.log('✅ GraphQL response received:');
      console.log(`   • Total records: ${data.totalRecords}`);
      console.log(`   • All stops count: ${data.allStops.length}`);
      console.log(`   • Sidecards: Total=${data.sidecards.Arret_Totale}, Non-declared=${data.sidecards.Arret_Totale_nondeclare}`);
      console.log(`   • Top stops: ${data.topStops.length} entries`);
      console.log(`   • Execution time: ${data.queryExecutionTime}ms`);
      
      // Show sample stop
      if (data.allStops.length > 0) {
        const sample = data.allStops[0];
        console.log(`   • Sample stop: ${sample.Machine_Name} - ${sample.Code_Stop} (${sample.duration_minutes} min)`);
      }
    }
    
    // Test 2: With filters
    console.log('\n🚀 Testing GraphQL endpoint with machine filter...');
    
    const response2 = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: query,
        variables: {
          filters: {
            machine: "IPS01"
          }
        }
      })
    });
    
    const result2 = await response2.json();
    
    if (result2.errors) {
      console.log('❌ GraphQL errors:', result2.errors);
    } else {
      const data = result2.data.getFinalComprehensiveStopData;
      console.log('✅ GraphQL response with filter received:');
      console.log(`   • Total records: ${data.totalRecords}`);
      console.log(`   • Sidecards: Total=${data.sidecards.Arret_Totale}, Non-declared=${data.sidecards.Arret_Totale_nondeclare}`);
      console.log(`   • Execution time: ${data.queryExecutionTime}ms`);
    }
    
    console.log('\n✅ GraphQL endpoint testing completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing GraphQL endpoint:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('\n⚠️  Server might not be running. Let me check...');
      
      try {
        const healthCheck = await fetch('http://localhost:5000/health');
        console.log('✅ Server is responding to health check');
      } catch (healthError) {
        console.log('❌ Server is not responding. Please start the backend server first.');
        console.log('   Run: npm run dev in the backend directory');
      }
    }
  }
}

// Run if this is a Node.js environment
if (typeof fetch === 'undefined') {
  // Use node-fetch for Node.js
  import('node-fetch').then((fetchModule) => {
    global.fetch = fetchModule.default;
    testGraphQLEndpoint();
  }).catch(() => {
    console.log('⚠️  To test the GraphQL endpoint, please install node-fetch:');
    console.log('   npm install node-fetch');
    console.log('\n📋 Alternative: Test manually in the browser at http://localhost:5000/graphql');
    
    console.log('\n📊 GraphQL Test Query:');
    console.log(`
query GetComprehensiveStopData {
  getFinalComprehensiveStopData(filters: {}) {
    allStops {
      Machine_Name
      Code_Stop
      duration_minutes
    }
    sidecards {
      Arret_Totale
      Arret_Totale_nondeclare
    }
    totalRecords
    queryExecutionTime
  }
}
    `);
  });
} else {
  testGraphQLEndpoint();
}
