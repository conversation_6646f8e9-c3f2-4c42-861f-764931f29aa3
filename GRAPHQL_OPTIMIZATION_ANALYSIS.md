# GraphQL Query Optimization Analysis

## Current State Analysis

### Backend Resolvers (stopTableResolvers.js)
Currently has **17 different resolvers** making individual database queries:

#### Core Data Queries (Most Used):
1. `getAllMachineStops` - Gets all stop records with filters
2. `getStopSidecards` - Gets count statistics (total stops, non-declared)
3. `getTop5Stops` - Gets top 5 most frequent stop reasons
4. `getMachineStopComparison` - Gets stops by machine
5. `getStopReasons` - Gets stop reasons analysis
6. `getOperatorStopStats` - Gets operator intervention stats
7. `getStopDurationTrend` - Gets hourly duration trends

#### Secondary/Utility Queries:
8. `getStopStats` - Gets stop counts by date
9. `getUniqueStopDates` - Gets available dates
10. `getStopDetailsByDate` - Gets stops for specific date
11. `getStopDurationTrend` - Gets duration trends
12. `getStopMachineModels` - Gets machine models list
13. `getStopMachineNames` - Gets machine names list
14. `getStopProductionDetails` - Gets production context
15. `getStopsByRange` - Gets stops by date range
16. `getStopsTableRange` - Gets paginated table data
17. `getStopsAnalysisData` - Composite query

### Frontend Hook (useStopTableGraphQL.js)
Makes **multiple GraphQL calls** for each data refresh:

#### Dashboard Data (getStopDashboardData):
- Normal scenario: **4 parallel queries**
  - `getAllMachineStops`
  - `getTop5Stops` 
  - `getStopSidecards`
  - `getMachineStopComparison`

- Triple filter scenario: **3 sequential queries**
  - `getAllMachineStops`
  - `getStopSidecards`
  - `getTop5Stops`

#### Analysis Data (getStopsAnalysisData):
- **4 parallel queries**
  - `getStopDurationTrend`
  - `getOperatorStopStats`
  - `getStopReasons`
  - `getStopStats`

### Current Context Usage (ArretContext.jsx)
Makes **2 main composite calls** per filter change:
```javascript
const dashboardData = await graphQL.getStopDashboardData(filters);
const analysisData = await graphQL.getStopsAnalysisData(filters);
```

## Major Optimization Opportunities

### 1. **Single Comprehensive Query**
Instead of 17 separate resolvers, create **1 unified resolver** that:
- Gets all stop records with all needed fields
- Calculates all statistics in the database
- Returns everything in one response

### 2. **Database-Level Calculations**
Move calculations from frontend to backend:
- Count statistics (total, non-declared)
- Top stops ranking
- Machine comparisons
- Operator statistics
- Duration trends
- Date groupings

### 3. **Data Processing Location**
Current: **Frontend processes raw data**
Optimized: **Backend processes and returns final data**

### 4. **Query Consolidation**
Instead of:
```javascript
// Current: Multiple database hits
const allStops = await getAllMachineStops(filters);
const sidecards = await getStopSidecards(filters);  
const topStops = await getTop5Stops(filters);
const comparison = await getMachineStopComparison(filters);
const reasons = await getStopReasons(filters);
const operators = await getOperatorStopStats(filters);
const trends = await getStopDurationTrend(filters);
```

Use:
```javascript
// Optimized: Single database hit
const allData = await getComprehensiveStopData(filters);
```

## Proposed Optimized Structure

### Backend: Single Comprehensive Resolver
```javascript
getComprehensiveStopData: {
  type: ComprehensiveStopDataType,
  args: { filters: { type: StopFilterInputType } },
  resolve: async (_, { filters }) => {
    // Single complex SQL query that calculates everything
    const query = `
      SELECT 
        -- Raw stop data
        Machine_Name, Date_Insert, Code_Stop, Debut_Stop, Fin_Stop_Time, Regleur_Prenom,
        Part_NO, duration_minutes,
        
        -- Aggregated statistics (calculated in SQL)
        COUNT(*) OVER() as total_stops,
        COUNT(*) OVER(PARTITION BY Code_Stop = 'Arrêt non déclaré') as non_declared_stops,
        ROW_NUMBER() OVER(PARTITION BY Code_Stop ORDER BY COUNT(*) DESC) as stop_rank,
        COUNT(*) OVER(PARTITION BY Machine_Name) as machine_stop_count,
        COUNT(*) OVER(PARTITION BY Regleur_Prenom) as operator_interventions,
        AVG(duration_minutes) OVER(PARTITION BY HOUR(Date_Insert)) as hourly_avg_duration,
        
        -- Date groupings
        DATE_FORMAT(Date_Insert, '%Y-%m-%d') as stop_date,
        HOUR(Date_Insert) as stop_hour
        
      FROM machine_stop_table_mould 
      WHERE [filters]
      ORDER BY Date_Insert DESC
    `;
    
    // Return structured data ready for frontend consumption
    return {
      rawStops: data,
      statistics: { /* calculated stats */ },
      topStops: { /* top 5 stops */ },
      machineComparison: { /* machine stats */ },
      operatorStats: { /* operator stats */ },
      durationTrend: { /* hourly trends */ },
      dateGroupings: { /* evolution data */ }
    };
  }
}
```

### Frontend: Single Hook Call
```javascript
// Instead of multiple calls
const data = await getComprehensiveStopData(filters);

// Data is ready to use immediately
setStopsData(data.rawStops);
setArretStats(data.statistics.totalStops);
setTopStopsData(data.topStops);
setMachineComparison(data.machineComparison);
setOperatorStats(data.operatorStats);
setDurationTrend(data.durationTrend);
setRawChartData(data.dateGroupings);
```

### Context: Simplified Data Fetching
```javascript
const fetchData = async (filters) => {
  const allData = await graphQL.getComprehensiveStopData(filters);
  
  // Direct assignment - no processing needed
  applyAllData(allData);
};
```

## Performance Benefits

### 1. **Reduced Network Calls**
- Current: **8+ GraphQL queries** per filter change
- Optimized: **1 GraphQL query** per filter change
- **87% reduction** in network requests

### 2. **Reduced Database Load**
- Current: **8+ database queries** with similar WHERE clauses
- Optimized: **1 database query** with complex SELECT
- **87% reduction** in database queries

### 3. **Faster Response Times**
- Current: **Sequential/parallel query overhead**
- Optimized: **Single query execution**
- **Estimated 60-80% faster** response times

### 4. **Better Caching**
- Current: **Multiple cache entries** per filter combination
- Optimized: **Single cache entry** with all data
- **More efficient** memory usage

### 5. **Simplified Error Handling**
- Current: **Multiple failure points**
- Optimized: **Single failure point**
- **More reliable** error recovery

## Implementation Plan

### Phase 1: Backend Optimization
1. Create comprehensive SQL query
2. Build unified resolver
3. Test with current frontend

### Phase 2: Frontend Simplification  
1. Update useStopTableGraphQL hook
2. Simplify ArretContext data flow
3. Remove unused query functions

### Phase 3: Performance Testing
1. Compare query execution times
2. Measure network payload sizes
3. Test with various filter combinations

### Phase 4: Cleanup
1. Remove old resolvers
2. Clean up hook exports
3. Update documentation

## Next Steps
1. ✅ **Analysis Complete** - Current state documented
2. 🔄 **Design Phase** - Create comprehensive resolver design
3. ⏳ **Implementation** - Build and test new resolver
4. ⏳ **Migration** - Update frontend to use new resolver
5. ⏳ **Cleanup** - Remove redundant code

This optimization should significantly improve the "triple filter freeze" issue by reducing the complexity and number of queries required for data loading.
