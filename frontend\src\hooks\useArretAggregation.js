import { useState, useEffect, useCallback, useMemo } from 'react';
import request from 'superagent';

// Simple date formatting function
const formatApiDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split('T')[0];
};

const API_BASE_URL = 'http://localhost:5000';

export const useArretAggregation = (filters = {}) => {
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);  // Memoize the aggregation requests to avoid unnecessary re-renders
  const aggregationRequests = useMemo(() => {
    console.log('🗓️ useArretAggregation - Building requests with filters:', { 
      selectedDate: filters.selectedDate,
      selectedMachineModel: filters.selectedMachineModel,
      selectedMachine: filters.selectedMachine,
      dateRangeType: filters.dateRangeType
    });
    
    const baseFilters = {
      model: filters.selectedMachineModel || 'IPS'
    };
    
    // Only add date filtering if a specific date is selected
    if (filters.selectedDate) {
      const formattedDate = formatApiDate(filters.selectedDate);
      if (formattedDate) {
        baseFilters.startDate = formattedDate;
        baseFilters.dateRangeType = filters.dateRangeType || 'day';
        console.log(`🗓️ useArretAggregation - Using date filter: ${formattedDate} (${baseFilters.dateRangeType})`);
      }
    } else {
      console.log('🗓️ useArretAggregation - No date filter applied, fetching all available data');
    }
    
    // Add machine filter if specific machine is selected
    if (filters.selectedMachine) {
      baseFilters.machine = filters.selectedMachine;
    }
    
    return [{
      key: 'stopsStats',
      type: 'stops_summary',
      filters: baseFilters
    },
    {
      key: 'topStops',
      type: 'top_stops',
      filters: baseFilters,
      options: { limit: 10 }
    },
    {
      key: 'machineStops',
      type: 'machine_stops',
      filters: baseFilters,
      options: { limit: 15 }
    },
    {
      key: 'stopReasons',
      type: 'stop_reasons',
      filters: baseFilters,
      options: { limit: 10 }
    },
    {
      key: 'recentStops',
      type: 'recent_stops',
      filters: baseFilters,
      options: { limit: 20 }
    }
    ];
  }, [filters.selectedMachineModel, filters.selectedMachine, filters.selectedDate, filters.dateRangeType]);

  // Function to fetch aggregated data
  const fetchAggregatedData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching aggregated stops data with requests:', aggregationRequests);

      const response = await request.post(`${API_BASE_URL}/api/dashboard-data`)
        .send({
          requests: aggregationRequests,
          batchId: `stops-dashboard-${Date.now()}`
        })
        .retry(2);

      if (response.body.success) {
        console.log('✅ Aggregated data received:', response.body);
        setData(response.body.data);
      } else {
        throw new Error('Failed to fetch aggregated data');
      }
    } catch (err) {
      console.error('❌ Error fetching aggregated stops data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [aggregationRequests]);

  // Effect to fetch data when filters change
  useEffect(() => {
    fetchAggregatedData();
  }, [fetchAggregatedData]);

  // Refresh function
  const refreshData = useCallback(() => {
    console.log('🔄 Manual refresh of aggregated stops data');
    return fetchAggregatedData();
  }, [fetchAggregatedData]);
  // Extract and format data from aggregated response
  const processedData = useMemo(() => {
    if (!data || Object.keys(data).length === 0) {
      return {
        arretStats: null,
        topStops: [],
        machineStops: [],
        stopReasons: [],
        recentStops: [],
        totalStops: 0,
        avgDuration: 0,
        totalDowntime: 0
      };
    }

    const stopsStats = data.stopsStats?.data || {};
    const topStops = data.topStops?.data || [];
    const machineStops = data.machineStops?.data || [];
    const stopReasons = data.stopReasons?.data || [];
    const recentStops = data.recentStops?.data || [];

    // Ensure numeric values are properly converted from strings
    const totalStops = parseInt(stopsStats.totalStops) || 0;
    const avgDuration = parseFloat(stopsStats.avgDuration) || 0;
    const totalDowntime = parseFloat(stopsStats.totalDowntime) || 0;

    return {
      arretStats: stopsStats,
      topStops,
      machineStops,
      stopReasons,
      recentStops,
      totalStops,
      avgDuration,
      totalDowntime
    };
  }, [data]);

  return {
    loading,
    error,
    refreshData,
    ...processedData
  };
};

export default useArretAggregation;
