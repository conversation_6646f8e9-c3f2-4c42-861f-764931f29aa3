/**
 * 🔒 SECURE Authentication Verification Script
 * Run this in the browser console to verify HTTP-only cookie authentication
 */

console.log('🔒 SECURE AUTHENTICATION VERIFICATION SCRIPT');
console.log('=' .repeat(60));

// Check localStorage tokens (should be empty for security)
console.log('\n🔒 Security Check - localStorage should be empty:');
const token = localStorage.getItem('token');
const authToken = localStorage.getItem('authToken');
const user = localStorage.getItem('user');

console.log('token:', token ? '⚠️ SECURITY RISK: Found in localStorage!' : '✅ Secure (not in localStorage)');
console.log('authToken:', authToken ? '⚠️ SECURITY RISK: Found in localStorage!' : '✅ Secure (not in localStorage)');
console.log('user:', user ? '⚠️ SECURITY RISK: Found in localStorage!' : '✅ Secure (not in localStorage)');

if (token) {
  try {
    // Decode JWT token (without verification)
    const payload = JSON.parse(atob(token.split('.')[1]));
    console.log('Token payload:', payload);
    console.log('Token expires:', new Date(payload.exp * 1000));
    console.log('Token valid:', new Date() < new Date(payload.exp * 1000) ? '✅ Yes' : '❌ Expired');
  } catch (e) {
    console.log('❌ Invalid token format');
  }
}

// Test API endpoints with HTTP-only cookies
console.log('\n🌐 API Endpoint Tests (HTTP-only cookies):');

const baseURL = window.location.hostname === 'localhost'
  ? 'http://localhost:5000'
  : 'https://charming-hermit-intense.ngrok-free.app';

// Test /api/me endpoint
console.log('\n🔑 Testing /api/me endpoint with HTTP-only cookies...');
fetch(`${baseURL}/api/me`, {
  method: 'GET',
  credentials: 'include', // 🔒 SECURITY: Uses HTTP-only cookies
  headers: {
    'Content-Type': 'application/json'
    // 🔒 SECURITY: No Authorization header needed
  }
})
.then(response => {
  console.log('/api/me status:', response.status);
  if (response.ok) {
    return response.json();
  } else {
    throw new Error(`HTTP ${response.status}`);
  }
})
.then(data => {
  console.log('✅ /api/me success:', data);
})
.catch(error => {
  console.log('❌ /api/me failed:', error.message);
});

// Test SSE token endpoint
console.log('\n🔌 Testing /api/sse-token endpoint with HTTP-only cookies...');
fetch(`${baseURL}/api/sse-token`, {
  method: 'GET',
  credentials: 'include', // 🔒 SECURITY: Uses HTTP-only cookies
  headers: {
    'Content-Type': 'application/json'
    // 🔒 SECURITY: No Authorization header needed
  }
})
.then(response => {
  console.log('/api/sse-token status:', response.status);
  if (response.ok) {
    return response.json();
  } else {
    throw new Error(`HTTP ${response.status}`);
  }
})
.then(data => {
  console.log('✅ /api/sse-token success:', data);
})
.catch(error => {
  console.log('❌ /api/sse-token failed:', error.message);
});

// Check cookies (HTTP-only cookies won't be visible)
console.log('\n🍪 Cookie Security Check:');
const cookies = document.cookie.split(';').reduce((acc, cookie) => {
  const [key, value] = cookie.trim().split('=');
  if (key) acc[key] = value;
  return acc;
}, {});

console.log('Visible cookies:', Object.keys(cookies));
console.log('🔒 SECURITY NOTE: HTTP-only auth cookies are hidden from JavaScript');
console.log('🔒 This is GOOD - auth tokens should not be visible here!');

// Check SSE connection state
console.log('\n🔌 SSE Connection State:');
if (window.React && window.React.version) {
  console.log('React version:', window.React.version);
  console.log('Check React DevTools for SSE context state');
} else {
  console.log('React DevTools not available');
}

console.log('\n✅ Verification complete. Check the results above.');
console.log('\n🔧 TROUBLESHOOTING:');
console.log('If you see 401 errors:');
console.log('1. Make sure you are logged in');
console.log('2. Check that token exists in localStorage');
console.log('3. Verify cookies are enabled in browser');
console.log('4. Try logging out and logging back in');
console.log('\n🔄 If issues persist after login, the authentication fixes should resolve them.');
