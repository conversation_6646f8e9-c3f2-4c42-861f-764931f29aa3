version: '3.8'

services:
  # Pomerium Proxy Service
  pomerium:
    image: pomerium/pomerium:v0.30.2
    container_name: locql-pomerium
    ports:
      - "8443:443"  # Changed to avoid conflicts with system port 443
      - "8080:80"   # Changed to avoid conflicts with system port 80
    restart: unless-stopped
    environment:
      # Pomerium Zero Token (replace with your actual token)
      POMERIUM_ZERO_TOKEN: AMf-vByA3DWxQo497-KQBkY7h7mb2Z5rpXvKCWJ0NcWIhLdhgZ1S6twYRWTQuqeo8wRII0WDazhz1hkp6IDV5d631bGYq_FvdFBn0VfnbwLDLmlocmgLSq83B_g9vkFoWhL_YcsjvuvDZzCrXcs4kcADIQzETaROLXs5w7Z4rbYCJB6zIQDsZoWRxVWDEAID80CtKO_zmhTu

      # Cache directory
      XDG_CACHE_HOME: /var/cache

      # Local development configuration
      INSECURE_SERVER: true
      ADDRESS: :80
      
      # Pomerium Configuration
      POMERIUM_CONFIG: |
        # Pomerium configuration for LOCQL project
        authenticate_service_url: https://verify.adapted-osprey-5307.pomerium.app
        
        # Routes configuration
        routes:
          # Frontend route
          - from: https://locql.adapted-osprey-5307.pomerium.app
            to: http://frontend:5173
            policy:
              - allow:
                  and:
                    - domain:
                        is: gmail.com
            cors_allow_preflight: true
            timeout: 30s
            
          # Backend API route
          - from: https://api.adapted-osprey-5307.pomerium.app
            to: http://backend:5000
            policy:
              - allow:
                  and:
                    - domain:
                        is: gmail.com
            cors_allow_preflight: true
            timeout: 30s
            preserve_host_header: true
            
          # WebSocket route for real-time data
          - from: https://ws.adapted-osprey-5307.pomerium.app
            to: http://backend:5000
            policy:
              - allow:
                  and:
                    - domain:
                        is: gmail.com
            allow_websockets: true
            timeout: 0s
            
        # TLS configuration
        autocert: true
        
        # Logging
        log_level: info
        
        # Security headers
        set_response_headers:
          X-Frame-Options: SAMEORIGIN
          X-Content-Type-Options: nosniff
          X-XSS-Protection: "1; mode=block"
          Strict-Transport-Security: "max-age=31536000; includeSubDomains"
          
    volumes:
      - pomerium-cache:/var/cache
    networks:
      - locql-network
    depends_on:
      - backend
      - frontend

  # Verification service for Pomerium
  verify:
    image: pomerium/verify:latest
    container_name: locql-verify
    networks:
      locql-network:
        aliases:
          - verify.adapted-osprey-5307.pomerium.app
          - verify
    restart: unless-stopped

  # Backend Service (same as before but with Pomerium integration)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: locql-backend
    ports:
      - "5000:5000"
    env_file:
      - pomerium.env
    environment:
      - NODE_ENV=development
    volumes:
      # Mount source code for development hot reload
      - ./backend:/app
      - node_modules_backend:/app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    # Add extra hosts to resolve host.docker.internal on Linux
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Frontend Service (same as before but with Pomerium integration)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: locql-frontend
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=https://api.adapted-osprey-5307.pomerium.app
      # Pomerium WebSocket configuration
      - VITE_WS_URL=wss://ws.adapted-osprey-5307.pomerium.app
      - VITE_POMERIUM_URL=https://locql.adapted-osprey-5307.pomerium.app
    volumes:
      # Mount source code for development hot reload
      - ./frontend:/app
      - node_modules_frontend:/app/node_modules
    networks:
      - locql-network
    restart: unless-stopped
    depends_on:
      - backend
    # Add extra hosts for Pomerium compatibility
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  locql-network:
    driver: bridge
    name: locql-network

volumes:
  pomerium-cache:
    driver: local
  node_modules_backend:
  node_modules_frontend:
