// Debug script to check chart data when date filtering is active

import fetch from 'node-fetch';

async function debugChartData() {
  const BASE_URL = 'http://localhost:5000';
  
  // First, fetch data without date filter
  console.log('🔍 Testing chart data without date filter...');
  try {
    const response = await fetch(`${BASE_URL}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getAllMachineStops(limit: 100) {
              Id_Arret
              Machine_Name
              Debut_Stop
              Fin_Stop_Time
              duration_minutes
              Cause
              Raison_Arret
              Regleur_Prenom
              Operateur
              Date_Insert
            }
          }
        `
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ No filter data received:', data?.data?.getAllMachineStops?.length || 0, 'stops');
    
    if (data?.data?.getAllMachineStops?.length > 0) {
      const sample = data.data.getAllMachineStops[0];
      console.log('📊 Sample stop data:', {
        Id_Arret: sample.Id_Arret,
        Machine_Name: sample.Machine_Name,
        Debut_Stop: sample.Debut_Stop,
        Fin_Stop_Time: sample.Fin_Stop_Time,
        duration_minutes: sample.duration_minutes,
        Cause: sample.Cause,
        Raison_Arret: sample.Raison_Arret,
        Regleur_Prenom: sample.Regleur_Prenom,
        Operateur: sample.Operateur
      });
    }
  } catch (error) {
    console.error('❌ Error fetching no filter data:', error);
  }

  // Now test with date filter
  console.log('\n🔍 Testing chart data with date filter...');
  try {
    const response = await fetch(`${BASE_URL}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getAllMachineStops(startDate: "01/04/2025", endDate: "31/04/2025") {
              Id_Arret
              Machine_Name
              Debut_Stop
              Fin_Stop_Time
              duration_minutes
              Cause
              Raison_Arret
              Regleur_Prenom
              Operateur
              Date_Insert
            }
          }
        `
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Date filtered data received:', data?.data?.getAllMachineStops?.length || 0, 'stops');
    
    if (data?.data?.getAllMachineStops?.length > 0) {
      const sample = data.data.getAllMachineStops[0];
      console.log('📊 Sample filtered stop data:', {
        Id_Arret: sample.Id_Arret,
        Machine_Name: sample.Machine_Name,
        Debut_Stop: sample.Debut_Stop,
        Fin_Stop_Time: sample.Fin_Stop_Time,
        duration_minutes: sample.duration_minutes,
        Cause: sample.Cause,
        Raison_Arret: sample.Raison_Arret,
        Regleur_Prenom: sample.Regleur_Prenom,
        Operateur: sample.Operateur
      });
      
      // Check duration distribution
      const durations = data.data.getAllMachineStops
        .filter(stop => stop.duration_minutes != null)
        .map(stop => parseFloat(stop.duration_minutes))
        .filter(d => !isNaN(d) && d > 0);
      
      console.log('📈 Duration statistics:', {
        totalStops: data.data.getAllMachineStops.length,
        validDurations: durations.length,
        avgDuration: durations.length > 0 ? durations.reduce((a,b) => a+b, 0) / durations.length : 0,
        minDuration: Math.min(...durations),
        maxDuration: Math.max(...durations)
      });
      
      // Check hourly distribution
      const hourlyCount = {};
      data.data.getAllMachineStops.forEach(stop => {
        if (stop.Debut_Stop) {
          const hour = new Date(stop.Debut_Stop).getHours();
          hourlyCount[hour] = (hourlyCount[hour] || 0) + 1;
        }
      });
      
      console.log('🕐 Hourly distribution:', hourlyCount);
    }
  } catch (error) {
    console.error('❌ Error fetching date filtered data:', error);
  }
}

debugChartData().catch(console.error);
