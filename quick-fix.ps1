# Quick Docker Dependency Fix Script
# This script provides a fast solution to the apicache dependency issue

Write-Host "🚀 Quick Docker Dependency Fix" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

# Stop everything and clean up
Write-Host "1. Stopping and cleaning up existing containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml down --volumes --remove-orphans 2>$null | Out-Null
docker rm -f locql-backend locql-frontend 2>$null | Out-Null

# Remove build cache
Write-Host "2. Clearing Docker build cache..." -ForegroundColor Yellow
docker builder prune -f 2>$null | Out-Null
docker image prune -f 2>$null | Out-Null

# Remove any existing images
Write-Host "3. Removing existing images..." -ForegroundColor Yellow
docker rmi locql-project-backend locql-project-frontend 2>$null | Out-Null

# Build backend with verbose output
Write-Host "4. Building backend container (this may take a few minutes)..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml build --no-cache backend

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Backend build failed!" -ForegroundColor Red
    exit 1
}

# Test the backend image for apicache
Write-Host "5. Verifying apicache dependency in backend..." -ForegroundColor Yellow
$testResult = docker run --rm locql-project-backend node -e "try { require('apicache'); console.log('✅ apicache found'); } catch(e) { console.error('❌ apicache missing:', e.message); process.exit(1); }" 2>&1

Write-Host $testResult

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ apicache verification failed!" -ForegroundColor Red
    exit 1
}

# Build frontend
Write-Host "6. Building frontend container..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml build --no-cache frontend

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Frontend build failed!" -ForegroundColor Red
    exit 1
}

# Start containers
Write-Host "7. Starting containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml up -d

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to start containers!" -ForegroundColor Red
    exit 1
}

# Wait and check status
Write-Host "8. Waiting for containers to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

$backendStatus = docker inspect locql-backend --format='{{.State.Status}}' 2>$null
$frontendStatus = docker inspect locql-frontend --format='{{.State.Status}}' 2>$null

Write-Host ""
Write-Host "Container Status:" -ForegroundColor Cyan
Write-Host "Backend: $backendStatus" -ForegroundColor $(if ($backendStatus -eq "running") { "Green" } else { "Red" })
Write-Host "Frontend: $frontendStatus" -ForegroundColor $(if ($frontendStatus -eq "running") { "Green" } else { "Red" })

if ($backendStatus -ne "running") {
    Write-Host ""
    Write-Host "❌ Backend container failed to start. Logs:" -ForegroundColor Red
    docker logs locql-backend --tail 20
    exit 1
}

if ($frontendStatus -ne "running") {
    Write-Host ""
    Write-Host "❌ Frontend container failed to start. Logs:" -ForegroundColor Red
    docker logs locql-frontend --tail 20
    exit 1
}

# Test API endpoint
Write-Host ""
Write-Host "9. Testing API endpoint..." -ForegroundColor Yellow
$maxAttempts = 30
$attempt = 1

while ($attempt -le $maxAttempts) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5000/api/health/ping" -TimeoutSec 2 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Backend API is responding!" -ForegroundColor Green
            break
        }
    } catch {
        # API not ready yet
    }
    
    Write-Host "." -NoNewline
    Start-Sleep -Seconds 2
    $attempt++
}

if ($attempt -gt $maxAttempts) {
    Write-Host ""
    Write-Host "❌ Backend API failed to respond within timeout" -ForegroundColor Red
    Write-Host "Backend logs:" -ForegroundColor Yellow
    docker logs locql-backend --tail 20
    exit 1
}

# Test frontend
Write-Host ""
Write-Host "10. Testing frontend..." -ForegroundColor Yellow
$attempt = 1

while ($attempt -le $maxAttempts) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5173/" -TimeoutSec 2 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Frontend is responding!" -ForegroundColor Green
            break
        }
    } catch {
        # Frontend not ready yet
    }
    
    Write-Host "." -NoNewline
    Start-Sleep -Seconds 2
    $attempt++
}

if ($attempt -gt $maxAttempts) {
    Write-Host ""
    Write-Host "❌ Frontend failed to respond within timeout" -ForegroundColor Red
    Write-Host "Frontend logs:" -ForegroundColor Yellow
    docker logs locql-frontend --tail 20
    exit 1
}

Write-Host ""
Write-Host "🎉 SUCCESS! Docker dependency issue has been resolved!" -ForegroundColor Green
Write-Host ""
Write-Host "Your application is now running:" -ForegroundColor Cyan
Write-Host "  • Frontend: http://localhost:5173" -ForegroundColor White
Write-Host "  • Backend API: http://localhost:5000" -ForegroundColor White
Write-Host "  • Health Check: http://localhost:5000/api/health/ping" -ForegroundColor White
Write-Host ""
Write-Host "To stop the application:" -ForegroundColor Yellow
Write-Host "  docker-compose -f docker-compose.local.yml down" -ForegroundColor White
Write-Host ""
Write-Host "To view logs:" -ForegroundColor Yellow
Write-Host "  docker-compose -f docker-compose.local.yml logs -f" -ForegroundColor White
