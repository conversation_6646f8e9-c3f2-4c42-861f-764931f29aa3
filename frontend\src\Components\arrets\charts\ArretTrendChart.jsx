import React, { memo } from 'react';
import { Spin } from 'antd';
import { Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip } from 'recharts';
import dayjs from 'dayjs';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const CHART_COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  secondary: SOMIPEM_COLORS.SECONDARY_BLUE,
  success: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to SOMIPEM Primary Blue
  warning: "#faad14",
  danger: "#f5222d",
  purple: SOMIPEM_COLORS.DARK_GRAY,
  pink: SOMIPEM_COLORS.LIGHT_GRAY,
  orange: SOMIPEM_COLORS.SECONDARY_BLUE,
  cyan: SOMIPEM_COLORS.SECONDARY_BLUE,
  lime: SOMIPEM_COLORS.PRIMARY_BLUE,
};

const ArretTrendChart = memo(({ data = [], loading }) => {
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" />
      </div>
    );
  }

  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);

  // Check if data is empty
  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
        <p style={{ color: '#999' }}>Aucune donnée disponible</p>
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={safeData} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="Stop_Date"
          tick={{ fill: "#666" }}
          tickFormatter={(date) => dayjs(date).format("DD/MM")}
          label={{
            value: "Date",
            position: "bottom",
            offset: 0,
            style: { fill: "#666" },
          }}
        />
        <YAxis
          label={{
            value: "Arrêts",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666" },
          }}
          tick={{ fill: "#666" }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #f0f0f0",
            borderRadius: 4,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
          }}
          formatter={(value) => [`${value} arrêts`, "Total"]}
        />
        <Line
          type="monotone"
          dataKey="Total_Stops"
          stroke={CHART_COLORS.primary}
          strokeWidth={2}
          dot={{ fill: CHART_COLORS.primary, strokeWidth: 2 }}
          activeDot={{ r: 6, fill: "#fff", stroke: CHART_COLORS.primary, strokeWidth: 2 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

export default ArretTrendChart;
