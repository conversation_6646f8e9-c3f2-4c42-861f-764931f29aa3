/**
 * Test Script: Default Data Sent to ArretLineChart.jsx
 * 
 * This script simulates the exact scenario when a user first loads the dashboard
 * and shows what data ArretLineChart.jsx receives by default.
 */

import { GraphQLClient } from 'graphql-request';

async function testDefaultDashboardScenario() {
  console.log('🎯 Testing Default ArretLineChart Data (Dashboard First Load)\n');

  const client = new GraphQLClient('http://localhost:5000/api/graphql');

  // Query that mimics the dashboard's default state
  const query = `
    query GetAllMachineStops($filters: StopFilterInput) {
      getAllMachineStops(filters: $filters) {
        Date_Insert
        Machine_Name
        Part_NO
        Code_Stop
        Debut_Stop
        Fin_Stop_Time
        Regleur_Prenom
        duration_minutes
      }
    }
  `;

  // Simulate dashboard default state: 
  // - First available machine model selected
  // - No specific machine selected
  // - No date filter (last 7 days of data)
  const defaultFilters = {
    model: "IPS", // Usually the first model available
    machine: null,
    date: null,
    startDate: null,
    endDate: null,
    dateRangeType: "month"
  };

  console.log('📋 Default Dashboard Scenario:');
  console.log('- Machine Model: "IPS" (first available)');
  console.log('- Machine: None selected');
  console.log('- Date Filter: None (shows all available data)');
  console.log('- Expected Result: All data for IPS machines\n');

  try {
    const result = await client.request(query, { filters: defaultFilters });
    const stopsData = result.getAllMachineStops || [];

    console.log('🔍 Backend Query Result:');
    console.log(`- Total stops found: ${stopsData.length}`);
    
    if (stopsData.length > 0) {
      console.log(`- Date range: ${stopsData[0]?.Date_Insert} to ${stopsData[stopsData.length - 1]?.Date_Insert}`);
      console.log(`- Available machines: ${[...new Set(stopsData.map(s => s.Machine_Name))].join(', ')}`);
    }

    // Process exactly like the dashboard does
    const chartData = processStopsDataForLineChart(stopsData);
    
    console.log('\n📊 Data Sent to ArretLineChart.jsx:');
    console.log(`- Chart data points: ${chartData.length}`);
    
    if (chartData.length > 0) {
      console.log('\n📈 Chart Data Preview:');
      chartData.forEach((point, index) => {
        console.log(`  ${index + 1}. ${point.displayDate}: ${point.stops} stops, ${point.duration}min total`);
      });
      
      console.log('\n📋 Complete Chart Data Object:');
      console.log(JSON.stringify(chartData, null, 2));
      
      // Analyze the data quality
      console.log('\n🔍 Data Analysis:');
      const totalStops = chartData.reduce((sum, day) => sum + day.stops, 0);
      const totalDuration = chartData.reduce((sum, day) => sum + day.duration, 0);
      const daysWithData = chartData.filter(day => day.stops > 0).length;
      
      console.log(`- Total stops across all days: ${totalStops}`);
      console.log(`- Total duration: ${totalDuration} minutes`);
      console.log(`- Days with stops: ${daysWithData}/${chartData.length}`);
      console.log(`- Average stops per day: ${(totalStops / chartData.length).toFixed(1)}`);
      
      if (totalDuration === 0) {
        console.log('⚠️ WARNING: All durations are 0 - this might indicate missing duration data');
      }
      
    } else {
      console.log('❌ No chart data generated!');
      console.log('\n🔍 Debugging empty chart data:');
      console.log(`- Raw stops received: ${stopsData.length}`);
      
      if (stopsData.length > 0) {
        const sampleStop = stopsData[0];
        console.log('- Sample stop structure:', {
          Date_Insert: sampleStop.Date_Insert,
          Machine_Name: sampleStop.Machine_Name,
          duration_minutes: sampleStop.duration_minutes
        });
        
        const uniqueDates = [...new Set(stopsData.map(stop => extractDate(stop.Date_Insert)))];
        console.log(`- Unique dates found: ${uniqueDates.length}`);
        console.log('- Date examples:', uniqueDates.slice(0, 5));
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

/**
 * Process stops data exactly like the dashboard does for the line chart
 */
function processStopsDataForLineChart(stopsData) {
  console.log('\n🔄 Processing stops data for line chart...');

  // Step 1: Group stops by date
  const dailyStats = {};
  
  stopsData.forEach(stop => {
    if (!stop.Date_Insert) return;
    
    const date = extractDate(stop.Date_Insert);
    if (!date) return;
    
    if (!dailyStats[date]) {
      dailyStats[date] = { date, stops: 0, duration: 0 };
    }
    
    dailyStats[date].stops++;
    
    if (stop.duration_minutes && stop.duration_minutes > 0) {
      dailyStats[date].duration += parseFloat(stop.duration_minutes);
    }
  });

  console.log(`📊 Generated daily stats for ${Object.keys(dailyStats).length} days`);

  // Step 2: Convert to array and sort by date
  let evolutionData = Object.values(dailyStats)
    .sort((a, b) => new Date(a.date) - new Date(b.date));

  // Step 3: Take all data (no longer limiting to 7 days)
  // evolutionData = evolutionData.slice(-7); // REMOVED: No longer limit to 7 days
  
  console.log(`📅 Using all available data: ${evolutionData.length} points`);

  // Step 4: Add display dates for the chart
  return evolutionData.map(item => {
    const displayDate = formatDateForDisplay(item.date);
    return {
      ...item,
      displayDate
    };
  });
}

/**
 * Extract date from the database format
 */
function extractDate(dateString) {
  if (!dateString) return null;
  
  const str = dateString.toString().trim();
  
  // Handle various date formats in the database
  
  // Format: " 3/12/2024 09:55:38" or "03/12/2024 09:55:38"
  const match1 = str.match(/^\s*(\d{1,2})\/(\d{1,2})\/(\d{4})/);
  if (match1) {
    const [_, day, month, year] = match1;
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  // Format: "2024-12-03" (ISO format)
  if (str.match(/^\d{4}-\d{2}-\d{2}/)) {
    return str.substring(0, 10);
  }
  
  // Format like: "2024 10:35:13-12- 3"
  const match2 = str.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);
  if (match2) {
    const [_, year, month, day] = match2;
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  console.warn('⚠️ Could not parse date:', str);
  return null;
}

/**
 * Format date for display in the chart
 */
function formatDateForDisplay(dateString) {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return dateString.slice(0, 10);
    }
    return date.toLocaleDateString('fr-FR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  } catch (error) {
    return dateString.slice(0, 10);
  }
}

// Run the test
testDefaultDashboardScenario().catch(console.error);
