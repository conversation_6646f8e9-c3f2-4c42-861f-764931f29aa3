/**
 * FINAL Optimized GraphQL resolvers for machine stop data
 * Single comprehensive query approach to minimize database calls and improve performance
 * This replaces multiple separate queries with one consolidated data fetch
 */

import { GraphQLObjectType, GraphQLString, GraphQLInt, GraphQLFloat, GraphQLList, GraphQLInputObjectType } from 'graphql';
import { executeQuery } from '../../utils/dbUtils.js';

// Helper function to handle machine filtering consistently
const addMachineFilters = (conditions, queryParams, filters) => {
  const { model, machine } = filters;

  if (model && !machine) {
    conditions.push(`Machine_Name LIKE ?`);
    queryParams.push(`${model}%`);
  } else if (machine) {
    conditions.push(`Machine_Name = ?`);
    queryParams.push(machine);
  }
  // Note: No default filter to allow viewing all data when no filter is specified
};

// Helper function to handle date range filtering
const addDateRangeFilter = (conditions, queryParams, filters) => {
  const { startDate, endDate } = filters;

  const parseDateColumn = `
    COALESCE(
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%d/%m/%Y %H:%i'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i:%s'), '%Y-%m-%d'),
      DATE_FORMAT(STR_TO_DATE(TRIM(Date_Insert), '%e/%m/%Y %H:%i'), '%Y-%m-%d')
    )
  `;

  if (startDate && endDate) {
    conditions.push(`${parseDateColumn} BETWEEN ? AND ?`);
    queryParams.push(startDate, endDate);
  }
};

// Comprehensive data type that contains all dashboard and analysis data
const ComprehensiveStopDataType = new GraphQLObjectType({
  name: 'FinalComprehensiveStopData',
  fields: {
    // Raw stop data
    allStops: { 
      type: new GraphQLList(new GraphQLObjectType({
        name: 'FinalOptimizedMachineStop',
        fields: {
          Machine_Name: { type: GraphQLString },
          Date_Insert: { type: GraphQLString },
          Part_NO: { type: GraphQLString },
          Code_Stop: { type: GraphQLString },
          Debut_Stop: { type: GraphQLString },
          Fin_Stop_Time: { type: GraphQLString },
          Regleur_Prenom: { type: GraphQLString },
          duration_minutes: { type: GraphQLInt },
          // Computed fields for frontend compatibility
          Cause: { 
            type: GraphQLString,
            resolve: (parent) => parent.Code_Stop || parent.Cause
          },
          Raison_Arret: { 
            type: GraphQLString,
            resolve: (parent) => parent.Code_Stop || parent.Raison_Arret
          },
          Operateur: { 
            type: GraphQLString,
            resolve: (parent) => parent.Regleur_Prenom || parent.Operateur
          }
        }
      }))
    },
    
    // Aggregated analytics data - matching frontend expectations
    topStops: { 
      type: new GraphQLList(new GraphQLObjectType({
        name: 'FinalOptimizedTopStop',
        fields: {
          stopName: { type: GraphQLString },
          count: { type: GraphQLInt }
        }
      }))
    },
    
    stopReasons: { 
      type: new GraphQLList(new GraphQLObjectType({
        name: 'FinalOptimizedStopReason',
        fields: {
          reason: { type: GraphQLString },
          count: { type: GraphQLInt }
        }
      }))
    },
    
    machineComparison: { 
      type: new GraphQLList(new GraphQLObjectType({
        name: 'FinalOptimizedMachineComparison',
        fields: {
          Machine_Name: { type: GraphQLString },
          stops: { type: GraphQLInt },
          totalDuration: { type: GraphQLFloat }
        }
      }))
    },
    
    operatorStats: { 
      type: new GraphQLList(new GraphQLObjectType({
        name: 'FinalOptimizedOperatorStats',
        fields: {
          operator: { type: GraphQLString },
          interventions: { type: GraphQLInt },
          totalDuration: { type: GraphQLFloat }
        }
      }))
    },
    
    durationTrend: { 
      type: new GraphQLList(new GraphQLObjectType({
        name: 'FinalOptimizedDurationTrend',
        fields: {
          hour: { type: GraphQLInt },
          avgDuration: { type: GraphQLFloat }
        }
      }))
    },
    
    stopStats: { 
      type: new GraphQLList(new GraphQLObjectType({
        name: 'FinalOptimizedStopStats',
        fields: {
          Stop_Date: { type: GraphQLString },
          Total_Stops: { type: GraphQLInt }
        }
      }))
    },
    
    // Summary statistics - matching frontend expectations
    sidecards: { 
      type: new GraphQLObjectType({
        name: 'FinalOptimizedSidecards',
        fields: {
          Arret_Totale: { type: GraphQLInt },
          Arret_Totale_nondeclare: { type: GraphQLInt }
        }
      })
    },
    
    // Metadata
    totalRecords: { type: GraphQLInt },
    queryExecutionTime: { type: GraphQLFloat },
    cacheHit: { type: GraphQLString }
  }
});

// Input type for filters
const FinalOptimizedStopFilterInputType = new GraphQLInputObjectType({
  name: 'FinalOptimizedStopFilterInput',
  fields: {
    date: { type: GraphQLString },
    startDate: { type: GraphQLString },
    endDate: { type: GraphQLString },
    dateRangeType: { type: GraphQLString },
    model: { type: GraphQLString },
    machine: { type: GraphQLString },
    limit: { type: GraphQLInt }
  }
});

// Main comprehensive resolver
const finalOptimizedStopResolvers = {
  getFinalComprehensiveStopData: {
    type: ComprehensiveStopDataType,
    args: {
      filters: { type: FinalOptimizedStopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      console.log('🚀 FINAL: getFinalComprehensiveStopData called with filters:', JSON.stringify(filters, null, 2));
      
      const startTime = Date.now();
      const limit = filters.limit || 1000;
      
      try {
        // Build query conditions
        const conditions = [];
        const queryParams = [];
        
        // Apply machine filtering
        addMachineFilters(conditions, queryParams, filters);
        
        // Apply date filtering
        if (filters.startDate && filters.endDate) {
          addDateRangeFilter(conditions, queryParams, filters);
        }
        
        const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        
        // Main data query
        const allStopsQuery = `
          SELECT 
            Machine_Name,
            Date_Insert,
            Part_NO,
            Code_Stop,
            Debut_Stop,
            Fin_Stop_Time,
            Regleur_Prenom,
            COALESCE(
              TIMESTAMPDIFF(MINUTE,
                STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
                STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
              ), 15
            ) AS duration_minutes
          FROM machine_stop_table_mould
          ${whereClause}
          ORDER BY Date_Insert DESC
          LIMIT ${limit}
        `;
        
        console.log('📊 FINAL: Executing main query with params:', queryParams);
        
        const { success, data, error } = await executeQuery(allStopsQuery, queryParams);
        
        if (!success) {
          console.error('❌ FINAL: Main query failed:', error);
          throw new Error(`Database query failed: ${error}`);
        }
        
        const allStops = data || [];
        
        // Now run aggregation queries on the same data set
        let topStops = [];
        let stopReasons = [];
        let machineComparison = [];
        let operatorStats = [];
        let durationTrend = [];
        let stopStats = [];
        let sidecards = { Arret_Totale: 0, Arret_Totale_nondeclare: 0 };
        
        // Execute aggregation queries in parallel
        if (allStops.length > 0) {
          console.log('📊 FINAL: Running aggregation queries...');
          
          const aggregationPromises = [
            // Top stops
            executeQuery(`
              SELECT 
                Code_Stop AS stopName,
                COUNT(*) AS count
              FROM machine_stop_table_mould
              ${whereClause}
              GROUP BY Code_Stop
              ORDER BY count DESC
              LIMIT 5
            `, queryParams),
            
            // Stop reasons (same as top stops for this use case)
            executeQuery(`
              SELECT 
                Code_Stop AS reason,
                COUNT(*) AS count
              FROM machine_stop_table_mould
              ${whereClause}
              GROUP BY Code_Stop
              ORDER BY count DESC
            `, queryParams),
            
            // Machine comparison
            executeQuery(`
              SELECT 
                Machine_Name,
                COUNT(*) AS stops,
                SUM(COALESCE(
                  TIMESTAMPDIFF(MINUTE,
                    STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
                    STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
                  ), 15
                )) AS totalDuration
              FROM machine_stop_table_mould
              ${whereClause}
              GROUP BY Machine_Name
              ORDER BY stops DESC
            `, queryParams),
            
            // Operator stats
            executeQuery(`
              SELECT 
                Regleur_Prenom AS operator,
                COUNT(*) AS interventions,
                SUM(COALESCE(
                  TIMESTAMPDIFF(MINUTE,
                    STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
                    STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
                  ), 15
                )) AS totalDuration
              FROM machine_stop_table_mould
              ${whereClause}
              AND Regleur_Prenom IS NOT NULL 
              AND Regleur_Prenom != ''
              GROUP BY Regleur_Prenom
              ORDER BY interventions DESC
            `, queryParams),
            
            // Duration trend by hour
            executeQuery(`
              SELECT 
                HOUR(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s')) AS hour,
                AVG(COALESCE(
                  TIMESTAMPDIFF(MINUTE,
                    STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i:%s'),
                    STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i:%s')
                  ), 15
                )) AS avgDuration
              FROM machine_stop_table_mould
              ${whereClause}
              GROUP BY hour
              ORDER BY hour
            `, queryParams),
            
            // Stop stats by date
            executeQuery(`
              SELECT 
                DATE_FORMAT(STR_TO_DATE(Date_Insert, '%d/%m/%Y %H:%i:%s'), '%Y-%m-%d') AS Stop_Date,
                COUNT(*) AS Total_Stops
              FROM machine_stop_table_mould
              ${whereClause}
              GROUP BY Stop_Date
              ORDER BY Stop_Date DESC
              LIMIT 30
            `, queryParams),
            
            // Sidecards - total stops
            executeQuery(`
              SELECT COUNT(*) AS total
              FROM machine_stop_table_mould
              ${whereClause}
            `, queryParams),
            
            // Sidecards - non-declared stops
            executeQuery(`
              SELECT COUNT(*) AS non_declared
              FROM machine_stop_table_mould
              ${whereClause}
              ${whereClause ? 'AND' : 'WHERE'} Code_Stop = 'Arrêt non déclaré'
            `, queryParams)
          ];
          
          const results = await Promise.all(aggregationPromises);
          
          // Process aggregation results
          if (results[0].success) topStops = results[0].data || [];
          if (results[1].success) stopReasons = results[1].data || [];
          if (results[2].success) machineComparison = results[2].data || [];
          if (results[3].success) operatorStats = results[3].data || [];
          if (results[4].success) durationTrend = results[4].data || [];
          if (results[5].success) stopStats = results[5].data || [];
          
          if (results[6].success && results[6].data[0]) {
            sidecards.Arret_Totale = results[6].data[0].total || 0;
          }
          if (results[7].success && results[7].data[0]) {
            sidecards.Arret_Totale_nondeclare = results[7].data[0].non_declared || 0;
          }
        }
        
        const executionTime = Date.now() - startTime;
        
        console.log(`✅ FINAL: Query completed in ${executionTime}ms`);
        console.log(`📊 FINAL: Retrieved ${allStops.length} stops, ${topStops.length} top stops, ${machineComparison.length} machine comparisons`);
        
        return {
          allStops,
          topStops,
          stopReasons,
          machineComparison,
          operatorStats,
          durationTrend,
          stopStats,
          sidecards,
          totalRecords: allStops.length,
          queryExecutionTime: executionTime,
          cacheHit: 'MISS'
        };
        
      } catch (error) {
        console.error('❌ FINAL: getFinalComprehensiveStopData error:', error);
        throw new Error(`Failed to fetch comprehensive stop data: ${error.message}`);
      }
    }
  },

  // Utility resolvers for machine models and names 
  getFinalStopMachineModels: {
    type: new GraphQLList(new GraphQLObjectType({
      name: 'FinalOptimizedMachineModel',
      fields: {
        model: { type: GraphQLString }
      }
    })),
    resolve: async () => {
      const query = `
        SELECT DISTINCT
          CASE
            WHEN Machine_Name REGEXP '^[A-Za-z]+[0-9]'
            THEN REGEXP_REPLACE(Machine_Name, '[0-9].*$', '')
            ELSE Machine_Name
          END AS model
        FROM machine_stop_table_mould
        WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
        ORDER BY model
      `;

      const { success, data, error } = await executeQuery(query);

      if (!success || !data || data.length === 0) {
        return [
          { model: 'IPS' },
          { model: 'AKROS' },
          { model: 'ML' },
          { model: 'FCS' }
        ];
      }

      return data;
    }
  },

  getFinalStopMachineNames: {
    type: new GraphQLList(new GraphQLObjectType({
      name: 'FinalOptimizedMachineName',
      fields: {
        Machine_Name: { type: GraphQLString }
      }
    })),
    args: {
      filters: { type: FinalOptimizedStopFilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const { model } = filters;
      
      let query = `
        SELECT DISTINCT Machine_Name
        FROM machine_stop_table_mould
        WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
      `;
      
      const queryParams = [];

      if (model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      }

      query += ` ORDER BY Machine_Name`;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success || !data || data.length === 0) {
        if (model === 'IPS') {
          return [
            { Machine_Name: 'IPS1' },
            { Machine_Name: 'IPS2' },
            { Machine_Name: 'IPS3' }
          ];
        } else if (model === 'AKROS') {
          return [
            { Machine_Name: 'AKROS1' },
            { Machine_Name: 'AKROS2' }
          ];
        } else if (model === 'ML') {
          return [
            { Machine_Name: 'ML1' },
            { Machine_Name: 'ML2' }
          ];
        } else if (model === 'FCS') {
          return [
            { Machine_Name: 'FCS1' },
            { Machine_Name: 'FCS2' }
          ];
        }
        return [];
      }

      return data;
    }
  }
};

// Export everything needed for schema integration
export const finalOptimizedStopTypes = {
  ComprehensiveStopDataType,
  FinalOptimizedStopFilterInputType
};

export const finalOptimizedStopQueries = finalOptimizedStopResolvers;

export default finalOptimizedStopResolvers;
