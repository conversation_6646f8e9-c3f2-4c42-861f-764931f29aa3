// Test script to verify that chart filtering is working properly
// This script will test the ArretContext to ensure filtered data is being applied to charts

// Mock test data
const mockStopsData = [
  {
    Machine_Name: 'IPS01',
    Date_Insert: '01/05/2025 10:00',
    Code_Stop: 'Machine OFF',
    duration_minutes: '30'
  },
  {
    Machine_Name: 'IPS01',
    Date_Insert: '02/05/2025 14:00',
    Code_Stop: 'Setup',
    duration_minutes: '45'
  },
  {
    Machine_Name: 'IPS02',
    Date_Insert: '01/05/2025 16:00',
    Code_Stop: 'Machine OFF',
    duration_minutes: '20'
  },
  {
    Machine_Name: 'IPS02',
    Date_Insert: '03/05/2025 09:00',
    Code_Stop: 'Maintenance',
    duration_minutes: '60'
  }
];

// Function to simulate the filtered data computation
function computeFilteredData(stopsData, dateFilterActive, selectedDate) {
  console.log('🧪 Testing chart data filtering...');
  console.log('📊 Input data:', stopsData.length, 'stops');
  console.log('🔍 Date filter active:', dateFilterActive);
  console.log('📅 Selected date:', selectedDate);
  
  if (!dateFilterActive || !selectedDate) {
    console.log('❌ No date filter active - using all data');
    return {
      topStopsData: [],
      chartData: [],
      stopReasons: [],
      machineComparison: []
    };
  }
  
  // Filter data based on selected date
  const filteredStops = stopsData.filter(stop => {
    const stopDate = stop.Date_Insert.split(' ')[0]; // Get date part
    const [day, month, year] = stopDate.split('/');
    const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    
    return formattedDate === selectedDate;
  });
  
  console.log('📊 Filtered stops:', filteredStops.length);
  
  // Compute machine comparison from filtered data
  const machineStatsMap = new Map();
  filteredStops.forEach(stop => {
    const machineName = stop.Machine_Name;
    const duration = parseFloat(stop.duration_minutes) || 0;
    
    if (machineStatsMap.has(machineName)) {
      const existing = machineStatsMap.get(machineName);
      existing.stops += 1;
      existing.totalDuration += duration;
    } else {
      machineStatsMap.set(machineName, {
        machine: machineName,
        stops: 1,
        totalDuration: duration
      });
    }
  });
  
  const machineComparison = Array.from(machineStatsMap.values());
  
  // Compute top stops from filtered data
  const stopReasonsMap = new Map();
  filteredStops.forEach(stop => {
    const reason = stop.Code_Stop;
    const duration = parseFloat(stop.duration_minutes) || 0;
    
    if (stopReasonsMap.has(reason)) {
      const existing = stopReasonsMap.get(reason);
      existing.count += 1;
      existing.duration += duration;
    } else {
      stopReasonsMap.set(reason, {
        reason,
        count: 1,
        duration,
        percentage: 0
      });
    }
  });
  
  const totalDuration = Array.from(stopReasonsMap.values()).reduce((sum, item) => sum + item.duration, 0);
  const topStopsData = Array.from(stopReasonsMap.values())
    .map(item => ({
      ...item,
      percentage: totalDuration > 0 ? (item.duration / totalDuration) * 100 : 0
    }))
    .sort((a, b) => b.duration - a.duration)
    .slice(0, 5);
  
  console.log('✅ Machine comparison:', machineComparison);
  console.log('✅ Top stops:', topStopsData);
  
  return {
    topStopsData,
    chartData: filteredStops,
    stopReasons: Array.from(stopReasonsMap.values()),
    machineComparison
  };
}

// Test scenarios
console.log('🧪 Test 1: No date filter');
const result1 = computeFilteredData(mockStopsData, false, null);
console.log('Result:', result1);

console.log('\n🧪 Test 2: Date filter for 2025-05-01');
const result2 = computeFilteredData(mockStopsData, true, '2025-05-01');
console.log('Result:', result2);

console.log('\n🧪 Test 3: Date filter for 2025-05-02');
const result3 = computeFilteredData(mockStopsData, true, '2025-05-02');
console.log('Result:', result3);

console.log('\n🧪 Test 4: Date filter for non-existent date');
const result4 = computeFilteredData(mockStopsData, true, '2025-05-05');
console.log('Result:', result4);

console.log('\n✅ All tests completed!');
