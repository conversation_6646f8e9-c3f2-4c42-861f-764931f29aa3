// Test backend endpoints
import express from 'express';

const app = express();

// Import routes
import shiftReportRoutes from './routes/shiftReportRoutes.js';

// Mount routes
app.use('/api/shift-reports', shiftReportRoutes);

// List all routes
function printRoutes(app) {
  const routes = [];
  
  app._router.stack.forEach(function(middleware) {
    if (middleware.route) {
      // Routes registered directly on the app
      routes.push({
        method: Object.keys(middleware.route.methods)[0].toUpperCase(),
        path: middleware.route.path
      });
    } else if (middleware.name === 'router') {
      // Router middleware
      middleware.handle.stack.forEach(function(handler) {
        if (handler.route) {
          routes.push({
            method: Object.keys(handler.route.methods)[0].toUpperCase(),
            path: middleware.regexp.source.replace('\\/?', '').replace('(?=\\/|$)', '') + handler.route.path
          });
        }
      });
    }
  });
  
  return routes;
}

const routes = printRoutes(app);
console.log('Available routes:');
routes.forEach(route => {
  console.log(`${route.method} ${route.path}`);
});

// Check specifically for generate-enhanced
const enhancedRoute = routes.find(r => r.path.includes('generate-enhanced'));
if (enhancedRoute) {
  console.log('✅ Enhanced route found:', enhancedRoute);
} else {
  console.log('❌ Enhanced route NOT found');
}
