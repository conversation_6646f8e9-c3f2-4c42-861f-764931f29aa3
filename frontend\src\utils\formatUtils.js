/**
 * Utilities for consistent data formatting
 * @module formatUtils
 */

import { format as d3Format } from 'd3-format';

/**
 * Format a number as percentage
 * @param {number} value - Value to format
 * @param {number} [precision=1] - Decimal precision
 * @param {boolean} [includeSymbol=true] - Whether to include % symbol
 * @returns {string} Formatted percentage
 */
export const formatPercent = (value, precision = 1, includeSymbol = true) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '-';
  }
  
  const formatter = d3Format(`.${precision}${includeSymbol ? '%' : 'f'}`);
  
  try {
    // If includeSymbol is false, multiply by 100 to convert to percentage
    const formattedValue = includeSymbol ? formatter(value / 100) : formatter(value);
    return formattedValue;
  } catch (error) {
    console.error('Error formatting percentage:', error);
    return '-';
  }
};

/**
 * Format a number with thousands separators
 * @param {number} value - Value to format
 * @param {number} [precision=0] - Decimal precision
 * @param {string} [locale='fr-FR'] - Locale for formatting
 * @returns {string} Formatted number
 */
export const formatNumber = (value, precision = 0, locale = 'fr-FR') => {
  if (value === null || value === undefined || isNaN(value)) {
    return '-';
  }
  
  try {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    }).format(value);
  } catch (error) {
    console.error('Error formatting number:', error);
    return '-';
  }
};

/**
 * Format a number as currency
 * @param {number} value - Value to format
 * @param {string} [currency='EUR'] - Currency code
 * @param {number} [precision=2] - Decimal precision
 * @param {string} [locale='fr-FR'] - Locale for formatting
 * @returns {string} Formatted currency
 */
export const formatCurrency = (value, currency = 'EUR', precision = 2, locale = 'fr-FR') => {
  if (value === null || value === undefined || isNaN(value)) {
    return '-';
  }
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    }).format(value);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return '-';
  }
};

/**
 * Format a number with a specific unit
 * @param {number} value - Value to format
 * @param {string} unit - Unit to append
 * @param {number} [precision=1] - Decimal precision
 * @param {string} [locale='fr-FR'] - Locale for formatting
 * @returns {string} Formatted number with unit
 */
export const formatWithUnit = (value, unit, precision = 1, locale = 'fr-FR') => {
  if (value === null || value === undefined || isNaN(value)) {
    return '-';
  }
  
  try {
    const formattedNumber = formatNumber(value, precision, locale);
    return `${formattedNumber} ${unit}`;
  } catch (error) {
    console.error('Error formatting with unit:', error);
    return '-';
  }
};

/**
 * Format a duration in seconds to a human-readable format
 * @param {number} seconds - Duration in seconds
 * @param {boolean} [showSeconds=true] - Whether to show seconds
 * @returns {string} Formatted duration
 */
export const formatDuration = (seconds, showSeconds = true) => {
  if (seconds === null || seconds === undefined || isNaN(seconds)) {
    return '-';
  }
  
  try {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    
    let result = '';
    
    if (hours > 0) {
      result += `${hours}h `;
    }
    
    if (minutes > 0 || hours > 0) {
      result += `${minutes}min `;
    }
    
    if (showSeconds && (remainingSeconds > 0 || (hours === 0 && minutes === 0))) {
      result += `${remainingSeconds}s`;
    }
    
    return result.trim();
  } catch (error) {
    console.error('Error formatting duration:', error);
    return '-';
  }
};

/**
 * Format a file size in bytes to a human-readable format
 * @param {number} bytes - Size in bytes
 * @param {number} [precision=1] - Decimal precision
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes, precision = 1) => {
  if (bytes === null || bytes === undefined || isNaN(bytes)) {
    return '-';
  }
  
  try {
    const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${formatNumber(size, precision)} ${units[unitIndex]}`;
  } catch (error) {
    console.error('Error formatting file size:', error);
    return '-';
  }
};

/**
 * Truncate text to a maximum length
 * @param {string} text - Text to truncate
 * @param {number} [maxLength=50] - Maximum length
 * @param {string} [ellipsis='...'] - Ellipsis to append
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength = 50, ellipsis = '...') => {
  if (!text) return '';
  
  try {
    if (text.length <= maxLength) {
      return text;
    }
    
    return `${text.substring(0, maxLength - ellipsis.length)}${ellipsis}`;
  } catch (error) {
    console.error('Error truncating text:', error);
    return text || '';
  }
};

/**
 * Format a phone number
 * @param {string} phoneNumber - Phone number to format
 * @param {string} [format='XX XX XX XX XX'] - Format pattern (X will be replaced)
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber, format = 'XX XX XX XX XX') => {
  if (!phoneNumber) return '';
  
  try {
    // Remove non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');
    
    // Replace X in format with digits
    let result = format;
    let digitIndex = 0;
    
    for (let i = 0; i < result.length; i++) {
      if (result[i] === 'X') {
        if (digitIndex < digits.length) {
          result = result.substring(0, i) + digits[digitIndex] + result.substring(i + 1);
          digitIndex++;
        } else {
          result = result.substring(0, i) + ' ' + result.substring(i + 1);
        }
      }
    }
    
    return result.trim();
  } catch (error) {
    console.error('Error formatting phone number:', error);
    return phoneNumber || '';
  }
};

/**
 * Capitalize first letter of a string
 * @param {string} text - Text to capitalize
 * @returns {string} Capitalized text
 */
export const capitalizeFirstLetter = (text) => {
  if (!text) return '';
  
  try {
    return text.charAt(0).toUpperCase() + text.slice(1);
  } catch (error) {
    console.error('Error capitalizing text:', error);
    return text || '';
  }
};

/**
 * Format a value based on its type
 * @param {*} value - Value to format
 * @param {string} type - Value type ('number', 'percent', 'currency', 'date', etc.)
 * @param {Object} [options={}] - Formatting options
 * @returns {string} Formatted value
 */
export const formatValue = (value, type, options = {}) => {
  if (value === null || value === undefined) {
    return options.placeholder || '-';
  }
  
  try {
    switch (type) {
      case 'number':
        return formatNumber(value, options.precision, options.locale);
        
      case 'percent':
        return formatPercent(value, options.precision, options.includeSymbol);
        
      case 'currency':
        return formatCurrency(value, options.currency, options.precision, options.locale);
        
      case 'unit':
        return formatWithUnit(value, options.unit, options.precision, options.locale);
        
      case 'duration':
        return formatDuration(value, options.showSeconds);
        
      case 'fileSize':
        return formatFileSize(value, options.precision);
        
      case 'text':
        return options.truncate 
          ? truncateText(value, options.maxLength, options.ellipsis)
          : String(value);
          
      case 'phone':
        return formatPhoneNumber(value, options.format);
        
      default:
        return String(value);
    }
  } catch (error) {
    console.error(`Error formatting value as ${type}:`, error);
    return String(value);
  }
};

export default {
  formatPercent,
  formatNumber,
  formatCurrency,
  formatWithUnit,
  formatDuration,
  formatFileSize,
  truncateText,
  formatPhoneNumber,
  capitalizeFirstLetter,
  formatValue,
};