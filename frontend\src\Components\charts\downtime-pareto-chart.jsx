"use client"

import React from "react"
import {
  <PERSON>sponsive<PERSON>ontainer,
  ComposedChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Cell,
} from "recharts"
import { Typography, Empty } from "antd"

const { Text } = Typography

// Chart colors
const CHART_COLORS = {
  primary: "#1890ff",
  secondary: "#13c2c2",
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
  purple: "#722ed1",
  pink: "#eb2f96",
  orange: "#fa8c16",
  cyan: "#13c2c2",
  lime: "#a0d911",
}

// Color array for bars
const COLORS = ["#1890ff", "#13c2c2", "#52c41a", "#faad14", "#f5222d", "#722ed1", "#eb2f96"]

// Helper function to format percentage values
const formatPercentage = (value) => {
  if (value === undefined || value === null) return "N/A";

  // Convert to number
  let numValue = Number(value);

  // Check if value is between 0 and 1, and convert to percentage if needed
  if (!isNaN(numValue) && numValue > 0 && numValue < 1) {
    numValue = numValue * 100;
  }

  return `${numValue.toFixed(1)}%`;
};

const DowntimeParetoChart = ({
  data = [],
  selectedMachine = "",
  selectedDate = null,
  dateRangeType = "day",
  loading = false,
}) => {
  // Process data to calculate cumulative percentages
  const processedData = React.useMemo(() => {
    // Check if data is an array and has items
    if (!data || !Array.isArray(data) || data.length === 0) return []

    try {
      // Sort data by duration in descending order
      const sortedData = [...data].sort((a, b) => b.duration - a.duration)

      // Calculate total duration
      const totalDuration = sortedData.reduce((sum, item) => sum + item.duration, 0)

      // Calculate cumulative percentages
      let cumulativeSum = 0
      return sortedData.map((item, index) => {
        cumulativeSum += item.duration
        const percentage = (cumulativeSum / totalDuration);
        return {
          ...item,
          // Store the raw percentage value (0-1) - it will be formatted for display later
          cumulativePercentage: percentage,
        }
      })
    } catch (error) {
      console.error("Error processing downtime pareto data:", error)
      return []
    }
  }, [data])

  // Custom tooltip to show detailed information
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || !payload.length) return null

    const item = payload[0].payload

    return (
      <div
        style={{
          backgroundColor: "#fff",
          padding: "10px",
          border: "1px solid #ccc",
          borderRadius: "4px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
      >
        <Text strong>{item.reason}</Text>
        <div>
          <Text>Durée: {item.duration} min</Text>
        </div>
        <div>
          <Text>Fréquence: {item.count} arrêts</Text>
        </div>
        <div>
          <Text>Impact cumulé: {formatPercentage(item.cumulativePercentage)}</Text>
        </div>
      </div>
    )
  }

  // Format x-axis labels to handle long text
  const formatXAxis = (value) => {
    if (!value) return ""
    if (value.length > 15) {
      return `${value.substring(0, 12)}...`
    }
    return value
  }

  if (!processedData || processedData.length === 0) {
    return <Empty description="Aucune donnée disponible pour l'analyse Pareto" />
  }

  // Filter to show only top causes (80/20 rule) or max 10 items
  const paretoThreshold = 0.8 // Show causes up to 80% cumulative impact (as decimal)

  // Process data for display - convert decimal percentages to actual percentages for the chart
  const displayData = processedData
    .filter((item) => item.cumulativePercentage <= paretoThreshold)
    .slice(0, 10)
    .map(item => ({
      ...item,
      // Convert the cumulative percentage from decimal to percentage for the chart
      cumulativePercentage: item.cumulativePercentage * 100
    }))

  return (
    <ResponsiveContainer width="100%" height={350}>
      <ComposedChart data={displayData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="reason"
          tickFormatter={formatXAxis}
          tick={{ fill: "#666", angle: -45, textAnchor: "end" }}
          height={70}
          interval={0}
        />
        <YAxis
          yAxisId="left"
          label={{
            value: "Durée d'arrêt (min)",
            angle: -90,
            position: "insideLeft",
            style: { fill: "#666" },
          }}
          tick={{ fill: "#666" }}
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          domain={[0, 100]}
          tickFormatter={(value) => `${value}%`}
          label={{
            value: "Pourcentage cumulé",
            angle: 90,
            position: "insideRight",
            style: { fill: CHART_COLORS.danger },
          }}
          tick={{ fill: CHART_COLORS.danger }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar yAxisId="left" dataKey="duration" name="Durée d'arrêt" barSize={30} isAnimationActive={!loading}>
          {displayData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Bar>
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="cumulativePercentage"
          name="Pourcentage cumulé"
          stroke={CHART_COLORS.danger}
          strokeWidth={2}
          dot={{ fill: CHART_COLORS.danger, strokeWidth: 2, r: 4 }}
          isAnimationActive={!loading}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default React.memo(DowntimeParetoChart)
