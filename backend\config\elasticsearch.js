import { Client } from '@elastic/elasticsearch';

// Elasticsearch configuration
const elasticsearchConfig = {
  node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
  // Remove auth for local development with security disabled
  // auth: {
  //   username: process.env.ELASTICSEARCH_USERNAME || 'elastic',
  //   password: process.env.ELASTICSEARCH_PASSWORD || 'changeme'
  // },
  requestTimeout: 60000,
  pingTimeout: 3000,
  maxRetries: 3,
  // Add compatibility mode for Elasticsearch 8.x
  headers: {
    'Accept': 'application/vnd.elasticsearch+json; compatible-with=8',
    'Content-Type': 'application/vnd.elasticsearch+json; compatible-with=8'
  }
};

// Create Elasticsearch client
const esClient = new Client(elasticsearchConfig);

// Index configurations for different data types
const indexConfigurations = {
  // Machine sessions index for historical data
  'machine-sessions': {
    settings: {
      number_of_shards: 1,
      number_of_replicas: 0,
      refresh_interval: '30s',
      'index.mapping.total_fields.limit': 2000
    },
    mappings: {
      properties: {
        sessionId: { type: 'keyword' },
        machineId: { type: 'keyword' },
        machineName: { type: 'text', analyzer: 'standard' },
        machineModel: { type: 'keyword' },
        timestamp: { type: 'date' },
        startTime: { type: 'date' },
        endTime: { type: 'date' },
        duration: { type: 'integer' },
        status: { type: 'keyword' },
        production: {
          properties: {
            total: { type: 'integer' },
            rate: { type: 'float' },
            target: { type: 'integer' },
            efficiency: { type: 'float' }
          }
        },
        quality: {
          properties: {
            goodParts: { type: 'integer' },
            defects: { type: 'integer' },
            rejects: { type: 'integer' },
            qualityRate: { type: 'float' }
          }
        },
        performance: {
          properties: {
            trs: { type: 'float' },
            availability: { type: 'float' },
            performance: { type: 'float' },
            quality: { type: 'float' }
          }
        },
        shift: { type: 'keyword' },
        operator: { type: 'text', analyzer: 'standard' },
        notes: { type: 'text', analyzer: 'standard' }
      }
    }
  },

  // Real-time machine data index
  'machine-realtime': {
    settings: {
      number_of_shards: 1,
      number_of_replicas: 0,
      refresh_interval: '5s'
    },
    mappings: {
      properties: {
        machineId: { type: 'keyword' },
        machineName: { type: 'text', analyzer: 'standard' },
        machineModel: { type: 'keyword' },
        timestamp: { type: 'date' },
        status: { type: 'keyword' },
        currentProduction: { type: 'integer' },
        productionRate: { type: 'float' },
        trs: { type: 'float' },
        alerts: {
          properties: {
            active: { type: 'boolean' },
            count: { type: 'integer' },
            severity: { type: 'keyword' },
            messages: { type: 'text', analyzer: 'standard' }
          }
        },
        maintenance: {
          properties: {
            required: { type: 'boolean' },
            lastMaintenance: { type: 'date' },
            nextMaintenance: { type: 'date' }
          }
        }
      }
    }
  },

  // Reports index for search functionality
  'reports': {
    settings: {
      number_of_shards: 1,
      number_of_replicas: 0,
      refresh_interval: '30s'
    },
    mappings: {
      properties: {
        reportId: { type: 'keyword' },
        type: { type: 'keyword' },
        title: { type: 'text', analyzer: 'standard' },
        description: { type: 'text', analyzer: 'standard' },
        content: { type: 'text', analyzer: 'standard' },
        date: { type: 'date' },
        shift: { type: 'keyword' },
        machineId: { type: 'keyword' },
        machineName: { type: 'text', analyzer: 'standard' },
        status: { type: 'keyword' },
        generatedAt: { type: 'date' },
        generatedBy: { type: 'text', analyzer: 'standard' },
        tags: { type: 'keyword' },
        data: { type: 'object', enabled: false } // Store as raw JSON
      }
    }
  },

  // Production data index for daily performance metrics
  'production-data': {
    settings: {
      number_of_shards: 1,
      number_of_replicas: 0,
      refresh_interval: '30s'
    },
    mappings: {
      properties: {
        recordId: { type: 'keyword' },
        machineId: { type: 'keyword' },
        machineName: { type: 'text', analyzer: 'standard' },
        machineModel: { type: 'keyword' },
        date: { type: 'date' },
        shift: { type: 'keyword' },
        operator: { type: 'text', analyzer: 'standard' },
        production: {
          properties: {
            good: { type: 'integer' },
            rejects: { type: 'integer' },
            total: { type: 'integer' },
            target: { type: 'integer' },
            rate: { type: 'float' }
          }
        },
        performance: {
          properties: {
            oee: { type: 'float' },
            availability: { type: 'float' },
            performance: { type: 'float' },
            quality: { type: 'float' },
            trs: { type: 'float' }
          }
        },
        timing: {
          properties: {
            runHours: { type: 'float' },
            downHours: { type: 'float' },
            speed: { type: 'float' }
          }
        },
        partInfo: {
          properties: {
            partNumber: { type: 'keyword' },
            mouldNumber: { type: 'keyword' },
            unitWeight: { type: 'float' },
            theoreticalCycle: { type: 'float' },
            purgeWeight: { type: 'float' }
          }
        },
        orderInfo: {
          properties: {
            orderNumber: { type: 'keyword' },
            article: { type: 'text', analyzer: 'standard' },
            plannedQuantity: { type: 'integer' }
          }
        }
      }
    }
  },

  // Machine stops/downtime index
  'machine-stops': {
    settings: {
      number_of_shards: 1,
      number_of_replicas: 0,
      refresh_interval: '30s'
    },
    mappings: {
      properties: {
        stopId: { type: 'keyword' },
        machineId: { type: 'keyword' },
        machineName: { type: 'text', analyzer: 'standard' },
        machineModel: { type: 'keyword' },
        stopCode: { type: 'keyword' },
        stopDescription: { type: 'text', analyzer: 'standard' },
        stopCategory: { type: 'keyword' },
        severity: { type: 'keyword' },
        dateInsert: { type: 'date' },
        startTime: { type: 'date' },
        endTime: { type: 'date' },
        duration: { type: 'integer' }, // in minutes
        operator: { type: 'text', analyzer: 'standard' },
        shift: { type: 'keyword' },
        partNumber: { type: 'keyword' },
        orderNumber: { type: 'keyword' },
        resolution: {
          properties: {
            resolved: { type: 'boolean' },
            resolvedBy: { type: 'text', analyzer: 'standard' },
            resolvedAt: { type: 'date' },
            notes: { type: 'text', analyzer: 'standard' }
          }
        },
        maintenance: {
          properties: {
            required: { type: 'boolean' },
            type: { type: 'keyword' },
            priority: { type: 'keyword' }
          }
        }
      }
    }
  },

  // Maintenance logs index (future implementation)
  'maintenance-logs': {
    settings: {
      number_of_shards: 1,
      number_of_replicas: 0,
      refresh_interval: '30s'
    },
    mappings: {
      properties: {
        logId: { type: 'keyword' },
        machineId: { type: 'keyword' },
        machineName: { type: 'text', analyzer: 'standard' },
        taskId: { type: 'keyword' },
        taskType: { type: 'keyword' },
        description: { type: 'text', analyzer: 'standard' },
        timestamp: { type: 'date' },
        duration: { type: 'integer' },
        technician: { type: 'text', analyzer: 'standard' },
        status: { type: 'keyword' },
        notes: { type: 'text', analyzer: 'standard' },
        parts: {
          properties: {
            used: { type: 'text', analyzer: 'standard' },
            cost: { type: 'float' }
          }
        }
      }
    }
  }
};

// Health check function
const checkElasticsearchHealth = async () => {
  try {
    const health = await esClient.cluster.health();
    console.log('Elasticsearch cluster health:', health.status);
    return health.status !== 'red';
  } catch (error) {
    console.error('Elasticsearch health check failed:', error.message);
    return false;
  }
};

// Initialize indices
const initializeIndices = async () => {
  try {
    for (const [indexName, config] of Object.entries(indexConfigurations)) {
      const indexExists = await esClient.indices.exists({ index: indexName });

      if (!indexExists) {
        await esClient.indices.create({
          index: indexName,
          ...config  // Spread config directly instead of using body
        });
        console.log(`Created Elasticsearch index: ${indexName}`);
      } else {
        console.log(`Elasticsearch index already exists: ${indexName}`);
      }
    }
  } catch (error) {
    console.error('Error initializing Elasticsearch indices:', error);
    throw error;
  }
};

export {
  esClient,
  indexConfigurations,
  checkElasticsearchHealth,
  initializeIndices
};
