/**
 * Migration Runner for 003_extend_reports_version_column.sql
 * Fixes the "Data too long for column 'version'" error in PDF generation
 */

import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || 'root',
  database: process.env.DB_NAME || 'Testingarea51',
  multipleStatements: true // Required for running multiple SQL statements
};

async function runMigration() {
  let connection;
  
  try {
    console.log('🚀 Starting Migration 003: Extend reports version column');
    console.log('=====================================\n');

    // Connect to database
    console.log('📡 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection established\n');

    // Check current table structure
    console.log('🔍 Checking current reports table structure...');
    const [currentSchema] = await connection.execute(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        CHARACTER_MAXIMUM_LENGTH,
        COLUMN_DEFAULT,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'reports' 
        AND COLUMN_NAME = 'version'
    `);

    if (currentSchema.length === 0) {
      console.log('❌ Reports table or version column not found');
      console.log('💡 This might be a new installation. Creating table with correct schema...');
      
      // If table doesn't exist, we'll let the application create it with the correct schema
      console.log('⚠️ Please ensure the application code creates the table with VARCHAR(50) for version column');
      return;
    }

    const currentColumn = currentSchema[0];
    console.log('📊 Current version column:', {
      type: currentColumn.DATA_TYPE,
      maxLength: currentColumn.CHARACTER_MAXIMUM_LENGTH,
      default: currentColumn.COLUMN_DEFAULT,
      nullable: currentColumn.IS_NULLABLE
    });

    // Check if migration is needed
    if (currentColumn.CHARACTER_MAXIMUM_LENGTH >= 50) {
      console.log('✅ Version column already has sufficient length');
      console.log('🎯 Migration not needed - column can already store "enhanced-react"');
      return;
    }

    // Check for existing data that might be affected
    console.log('\n📋 Checking existing reports data...');
    const [existingReports] = await connection.execute(`
      SELECT 
        COUNT(*) as total_reports,
        COUNT(DISTINCT version) as unique_versions,
        GROUP_CONCAT(DISTINCT version) as current_versions
      FROM reports
    `);

    const reportStats = existingReports[0];
    console.log('📊 Existing reports:', {
      total: reportStats.total_reports,
      uniqueVersions: reportStats.unique_versions,
      versions: reportStats.current_versions
    });

    // Read and execute migration SQL
    console.log('\n🔧 Executing migration...');
    const migrationPath = path.join(__dirname, '../migrations/003_extend_reports_version_column.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split SQL into individual statements (excluding comments and empty lines)
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    let executedStatements = 0;
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement);
          executedStatements++;
        } catch (error) {
          // Some statements might fail (like CREATE INDEX if it already exists)
          // Log but continue with migration
          if (!error.message.includes('Duplicate key name')) {
            console.log(`⚠️ Statement warning: ${error.message}`);
          }
        }
      }
    }

    console.log(`✅ Migration executed (${executedStatements} statements processed)`);

    // Verify the migration was successful
    console.log('\n🔍 Verifying migration results...');
    const [newSchema] = await connection.execute(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        CHARACTER_MAXIMUM_LENGTH,
        COLUMN_DEFAULT,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'reports' 
        AND COLUMN_NAME = 'version'
    `);

    const newColumn = newSchema[0];
    console.log('📊 Updated version column:', {
      type: newColumn.DATA_TYPE,
      maxLength: newColumn.CHARACTER_MAXIMUM_LENGTH,
      default: newColumn.COLUMN_DEFAULT,
      nullable: newColumn.IS_NULLABLE
    });

    // Test that we can now insert the new version identifier
    console.log('\n🧪 Testing enhanced-react version insertion...');
    try {
      await connection.execute('START TRANSACTION');
      
      await connection.execute(`
        INSERT INTO reports (
          type, title, description, date, status, generated_at, version
        ) VALUES (
          'test', 'Migration Test', 'Testing enhanced-react version', CURDATE(), 
          'completed', NOW(), 'enhanced-react'
        )
      `);
      
      console.log('✅ Test insertion successful - enhanced-react version can be stored');
      
      await connection.execute('ROLLBACK');
      console.log('🔄 Test data rolled back');
      
    } catch (error) {
      await connection.execute('ROLLBACK');
      console.error('❌ Test insertion failed:', error.message);
      throw error;
    }

    // Show final statistics
    console.log('\n📈 Final migration summary:');
    const [finalStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_reports,
        COUNT(DISTINCT version) as unique_versions,
        GROUP_CONCAT(DISTINCT version ORDER BY version) as all_versions
      FROM reports
    `);

    const final = finalStats[0];
    console.log('📊 Reports table status:', {
      totalReports: final.total_reports,
      uniqueVersions: final.unique_versions,
      supportedVersions: final.all_versions
    });

    console.log('\n🎉 Migration 003 completed successfully!');
    console.log('✅ The reports.version column now supports "enhanced-react" identifier');
    console.log('🚀 PDF generation system should now work without database errors');

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error('📋 Error details:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n📡 Database connection closed');
    }
  }
}

// Load environment variables
if (fs.existsSync(path.join(__dirname, '../config.env'))) {
  const envContent = fs.readFileSync(path.join(__dirname, '../config.env'), 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
}

// Run migration
runMigration().catch(error => {
  console.error('❌ Migration runner failed:', error);
  process.exit(1);
});
