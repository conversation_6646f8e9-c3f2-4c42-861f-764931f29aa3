/**
 * User management routes
 */
import express from 'express';
import auth from '../middleware/auth.js';
import { checkPermission, filterByDepartment } from '../middleware/permission.js';
import { corsOptions } from '../middleware/cors.js';
import cors from 'cors';
import bcrypt from 'bcrypt';
import { executeQuery } from '../utils/dbUtils.js';
import { sendSuccess, sendError, asyncHandler } from '../utils/responseUtils.js';

const router = express.Router();

/**
 * @route   GET api/users
 * @desc    Get all users
 * @access  Private (with manage_users permission)
 */
router.get('/users', cors(corsOptions), auth, checkPermission(['manage_users', 'view_users']), asyncHandler(async (_, res) => {
  const query = `
    SELECT u.id, u.username, u.email, u.role, u.department_id,
           r.name as role_name, d.name as department_name, u.created_at
    FROM users u
    LEFT JOIN roles r ON u.role_id = r.id
    LEFT JOIN departments d ON u.department_id = d.id
    ORDER BY u.created_at DESC
  `;

  const { success, data, error } = await executeQuery(query);

  if (!success) {
    return sendError(res, 'Erreur serveur', 500, error);
  }

  return sendSuccess(res, data);
}));

/**
 * @route   GET api/users/department
 * @desc    Get users filtered by user's department access
 * @access  Private (with view_department_users permission)
 */
router.get('/users/department', cors(corsOptions), auth, filterByDepartment(), asyncHandler(async (req, res) => {
  // If no department filter is applied (admin with view_all_departments)
  if (!req.departmentFilter || req.departmentFilter.departments.length === 0) {
    return sendError(res, 'Accès non autorisé', 403);
  }

  const query = `
    SELECT u.id, u.username, u.email, u.role, u.department_id,
           r.name as role_name, d.name as department_name, u.created_at
    FROM users u
    LEFT JOIN roles r ON u.role_id = r.id
    LEFT JOIN departments d ON u.department_id = d.id
    WHERE u.department_id IN (?)
    ORDER BY u.created_at DESC
  `;

  const { success, data, error } = await executeQuery(query, [req.departmentFilter.departments]);

  if (!success) {
    return sendError(res, 'Erreur serveur', 500, error);
  }

  return sendSuccess(res, data);
}));

/**
 * @route   POST api/users
 * @desc    Create a new user (admin only)
 * @access  Private/Admin
 */
router.post('/users', cors(corsOptions), auth, asyncHandler(async (req, res) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    return sendError(res, 'Accès non autorisé', 403);
  }

  const { username, email, password, role_id, department_id } = req.body;

  // Validate required fields
  if (!username || !email || !password) {
    return sendError(res, 'Nom d\'utilisateur, email et mot de passe sont requis', 400);
  }

  // Check if user already exists
  const checkQuery = 'SELECT * FROM users WHERE email = ?';
  const checkResult = await executeQuery(checkQuery, [email]);

  if (!checkResult.success) {
    return sendError(res, 'Erreur serveur', 500, checkResult.error);
  }

  if (checkResult.data.length > 0) {
    return sendError(res, 'Un utilisateur avec cet email existe déjà', 400);
  }

  // Get role name from role_id
  let roleName = 'user'; // Default role
  if (role_id) {
    const roleResult = await executeQuery('SELECT name FROM roles WHERE id = ?', [role_id]);
    if (roleResult.success && roleResult.data.length > 0) {
      roleName = roleResult.data[0].name;
    }
  }

  // Hash password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(password, salt);

  // Create user
  const insertQuery = 'INSERT INTO users (username, email, password, role, role_id, department_id) VALUES (?, ?, ?, ?, ?, ?)';
  const insertResult = await executeQuery(insertQuery, [
    username,
    email,
    hashedPassword,
    roleName,
    role_id || null,
    department_id || null
  ]);

  if (!insertResult.success) {
    return sendError(res, 'Erreur lors de la création de l\'utilisateur', 500, insertResult.error);
  }

  // Get created user
  const userId = insertResult.data.insertId;
  const selectQuery = `
    SELECT u.id, u.username, u.email, u.role, u.department_id,
           r.name as role_name, d.name as department_name, u.created_at
    FROM users u
    LEFT JOIN roles r ON u.role_id = r.id
    LEFT JOIN departments d ON u.department_id = d.id
    WHERE u.id = ?
  `;
  const selectResult = await executeQuery(selectQuery, [userId], true);

  if (!selectResult.success) {
    return sendError(res, 'Utilisateur créé mais erreur lors de la récupération des données', 500, selectResult.error);
  }

  return sendSuccess(res, selectResult.data, 'Utilisateur créé avec succès', 201);
}));

/**
 * @route   GET api/users/:id
 * @desc    Get user by ID
 * @access  Private/Admin or Self
 */
router.get('/users/:id', cors(corsOptions), auth, asyncHandler(async (req, res) => {
  // Check if user is admin or the user themselves
  if (req.user.role !== 'admin' && req.user.id !== req.params.id) {
    return sendError(res, 'Accès non autorisé', 403);
  }

  const query = 'SELECT u.id, u.username, u.email, u.role, u.role_id, r.name as role_name, u.created_at FROM users u LEFT JOIN roles r ON u.role_id = r.id WHERE u.id = ?';
  const { success, data, error } = await executeQuery(query, [req.params.id], true);

  if (!success) {
    return sendError(res, 'Erreur serveur', 500, error);
  }

  if (!data) {
    return sendError(res, 'Utilisateur non trouvé', 404);
  }

  return sendSuccess(res, data);
}));

/**
 * @route   PUT api/users/:id
 * @desc    Update user
 * @access  Private/Admin
 */
router.put('/users/:id', cors(corsOptions), auth, asyncHandler(async (req, res) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    return sendError(res, 'Accès non autorisé', 403);
  }

  const { username, email, role_id } = req.body;

  // Get role name from role_id
  let roleName = null;
  if (role_id) {
    const roleResult = await executeQuery('SELECT name FROM roles WHERE id = ?', [role_id]);
    if (roleResult.success && roleResult.data.length > 0) {
      roleName = roleResult.data[0].name;
    }
  }

  // Update user with both role and role_id
  const updateQuery = 'UPDATE users SET username = ?, email = ?, role_id = ?, role = ? WHERE id = ?';
  const updateResult = await executeQuery(updateQuery, [username, email, role_id, roleName, req.params.id]);

  if (!updateResult.success) {
    return sendError(res, 'Erreur serveur', 500, updateResult.error);
  }

  if (updateResult.data.affectedRows === 0) {
    return sendError(res, 'Utilisateur non trouvé', 404);
  }

  // Get updated user
  const selectQuery = 'SELECT u.id, u.username, u.email, u.role, u.role_id, r.name as role_name, u.created_at FROM users u LEFT JOIN roles r ON u.role_id = r.id WHERE u.id = ?';
  const { success, data, error } = await executeQuery(selectQuery, [req.params.id], true);

  if (!success) {
    return sendError(res, 'Erreur serveur', 500, error);
  }

  return sendSuccess(res, data);
}));

/**
 * @route   DELETE api/users/:id
 * @desc    Delete user
 * @access  Private/Admin
 */
router.delete('/users/:id', cors(corsOptions), auth, asyncHandler(async (req, res) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    return sendError(res, 'Accès non autorisé', 403);
  }

  // Check if trying to delete self
  if (req.user.id === req.params.id) {
    return sendError(res, 'Vous ne pouvez pas supprimer votre propre compte', 400);
  }

  // Delete user
  const query = 'DELETE FROM users WHERE id = ?';
  const { success, data, error } = await executeQuery(query, [req.params.id]);

  if (!success) {
    return sendError(res, 'Erreur serveur', 500, error);
  }

  if (data.affectedRows === 0) {
    return sendError(res, 'Utilisateur non trouvé', 404);
  }

  return sendSuccess(res, null, 'Utilisateur supprimé avec succès');
}));

/**
 * @route   PUT api/users/update-profile
 * @desc    Update user profile (for self)
 * @access  Private
 */
router.put('/users/update-profile', cors(corsOptions), auth, asyncHandler(async (req, res) => {
  const { username, email } = req.body;

  // Update user
  const updateQuery = 'UPDATE users SET username = ?, email = ? WHERE id = ?';
  const updateResult = await executeQuery(updateQuery, [username, email, req.user.id]);

  if (!updateResult.success) {
    return sendError(res, 'Erreur serveur', 500, updateResult.error);
  }

  // Get updated user
  const selectQuery = 'SELECT u.id, u.username, u.email, u.role, u.role_id, r.name as role_name, u.created_at FROM users u LEFT JOIN roles r ON u.role_id = r.id WHERE u.id = ?';
  const { success, data, error } = await executeQuery(selectQuery, [req.user.id], true);

  if (!success) {
    return sendError(res, 'Erreur serveur', 500, error);
  }

  return sendSuccess(res, data);
}));

/**
 * @route   PUT api/users/change-password
 * @desc    Change user password
 * @access  Private
 */
router.put('/users/change-password', cors(corsOptions), auth, asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  // Validate input
  if (!currentPassword || !newPassword) {
    return sendError(res, 'Veuillez fournir le mot de passe actuel et le nouveau mot de passe', 400);
  }

  // Get user
  const selectQuery = 'SELECT * FROM users WHERE id = ?';
  const { success, data: user, error } = await executeQuery(selectQuery, [req.user.id], true);

  if (!success) {
    return sendError(res, 'Erreur serveur', 500, error);
  }

  if (!user) {
    return sendError(res, 'Utilisateur non trouvé', 404);
  }

  // Check current password
  const isMatch = await bcrypt.compare(currentPassword, user.password);
  if (!isMatch) {
    return sendError(res, 'Le mot de passe actuel est incorrect', 400);
  }

  // Hash new password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(newPassword, salt);

  // Update password
  const updateQuery = 'UPDATE users SET password = ? WHERE id = ?';
  const updateResult = await executeQuery(updateQuery, [hashedPassword, req.user.id]);

  if (!updateResult.success) {
    return sendError(res, 'Erreur serveur', 500, updateResult.error);
  }

  return sendSuccess(res, null, 'Mot de passe mis à jour avec succès');
}));

/**
 * @route   POST api/users/set-test-permissions
 * @desc    Set test permissions for a user (for testing purposes only)
 * @access  Private
 */
router.post('/users/set-test-permissions', cors(corsOptions), auth, asyncHandler(async (req, res) => {
  const userId = req.body.userId || req.user.id;
  const roleName = req.body.role || 'RoleTest';

  try {
    // First, check if the role exists in the roles table
    const roleQuery = 'SELECT id FROM roles WHERE name = ?';
    const roleResult = await executeQuery(roleQuery, [roleName]);

    let roleId = null;

    // If role doesn't exist, create it
    if (!roleResult.success || roleResult.data.length === 0) {
      // Get the permissions for the role from roleHierarchy.js
      const { DEFAULT_ROLE_PERMISSIONS } = await import('../utils/roleHierarchy.js');
      const rolePermissions = DEFAULT_ROLE_PERMISSIONS[roleName] || [];

      // Insert the role into the database
      const insertRoleQuery = 'INSERT INTO roles (name, description, permissions) VALUES (?, ?, ?)';
      const insertRoleResult = await executeQuery(insertRoleQuery, [
        roleName,
        `Test role for ${roleName}`,
        JSON.stringify(rolePermissions)
      ]);

      if (!insertRoleResult.success) {
        return sendError(res, 'Error creating role', 500, insertRoleResult.error);
      }

      roleId = insertRoleResult.data.insertId;
    } else {
      roleId = roleResult.data[0].id;
    }

    // Update the user's role and permissions
    const updateQuery = 'UPDATE users SET role = ?, role_id = ?, permissions = ? WHERE id = ?';
    const permissions = JSON.stringify([
      'system:view_dashboard',
      'system:view_reports',
      'system:create_reports',
      'production:view_production',
      'production:manage_production'
    ]);

    const updateResult = await executeQuery(updateQuery, [roleName, roleId, permissions, userId]);

    if (!updateResult.success) {
      return sendError(res, 'Error updating user permissions', 500, updateResult.error);
    }

    // Get the updated user
    const userQuery = `
      SELECT u.id, u.username, u.email, u.role, u.role_id, u.permissions,
             r.name as role_name, r.permissions as role_permissions
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.id = ?
    `;

    const userResult = await executeQuery(userQuery, [userId], true);

    if (!userResult.success) {
      return sendError(res, 'User updated but error retrieving data', 500, userResult.error);
    }

    return sendSuccess(res, userResult.data, 'Test permissions set successfully');
  } catch (error) {
    console.error('Error setting test permissions:', error);
    return sendError(res, 'Server error', 500, error);
  }
}));

export default router;