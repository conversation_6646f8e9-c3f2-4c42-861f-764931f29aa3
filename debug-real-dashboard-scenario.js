/**
 * Debug Script: Test Date Filtering with Real Dashboard Filters
 * 
 * This script tests the exact scenario from the screenshots:
 * Machine IPS01 + April 2025 date filter
 */

import { GraphQLClient } from 'graphql-request';

async function debugRealDashboardScenario() {
  console.log('🔍 Testing Real Dashboard Date Filtering Issue...\n');

  const client = new GraphQLClient('http://localhost:5000/api/graphql');

  const query = `
    query GetAllMachineStops($filters: StopFilterInput) {
      getAllMachineStops(filters: $filters) {
        Date_Insert
        Machine_Name
        duration_minutes
      }
    }
  `;

  // Test the exact scenario from screenshot
  const filters = {
    model: "IPS",
    machine: "IPS01", 
    date: "2025-04-01",     // April 2025 selected
    startDate: "2025-04-01", 
    endDate: "2025-04-30",
    dateRangeType: "month"
  };

  console.log('📋 Dashboard Scenario Test:');
  console.log(JSON.stringify(filters, null, 2));

  try {
    const result = await client.request(query, { filters });
    const stopsData = result.getAllMachineStops || [];

    console.log(`\n📊 Backend returned ${stopsData.length} stops`);
    
    if (stopsData.length > 0) {
      // Show sample dates to understand format
      console.log('\n📅 Sample dates from backend:');
      stopsData.slice(0, 5).forEach((stop, i) => {
        console.log(`  ${i + 1}. ${stop.Date_Insert} (${stop.Machine_Name})`);
      });

      // Process exactly like frontend
      console.log('\n🔄 Frontend Processing:');
      const dailyStats = {};
      let processedCount = 0;
      let skippedCount = 0;

      stopsData.forEach(stop => {
        if (stop.Date_Insert && typeof stop.Date_Insert === 'string') {
          const parsedDate = parseDate(stop.Date_Insert);
          if (parsedDate) {
            processedCount++;
            if (!dailyStats[parsedDate]) {
              dailyStats[parsedDate] = { date: parsedDate, stops: 0, duration: 0 };
            }
            dailyStats[parsedDate].stops++;
            if (stop.duration_minutes && stop.duration_minutes > 0) {
              dailyStats[parsedDate].duration += parseFloat(stop.duration_minutes);
            }
          } else {
            skippedCount++;
          }
        } else {
          skippedCount++;
        }
      });

      console.log(`- Processed: ${processedCount} stops`);
      console.log(`- Skipped: ${skippedCount} stops`);
      console.log(`- Daily aggregates: ${Object.keys(dailyStats).length} days`);

      if (Object.keys(dailyStats).length > 0) {
        console.log('\n📊 Daily Aggregates:');
        Object.entries(dailyStats).forEach(([date, stats]) => {
          console.log(`  ${date}: ${stats.stops} stops, ${stats.duration}min`);
        });

        // Test date filtering
        console.log('\n🎯 Testing Date Filtering:');
        const evolutionData = Object.values(dailyStats).sort((a, b) => new Date(a.date) - new Date(b.date));
        console.log(`- Before filter: ${evolutionData.length} points`);

        // Apply month filter (April 2025)
        const filterDate = new Date('2025-04-01');
        const filterMonth = filterDate.getMonth(); // Should be 3 (April)
        const filterYear = filterDate.getFullYear(); // Should be 2025

        console.log(`- Filter for: Month ${filterMonth + 1} (${filterMonth}) of Year ${filterYear}`);

        const filteredData = evolutionData.filter(item => {
          const itemDate = new Date(item.date);
          const itemMonth = itemDate.getMonth();
          const itemYear = itemDate.getFullYear();
          const matches = itemMonth === filterMonth && itemYear === filterYear;
          
          console.log(`  ${item.date}: Month ${itemMonth + 1} (${itemMonth}), Year ${itemYear} => ${matches ? 'KEEP' : 'FILTER OUT'}`);
          return matches;
        });

        console.log(`- After filter: ${filteredData.length} points`);

        if (filteredData.length > 0) {
          console.log('\n✅ CHART SHOULD SHOW DATA:');
          filteredData.forEach(point => {
            console.log(`  ${point.date}: ${point.stops} stops`);
          });
        } else {
          console.log('\n❌ NO DATA AFTER FILTERING - This explains the empty chart!');
        }
      }
    } else {
      console.log('❌ No data returned from backend');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

function parseDate(dateString) {
  let originalDate = dateString.toString();
  
  if (originalDate.includes('/')) {
    // Format: DD/MM/YYYY HH:MM:SS
    const parts = originalDate.split(' ');
    const datePart = parts[0]; // DD/MM/YYYY
    if (datePart && datePart.includes('/')) {
      const dateParts = datePart.split('/');
      if (dateParts.length === 3) {
        const [day, month, year] = dateParts;
        if (day && month && year) {
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      }
    }
  } else if (originalDate.includes('T')) {
    return originalDate.split('T')[0];
  } else if (originalDate.match(/^\d{4} \d{2}:\d{2}:\d{2}-\d{1,2}-\s*\d{1,2}$/)) {
    const match = originalDate.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);
    if (match && match.length === 4) {
      const [_, year, month, day] = match;
      if (year && month && day) {
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
    }
  } else {
    const dateMatch = originalDate.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
    if (dateMatch && dateMatch.length === 4) {
      const [_, year, month, day] = dateMatch;
      if (year && month && day) {
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
    }
  }
  
  return null;
}

debugRealDashboardScenario().catch(console.error);
