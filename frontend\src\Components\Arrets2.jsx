"use client"

import React, { useEffect, useState, useCallback, memo, useRef } from "react"
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  DatePicker,
  Typography,
  Grid,
  Tabs,
  Badge,
  Button,
  Divider,
  Empty,
  Tag,
  Progress,
  Space,
  Tooltip as AntTooltip,
  Select,
  Popover,
  Segmented,
  Alert,
  Spin,
  message,
} from "antd"
import {
  AlertOutlined,
  LineChartOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  ClockCircleOutlined,
  UserOutlined,
  SettingOutlined,
  WarningOutlined,
  ToolOutlined,
  DashboardOutlined,
  FilterOutlined,
  DownloadOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  SearchOutlined,
} from "@ant-design/icons"
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  AreaChart,
  Area,
} from "recharts"
import request from "superagent"
import dayjs from "dayjs"
import "dayjs/locale/fr"
import weekOfYear from "dayjs/plugin/weekOfYear"
import isoWeek from "dayjs/plugin/isoWeek"
import advancedFormat from "dayjs/plugin/advancedFormat"
import customParseFormat from "dayjs/plugin/customParseFormat"

// Add these imports at the top of the file with the other imports
import DisponibiliteTrendChart from "./charts/disponibilite-trend-chart"
import DowntimeParetoChart from "./charts/downtime-pareto-chart"
import DisponibiliteByMachineChart from "./charts/disponibilite-by-machine-chart"
import MttrHeatCalendar from "./charts/mttr-heat-calendar"
import PerformanceMetricsGauge from "./charts/performance-metrics-gauge"
import FilterPanel from "./FilterPanel"
import SearchResultsDisplay from "./search/SearchResultsDisplay"
import GlobalSearchModal from "./search/GlobalSearchModal"

// Add required dayjs plugins
dayjs.extend(weekOfYear)
dayjs.extend(isoWeek)
dayjs.extend(advancedFormat)
dayjs.extend(customParseFormat)

// Configuration de dayjs en français
dayjs.locale("fr")

const { useBreakpoint } = Grid
const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs
const { Option } = Select
const { RangePicker } = DatePicker

// Palette de couleurs améliorée
const COLORS = ["#1890ff", "#13c2c2", "#52c41a", "#faad14", "#f5222d", "#722ed1", "#eb2f96"]
const CHART_COLORS = {
  primary: "#1890ff",
  secondary: "#13c2c2",
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
  purple: "#722ed1",
  pink: "#eb2f96",
  orange: "#fa8c16",
  cyan: "#13c2c2",
  lime: "#a0d911",
}

// Composant de graphique en ligne mémorisé
const MemoizedLineChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <LineChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
      <XAxis
        dataKey="Stop_Date"
        tick={{ fill: "#666" }}
        tickFormatter={(date) => dayjs(date).format("DD/MM")}
        label={{
          value: "Date",
          position: "bottom",
          offset: 0,
          style: { fill: "#666" },
        }}
      />
      <YAxis
        label={{
          value: "Arrêts",
          angle: -90,
          position: "insideLeft",
          style: { fill: "#666" },
        }}
        tick={{ fill: "#666" }}
      />
      <Tooltip
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 4,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
        formatter={(value) => [`${value} arrêts`, "Total"]}
      />
      <Line
        type="monotone"
        dataKey="Total_Stops"

        stroke={CHART_COLORS.primary}
        strokeWidth={2}
        dot={{ fill: CHART_COLORS.primary, strokeWidth: 2 }}
        activeDot={{ r: 6, fill: "#fff", stroke: CHART_COLORS.primary, strokeWidth: 2 }}
      />
    </LineChart>
  </ResponsiveContainer>
))

// Composant de graphique en secteurs mémorisé
const MemoizedPieChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <PieChart margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <Pie
        data={data}
        dataKey="count"
        nameKey="stopName"
        cx="50%"
        cy="50%"
        innerRadius={60}
        outerRadius={80}
        paddingAngle={5}
        labelLine={true}
        label={({ name, percent }) => `${(percent * 100).toFixed(0)}%`}
      >
        {data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} stroke="#fff" strokeWidth={2} />
        ))}
      </Pie>
      <Tooltip
        formatter={(value, name, props) => [`${value} arrêts`, props.payload.stopName]}
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 4,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
      />
      <Legend
        layout="horizontal"
        verticalAlign="bottom"
        align="center"
        wrapperStyle={{
          paddingTop: 20,
          fontSize: 12,
          color: "#666",
        }}
        formatter={(value, entry, index) => (
          <span style={{ color: COLORS[index % COLORS.length], fontWeight: "bold" }}>{value}</span>
        )}
      />
    </PieChart>
  </ResponsiveContainer>
))

// Composant de tableau mémorisé
const MemoizedTable = memo(({ data }) => {
  const screens = useBreakpoint()

  const getStatusColor = (codeStop) => {
    if (!codeStop) return "default"
    if (codeStop.toLowerCase().includes("non déclaré")) return "error"
    if (codeStop.toLowerCase().includes("maintenance")) return "warning"
    if (codeStop.toLowerCase().includes("changement")) return "processing"
    if (codeStop.toLowerCase().includes("réglage")) return "cyan"
    if (codeStop.toLowerCase().includes("problème")) return "orange"
    return "default"
  }

  // Handle potentially invalid date formats
  const formatDate = (dateStr) => {
    if (!dateStr || dateStr.includes("Invalid")) return "N/A"
    try {
      // Try to parse with the format from your data
      return dayjs(dateStr, "DD/MM/YYYY HH:mm").format("DD/MM/YYYY HH:mm")
    } catch (e) {
      return "N/A"
    }
  }

  const formatTime = (dateStr) => {
    if (!dateStr || dateStr.includes("Invalid")) return "N/A"
    try {
      // Try to parse with the format from your data
      return dayjs(dateStr, "DD/MM/YYYY HH:mm").format("HH:mm")
    } catch (e) {
      return "N/A"
    }
  }
  const calculateDuration = (start, end) => {
    if (!start || !end || start.includes("Invalid") || end.includes("Invalid")) return "N/A"
    try {
      // Use the format from your data
      const startTime = dayjs(start, "DD/MM/YYYY HH:mm")
      const endTime = dayjs(end, "DD/MM/YYYY HH:mm")
      if (!startTime.isValid() || !endTime.isValid()) return "N/A"

      const durationMinutes = endTime.diff(startTime, "minute")
      if (durationMinutes < 0) return "N/A"
      return `${durationMinutes} min`
    } catch (e) {
      return "N/A"
    }
  }

  // Process data to handle invalid values and field name inconsistencies
  const processedData = data.map((item) => {
    // Try to parse dates to check validity
    let dateInsert = item.Date_Insert;
    let debutStop = item.Debut_Stop;
    let finStopTime = item.Fin_Stop_Time;

    // Function to parse dates with multiple formats
    const parseDate = (dateStr) => {
      if (!dateStr) return null;

      // Try multiple formats (with both single and double digit days/months)
      const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];

      for (const format of formats) {
        const parsed = dayjs(dateStr, format);
        if (parsed.isValid()) {
          return dateStr;
        }
      }

      // If none of the formats work, try automatic parsing
      const parsed = dayjs(dateStr);
      if (parsed.isValid()) {
        return dateStr;
      }

      return null;
    };

    // Parse all date fields
    dateInsert = parseDate(dateInsert);
    debutStop = parseDate(debutStop);
    finStopTime = parseDate(finStopTime);

    return {
      ...item,
      // Ensure all required fields exist and handle field name variations
      Date_Insert: dateInsert,
      Machine_Name: item.Machine_Name || "N/A",
      // Use Part_NO as returned by the backend
      Part_No: item.Part_NO || "N/A",
      Code_Stop: item.Code_Stop || "N/A",
      Debut_Stop: debutStop,
      Fin_Stop_Time: finStopTime,
      Regleur_Prenom: item.Regleur_Prenom || "Non assigné",
      // Use the duration_minutes field from the API if available
      duration_minutes: item.duration_minutes || null,
    };
  })

  return (
    <Table
      columns={[
        {
          title: "Date",
          dataIndex: "Date_Insert",
          key: "Date_Insert",
          render: (text) => {
            if (!text) {
              return (
                <Text type="secondary" style={{ fontStyle: "italic" }}>
                  Non disponible
                </Text>
              )
            }
            try {
              // Try multiple formats
              const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];
              let validDate = null;

              for (const format of formats) {
                const date = dayjs(text, format);
                if (date.isValid()) {
                  validDate = date;
                  break;
                }
              }

              // If no format worked, try automatic parsing
              if (!validDate) {
                validDate = dayjs(text);
              }

              if (validDate && validDate.isValid()) {
                return validDate.format("DD/MM/YYYY");
              } else {
                return (
                  <Text type="secondary" style={{ fontStyle: "italic" }}>
                    Non disponible
                  </Text>
                )
              }
            } catch (e) {
              return (
                <Text type="secondary" style={{ fontStyle: "italic" }}>
                  Non disponible
                </Text>
              )
            }
          },
          sorter: (a, b) => {
            try {
              if (!a.Date_Insert || !b.Date_Insert) {
                return 0;
              }

              // Function to parse dates with multiple formats
              const parseDate = (dateStr) => {
                if (!dateStr) return null;

                // Try multiple formats
                const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];

                for (const format of formats) {
                  const parsed = dayjs(dateStr, format);
                  if (parsed.isValid()) {
                    return parsed;
                  }
                }

                // If none of the formats work, try automatic parsing
                const parsed = dayjs(dateStr);
                if (parsed.isValid()) {
                  return parsed;
                }

                return null;
              };

              const dateA = parseDate(a.Date_Insert);
              const dateB = parseDate(b.Date_Insert);

              if (!dateA || !dateB || !dateA.isValid() || !dateB.isValid()) {
                return 0;
              }

              return dateA.unix() - dateB.unix();
            } catch (e) {
              return 0;
            }
          },
        },
        {
          title: "Machine",
          dataIndex: "Machine_Name",
          key: "Machine_Name",
          render: (text) => <Tag color="blue">{text || "N/A"}</Tag>,
          filters: [...new Set(processedData.map((item) => item.Machine_Name).filter(Boolean))].map((machine) => ({
            text: machine,
            value: machine,
          })),
          onFilter: (value, record) => record.Machine_Name === value,
        },
        {
          title: "OF",
          dataIndex: "Part_No",
          key: "Part_No",
          render: (text) => (text && text !== "N/A" ? text : <Text type="secondary">Non spécifié</Text>),
          responsive: ["md"],
        },
        {
          title: "Code Arrêt",
          dataIndex: "Code_Stop",
          key: "Code_Stop",
          render: (text) => <Badge status={getStatusColor(text)} text={text || "N/A"} />,
          filters: [...new Set(processedData.map((item) => item.Code_Stop).filter(Boolean))].map((code) => ({
            text: code,
            value: code,
          })),
          onFilter: (value, record) => record.Code_Stop === value,
        },
        {
          title: "Début",
          dataIndex: "Debut_Stop",
          key: "Debut_Stop",
          render: (text) => {
            if (!text) {
              return (
                <Text type="secondary" style={{ fontStyle: "italic" }}>
                  Non disponible
                </Text>
              )
            }
            try {
              // Try multiple formats
              const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];
              let validTime = null;

              for (const format of formats) {
                const time = dayjs(text, format);
                if (time.isValid()) {
                  validTime = time;
                  break;
                }
              }

              // If no format worked, try automatic parsing
              if (!validTime) {
                validTime = dayjs(text);
              }

              if (validTime && validTime.isValid()) {
                return validTime.format("HH:mm");
              } else {
                return (
                  <Text type="secondary" style={{ fontStyle: "italic" }}>
                    Non disponible
                  </Text>
                )
              }
            } catch (e) {
              return (
                <Text type="secondary" style={{ fontStyle: "italic" }}>
                  Non disponible
                </Text>
              )
            }
          },
        },
        {
          title: "Fin",
          dataIndex: "Fin_Stop_Time",
          key: "Fin_Stop_Time",
          render: (text) => {
            if (!text) {
              return (
                <Text type="secondary" style={{ fontStyle: "italic" }}>
                  Non disponible
                </Text>
              )
            }
            try {
              // Try multiple formats
              const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];
              let validTime = null;

              for (const format of formats) {
                const time = dayjs(text, format);
                if (time.isValid()) {
                  validTime = time;
                  break;
                }
              }

              // If no format worked, try automatic parsing
              if (!validTime) {
                validTime = dayjs(text);
              }

              if (validTime && validTime.isValid()) {
                return validTime.format("HH:mm");
              } else {
                return (
                  <Text type="secondary" style={{ fontStyle: "italic" }}>
                    Non disponible
                  </Text>
                )
              }
            } catch (e) {
              return (
                <Text type="secondary" style={{ fontStyle: "italic" }}>
                  Non disponible
                </Text>
              )
            }
          },
        },
        {
          title: "Durée",
          key: "duration",
          render: (_, record) => {
            // First try to use the duration_minutes field from the API
            if (record.duration_minutes !== null && record.duration_minutes !== undefined) {
              return `${record.duration_minutes} min`;
            }

            // Fallback to calculating duration if the field is not available
            if (!record.Debut_Stop || !record.Fin_Stop_Time) {
              return (
                <Text type="secondary" style={{ fontStyle: "italic" }}>
                  Non calculable
                </Text>
              )
            }
            try {
              // Try multiple formats for both start and end times
              const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];

              let validStartTime = null;
              let validEndTime = null;

              // Try to parse start time with different formats
              for (const format of formats) {
                const startTime = dayjs(record.Debut_Stop, format);
                if (startTime.isValid()) {
                  validStartTime = startTime;
                  break;
                }
              }

              // If no format worked, try automatic parsing
              if (!validStartTime) {
                validStartTime = dayjs(record.Debut_Stop);
              }

              // Try to parse end time with different formats
              for (const format of formats) {
                const endTime = dayjs(record.Fin_Stop_Time, format);
                if (endTime.isValid()) {
                  validEndTime = endTime;
                  break;
                }
              }

              // If no format worked, try automatic parsing
              if (!validEndTime) {
                validEndTime = dayjs(record.Fin_Stop_Time);
              }

              if (!validStartTime.isValid() || !validEndTime.isValid()) {
                return (
                  <Text type="secondary" style={{ fontStyle: "italic" }}>
                    Non calculable
                  </Text>
                )
              }

              const durationMinutes = validEndTime.diff(validStartTime, "minute")
              if (durationMinutes < 0) {
                return (
                  <Text type="secondary" style={{ fontStyle: "italic" }}>
                    Non calculable
                  </Text>
                )
              }
              return `${durationMinutes} min`
            } catch (e) {
              return (
                <Text type="secondary" style={{ fontStyle: "italic" }}>
                  Non calculable
                </Text>
              )
            }
          },
          sorter: (a, b) => {
            // Use duration_minutes from API if available
            if (a.duration_minutes !== null && a.duration_minutes !== undefined &&
                b.duration_minutes !== null && b.duration_minutes !== undefined) {
              return a.duration_minutes - b.duration_minutes;
            }

            // Fallback to calculating duration
            try {
              if (!a.Debut_Stop || !a.Fin_Stop_Time || !b.Debut_Stop || !b.Fin_Stop_Time) {
                return 0
              }

              // Function to parse dates with multiple formats
              const parseDateTime = (dateStr) => {
                if (!dateStr) return null;

                // Try multiple formats
                const formats = ["DD/MM/YYYY HH:mm:ss", "DD/MM/YYYY HH:mm", "D/MM/YYYY HH:mm:ss", "D/MM/YYYY HH:mm"];

                for (const format of formats) {
                  const parsed = dayjs(dateStr, format);
                  if (parsed.isValid()) {
                    return parsed;
                  }
                }

                // If none of the formats work, try automatic parsing
                const parsed = dayjs(dateStr);
                if (parsed.isValid()) {
                  return parsed;
                }

                return null;
              };

              // Parse all date fields
              const startTimeA = parseDateTime(a.Debut_Stop);
              const endTimeA = parseDateTime(a.Fin_Stop_Time);
              const startTimeB = parseDateTime(b.Debut_Stop);
              const endTimeB = parseDateTime(b.Fin_Stop_Time);

              if (!startTimeA || !endTimeA || !startTimeB || !endTimeB ||
                  !startTimeA.isValid() || !endTimeA.isValid() || !startTimeB.isValid() || !endTimeB.isValid()) {
                return 0
              }

              const durationA = endTimeA.diff(startTimeA, "minute")
              const durationB = endTimeB.diff(startTimeB, "minute")
              return durationA - durationB
            } catch (e) {
              return 0
            }
          },
        },
        {
          title: "Responsable",
          dataIndex: "Regleur_Prenom",
          key: "Regleur_Prenom",
          render: (text) => text || "Non assigné",
          filters: [...new Set(processedData.map((item) => item.Regleur_Prenom || "Non assigné").filter(Boolean))].map(
            (name) => ({
              text: name,
              value: name === "Non assigné" ? null : name,
            }),
          ),
          onFilter: (value, record) => (value ? record.Regleur_Prenom === value : !record.Regleur_Prenom),
        },
      ]}
      dataSource={processedData}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        showTotal: (total) => `Total ${total} arrêts`,
      }}
      scroll={{ x: screens.md ? undefined : 1000 }}
      bordered
      size="middle"
      rowKey={(record, index) => `${record.Date_Insert}-${index}`}
      rowClassName={(record) => {
        if (record.Code_Stop && record.Code_Stop.toLowerCase().includes("non déclaré")) return "highlight-row-error"
        return ""
      }}
    />
  )
})

// Composant de graphique à barres mémorisé pour la comparaison par machine
const MemoizedBarChart = memo(({ data }) => {
  // Process data to ensure consistent field names
  const processedData = data.map(item => ({
    machine: item.Machine_Name || item.machine || "N/A",
    stops: item.stops || 0,
    totalDuration: item.totalDuration || 0
  }));

  return (
    <div style={{ width: "100%", height: 300 }}>
      <Row gutter={[0, 16]}>
        <Col span={24}>
          <ResponsiveContainer width="100%" height={140}>
            <BarChart data={processedData} margin={{ top: 5, right: 24, left: 24, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="machine" />
              <YAxis
                label={{
                  value: "Nombre d'arrêts",
                  angle: -90,
                  position: "insideLeft",
                  style: { fontSize: 12 },
                }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#fff",
                  border: "1px solid #f0f0f0",
                  borderRadius: 4,
                  boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                }}
                formatter={(value) => [`${value} arrêts`, "Nombre d'arrêts"]}
              />
              <Bar dataKey="stops" fill={CHART_COLORS.primary} name="Nombre d'arrêts" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </Col>
        <Col span={24}>
          <ResponsiveContainer width="100%" height={140}>
            <BarChart data={processedData} margin={{ top: 5, right: 24, left: 24, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="machine" />
              <YAxis
                label={{
                  value: "Durée (min)",
                  angle: -90,
                  position: "insideLeft",
                  style: { fontSize: 12 },
                }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#fff",
                  border: "1px solid #f0f0f0",
                  borderRadius: 4,
                  boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                }}
                formatter={(value) => [`${value} minutes`, "Durée totale"]}
              />
              <Bar
                dataKey="totalDuration"
                fill={CHART_COLORS.secondary}
                name="Durée totale (min)"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </Col>
      </Row>
    </div>
  );
})

// Composant de graphique en aires mémorisé
const MemoizedAreaChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <AreaChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis
        dataKey="hour"
        label={{
          value: "Heure de la journée",
          position: "bottom",
          offset: 0,
        }}
      />
      <YAxis
        label={{
          value: "Durée moyenne (min)",
          angle: -90,
          position: "insideLeft",
        }}
      />
      <Tooltip
        formatter={(value) => [`${value.toFixed(1)} min`, "Durée moyenne"]}
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 4,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
      />

      <Area
        type="monotone"
        dataKey="avgDuration"
        stroke={CHART_COLORS.success}
        fill={`${CHART_COLORS.success}33`}
      />
    </AreaChart>
  </ResponsiveContainer>
))

// Composant de graphique à barres horizontales mémorisé pour les raisons d'arrêt
const MemoizedHorizontalBarChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <BarChart
      data={data.sort((a, b) => b.count - a.count)}
      layout="vertical"
      margin={{ top: 16, right: 24, left: 24, bottom: 16 }}
    >
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis type="number" />
      <YAxis type="category" dataKey="reason" width={120} tick={{ fontSize: 12 }} />
      <Tooltip
        formatter={(value) => [`${value} occurrences`, "Fréquence"]}
        contentStyle={{
          backgroundColor: "#fff",
          border: "1px solid #f0f0f0",
          borderRadius: 4,
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
      />

      <Bar dataKey="count" name="Fréquence" fill={CHART_COLORS.purple} barSize={20} radius={[0, 4, 4, 0]} />
    </BarChart>
  </ResponsiveContainer>
))

// Composant principal
const Arrets2 = () => {
  // Machine selection state
  const [machineModels, setMachineModels] = useState([])
  const [machineNames, setMachineNames] = useState([])
  const [selectedMachineModel, setSelectedMachineModel] = useState("")
  const [selectedMachine, setSelectedMachine] = useState("")

  // Elasticsearch search state
  const [searchResults, setSearchResults] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchMode, setSearchMode] = useState(false)
  const [globalSearchVisible, setGlobalSearchVisible] = useState(false)
  const [filteredMachineNames, setFilteredMachineNames] = useState([])

  // Enhanced date filtering state
  const [dateRangeType, setDateRangeType] = useState("day") // "day", "week", "month"
  const [selectedDate, setSelectedDate] = useState(null)
  const [dateRangeDescription, setDateRangeDescription] = useState("")
  const [dateFilterActive, setDateFilterActive] = useState(false)
  const [dateOptions, setDateOptions] = useState([])

  // Add these new state variables at the top of the component with the other state variables
  const [disponibiliteTrendData, setDisponibiliteTrendData] = useState([])
  const [downtimeParetoData, setDowntimeParetoData] = useState([])
  const [disponibiliteByMachineData, setDisponibiliteByMachineData] = useState([])
  const [mttrCalendarData, setMttrCalendarData] = useState([])

  // Data state
  const [chartData, setChartData] = useState([])
  const [stopsData, setStopsData] = useState([])
  const [sidebarStats, setSidebarStats] = useState([])
  const [topStopsData, setTopStopsData] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [durationTrend, setDurationTrend] = useState([])
  const [machineComparison, setMachineComparison] = useState([])
  const [operatorStats, setOperatorStats] = useState([])
  const [totalDuration, setTotalDuration] = useState(0)
  const [avgDuration, setAvgDuration] = useState(0)
  const [stopReasons, setStopReasons] = useState([])

  // Performance metrics state
  const [mttr, setMttr] = useState(0)
  const [mtbf, setMtbf] = useState(0)
  const [doper, setDoper] = useState(0)
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState(false)

  const [activeTab, setActiveTab] = useState("1")

  // Add a ref to track if component is mounted
  const isMounted = useRef(true)

  // Add a ref to track pending data fetches
  const pendingFetch = useRef(false)

  // Add a ref to track filter changes that need data refresh
  const filtersChanged = useRef(false)

  const screens = useBreakpoint()

  // URL de base pour les requêtes API
  const baseURL =
    process.env.NODE_ENV === "production" ? "https://charming-hermit-intense.ngrok-free.app" : "http://localhost:5000"

  // 🔒 SECURITY: HTTP-only cookies configuration - no axios configuration needed
  // SuperAgent handles authentication automatically via HTTP-only cookies

  // Helper function to format date range for display
  const formatDateRange = useCallback((date, rangeType) => {
    if (!date) return { short: "", full: "" }

    const formattedDate = dayjs(date)

    if (rangeType === "day") {
      return {
        short: formattedDate.format("DD/MM/YYYY"),
        full: `le ${formattedDate.format("DD MMMM YYYY")}`,
      }
    } else if (rangeType === "week") {
      const startOfWeek = formattedDate.startOf("isoWeek")
      const endOfWeek = formattedDate.endOf("isoWeek")
      return {
        short: `S${formattedDate.isoWeek()} ${formattedDate.format("YYYY")}`,
        full: `du ${startOfWeek.format("DD MMMM")} au ${endOfWeek.format("DD MMMM YYYY")}`,
      }
    } else if (rangeType === "month") {
      return {
        short: formattedDate.format("MMMM YYYY"),
        full: `${formattedDate.format("MMMM YYYY")}`,
      }
    }

    return { short: "", full: "" }
  }, [])

  // Function to build query parameters for API requests
  const buildQueryParams = useCallback(() => {
    const queryParams = new URLSearchParams()

    // Add machine filtering
    if (selectedMachineModel && !selectedMachine) {
      queryParams.append("model", selectedMachineModel)
    } else if (selectedMachine) {
      queryParams.append("machine", selectedMachine)
    }

    // Add date range type if a date is selected
    if (selectedDate) {
      queryParams.append("dateRangeType", dateRangeType)
    }

    return queryParams.toString() ? `?${queryParams.toString()}` : ""
  }, [selectedMachineModel, selectedMachine, selectedDate, dateRangeType])

  // Fetch machine data
  const fetchMachineData = useCallback(async () => {
    try {
      console.log("Fetching machine data...")
      const [modelsRes, namesRes] = await Promise.all([
        request.get("/api/stops/machine-models").retry(2).withCredentials(),
        request.get("/api/stops/machine-names").retry(2).withCredentials(),
      ])

      // Set the machine models and names
      if (modelsRes.body && modelsRes.body.length > 0) {
        const models = modelsRes.body.map((item) => item.model || item)
        console.log("Machine models fetched:", models)
        setMachineModels(models)
      } else {
        // Set default models if API returns empty
        console.log("No machine models returned from API, using defaults")
        setMachineModels(["IPS", "CCM24"])
      }

      if (namesRes.body && namesRes.body.length > 0) {
        console.log("Machine names fetched:", namesRes.body)
        setMachineNames(namesRes.body)
        // Find IPS01 in the machine names to set as default display value
        const ips01Machine = namesRes.body.find((m) => m.Machine_Name === "IPS01")
        if (ips01Machine) {
          // We're not setting this value in state since the backend applies the default
          // But we need to visually indicate the default selection
          // Find the model for IPS01
          if (modelsRes.body.length > 0) {
            const ipsModel = modelsRes.body.find((m) => (m.model || m) === "IPS")
            if (ipsModel) {
              // Setting the model but not the specific machine - this way
              // we show IPS as selected model with no specific machine selected
              // which will default to IPS01 on the backend
              setSelectedMachineModel("IPS")
            }
          }
        }
      } else {
        // Set default machine names if API returns empty
        console.log("No machine names returned from API, using defaults")
        setMachineNames([
          { Machine_Name: "IPS01" },
          { Machine_Name: "IPS02" },
          { Machine_Name: "IPS03" },
          { Machine_Name: "IPS04" },
        ])
      }
    } catch (error) {
      console.error("Error loading machine data:", error)
      setError("Erreur lors du chargement des données machines. Veuillez réessayer.")
      // Set default values if API fails
      setMachineModels(["IPS", "CCM24"])
      setMachineNames([
        { Machine_Name: "IPS01" },
        { Machine_Name: "IPS02" },
        { Machine_Name: "IPS03" },
        { Machine_Name: "IPS04" },
      ])
    }
  }, [])

  // Effect to filter machines by selected model
  useEffect(() => {
    if (selectedMachineModel) {
      // Filter machines that belong to the selected model
      const filtered = machineNames.filter(
        (machine) => machine.Machine_Name && machine.Machine_Name.startsWith(selectedMachineModel),
      )
      setFilteredMachineNames(filtered)

      // Don't clear machine selection when model changes if the machine still belongs to the model
      if (selectedMachine && !filtered.some((m) => m.Machine_Name === selectedMachine)) {
        setSelectedMachine("")
      }

      // Mark filters as changed to trigger data refresh
      filtersChanged.current = true
    } else {
      setFilteredMachineNames([])
      setSelectedMachine("")

      // Mark filters as changed to trigger data refresh
      filtersChanged.current = true
    }
  }, [selectedMachineModel, machineNames, selectedMachine])

  // Main function to fetch data
  const fetchData = useCallback(async () => {
    // Set a timeout to automatically clear loading state if fetch takes too long
    const loadingTimeout = setTimeout(() => {
      if (isMounted.current) {
        setLoading(false)
        pendingFetch.current = false
        console.warn("Data fetch timeout - forcing loading state to false")
        message.warning("Le chargement des données a pris trop de temps. Veuillez réessayer.")
      }
    }, 15000) // 15 seconds timeout

    // Prevent multiple simultaneous fetches
    if (pendingFetch.current) {
      console.log("Fetch already in progress, skipping...")
      return
    }

    pendingFetch.current = true
    setLoading(true)
    setError(null)

    try {
      // Build query parameters
      const params = {}
      if (selectedMachineModel && !selectedMachine) {
        params.model = selectedMachineModel
      } else if (selectedMachine) {
        params.machine = selectedMachine
      }

      if (selectedDate) {
        // Add date range parameters based on dateRangeType
        if (dateRangeType === "day") {
          params.startDate = selectedDate.format("YYYY-MM-DD")
          params.endDate = selectedDate.clone().add(1, "day").format("YYYY-MM-DD")
        } else if (dateRangeType === "week") {
          params.startDate = selectedDate.clone().startOf("isoWeek").format("YYYY-MM-DD")
          params.endDate = selectedDate.clone().endOf("isoWeek").format("YYYY-MM-DD")
        } else if (dateRangeType === "month") {
          params.startDate = selectedDate.clone().startOf("month").format("YYYY-MM-DD")
          params.endDate = selectedDate.clone().endOf("month").format("YYYY-MM-DD")
        }
        params.dateRangeType = dateRangeType
      }

      console.log("Fetching data with params:", params)

      // Fetch all required data in parallel
      const [
        datesRes,
        arretRes,
        nonDeclareRes,
        topStopsRes,
        chartRes,
        allStopsRes,
        allDurationRes,
        allMachineRes,
        allOperatorRes,
        allReasonsRes,
        // New API calls for the charts
        disponibiliteTrendRes,
        downtimeParetoRes,
        disponibiliteByMachineRes,
        mttrCalendarRes,
      ] = await Promise.all([
        request.get(`${baseURL}/api/unique-dates`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/sidecards-arret`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/sidecards-arretnonDeclare`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/top-5-stops`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/arrets-by-range`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/arrets-table-range/${selectedDate ? selectedDate.format("YYYY-MM-DD") : ""}`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/stop-duration-trend/${selectedDate ? selectedDate.format("YYYY-MM-DD") : ""}`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/machine-stop-comparison/${selectedDate ? selectedDate.format("YYYY-MM-DD") : ""}`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/operator-stop-stats/${selectedDate ? selectedDate.format("YYYY-MM-DD") : ""}`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/stop-reasons/${selectedDate ? selectedDate.format("YYYY-MM-DD") : ""}`).query(params).retry(2).withCredentials(),
        // New API calls
        request.get(`${baseURL}/api/disponibilite-trend`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/downtime-pareto`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/disponibilite-by-machine`).query(params).retry(2).withCredentials(),
        request.get(`${baseURL}/api/mttr-calendar`).query(params).retry(2).withCredentials(),
      ])

      // Check if component is still mounted before updating state
      if (!isMounted.current) return

      // Process and update all data states
      setStopsData(allStopsRes.body)

      // Process duration trend data
      const sanitizedDurationData = allDurationRes.body
        ? allDurationRes.body
            .map((item) => ({
              ...item,
              hour: Number.parseInt(item.hour),
              avgDuration: Number.parseFloat(item.avgDuration) || 0,
            }))
            .sort((a, b) => a.hour - b.hour)
        : []
      setDurationTrend(sanitizedDurationData)

      // Calculate total and average durations
      const newTotalDuration = Array.isArray(allMachineRes.body)
  ? allMachineRes.body.reduce((sum, item) => {
      const duration = parseFloat(item.totalDuration)
      return sum + (isNaN(duration) ? 0 : duration)
    }, 0)
  : 0
      const newAvgDuration = sanitizedDurationData.length
        ? sanitizedDurationData.reduce((sum, item) => sum + item.avgDuration, 0) / sanitizedDurationData.length
        : 0

      // Update all other data states
      setMachineComparison(allMachineRes.body || [])
      setOperatorStats(allOperatorRes.body || [])
      setTotalDuration(newTotalDuration)
      setAvgDuration(newAvgDuration)
      setStopReasons(allReasonsRes.body || [])
      setDateOptions(datesRes.body)

      // Update sidebar stats
      setSidebarStats([
        {
          title: "Arrêts Totaux",
          value: arretRes.body[0]?.Arret_Totale || 0,
          icon: <AlertOutlined />,
          color: CHART_COLORS.primary,
        },
        {
          title: "Arrêts Non Déclarés",
          value: nonDeclareRes.body[0]?.Arret_Totale_nondeclare || 0,
          icon: <WarningOutlined />,
          color: CHART_COLORS.danger,
        },
      ])

      setTopStopsData(topStopsRes.body)

      setChartData(
        chartRes.body
          .map((item) => ({
            Stop_Date: dayjs(item.Stop_Date).format("YYYY-MM-DD"),
            Total_Stops: item.Total_Stops,
          }))
          .sort((a, b) => dayjs(a.Stop_Date).unix() - dayjs(b.Stop_Date).unix()),
      )

      // Calculate performance metrics if a machine is selected
      if (selectedMachine) {
        calculatePerformanceMetrics(allStopsRes.body)
      } else {
        setShowPerformanceMetrics(false)
      }

      // Update date range description if a date is selected
      if (selectedDate) {
        const { full } = formatDateRange(selectedDate, dateRangeType)
        setDateRangeDescription(full)
        setDateFilterActive(true)
      } else {
        setDateRangeDescription("")
        setDateFilterActive(false)
      }

      // Reset the filters changed flag
      filtersChanged.current = false

      // Show success message for better UX
      message.success("Données mises à jour avec succès")

      // Add new state updates for the chart data with proper validation
      setDisponibiliteTrendData(Array.isArray(disponibiliteTrendRes.data) ? disponibiliteTrendRes.data : [])
      setDowntimeParetoData(Array.isArray(downtimeParetoRes.data) ? downtimeParetoRes.data : [])
      setDisponibiliteByMachineData(Array.isArray(disponibiliteByMachineRes.data) ? disponibiliteByMachineRes.data : [])
      setMttrCalendarData(Array.isArray(mttrCalendarRes.data) ? mttrCalendarRes.data : [])
      console.log("Params sent to disponibiliteTrend:", params)

      console.log("disponibiliteTrendData fetched successfully" , disponibiliteTrendData)
      + console.log("disponibiliteByMachineData fetched successfully", disponibiliteByMachineRes.data)
      console.log("mttrCalendarData fetched successfully", mttrCalendarData)
      console.log("downtimeParetoData fetched successfully", downtimeParetoData)
    } catch (err) {
      console.error("Erreur lors du chargement des données:", err)

      // Check if component is still mounted before updating state
      if (!isMounted.current) return

      if (err.message === "Network Error") {
        setError("Erreur de connexion au serveur. Veuillez vérifier que le serveur API est en cours d'exécution.")
      } else {
        setError(`Erreur lors du chargement des données: ${err.message}`)
      }

      // Set default empty values to prevent further errors
      setStopsData([])
      setChartData([])
      setDurationTrend([])
      setMachineComparison([])
      setOperatorStats([])
      setTopStopsData([])
      setStopReasons([])

      // Add these to the error handling section - ensure they're initialized as empty arrays
      setDisponibiliteTrendData([])
      setDowntimeParetoData([])
      setDisponibiliteByMachineData([])
      setMttrCalendarData([])

      // Show error message for better UX
      message.error("Erreur lors du chargement des données")
    } finally {
      // Clear the timeout
      clearTimeout(loadingTimeout)

      // Always reset loading state and pendingFetch flag if component is still mounted
      if (isMounted.current) {
        setLoading(false)
      }
      pendingFetch.current = false
    }
  }, [selectedMachineModel, selectedMachine, selectedDate, dateRangeType, baseURL, formatDateRange])

  // Add this new helper function to calculate performance metrics
  const calculatePerformanceMetrics = useCallback(
    (stopsData) => {
      // Calculate total downtime in minutes
      const totalDowntime = stopsData.reduce((total, stop) => {
        try {
          if (
            !stop.Debut_Stop ||
            !stop.Fin_Stop_Time ||
            stop.Debut_Stop.includes("Invalid") ||
            stop.Fin_Stop_Time.includes("Invalid")
          ) {
            return total
          }

          const startTime = dayjs(stop.Debut_Stop, "DD/MM/YYYY HH:mm")
          const endTime = dayjs(stop.Fin_Stop_Time, "DD/MM/YYYY HH:mm")

          if (!startTime.isValid() || !endTime.isValid()) {
            return total
          }

          const durationMinutes = endTime.diff(startTime, "minute")
          return durationMinutes > 0 ? total + durationMinutes : total
        } catch (e) {
          return total
        }
      }, 0)

      // Calculate number of stops
      const numberOfStops = stopsData.length

      // Calculate total available time based on date range
      let totalAvailableTime = 24 * 60 // Default to 24 hours in minutes

      if (selectedDate) {
        if (dateRangeType === "day") {
          totalAvailableTime = 24 * 60 // 24 hours in minutes
        } else if (dateRangeType === "week") {
          totalAvailableTime = 7 * 24 * 60 // 7 days in minutes
        } else if (dateRangeType === "month") {
          // Approximate month length
          const daysInMonth = selectedDate.daysInMonth()
          totalAvailableTime = daysInMonth * 24 * 60
        }
      }

      // Calculate MTTR (Mean Time To Repair)
      const calculatedMttr = numberOfStops > 0 ? totalDowntime / numberOfStops : 0

      // Calculate MTBF (Mean Time Between Failures)
      const calculatedMtbf = numberOfStops > 0 ? (totalAvailableTime - totalDowntime) / numberOfStops : 0

      // Calculate DOPER (now renamed to Disponibilité)
      const calculatedDisponibilite =
        calculatedMtbf + calculatedMttr > 0 ? (calculatedMtbf / (calculatedMtbf + calculatedMttr)) * 100 : 0

      // Update state
      setMttr(calculatedMttr)
      setMtbf(calculatedMtbf)
      setDoper(calculatedDisponibilite) // Keep the variable name but it now represents "Disponibilité"
      setShowPerformanceMetrics(true)
    },
    [selectedDate, dateRangeType],
  )

  // Replace the existing fetchMachineData function with these separate functions
  // and update the related useEffect hooks

  // Replace the existing useEffect for machine data with these separate ones
  // First, replace the fetchMachineData function and its useEffect
  const fetchMachineModels = useCallback(async () => {
    try {
      console.log("Fetching machine models...")
      const response = await request.get(`${baseURL}/api/stops/machine-models`).retry(2).withCredentials()

      if (response.body && response.body.length > 0) {
        const models = response.body.map((item) => item.model || item)
        console.log("Machine models fetched:", models)
        setMachineModels(models)
      } else {
        // Set default models if API returns empty
        console.log("No machine models returned from API, using defaults")
        setMachineModels(["IPS", "CCM24"])
      }
    } catch (error) {
      console.error("Error loading machine models:", error)
      setError("Erreur lors du chargement des modèles de machines. Veuillez réessayer.")
      // Set default values if API fails
      setMachineModels(["IPS", "CCM24"])
    }
  }, [baseURL])

  const fetchMachineNames = useCallback(async () => {
    try {
      console.log("Fetching machine names...")
      const response = await request.get(`${baseURL}/api/stops/machine-names`).retry(2).withCredentials()

      if (response.body && response.body.length > 0) {
        console.log("Machine names fetched:", response.body)
        setMachineNames(response.body)

        // Find IPS01 in the machine names to set as default display value
        const ips01Machine = response.body.find((m) => m.Machine_Name === "IPS01")
        if (ips01Machine && !selectedMachineModel) {
          // Set IPS as the default model if not already selected
          setSelectedMachineModel("IPS")
        }
      } else {
        // Set default machine names if API returns empty
        console.log("No machine names returned from API, using defaults")
        setMachineNames([
          { Machine_Name: "IPS01" },
          { Machine_Name: "IPS02" },
          { Machine_Name: "IPS03" },
          { Machine_Name: "IPS04" },
        ])
      }
    } catch (error) {
      console.error("Error loading machine names:", error)
      setError("Erreur lors du chargement des noms de machines. Veuillez réessayer.")
      // Set default values if API fails
      setMachineNames([
        { Machine_Name: "IPS01" },
        { Machine_Name: "IPS02" },
        { Machine_Name: "IPS03" },
        { Machine_Name: "IPS04" },
      ])
    }
  }, [baseURL, selectedMachineModel])

  // Add these useEffect hooks for machine data fetching
  useEffect(() => {
    fetchMachineModels()
  }, [fetchMachineModels])

  useEffect(() => {
    fetchMachineNames()
  }, [fetchMachineNames])

  // Update the useEffect for data fetching to properly handle filter changes
  useEffect(() => {
    // Set isMounted to true when component mounts
    isMounted.current = true

    // Initial data fetch
    fetchData()

    // Cleanup function
    return () => {
      isMounted.current = false
      // Ensure we reset loading state and pendingFetch flag on unmount
      setLoading(false)
      pendingFetch.current = false
    }
  }, [fetchData])

  // Add this useEffect to trigger data fetch when filters change
  useEffect(() => {
    if (filtersChanged.current) {
      fetchData()
    }
  }, [selectedMachineModel, selectedMachine, selectedDate, dateRangeType, fetchData])

  // Update the event handlers to mark filters as changed
  const handleDateRangeTypeChange = (value) => {
    setDateRangeType(value)
    filtersChanged.current = true

    // If a date is already selected, update the description
    if (selectedDate) {
      const { full } = formatDateRange(selectedDate, value)
      setDateRangeDescription(full)
    }
  }

  const handleDateChange = (date) => {
    if (!date) {
      resetDateFilter()
      return
    }

    setSelectedDate(date)
    const { full } = formatDateRange(date, dateRangeType)
    setDateRangeDescription(full)
    setDateFilterActive(true)
    filtersChanged.current = true
  }

  const resetDateFilter = () => {
    setSelectedDate(null)
    setDateRangeDescription("")
    setDateFilterActive(false)
    filtersChanged.current = true
  }

  const handleMachineModelChange = (value) => {
    if (value === selectedMachineModel) return
    setSelectedMachineModel(value)
    setSelectedMachine("") // Clear machine selection when model changes
    filtersChanged.current = true
  }

  const handleMachineChange = (value) => {
    if (value === selectedMachine) return
    setSelectedMachine(value)
    filtersChanged.current = true
  }

  const handleResetMachineSelection = () => {
    setSelectedMachineModel("")
    setSelectedMachine("")
    filtersChanged.current = true
  }

  // Refresh data
  const handleRefresh = () => {
    fetchData()
  }

  // Elasticsearch search handlers
  const handleSearchResults = useCallback((results, query) => {
    setSearchResults(results);
    setSearchQuery(query);
    setSearchMode(!!results);
  }, []);

  const handleGlobalSearchResult = useCallback((result) => {
    // Handle global search result selection
    console.log('Global search result selected:', result);
    setGlobalSearchVisible(false);

    // You can add navigation logic here based on result type
    if (result.type === 'machine-stop') {
      // Handle machine stop result selection
      console.log('Machine stop selected:', result.data);
    }
  }, []);

  const clearSearchMode = useCallback(() => {
    setSearchResults(null);
    setSearchQuery('');
    setSearchMode(false);
  }, []);

  // Calculate additional statistics
  const percentageNonDeclared =
    sidebarStats.length >= 2 && sidebarStats[0].value > 0
      ? ((sidebarStats[1].value / sidebarStats[0].value) * 100).toFixed(1)
      : 0

  // Extended statistics
  const extendedStats = [
    ...sidebarStats,
    {
      title: "Durée Totale",
      value: Math.round(totalDuration),
      suffix: "min",
      icon: <ClockCircleOutlined />,
      color: CHART_COLORS.secondary,
    },
    {
      title: "Durée Moyenne",
      value: avgDuration.toFixed(1),
      suffix: "min",
      icon: <ClockCircleOutlined />,
      color: CHART_COLORS.success,
    },
    {
      title: "Interventions",
      value: operatorStats.reduce((sum, op) => sum + op.interventions, 0),
      icon: <ToolOutlined />,
      color: CHART_COLORS.purple,
    },
  ]

  // Function to render the DatePicker based on the selected date range type
  const renderDatePicker = () => {
    if (dateRangeType === "day") {
      return (
        <DatePicker
          value={selectedDate}
          onChange={handleDateChange}
          placeholder="Sélectionner une date"
          format="DD/MM/YYYY"
        />
      )
    } else if (dateRangeType === "week") {
      return (
        <DatePicker
          picker="week"
          value={selectedDate}
          onChange={handleDateChange}
          placeholder="Sélectionner une semaine"
          format="DD/MM/YYYY"
        />
      )
    } else if (dateRangeType === "month") {
      return (
        <DatePicker
          picker="month"
          value={selectedDate}
          onChange={handleDateChange}
          placeholder="Sélectionner un mois"
          format="MM/YYYY"
        />
      )
    }
    return null
  }

  // Add a new useEffect to monitor and debug the loading state
  // Add this after the other useEffect hooks:

  useEffect(() => {
    console.log("Loading state changed:", loading)
  }, [loading])

  return (
    <div style={{ padding: screens.md ? 24 : 16 }}>
      <Spin spinning={loading} tip="Chargement des données..." size="large">
        {error && (
          <Alert message="Erreur" description={error} type="error" showIcon closable style={{ marginBottom: 16 }} />
        )}

        <Row gutter={[24, 24]}>
          {/* Header section */}
          <Col span={24}>
            <Card bordered={false} bodyStyle={{ padding: screens.md ? 24 : 16 }}>
              <Row gutter={[24, 24]} align="middle">
                <Col xs={24} md={12}>
                  <Title level={3} style={{ marginBottom: 8 }}>
                    <AlertOutlined style={{ marginRight: 12, color: CHART_COLORS.danger }} />
                    Analyse des Arrêts de Production
                  </Title>
                  <Paragraph type="secondary">
                    Visualisation et analyse des arrêts machines pour optimiser la production
                  </Paragraph>
                </Col>

                {/* Enhanced Filters section with Elasticsearch */}
                <Col xs={24} md={12}>
                  <FilterPanel
                    selectedMachineModel={selectedMachineModel}
                    selectedMachine={selectedMachine}
                    machineModels={machineModels.map(model => typeof model === "object" ? model.model : model)}
                    filteredMachineNames={filteredMachineNames}
                    dateRangeType={dateRangeType}
                    dateFilter={selectedDate}
                    dateFilterActive={dateFilterActive}
                    handleMachineModelChange={handleMachineModelChange}
                    handleMachineChange={handleMachineChange}
                    handleDateRangeTypeChange={handleDateRangeTypeChange}
                    handleDateChange={handleDateChange}
                    resetFilters={() => {
                      handleResetMachineSelection();
                      resetDateFilter();
                    }}
                    handleRefresh={handleRefresh}
                    loading={loading}
                    dataSize={stopsData.length}
                    pageType="arrets"
                    onSearchResults={handleSearchResults}
                    enableElasticsearch={true}
                  />
                </Col>
              </Row>
            </Card>
          </Col>

          {/* Statistics cards */}
          {extendedStats.map((stat, index) => (
            <Col key={index} xs={24} sm={12} md={8} lg={6}>
              <Card
                bordered={false}
                hoverable
                style={{
                  borderTop: `2px solid ${stat.color || COLORS[index % COLORS.length]}`,
                  height: "100%",
                }}
              >
                <Statistic
                  title={
                    <Space>
                      {stat.icon &&
                        React.cloneElement(stat.icon, {
                          style: { color: stat.color || COLORS[index % COLORS.length] },
                        })}
                      <span>{stat.title}</span>
                      {stat.title === "Arrêts Totaux" && dateFilterActive && (
                        <Popover content={`Nombre total d'arrêts ${dateRangeDescription}`} title="Période sélectionnée">
                          <InfoCircleOutlined style={{ color: CHART_COLORS.primary, cursor: "pointer" }} />
                        </Popover>
                      )}
                    </Space>
                  }
                  value={stat.value}
                  suffix={stat.suffix}
                  valueStyle={{
                    fontSize: 24,
                    color: stat.color || COLORS[index % COLORS.length],
                  }}
                />
                {stat.title === "Arrêts Non Déclarés" && (
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">{percentageNonDeclared}% du total</Text>
                    <Progress
                      percent={Number.parseFloat(percentageNonDeclared)}
                      showInfo={false}
                      strokeColor={CHART_COLORS.danger}
                      size="small"
                    />
                  </div>
                )}
              </Card>
            </Col>
          ))}

          {/* Performance Metrics Cards - Only visible when a machine is selected */}
          {showPerformanceMetrics && (
            <>
            {/*
              <Col xs={24}>
                <Divider orientation="left">
                  <Space>
                    <DashboardOutlined />
                    <span>Indicateurs de Performance pour {selectedMachine}</span>
                  </Space>
                </Divider>
              </Col>
*/}
              {/* MTTR Card
}
              <Col xs={24} sm={12} md={8}>
                <Card
                  bordered={false}
                  hoverable
                  style={{
                    borderTop: `2px solid ${CHART_COLORS.orange}`,
                    height: "100%",
                  }}
                >
                  <Statistic
                    title={
                      <Space>
                        <ToolOutlined style={{ color: CHART_COLORS.orange }} />
                        <span>MTTR (Mean Time To Repair)</span>
                        <Popover
                          content="Temps moyen de réparation, calculé comme le temps total d'arrêt divisé par le nombre d'arrêts."
                          title="Définition"
                        >
                          <InfoCircleOutlined style={{ color: CHART_COLORS.orange, cursor: "pointer" }} />
                        </Popover>
                      </Space>
                    }
                    value={mttr.toFixed(1)}
                    suffix="min"
                    valueStyle={{
                      fontSize: 24,
                      color: CHART_COLORS.orange,
                    }}
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">Temps moyen nécessaire pour réparer une panne</Text>
                    <Progress
                      percent={Math.min(100, (mttr / 60) * 100)}
                      showInfo={false}
                      strokeColor={
                        mttr > 45 ? CHART_COLORS.danger : mttr > 20 ? CHART_COLORS.warning : CHART_COLORS.success
                      }
                      size="small"
                    />
                  </div>
                </Card>
              </Col>
            */ }
              {/* MTBF Card
              }
              <Col xs={24} sm={12} md={8}>
                <Card
                  bordered={false}
                  hoverable
                  style={{
                    borderTop: `2px solid ${CHART_COLORS.cyan}`,
                    height: "100%",
                  }}
                >
                  <Statistic
                    title={
                      <Space>
                        <ClockCircleOutlined style={{ color: CHART_COLORS.cyan }} />
                        <span>MTBF (Mean Time Between Failures)</span>
                        <Popover
                          content="Temps moyen entre les pannes, calculé comme (temps total disponible - temps total d'arrêt) / nombre d'arrêts."
                          title="Définition"
                        >
                          <InfoCircleOutlined style={{ color: CHART_COLORS.cyan, cursor: "pointer" }} />
                        </Popover>
                      </Space>
                    }
                    value={mtbf.toFixed(1)}
                    suffix="min"
                    valueStyle={{
                      fontSize: 24,
                      color: CHART_COLORS.cyan,
                    }}
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">Temps moyen entre deux pannes consécutives</Text>
                    <Progress
                      percent={Math.min(100, Math.max(0, (mtbf / 1440) * 100))} // 1440 = 24h in minutes
                      showInfo={false}
                      strokeColor={CHART_COLORS.cyan}
                      size="small"
                    />
                  </div>
                </Card>
              </Col>
            */ }
              {/* DOPER Card
              }
              <Col xs={24} sm={12} md={8}>
                <Card
                  bordered={false}
                  hoverable
                  style={{
                    borderTop: `2px solid ${CHART_COLORS.purple}`,
                    height: "100%",
                  }}
                >
                  <Statistic
                    title={
                      <Space>
                        <PieChartOutlined style={{ color: CHART_COLORS.purple }} />
                        <span>Disponibilité</span>
                        <Popover
                          content="Indicateur de performance opérationnelle, calculé comme MTBF / (MTBF + MTTR)."
                          title="Définition"
                        >
                          <InfoCircleOutlined style={{ color: CHART_COLORS.purple, cursor: "pointer" }} />
                        </Popover>
                      </Space>
                    }
                    value={doper.toFixed(1)}
                    suffix="%"
                    valueStyle={{
                      fontSize: 24,
                      color: CHART_COLORS.purple,
                    }}
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">Ratio de disponibilité opérationnelle</Text>
                    <Progress
                      percent={doper}
                      showInfo={false}
                      strokeColor={
                        doper < 70 ? CHART_COLORS.danger : doper < 85 ? CHART_COLORS.warning : CHART_COLORS.success
                      }
                      size="small"
                    />
                  </div>
                </Card>
              </Col>
            */ }
            </>
          )}

          {/* Tabs for charts */}
          <Col span={24}>
            <Card bordered={false}>
              <Tabs
                defaultActiveKey="1"
                onChange={setActiveTab}
                tabBarExtraContent={
                  <Space>
                    <Button
                      type="link"
                      icon={<SearchOutlined />}
                      onClick={() => setGlobalSearchVisible(true)}
                    >
                      Recherche globale
                    </Button>
                    {dateFilterActive && (
                      <Tag color="blue">
                        <CalendarOutlined style={{ marginRight: 4 }} />
                        {dateRangeDescription}
                      </Tag>
                    )}
                    <Button type="link" icon={<DownloadOutlined />} disabled>
                      Exporter
                    </Button>
                  </Space>
                }
              >
                <TabPane
                  tab={
                    <span>
                      <LineChartOutlined />
                      Tendances
                    </span>
                  }
                  key="1"
                >
                  <Row gutter={[24, 24]}>
                    <Col xs={24} lg={12}>
                      <Card title="Évolution des Arrêts" bordered={false}>
                        {chartData.length > 0 ? (
                          <MemoizedLineChart data={chartData} />
                        ) : (
                          <Empty description="Aucune donnée disponible" />
                        )}
                      </Card>
                    </Col>
                    <Col xs={24} lg={12}>
                      <Card
                        title="Tendance de la Durée des Arrêts"
                        bordered={false}
                      >
                        {durationTrend.length > 0 ? (
                          <MemoizedAreaChart data={durationTrend} />
                        ) : (
                          <Empty description="Aucune donnée disponible" />
                        )}
                      </Card>
                    </Col>
                  </Row>
                </TabPane>

                <TabPane
                  tab={
                    <span>
                      <PieChartOutlined />
                      Répartition
                    </span>
                  }
                  key="2"
                >
                  <Row gutter={[24, 24]}>
                    <Col xs={24} lg={12}>
                      <Card title="Top 5 des Causes d'Arrêt" bordered={false} extra={<Tag color="purple">Global</Tag>}>
                        {topStopsData.length > 0 ? (
                          <MemoizedPieChart data={topStopsData} />
                        ) : (
                          <Empty description="Aucune donnée disponible" />
                        )}
                      </Card>
                    </Col>
                    <Col xs={24} lg={12}>
                      <Card
                        title="Causes d'Arrêt"
                        bordered={false}
                        extra={<Tag color="cyan">{dateFilterActive ? dateRangeDescription : "Toutes les données"}</Tag>}
                      >
                        {stopReasons.length > 0 ? (
                          <MemoizedHorizontalBarChart data={stopReasons} />
                        ) : (
                          <Empty description="Aucune donnée disponible" />
                        )}
                      </Card>
                    </Col>
                  </Row>
                </TabPane>

                <TabPane
                  tab={
                    <span>
                      <BarChartOutlined />
                      Comparaisons
                    </span>
                  }
                  key="3"
                >
                  <Row gutter={[24, 24]}>
                    <Col xs={24} lg={12}>
                      <Card
                        title="Comparaison par Machine"
                        bordered={false}
                        extra={
                          <Tag color="orange">{dateFilterActive ? dateRangeDescription : "Toutes les données"}</Tag>
                        }
                      >
                        {machineComparison.length > 0 ? (
                          <MemoizedBarChart data={machineComparison} />
                        ) : (
                          <Empty description="Aucune donnée disponible" />
                        )}
                      </Card>
                    </Col>
                    <Col xs={24} lg={12}>
                      <Card
                        title="Interventions par Opérateur"
                        bordered={false}
                        extra={<Badge count={operatorStats.length} style={{ backgroundColor: CHART_COLORS.purple }} />}
                      >
                        {operatorStats.length > 0 ? (
                          <Table
                            dataSource={operatorStats}
                            columns={[
                              {
                                title: "Opérateur",
                                dataIndex: "operator",
                                render: (text) => (
                                  <Space>
                                    <UserOutlined style={{ color: CHART_COLORS.purple }} />
                                    {text || "Non assigné"}
                                  </Space>
                                ),
                              },
                              {
                                title: "Interventions",
                                dataIndex: "interventions",
                                render: (value) => <Tag color="purple">{value}</Tag>,
                                sorter: (a, b) => a.interventions - b.interventions,
                                defaultSortOrder: "descend",
                              },
                            ]}
                            pagination={false}
                            size="middle"
                            rowKey="operator"
                          />
                        ) : (
                          <Empty description="Aucune donnée disponible" />
                        )}
                      </Card>
                    </Col>
                  </Row>
                </TabPane>

                <TabPane
                  tab={
                    <span>
                      <DashboardOutlined />
                      Tableau de Bord
                    </span>
                  }
                  key="4"
                >
                  <Card
                    title={
                      <Space>
                        <FilterOutlined />
                        <span>
                          Détails des Arrêts {dateFilterActive ? `(${dateRangeDescription})` : "(toutes les données)"}
                        </span>
                      </Space>
                    }
                    bordered={false}
                    extra={
                      <Space>
                        <AntTooltip title="Nombre total d'arrêts">
                          <Badge
                            count={stopsData.length}
                            style={{ backgroundColor: CHART_COLORS.primary }}
                            overflowCount={999}
                          />
                        </AntTooltip>
                        <AntTooltip title="Exporter les données">
                          <Button type="link" icon={<DownloadOutlined />} disabled>
                            Exporter
                          </Button>
                        </AntTooltip>
                      </Space>
                    }
                  >
                    {stopsData.length > 0 ? (
                      <>
                        <div style={{ marginBottom: 16 }}>
                          <Text type="secondary">
                            Ce tableau présente tous les arrêts enregistrés pour la période sélectionnée. Vous pouvez
                            filtrer par machine, code d'arrêt ou responsable, et trier par durée ou date.
                          </Text>
                        </div>
                        <MemoizedTable data={stopsData} />
                        <style jsx>{`
                          .highlight-row-error {
                            background-color: rgba(245, 34, 45, 0.05);
                          }
                          .ant-table-row:hover {
                            cursor: pointer;
                            background-color: rgba(24, 144, 255, 0.05) !important;
                          }
                        `}</style>
                      </>
                    ) : (
                      <Empty
                        description={`Aucun arrêt enregistré pour cette ${dateRangeType === "day" ? "journée" : dateRangeType === "week" ? "semaine" : "période"}`}
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      />
                    )}
                  </Card>
                </TabPane>
                <TabPane
                  tab={
                    <span>
                      <DashboardOutlined />
                      Performance
                    </span>
                  }
                  key="5"
                >
                  <Row gutter={[24, 24]}>
                    {/* Performance Metrics Gauges */}
                    <Col span={24}>
                      <Card
                        title="Indicateurs de Performance"
                        bordered={false}
                        extra={
                          selectedMachine ? (
                            <Tag color="blue">{selectedMachine}</Tag>
                          ) : (
                            <Tag color="blue">Toutes les machines</Tag>
                          )
                        }
                      >
                        <PerformanceMetricsGauge
                          data={{
                            disponibilite: doper, // Use the renamed metric
                            mttr: mttr,
                            mtbf: mtbf,
                          }}
                          selectedMachine={selectedMachine}
                          loading={loading}
                        />
                      </Card>
                    </Col>

<Col xs={24} lg={12}>
                      <Card
                        title="Analyse Pareto des Arrêts"
                        bordered={false}
                        extra={<Tag color="orange">Impact cumulé</Tag>}
                      >
                        <DowntimeParetoChart
                          data={downtimeParetoData}
                          selectedMachine={selectedMachine}
                          selectedDate={selectedDate}
                          dateRangeType={dateRangeType}
                          loading={loading}
                        />
                      </Card>
                    </Col>
                    {/* Disponibilité by Machine Chart */}
                    <Col xs={24} lg={12}>
                      <Card
                        title="Disponibilité par Machine"
                        bordered={false}
                        extra={selectedMachineModel ? <Tag color="purple">{selectedMachineModel}</Tag> : null}
                      >
                        <DisponibiliteByMachineChart
                          data={disponibiliteByMachineData}
                          selectedMachine={selectedMachine}
                          selectedMachineModel={selectedMachineModel}
                          loading={loading}
                        />
                      </Card>
                    </Col>



                    {/* MTTR Heat Calendar */}
                    <Col xs={24} lg={24}>
                      <Card title="Calendrier MTTR" bordered={false} extra={<Tag color="cyan">Vue calendrier</Tag>}>
                        <MttrHeatCalendar
                          data={mttrCalendarData}
                          selectedMachine={selectedMachine}
                          selectedDate={selectedDate}
                          dateRangeType={dateRangeType}
                          loading={loading}
                        />
                      </Card>
                    </Col>
                  </Row>
                </TabPane>
              </Tabs>
            </Card>
          </Col>

          {/* Summary statistics */}
          <Col span={24}>
            <Card
              title={
                <Space>
                  <SettingOutlined />
                  <span>Résumé des Arrêts {dateFilterActive ? `(${dateRangeDescription})` : ""}</span>
                </Space>
              }
              bordered={false}
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={12}>
                  <Statistic
                    title="Taux d'Arrêts Non Déclarés"
                    value={percentageNonDeclared}
                    suffix="%"
                    valueStyle={{ color: CHART_COLORS.danger }}
                    prefix={<WarningOutlined />}
                  />
                  <Progress
                    percent={Number.parseFloat(percentageNonDeclared)}
                    status={percentageNonDeclared > 30 ? "exception" : "normal"}
                    strokeColor={
                      percentageNonDeclared > 30
                        ? CHART_COLORS.danger
                        : percentageNonDeclared > 15
                          ? CHART_COLORS.warning
                          : CHART_COLORS.success
                    }
                  />
                  <Divider />
                  <Paragraph>
                    <Text strong>Analyse: </Text>
                    {percentageNonDeclared > 30
                      ? "Le taux d'arrêts non déclarés est très élevé. Une investigation est nécessaire pour améliorer le processus de déclaration."
                      : percentageNonDeclared > 15
                        ? "Le taux d'arrêts non déclarés est modéré. Des améliorations peuvent être apportées au processus de déclaration."
                        : "Le taux d'arrêts non déclarés est faible, ce qui indique un bon suivi des procédures."}
                  </Paragraph>
                </Col>
                <Col xs={24} md={12}>
                  <Statistic
                    title="Durée Moyenne des Arrêts"
                    value={parseFloat(avgDuration).toFixed(2)}
                    suffix="min"
                    valueStyle={{ color: CHART_COLORS.secondary }}
                    prefix={<ClockCircleOutlined />}
                  />
                  <Progress
                    percent={Math.min(100, (parseFloat(avgDuration) / 60) * 100).toFixed(2)}
                    status={avgDuration > 45 ? "exception" : "normal"}
                    strokeColor={
                      avgDuration > 45
                        ? CHART_COLORS.danger
                        : avgDuration > 20
                          ? CHART_COLORS.warning
                          : CHART_COLORS.success
                    }
                  />
                  <Divider />
                  <Paragraph>
                    <Text strong>Analyse: </Text>
                    {avgDuration > 45
                      ? "La durée moyenne des arrêts est très élevée. Des actions correctives sont nécessaires pour réduire le temps d'intervention."
                      : avgDuration > 20
                        ? "La durée moyenne des arrêts est modérée. Des optimisations peuvent être envisagées pour réduire le temps d'intervention."
                        : "La durée moyenne des arrêts est faible, ce qui indique une bonne réactivité des équipes d'intervention."}
                  </Paragraph>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </Spin>

      {/* Search Results Display */}
      {searchMode && searchResults && (
        <div style={{ marginTop: 24 }}>
          <SearchResultsDisplay
            results={searchResults}
            searchQuery={searchQuery}
            pageType="arrets"
            loading={loading}
            onResultSelect={(result) => {
              console.log('Arrets result selected:', result);
              // Handle result selection - could navigate to specific stop details
            }}
            onPageChange={(page) => {
              // Handle pagination
              console.log('Page changed:', page);
            }}
          />
        </div>
      )}

      {/* Global Search Modal */}
      <GlobalSearchModal
        visible={globalSearchVisible}
        onClose={() => setGlobalSearchVisible(false)}
        onResultSelect={handleGlobalSearchResult}
      />
    </div>
  )
}

export default Arrets2
