// Test backend connection
console.log('🧪 Testing backend connection...');

const testRequest = async () => {
  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getStopSidecards {
              Arret_Totale
              Arret_Totale_nondeclare
            }
          }
        `
      })
    });
    
    const result = await response.json();
    console.log('📊 Backend Response:', JSON.stringify(result, null, 2));
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
    } else {
      console.log('✅ Backend working correctly');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
};

testRequest();
