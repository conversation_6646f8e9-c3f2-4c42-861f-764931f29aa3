/**
 * Test script for the optimized frontend hook
 * Tests the new comprehensive query approach
 */

import fetch from 'node-fetch';

const GRAPHQL_ENDPOINT = 'http://localhost:5000/api/graphql';

// Test the comprehensive query from frontend perspective
async function testComprehensiveQuery() {
  console.log('🧪 Testing optimized frontend hook query...');
  
  const query = `
    query GetFinalComprehensiveStopData($filters: FinalOptimizedStopFilterInput) {
      getFinalComprehensiveStopData(filters: $filters) {
        # Raw stop data
        allStops {
          Machine_Name
          Date_Insert
          Part_NO
          Code_Stop
          Debut_Stop
          Fin_Stop_Time
          Regleur_Prenom
          duration_minutes
          Cause
          Raison_Arret
          Operateur
        }
        
        # Pre-computed analytics
        topStops {
          stopName
          count
        }
        
        stopReasons {
          reason
          count
        }
        
        machineComparison {
          Machine_Name
          stops
          totalDuration
        }
        
        operatorStats {
          operator
          interventions
          totalDuration
        }
        
        durationTrend {
          hour
          avgDuration
        }
        
        stopStats {
          Stop_Date
          Total_Stops
        }
        
        # Summary statistics
        sidecards {
          Arret_Totale
          Arret_Totale_nondeclare
        }
        
        # Metadata
        totalRecords
        queryExecutionTime
        cacheHit
      }
    }
  `;

  const filters = {
    model: 'IPS',
    limit: 100
  };

  try {
    console.log('📤 Sending GraphQL request with filters:', filters);
    
    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        query,
        variables: { filters }
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL errors:', result.errors);
      return;
    }

    const data = result.data.getFinalComprehensiveStopData;
    
    console.log('✅ Frontend hook test successful!');
    console.log(`⏱️  Query execution time: ${data.queryExecutionTime}ms`);
    console.log(`📊 Total records: ${data.totalRecords}`);
    console.log(`🏭 All stops count: ${data.allStops.length}`);
    console.log(`🔝 Top stops count: ${data.topStops.length}`);
    console.log(`🏗️  Machine comparison count: ${data.machineComparison.length}`);
    console.log(`👷 Operator stats count: ${data.operatorStats.length}`);
    console.log(`📈 Duration trend count: ${data.durationTrend.length}`);
    console.log(`📊 Stop stats count: ${data.stopStats.length}`);
    console.log(`📋 Sidecards:`, data.sidecards);
    
    // Test a few sample records
    if (data.allStops.length > 0) {
      console.log('\n📋 Sample stop record:');
      console.log(JSON.stringify(data.allStops[0], null, 2));
    }
    
    if (data.topStops.length > 0) {
      console.log('\n🔝 Top stops sample:');
      console.log(data.topStops.slice(0, 3));
    }
    
    return data;
    
  } catch (error) {
    console.error('❌ Frontend hook test failed:', error);
    throw error;
  }
}

// Test utility queries
async function testUtilityQueries() {
  console.log('\n🧪 Testing utility queries...');
  
  // Test machine models
  const modelsQuery = `
    query {
      getFinalStopMachineModels {
        model
      }
    }
  `;
  
  // Test machine names
  const namesQuery = `
    query GetFinalStopMachineNames($filters: FinalOptimizedStopFilterInput) {
      getFinalStopMachineNames(filters: $filters) {
        Machine_Name
      }
    }
  `;
  
  try {
    // Test models
    console.log('📤 Testing machine models query...');
    const modelsResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: modelsQuery })
    });
    
    const modelsResult = await modelsResponse.json();
    if (modelsResult.errors) {
      console.error('❌ Models query errors:', modelsResult.errors);
    } else {
      console.log('✅ Machine models:', modelsResult.data.getFinalStopMachineModels);
    }
    
    // Test names with model filter
    console.log('📤 Testing machine names query with IPS filter...');
    const namesResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query: namesQuery,
        variables: { filters: { model: 'IPS' } }
      })
    });
    
    const namesResult = await namesResponse.json();
    if (namesResult.errors) {
      console.error('❌ Names query errors:', namesResult.errors);
    } else {
      console.log('✅ IPS machine names:', namesResult.data.getFinalStopMachineNames);
    }
    
  } catch (error) {
    console.error('❌ Utility queries test failed:', error);
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting optimized frontend hook tests...\n');
  
  try {
    await testComprehensiveQuery();
    await testUtilityQueries();
    
    console.log('\n✅ All frontend hook tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Frontend hook tests failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  runTests();
}

export { testComprehensiveQuery, testUtilityQueries };
