# LOCQL Docker + Pomerium Startup Script (PowerShell)
# This script starts the LOCQL application with Pomerium proxy

Write-Host "🚀 LOCQL Docker + Pomerium Startup (Windows)" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Configuration
$POMERIUM_FRONTEND_URL = "https://locql.adapted-osprey-5307.pomerium.app"
$POMERIUM_API_URL = "https://api.adapted-osprey-5307.pomerium.app"
$POMERIUM_WS_URL = "wss://ws.adapted-osprey-5307.pomerium.app"
$BACKEND_PORT = 5000
$FRONTEND_PORT = 5173
$POMERIUM_PORT = 443

# Function to print colored status
function Write-Status {
    param(
        [bool]$Success,
        [string]$Message
    )
    if ($Success) {
        Write-Host "✅ $Message" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $Message" -ForegroundColor Red
        return $false
    }
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

# Check if Docker is running
function Test-Docker {
    Write-Info "Checking Docker status..."
    
    try {
        $dockerVersion = docker --version 2>$null
        $dockerPs = docker ps 2>$null
        if ($dockerVersion -and $dockerPs) {
            return Write-Status $true "Docker is running"
        } else {
            return Write-Status $false "Docker is not running or accessible"
        }
    } catch {
        return Write-Status $false "Docker is not running or accessible"
    }
}

# Check if MySQL is accessible
function Test-MySQL {
    Write-Info "Checking MySQL connectivity..."
    
    try {
        $mysqlPath = Get-Command mysql -ErrorAction SilentlyContinue
        if ($mysqlPath) {
            $mysqlTest = mysql -h localhost -u root -proot -e "SELECT 1;" 2>$null
            if ($LASTEXITCODE -eq 0) {
                return Write-Status $true "MySQL is accessible"
            } else {
                return Write-Status $false "MySQL connection failed"
            }
        } else {
            Write-Warning "MySQL client not found, skipping database check"
            return $true
        }
    } catch {
        Write-Warning "MySQL client not found, skipping database check"
        return $true
    }
}

# Check if ports are available
function Test-Ports {
    Write-Info "Checking port availability..."
    
    $portsOk = $true
    
    # Check backend port
    $port5000 = Get-NetTCPConnection -LocalPort $BACKEND_PORT -ErrorAction SilentlyContinue
    if (-not $port5000) {
        Write-Status $true "Port $BACKEND_PORT (backend) is available"
    } else {
        Write-Warning "Port $BACKEND_PORT is already in use"
        $portsOk = $false
    }
    
    # Check frontend port
    $port5173 = Get-NetTCPConnection -LocalPort $FRONTEND_PORT -ErrorAction SilentlyContinue
    if (-not $port5173) {
        Write-Status $true "Port $FRONTEND_PORT (frontend) is available"
    } else {
        Write-Warning "Port $FRONTEND_PORT is already in use"
        $portsOk = $false
    }
    
    # Check Pomerium port
    $port443 = Get-NetTCPConnection -LocalPort $POMERIUM_PORT -ErrorAction SilentlyContinue
    if (-not $port443) {
        Write-Status $true "Port $POMERIUM_PORT (Pomerium HTTPS) is available"
    } else {
        Write-Warning "Port $POMERIUM_PORT is already in use"
        $portsOk = $false
    }
    
    return $portsOk
}

# Check required files
function Test-RequiredFiles {
    Write-Info "Checking required files..."
    
    $requiredFiles = @(
        "docker-compose.pomerium.yml",
        "pomerium.env",
        "backend/Dockerfile",
        "frontend/Dockerfile"
    )
    
    $allFilesExist = $true
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Status $true "$file exists"
        } else {
            Write-Status $false "$file is missing"
            $allFilesExist = $false
        }
    }
    
    return $allFilesExist
}

# Start Docker containers with Pomerium
function Start-PomeriumContainers {
    Write-Info "Starting Docker containers with Pomerium..."
    
    # Stop any existing containers
    docker-compose -f docker-compose.pomerium.yml down 2>$null | Out-Null
    
    # Start containers
    try {
        docker-compose -f docker-compose.pomerium.yml up --build -d
        if ($LASTEXITCODE -eq 0) {
            return Write-Status $true "Docker containers with Pomerium started successfully"
        } else {
            return Write-Status $false "Failed to start Docker containers with Pomerium"
        }
    } catch {
        return Write-Status $false "Failed to start Docker containers with Pomerium"
    }
}

# Wait for services to be ready
function Wait-ForPomeriumServices {
    Write-Info "Waiting for services to be ready..."
    
    $maxAttempts = 60  # Increased for Pomerium startup time
    $attempt = 1
    
    # Wait for backend
    Write-Info "Waiting for backend service..."
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$BACKEND_PORT/api/health/ping" -TimeoutSec 2 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Status $true "Backend service is ready"
                break
            }
        } catch {
            # Service not ready yet
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
        $attempt++
    }
    
    if ($attempt -gt $maxAttempts) {
        Write-Status $false "Backend service failed to start within timeout"
        return $false
    }
    
    # Wait for frontend
    Write-Info "Waiting for frontend service..."
    $attempt = 1
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$FRONTEND_PORT/" -TimeoutSec 2 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Status $true "Frontend service is ready"
                break
            }
        } catch {
            # Service not ready yet
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
        $attempt++
    }
    
    if ($attempt -gt $maxAttempts) {
        Write-Status $false "Frontend service failed to start within timeout"
        return $false
    }
    
    # Wait for Pomerium
    Write-Info "Waiting for Pomerium proxy..."
    $attempt = 1
    while ($attempt -le $maxAttempts) {
        try {
            # Check if Pomerium is responding (may get certificate errors, that's OK)
            $response = Invoke-WebRequest -Uri $POMERIUM_FRONTEND_URL -TimeoutSec 5 -UseBasicParsing -SkipCertificateCheck
            if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 302) {
                Write-Status $true "Pomerium proxy is ready"
                break
            }
        } catch {
            # Pomerium not ready yet or certificate issues (expected initially)
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 3
        $attempt++
    }
    
    if ($attempt -gt $maxAttempts) {
        Write-Warning "Pomerium proxy may still be starting up (this is normal)"
    }
    
    return $true
}

# Main execution
function Main {
    Write-Host ""
    Write-Info "Starting pre-flight checks..."
    
    # Check Docker
    if (-not (Test-Docker)) {
        Write-Host ""
        Write-Warning "Please start Docker Desktop and try again"
        exit 1
    }
    
    # Check MySQL
    Test-MySQL | Out-Null
    
    # Check ports
    if (-not (Test-Ports)) {
        Write-Host ""
        Write-Warning "Some ports are in use. Please stop conflicting services or change ports."
        $continue = Read-Host "Continue anyway? (y/N)"
        if ($continue -ne 'y' -and $continue -ne 'Y') {
            exit 1
        }
    }
    
    # Check required files
    if (-not (Test-RequiredFiles)) {
        Write-Host ""
        Write-Warning "Some required files are missing. Please ensure all files are present."
        exit 1
    }
    
    Write-Host ""
    Write-Info "All checks passed! Starting containers with Pomerium..."
    
    # Start containers
    if (-not (Start-PomeriumContainers)) {
        exit 1
    }
    
    # Wait for services
    if (-not (Wait-ForPomeriumServices)) {
        Write-Warning "Some services may not be fully ready"
    }
    
    Write-Host ""
    Write-Status $true "LOCQL application with Pomerium is running!"
    Write-Host ""
    Write-Info "Access URLs:"
    Write-Host "  • Frontend (Pomerium):  $POMERIUM_FRONTEND_URL" -ForegroundColor White
    Write-Host "  • API (Pomerium):       $POMERIUM_API_URL" -ForegroundColor White
    Write-Host "  • WebSocket (Pomerium): $POMERIUM_WS_URL/api/machine-data-ws" -ForegroundColor White
    Write-Host "  • Frontend (Local):     http://localhost:$FRONTEND_PORT" -ForegroundColor Gray
    Write-Host "  • Backend (Local):      http://localhost:$BACKEND_PORT" -ForegroundColor Gray
    Write-Host ""
    Write-Info "Authentication:"
    Write-Host "  • You'll be redirected to authenticate via your configured identity provider" -ForegroundColor White
    Write-Host "  • Currently configured for Gmail domain authentication" -ForegroundColor White
    Write-Host ""
    Write-Info "To stop the application:"
    Write-Host "  docker-compose -f docker-compose.pomerium.yml down" -ForegroundColor White
    Write-Host ""
    Write-Info "To view logs:"
    Write-Host "  docker-compose -f docker-compose.pomerium.yml logs -f" -ForegroundColor White
    Write-Host ""
    Write-Warning "Note: First access may take a few minutes as Pomerium generates SSL certificates"
}

# Run main function
Main
