/**
 * Test the enhanced PDF generation endpoint after database fix
 */

import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:5000';

async function testEnhancedEndpoint() {
  console.log('🧪 Testing Enhanced PDF Generation Endpoint');
  console.log('==========================================\n');

  // Test payload
  const testPayload = {
    machineId: 'TEST01',
    date: '2025-01-16',
    shift: 'Matin'
  };

  console.log('📋 Test payload:', testPayload);

  try {
    console.log('\n🚀 Calling enhanced PDF generation endpoint...');
    const response = await fetch(`${BACKEND_URL}/api/shift-reports/generate-enhanced`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: In production, you'd need proper authentication headers
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`📊 Response status: ${response.status} ${response.statusText}`);

    if (response.ok) {
      const result = await response.json();
      console.log('\n✅ Enhanced PDF generation successful!');
      console.log('📄 Response details:', {
        success: result.success,
        reportId: result.reportId,
        version: result.version,
        fileSize: result.fileSize ? `${(result.fileSize / 1024).toFixed(2)} KB` : 'Unknown',
        downloadPath: result.downloadPath,
        message: result.message
      });

      // Verify the version was saved correctly
      if (result.version === 'enhanced-react') {
        console.log('\n🎉 Database schema fix confirmed!');
        console.log('✅ "enhanced-react" version identifier saved successfully');
        console.log('✅ No more "Data too long for column" errors');
      } else {
        console.log(`\n⚠️ Unexpected version: ${result.version}`);
      }

    } else {
      const errorText = await response.text();
      console.log('\n❌ Enhanced PDF generation failed');
      console.log('📋 Error response:', errorText);
      
      // Check if it's still the database error
      if (errorText.includes('Data too long for column')) {
        console.log('\n💡 The database schema fix may not have been applied correctly');
        console.log('   Please run: node backend/scripts/fixReportsVersionColumn.js');
      } else if (errorText.includes('Machine ID is required')) {
        console.log('\n💡 This is a validation error, not a database schema issue');
      } else if (errorText.includes('PDF generation failed')) {
        console.log('\n💡 This is a PDF generation error, not a database schema issue');
      }
    }

  } catch (error) {
    console.error('\n❌ Request failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Backend server is not running');
      console.error('   Please start it with: cd backend && npm run dev');
    }
  }
}

// Run the test
testEnhancedEndpoint().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
