// Test script to debug the machine comparison data structure
import fetch from 'node-fetch';

const testGraphQLQuery = async () => {
  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query GetStopDashboardData($filters: StopFilterInput) {
            getStopDashboardData(filters: $filters) {
              sidecards {
                Arret_Totale
                Arret_Totale_nondeclare
              }
              stopComparison {
                Machine_Name
                stops
                totalDuration
              }
              allStops {
                Machine_Name
                Date_Insert
                Code_Stop
                duration_minutes
              }
            }
          }
        `,
        variables: {
          filters: {
            model: "IPS",
            limit: 10
          }
        }
      })
    });

    const data = await response.json();
    
    console.log('🧪 GraphQL Response:');
    console.log('- Success:', !data.errors);
    console.log('- Sidecards:', data.data?.getStopDashboardData?.sidecards);
    console.log('- Stop Comparison:', data.data?.getStopDashboardData?.stopComparison);
    console.log('- All Stops count:', data.data?.getStopDashboardData?.allStops?.length);
    
    if (data.data?.getStopDashboardData?.allStops?.length > 0) {
      console.log('- Sample stops:', data.data.getStopDashboardData.allStops.slice(0, 3));
      
      // Test machine name extraction
      const machineNames = [...new Set(data.data.getStopDashboardData.allStops.map(stop => stop.Machine_Name))];
      console.log('- Unique machine names:', machineNames);
    }
    
    if (data.errors) {
      console.error('GraphQL Errors:', data.errors);
    }
    
  } catch (error) {
    console.error('Request failed:', error);
  }
};

testGraphQLQuery();
