import express from 'express';
import db from '../db.js';
import auth from "../middleware/auth.js";
const router = express.Router()

// Get user settings
router.get("/", auth, async (req, res) => {
  try {
    db.execute("SELECT * FROM user_settings WHERE user_id = ?", [req.user.id], (err, results) => {
      if (err) {
        console.error("Database error:", err)
        return res.status(500).json({ error: "Server error" })
      }

      if (results.length === 0) {
        // Return default settings if none exist
        return res.json({
          // Interface settings
          darkMode: false,
          dashboardRefreshRate: 60,
          dataDisplayMode: "chart",
          compactMode: false,
          animationsEnabled: true,
          chartAnimations: true,
          defaultView: "dashboard",
          tableRowsPerPage: 20,

          // Notification settings
          notificationsEnabled: true,
          notifyMachineAlerts: true,
          notifyMaintenance: true,
          notifyUpdates: true,

          // Email settings
          emailNotifications: true,
          emailFormat: "html",
          emailDigest: false,

          // Shift report settings
          defaultShift: "Matin",
          shiftReportNotifications: true,
          shiftReportEmails: true,
          shift1Notifications: true,
          shift2Notifications: true,
          shift3Notifications: true,
          shift1Emails: true,
          shift2Emails: true,
          shift3Emails: true,

          // Report settings
          defaultReportFormat: "pdf",
          reportAutoDownload: false,

          // Security settings
          sessionTimeout: 60,
          loginNotifications: true,
          twoFactorAuth: false,
        })
      }

      // Parse JSON settings
      const settings = results[0]
      const parsedSettings = {}

      // Convert JSON strings back to objects/arrays
      for (const [key, value] of Object.entries(settings)) {
        if (key === "user_id" || key === "id" || key === "created_at" || key === "updated_at") {
          continue
        }

        try {
          // Try to parse as JSON if it's a string that looks like JSON
          if (typeof value === "string" && (value.startsWith("[") || value.startsWith("{"))) {
            parsedSettings[key] = JSON.parse(value)
          } else {
            parsedSettings[key] = value
          }
        } catch (e) {
          parsedSettings[key] = value
        }
      }

      res.json(parsedSettings)
    })
  } catch (err) {
    console.error("Error fetching settings:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Update user settings
router.put("/", auth, async (req, res) => {
  try {
    const settings = req.body

    // Check if settings exist for this user
    db.execute("SELECT id FROM user_settings WHERE user_id = ?", [req.user.id], (err, results) => {
      if (err) {
        console.error("Database error:", err)
        return res.status(500).json({ error: "Server error" })
      }

      // Prepare settings for database storage
      const settingsToStore = {}

      // Convert objects/arrays to JSON strings
      for (const [key, value] of Object.entries(settings)) {
        if (value === null || value === undefined) {
          continue
        }

        if (typeof value === "object") {
          settingsToStore[key] = JSON.stringify(value)
        } else {
          settingsToStore[key] = value
        }
      }

      if (results.length === 0) {
        // Insert new settings
        // Build the columns and placeholders for the SQL query
        const columns = Object.keys(settingsToStore)
        const placeholders = columns.map(() => "?").join(", ")
        const values = Object.values(settingsToStore)

        const query = `INSERT INTO user_settings (user_id, ${columns.join(", ")}, created_at, updated_at) 
                       VALUES (?, ${placeholders}, NOW(), NOW())`

        db.execute(query, [req.user.id, ...values], (err) => {
          if (err) {
            console.error("Database error:", err)
            return res.status(500).json({ error: "Server error" })
          }

          res.json({ success: true })
        })
      } else {
        // Update existing settings
        let updateQuery = "UPDATE user_settings SET updated_at = NOW()"
        const queryParams = []

        // Add each setting to the query
        for (const [key, value] of Object.entries(settingsToStore)) {
          updateQuery += `, ${key} = ?`
          queryParams.push(value)
        }

        // Add WHERE clause
        updateQuery += " WHERE user_id = ?"
        queryParams.push(req.user.id)

        db.execute(updateQuery, queryParams, (err) => {
          if (err) {
            console.error("Database error:", err)
            return res.status(500).json({ error: "Server error" })
          }

          res.json({ success: true })
        })
      }
    })
  } catch (err) {
    console.error("Error updating settings:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Get specific setting
router.get("/:key", auth, async (req, res) => {
  try {
    const key = req.params.key

    db.execute("SELECT * FROM user_settings WHERE user_id = ?", [req.user.id], (err, results) => {
      if (err) {
        console.error("Database error:", err)
        return res.status(500).json({ error: "Server error" })
      }

      if (results.length === 0) {
        // Return default value based on the key
        const defaultValues = {
          darkMode: false,
          notificationsEnabled: true,
          emailNotifications: true,
          dashboardRefreshRate: 60,
          dataDisplayMode: "chart",
          defaultShift: "Matin",
          shiftReportNotifications: true,
          shiftReportEmails: true,
          shift1Notifications: true,
          shift2Notifications: true,
          shift3Notifications: true,
          shift1Emails: true,
          shift2Emails: true,
          shift3Emails: true,
          defaultReportFormat: "pdf",
        }

        return res.json({ [key]: defaultValues[key] || null })
      }

      const settings = results[0]
      let value = settings[key]

      // Parse JSON if needed
      if (typeof value === "string" && (value.startsWith("[") || value.startsWith("{"))) {
        try {
          value = JSON.parse(value)
        } catch (e) {
          // Keep as string if parsing fails
        }
      }

      res.json({ [key]: value })
    })
  } catch (err) {
    console.error("Error fetching setting:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Update specific setting
router.put("/:key", auth, async (req, res) => {
  try {
    const key = req.params.key
    const { value } = req.body

    if (value === undefined) {
      return res.status(400).json({ error: "Value is required" })
    }

    // Check if settings exist for this user
    db.execute("SELECT id FROM user_settings WHERE user_id = ?", [req.user.id], (err, results) => {
      if (err) {
        console.error("Database error:", err)
        return res.status(500).json({ error: "Server error" })
      }

      // Prepare value for storage
      let valueToStore = value
      if (typeof value === "object") {
        valueToStore = JSON.stringify(value)
      }

      if (results.length === 0) {
        // Insert new settings with just this key
        db.execute(
          `INSERT INTO user_settings (user_id, ${key}, created_at, updated_at) 
             VALUES (?, ?, NOW(), NOW())`,
          [req.user.id, valueToStore],
          (err) => {
            if (err) {
              console.error("Database error:", err)
              return res.status(500).json({ error: "Server error" })
            }

            res.json({ success: true })
          },
        )
      } else {
        // Update just this setting
        db.execute(
          `UPDATE user_settings SET ${key} = ?, updated_at = NOW() WHERE user_id = ?`,
          [valueToStore, req.user.id],
          (err) => {
            if (err) {
              console.error("Database error:", err)
              return res.status(500).json({ error: "Server error" })
            }

            res.json({ success: true })
          },
        )
      }
    })
  } catch (err) {
    console.error("Error updating setting:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Get email notification settings
router.get("/email/notifications", auth, async (req, res) => {
  try {
    db.execute(
      `SELECT 
        email_notifications, 
        shift_report_emails, 
        shift1_emails, 
        shift2_emails, 
        shift3_emails,
        email_format,
        email_digest
      FROM user_settings 
      WHERE user_id = ?`,
      [req.user.id],
      (err, results) => {
        if (err) {
          console.error("Database error:", err)
          return res.status(500).json({ error: "Server error" })
        }

        if (results.length === 0) {
          // Return default email settings
          return res.json({
            emailNotifications: true,
            shiftReportEmails: true,
            shift1Emails: true,
            shift2Emails: true,
            shift3Emails: true,
            emailFormat: "html",
            emailDigest: false,
          })
        }

        // Convert snake_case to camelCase for frontend
        const settings = results[0]
        res.json({
          emailNotifications: settings.email_notifications,
          shiftReportEmails: settings.shift_report_emails,
          shift1Emails: settings.shift1_emails,
          shift2Emails: settings.shift2_emails,
          shift3Emails: settings.shift3_emails,
          emailFormat: settings.email_format,
          emailDigest: settings.email_digest,
        })
      },
    )
  } catch (err) {
    console.error("Error fetching email settings:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Get shift report settings
router.get("/shift/reports", auth, async (req, res) => {
  try {
    db.execute(
      `SELECT 
        default_shift,
        shift_report_notifications,
        shift_report_emails,
        shift1_notifications,
        shift2_notifications,
        shift3_notifications,
        shift1_emails,
        shift2_emails,
        shift3_emails
      FROM user_settings 
      WHERE user_id = ?`,
      [req.user.id],
      (err, results) => {
        if (err) {
          console.error("Database error:", err)
          return res.status(500).json({ error: "Server error" })
        }

        if (results.length === 0) {
          // Return default shift report settings
          return res.json({
            defaultShift: "Matin",
            shiftReportNotifications: true,
            shiftReportEmails: true,
            shift1Notifications: true,
            shift2Notifications: true,
            shift3Notifications: true,
            shift1Emails: true,
            shift2Emails: true,
            shift3Emails: true,
          })
        }

        // Convert snake_case to camelCase for frontend
        const settings = results[0]
        res.json({
          defaultShift: settings.default_shift,
          shiftReportNotifications: settings.shift_report_notifications,
          shiftReportEmails: settings.shift_report_emails,
          shift1Notifications: settings.shift1_notifications,
          shift2Notifications: settings.shift2_notifications,
          shift3Notifications: settings.shift3_notifications,
          shift1Emails: settings.shift1_emails,
          shift2Emails: settings.shift2_emails,
          shift3Emails: settings.shift3_emails,
        })
      },
    )
  } catch (err) {
    console.error("Error fetching shift report settings:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Get report settings
router.get("/reports/preferences", auth, async (req, res) => {
  try {
    db.execute(
      `SELECT 
        default_report_format,
        report_auto_download
      FROM user_settings 
      WHERE user_id = ?`,
      [req.user.id],
      (err, results) => {
        if (err) {
          console.error("Database error:", err)
          return res.status(500).json({ error: "Server error" })
        }

        if (results.length === 0) {
          // Return default report settings
          return res.json({
            defaultReportFormat: "pdf",
            reportAutoDownload: false,
          })
        }

        // Convert snake_case to camelCase for frontend
        const settings = results[0]
        res.json({
          defaultReportFormat: settings.default_report_format,
          reportAutoDownload: settings.report_auto_download,
        })
      },
    )
  } catch (err) {
    console.error("Error fetching report settings:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Test email settings
router.post("/email/test", auth, async (req, res) => {
  try {
    const nodemailer = require("nodemailer")

    // Create test SMTP transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: process.env.SMTP_SECURE === "true",
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
    })

    // Get user email
    db.execute("SELECT email FROM users WHERE id = ?", [req.user.id], async (err, results) => {
      if (err) {
        console.error("Database error:", err)
        return res.status(500).json({ error: "Server error" })
      }

      if (results.length === 0) {
        return res.status(404).json({ error: "User not found" })
      }

      const userEmail = results[0].email

      // Send test email
      try {
        await transporter.sendMail({
          from: process.env.EMAIL_FROM,
          to: userEmail,
          subject: "Test des paramètres d'email - Somipem Dashboard",
          html: `
              <html>
                <head>
                  <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    h1 { color: #1890ff; }
                    .footer { margin-top: 30px; font-size: 12px; color: #888; }
                  </style>
                </head>
                <body>
                  <div class="container">
                    <h1>Test des paramètres d'email</h1>
                    <p>Cet email confirme que vos paramètres d'email sont correctement configurés.</p>
                    <p>Vous recevrez désormais les notifications et rapports par email selon vos préférences.</p>
                    
                    <div class="footer">
                      <p>Cet email a été envoyé automatiquement depuis Somipem Dashboard.</p>
                      <p>Pour modifier vos préférences de notification, veuillez accéder à la page des paramètres de votre compte.</p>
                    </div>
                  </div>
                </body>
              </html>
            `,
          text: `
Test des paramètres d'email

Cet email confirme que vos paramètres d'email sont correctement configurés.
Vous recevrez désormais les notifications et rapports par email selon vos préférences.

Cet email a été envoyé automatiquement depuis Somipem Dashboard.
Pour modifier vos préférences de notification, veuillez accéder à la page des paramètres de votre compte.
            `,
        })

        res.json({ success: true, message: "Email de test envoyé avec succès" })
      } catch (error) {
        console.error("Error sending test email:", error)
        res.status(500).json({
          error: "Erreur lors de l'envoi de l'email de test",
          details: error.message,
        })
      }
    })
  } catch (err) {
    console.error("Error testing email settings:", err)
    res.status(500).json({ error: "Server error" })
  }
})

// Add this endpoint to check the schema
router.get('/check-schema', auth, (req, res) => {
  db.execute(
    'DESCRIBE user_settings',
    (err, results) => {
      if (err) {
        console.error('Error checking settings schema:', err);
        return res.status(500).json({ error: 'Failed to check settings schema' });
      }
      
      // Extract column names from the results
      const columns = results.map(row => row.Field);
      
      res.json({ 
        success: true, 
        columns 
      });
    }
  );
});

export default router;


