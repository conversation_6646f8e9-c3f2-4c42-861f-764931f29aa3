const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// Backend URL
const BACKEND_URL = 'http://localhost:5000/api/graphql';

// Test GraphQL queries
const queries = {
  machineModels: `
    query GetStopMachineModels {
      getStopMachineModels {
        model
      }
    }
  `,
  
  machineNames: `
    query GetStopMachineNames {
      getStopMachineNames {
        Machine_Name
      }
    }
  `,
  
  stopStats: `
    query GetStopStats($filters: StopFilterInput) {
      getStopStats(filters: $filters) {
        Stop_Date
        Total_Stops
      }
    }
  `,
  
  topStops: `
    query GetTop5Stops($filters: StopFilterInput) {
      getTop5Stops(filters: $filters) {
        stopName
        count
      }
    }
  `,
  
  allMachineStops: `
    query GetAllMachineStops($filters: StopFilterInput) {
      getAllMachineStops(filters: $filters) {
        Machine_Name
        Date_Insert
        Code_Stop
        Debut_Stop
        Fin_Stop_Time
        Regleur_Prenom
        duration_minutes
        Cause
        Raison_Arret
        Operateur
      }
    }
  `,
  
  machineComparison: `
    query GetMachineStopComparison($filters: StopFilterInput) {
      getMachineStopComparison(filters: $filters) {
        Machine_Name
        stops
        totalDuration
      }
    }
  `,
  
  stopReasons: `
    query GetStopReasons($filters: StopFilterInput) {
      getStopReasons(filters: $filters) {
        reason
        count
      }
    }
  `,
  
  durationTrend: `
    query GetStopDurationTrend($date: String, $filters: StopFilterInput) {
      getStopDurationTrend(date: $date, filters: $filters) {
        hour
        avgDuration
      }
    }
  `
};

async function testGraphQL(query, variables = {}) {
  try {
    console.log(`\n🔍 Testing query: ${Object.keys(queries).find(key => queries[key] === query)}`);
    console.log('Variables:', variables);
    
    const response = await fetch(BACKEND_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, variables })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return null;
    }
    
    console.log('✅ Success! Data received:');
    console.log(JSON.stringify(result.data, null, 2));
    
    return result.data;
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting backend data debug tests...\n');
  
  // Test 1: Get machine models
  const machineModels = await testGraphQL(queries.machineModels);
  
  // Test 2: Get machine names
  const machineNames = await testGraphQL(queries.machineNames);
  
  // Test 3: Get stop stats with IPS filter
  const stopStats = await testGraphQL(queries.stopStats, {
    filters: { model: 'IPS' }
  });
  
  // Test 4: Get top 5 stops with IPS filter
  const topStops = await testGraphQL(queries.topStops, {
    filters: { model: 'IPS' }
  });
  
  // Test 5: Get all machine stops with IPS filter (limited)
  const allStops = await testGraphQL(queries.allMachineStops, {
    filters: { model: 'IPS', limit: 10 }
  });
  
  // Test 6: Get machine comparison
  const machineComparison = await testGraphQL(queries.machineComparison, {
    filters: { model: 'IPS' }
  });
  
  // Test 7: Get stop reasons
  const stopReasons = await testGraphQL(queries.stopReasons, {
    filters: { model: 'IPS' }
  });
  
  // Test 8: Get specific machine data if we have machines
  if (machineNames && machineNames.getStopMachineNames && machineNames.getStopMachineNames.length > 0) {
    const firstMachine = machineNames.getStopMachineNames[0].Machine_Name;
    console.log(`\n🎯 Testing with specific machine: ${firstMachine}`);
    
    const specificMachineStops = await testGraphQL(queries.allMachineStops, {
      filters: { model: 'IPS', machine: firstMachine, limit: 5 }
    });
    
    const specificMachineComparison = await testGraphQL(queries.machineComparison, {
      filters: { model: 'IPS', machine: firstMachine }
    });
  }
  
  console.log('\n📊 Summary of results:');
  console.log('- Machine Models:', machineModels ? '✅ Success' : '❌ Failed');
  console.log('- Machine Names:', machineNames ? '✅ Success' : '❌ Failed');
  console.log('- Stop Stats:', stopStats ? '✅ Success' : '❌ Failed');
  console.log('- Top Stops:', topStops ? '✅ Success' : '❌ Failed');
  console.log('- All Stops:', allStops ? '✅ Success' : '❌ Failed');
  console.log('- Machine Comparison:', machineComparison ? '✅ Success' : '❌ Failed');
  console.log('- Stop Reasons:', stopReasons ? '✅ Success' : '❌ Failed');
  
  // Detailed analysis
  if (topStops && topStops.getTop5Stops) {
    console.log('\n📈 Top stops analysis:');
    console.log('- Count:', topStops.getTop5Stops.length);
    console.log('- Sample:', topStops.getTop5Stops[0]);
  }
  
  if (machineComparison && machineComparison.getMachineStopComparison) {
    console.log('\n📊 Machine comparison analysis:');
    console.log('- Count:', machineComparison.getMachineStopComparison.length);
    console.log('- Sample:', machineComparison.getMachineStopComparison[0]);
  }
  
  if (allStops && allStops.getAllMachineStops) {
    console.log('\n📋 All stops analysis:');
    console.log('- Total stops:', allStops.getAllMachineStops.length);
    console.log('- Sample stop:', allStops.getAllMachineStops[0]);
  }
}

// Run the tests
runTests().catch(console.error);
