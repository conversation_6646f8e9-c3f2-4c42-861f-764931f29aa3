import pool, { execute } from './db.js';

async function testStopsCredibility() {
  console.log('=== Testing Stop Data Credibility ===');
  
  try {
    const tableName = 'machine_stop_table_mould';
    
    // Test exactly what the GraphQL resolver is doing
    console.log('\n=== Testing GraphQL Resolver Logic ===');
    
    // Total stops count
    const totalQuery = `SELECT COUNT(*) AS total FROM ${tableName}`;
    const [totalResult] = await execute(totalQuery);
    console.log(`Total stops: ${totalResult[0].total}`);
    
    // Non-declared stops count (exact same query as GraphQL resolver)
    const nonDeclaredQuery = `
      SELECT COUNT(*) AS non_declared
      FROM ${tableName}
      WHERE Code_Stop = 'Arrêt non déclaré'
    `;
    const [nonDeclaredResult] = await execute(nonDeclaredQuery);
    console.log(`Non-declared stops (GraphQL logic): ${nonDeclaredResult[0].non_declared}`);
    
    // Check exact string matches
    const exactMatchQuery = `
      SELECT Code_Stop, COUNT(*) as count 
      FROM ${tableName} 
      WHERE Code_Stop LIKE '%non déclaré%'
      GROUP BY Code_Stop
    `;
    const [exactMatchResult] = await execute(exactMatchQuery);
    console.log('\nExact string matches for "non déclaré":');
    exactMatchResult.forEach(row => {
      console.log(`  "${row.Code_Stop}": ${row.count}`);
    });
    
    // Check for variations in the text
    const variationsQuery = `
      SELECT DISTINCT Code_Stop 
      FROM ${tableName} 
      WHERE Code_Stop LIKE '%déclaré%' OR Code_Stop LIKE '%declare%'
      ORDER BY Code_Stop
    `;
    const [variationsResult] = await execute(variationsQuery);
    console.log('\nAll variations containing "déclaré" or "declare":');
    variationsResult.forEach(row => {
      console.log(`  "${row.Code_Stop}"`);
    });
    
    // Test the actual GraphQL query simulation
    console.log('\n=== Simulating Complete GraphQL Response ===');
    
    const sidecardsQuery = `
      SELECT 
        COUNT(*) as Arret_Totale,
        COUNT(CASE WHEN Code_Stop = 'Arrêt non déclaré' THEN 1 END) as Arret_Totale_nondeclare
      FROM ${tableName}
    `;
    const [sidecardsResult] = await execute(sidecardsQuery);
    console.log('Sidecards simulation:');
    console.log(`  Arret_Totale: ${sidecardsResult[0].Arret_Totale}`);
    console.log(`  Arret_Totale_nondeclare: ${sidecardsResult[0].Arret_Totale_nondeclare}`);
    
    // Let's also check if there are any encoding issues
    const encodingTestQuery = `
      SELECT Code_Stop, HEX(Code_Stop) as hex_value, LENGTH(Code_Stop) as length_value
      FROM ${tableName} 
      WHERE Code_Stop LIKE '%non%' 
      LIMIT 3
    `;
    const [encodingResult] = await execute(encodingTestQuery);
    console.log('\nEncoding test for Code_Stop values:');
    encodingResult.forEach(row => {
      console.log(`  "${row.Code_Stop}" | HEX: ${row.hex_value} | Length: ${row.length_value}`);
    });
    
  } catch (error) {
    console.error('Error testing stops credibility:', error);
  } finally {
    process.exit(0);
  }
}

testStopsCredibility();
