/**
 * Test script for the new optimized GraphQL resolver
 * Tests the getComprehensiveStopData query with various filters
 */

import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:5000/api/graphql';

// Test query for the new comprehensive resolver
const COMPREHENSIVE_QUERY = `
  query GetComprehensiveStopData($filters: OptimizedStopFilterInput) {
    getComprehensiveStopData(filters: $filters) {
      totalRecords
      queryExecutionTime
      cacheHit
      
      sidecards {
        Arret_Totale
        Arret_Totale_nondeclare
      }
      
      allStops {
        Machine_Name
        Date_Insert
        Code_Stop
        Debut_Stop
        Fin_Stop_Time
        Regleur_Prenom
        duration_minutes
      }
      
      topStops {
        stopName
        count
      }
      
      stopReasons {
        reason
        count
      }
      
      machineComparison {
        Machine_Name
        stops
        totalDuration
      }
      
      operatorStats {
        operator
        interventions
        totalDuration
      }
      
      durationTrend {
        hour
        avgDuration
      }
      
      stopStats {
        Stop_Date
        Total_Stops
      }
    }
  }
`;

// Test machine models query
const MACHINE_MODELS_QUERY = `
  query GetOptimizedStopMachineModels {
    getOptimizedStopMachineModels {
      model
    }
  }
`;

// Test machine names query
const MACHINE_NAMES_QUERY = `
  query GetOptimizedStopMachineNames($filters: OptimizedStopFilterInput) {
    getOptimizedStopMachineNames(filters: $filters) {
      Machine_Name
    }
  }
`;

async function testGraphQLQuery(query, variables = {}, description = '') {
  console.log(`\n🧪 Testing: ${description}`);
  console.log(`📝 Variables:`, JSON.stringify(variables, null, 2));
  
  try {
    const response = await fetch(BACKEND_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return false;
    }

    console.log('✅ Query successful!');
    console.log('📊 Result summary:');
    
    if (result.data.getComprehensiveStopData) {
      const data = result.data.getComprehensiveStopData;
      console.log(`   - Total Records: ${data.totalRecords}`);
      console.log(`   - Query Time: ${data.queryExecutionTime}ms`);
      console.log(`   - All Stops: ${data.allStops?.length || 0} records`);
      console.log(`   - Top Stops: ${data.topStops?.length || 0} categories`);
      console.log(`   - Machine Comparisons: ${data.machineComparison?.length || 0} machines`);
      console.log(`   - Operator Stats: ${data.operatorStats?.length || 0} operators`);
      console.log(`   - Duration Trend: ${data.durationTrend?.length || 0} hours`);
      console.log(`   - Stop Stats: ${data.stopStats?.length || 0} days`);
      console.log(`   - Sidecards: ${data.sidecards?.Arret_Totale || 0} total, ${data.sidecards?.Arret_Totale_nondeclare || 0} non-declared`);
    } else if (result.data.getOptimizedStopMachineModels) {
      console.log(`   - Machine Models: ${result.data.getOptimizedStopMachineModels.length} found`);
      console.log(`   - Models:`, result.data.getOptimizedStopMachineModels.map(m => m.model).join(', '));
    } else if (result.data.getOptimizedStopMachineNames) {
      console.log(`   - Machine Names: ${result.data.getOptimizedStopMachineNames.length} found`);
      console.log(`   - Names:`, result.data.getOptimizedStopMachineNames.map(m => m.Machine_Name).join(', '));
    }
    
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Optimized GraphQL Resolver Tests');
  console.log('='.repeat(50));

  // Test 1: Basic comprehensive query with no filters
  await testGraphQLQuery(
    COMPREHENSIVE_QUERY,
    {},
    'Basic comprehensive query (no filters)'
  );

  // Test 2: Machine model filter
  await testGraphQLQuery(
    COMPREHENSIVE_QUERY,
    { filters: { model: 'IPS', limit: 100 } },
    'IPS machines filter'
  );

  // Test 3: Specific machine filter
  await testGraphQLQuery(
    COMPREHENSIVE_QUERY,
    { filters: { machine: 'IPS01', limit: 50 } },
    'Specific IPS01 machine filter'
  );

  // Test 4: Date range filter
  await testGraphQLQuery(
    COMPREHENSIVE_QUERY,
    { 
      filters: { 
        startDate: '2024-01-01', 
        endDate: '2024-01-31',
        limit: 200 
      } 
    },
    'January 2024 date range filter'
  );

  // Test 5: Combined filters
  await testGraphQLQuery(
    COMPREHENSIVE_QUERY,
    { 
      filters: { 
        model: 'IPS',
        startDate: '2024-01-01', 
        endDate: '2024-01-31',
        limit: 100 
      } 
    },
    'Combined IPS + January 2024 filters'
  );

  // Test 6: Machine models
  await testGraphQLQuery(
    MACHINE_MODELS_QUERY,
    {},
    'Machine models query'
  );

  // Test 7: Machine names for IPS model
  await testGraphQLQuery(
    MACHINE_NAMES_QUERY,
    { filters: { model: 'IPS' } },
    'IPS machine names query'
  );

  // Test 8: All machine names
  await testGraphQLQuery(
    MACHINE_NAMES_QUERY,
    {},
    'All machine names query'
  );

  console.log('\n🏁 Tests completed!');
  console.log('='.repeat(50));
}

// Performance comparison test
async function performanceComparison() {
  console.log('\n⚡ Performance Comparison');
  console.log('='.repeat(30));

  // Time the new optimized query
  const start = Date.now();
  const success = await testGraphQLQuery(
    COMPREHENSIVE_QUERY,
    { filters: { limit: 500 } },
    'Performance test - Optimized single query'
  );
  const optimizedTime = Date.now() - start;

  if (success) {
    console.log(`\n📈 Optimized Query Performance:`);
    console.log(`   - Total Time: ${optimizedTime}ms`);
    console.log(`   - Single database call for all dashboard data`);
    console.log(`   - Reduces frontend complexity and network overhead`);
  }
}

// Run all tests
async function main() {
  try {
    await runTests();
    await performanceComparison();
  } catch (error) {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { testGraphQLQuery, COMPREHENSIVE_QUERY };
