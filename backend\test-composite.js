const query = `{
  getStopDashboardData(filters: {model: "IPS"}) {
    allStops {
      Machine_Name
      Date_Insert
      Code_Stop
    }
    topStops {
      stopName
      count
    }
    sidecards {
      Arret_Totale
      Arret_Totale_nondeclare
    }
  }
}`;

const body = JSON.stringify({ query });

console.log('Testing composite GraphQL query...');
fetch('http://localhost:5000/api/graphql', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body
})
.then(r => r.json())
.then(result => {
  if (result.errors) {
    console.error('GraphQL errors:', result.errors);
  } else {
    console.log('Composite query result:');
    console.log('- All stops count:', result.data?.getStopDashboardData?.allStops?.length || 0);
    console.log('- Top stops count:', result.data?.getStopDashboardData?.topStops?.length || 0);
    console.log('- Sidecards:', result.data?.getStopDashboardData?.sidecards);
  }
})
.catch(error => {
  console.error('Error:', error);
});
