# Fix Docker Dependencies Script (PowerShell)
# This script diagnoses and fixes Docker dependency issues

Write-Host "🔧 LOCQL Docker Dependency Fix" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# Function to print colored status
function Write-Status {
    param(
        [bool]$Success,
        [string]$Message
    )
    if ($Success) {
        Write-Host "✅ $Message" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ $Message" -ForegroundColor Red
        return $false
    }
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

# Step 1: Check if apicache is in package.json
function Test-PackageJson {
    Write-Info "Checking package.json for apicache dependency..."
    
    if (Test-Path "package.json") {
        $packageContent = Get-Content "package.json" -Raw
        if ($packageContent -match '"apicache"') {
            return Write-Status $true "apicache found in package.json"
        } else {
            return Write-Status $false "apicache NOT found in package.json"
        }
    } else {
        return Write-Status $false "package.json not found"
    }
}

# Step 2: Comprehensive Docker cleanup
function Clear-DockerCache {
    Write-Info "Performing comprehensive Docker cleanup..."

    try {
        # Stop and remove existing containers with volumes
        Write-Info "Stopping existing containers..."
        docker-compose -f docker-compose.local.yml down --volumes --remove-orphans 2>$null | Out-Null

        # Remove specific containers if they exist
        docker rm -f locql-backend locql-frontend 2>$null | Out-Null

        # Remove Docker build cache
        Write-Info "Clearing build cache..."
        docker builder prune -f 2>$null | Out-Null

        # Remove dangling images
        Write-Info "Removing dangling images..."
        docker image prune -f 2>$null | Out-Null

        # Remove specific images if they exist
        docker rmi locql-project-backend locql-project-frontend 2>$null | Out-Null

        # Remove anonymous volumes
        Write-Info "Cleaning up volumes..."
        docker volume prune -f 2>$null | Out-Null

        return Write-Status $true "Comprehensive Docker cleanup completed"
    } catch {
        return Write-Status $false "Failed to clean Docker cache: $($_.Exception.Message)"
    }
}

# Step 3: Rebuild containers with dependency verification
function Build-ContainersWithVerification {
    Write-Info "Rebuilding Docker containers with dependency verification..."

    try {
        # Build backend with no cache and verbose output
        Write-Info "Building backend container..."
        docker-compose -f docker-compose.local.yml build --no-cache backend

        if ($LASTEXITCODE -ne 0) {
            Write-Status $false "Backend container build failed"
            return $false
        }

        # Verify dependencies in the built backend image
        Write-Info "Verifying backend dependencies..."
        $verifyResult = docker run --rm locql-project-backend /app/verify-deps.sh 2>&1
        Write-Host $verifyResult

        if ($LASTEXITCODE -ne 0) {
            Write-Status $false "Backend dependency verification failed"
            return $false
        }

        # Build frontend
        Write-Info "Building frontend container..."
        docker-compose -f docker-compose.local.yml build --no-cache frontend

        if ($LASTEXITCODE -eq 0) {
            return Write-Status $true "Containers rebuilt and verified successfully"
        } else {
            return Write-Status $false "Frontend container build failed"
        }
    } catch {
        return Write-Status $false "Failed to rebuild containers: $($_.Exception.Message)"
    }
}

# Step 4: Test container startup
function Test-ContainerStartup {
    Write-Info "Testing container startup..."
    
    try {
        # Start containers in detached mode
        docker-compose -f docker-compose.local.yml up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status $true "Containers started"
            
            # Wait a moment for startup
            Start-Sleep -Seconds 5
            
            # Check container status
            $backendStatus = docker inspect locql-backend --format='{{.State.Status}}' 2>$null
            $frontendStatus = docker inspect locql-frontend --format='{{.State.Status}}' 2>$null
            
            if ($backendStatus -eq "running") {
                Write-Status $true "Backend container is running"
            } else {
                Write-Status $false "Backend container failed to start (Status: $backendStatus)"
                
                # Show backend logs
                Write-Info "Backend container logs:"
                docker logs locql-backend --tail 20
                return $false
            }
            
            if ($frontendStatus -eq "running") {
                Write-Status $true "Frontend container is running"
            } else {
                Write-Status $false "Frontend container failed to start (Status: $frontendStatus)"
                
                # Show frontend logs
                Write-Info "Frontend container logs:"
                docker logs locql-frontend --tail 20
                return $false
            }
            
            return $true
        } else {
            return Write-Status $false "Failed to start containers"
        }
    } catch {
        return Write-Status $false "Failed to test container startup: $($_.Exception.Message)"
    }
}

# Step 5: Test API endpoints
function Test-ApiEndpoints {
    Write-Info "Testing API endpoints..."
    
    $maxAttempts = 30
    $attempt = 1
    
    # Wait for backend to be ready
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5000/api/health/ping" -TimeoutSec 2 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Status $true "Backend API is responding"
                break
            }
        } catch {
            # API not ready yet
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
        $attempt++
    }
    
    if ($attempt -gt $maxAttempts) {
        Write-Status $false "Backend API failed to respond within timeout"
        return $false
    }
    
    # Test frontend
    $attempt = 1
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:5173/" -TimeoutSec 2 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Status $true "Frontend is responding"
                break
            }
        } catch {
            # Frontend not ready yet
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
        $attempt++
    }
    
    if ($attempt -gt $maxAttempts) {
        Write-Status $false "Frontend failed to respond within timeout"
        return $false
    }
    
    return $true
}

# Main execution
function Main {
    Write-Host ""
    Write-Info "Starting Docker dependency diagnosis and fix..."
    
    # Step 1: Check package.json
    if (-not (Test-PackageJson)) {
        Write-Warning "Please ensure apicache is listed in package.json dependencies"
        exit 1
    }
    
    # Step 2: Clean Docker cache
    if (-not (Clear-DockerCache)) {
        Write-Warning "Failed to clean Docker cache, continuing anyway..."
    }
    
    # Step 3: Rebuild containers with verification
    if (-not (Build-ContainersWithVerification)) {
        Write-Host ""
        Write-Warning "Container rebuild or verification failed. Check the build logs above."
        exit 1
    }
    
    # Step 4: Test container startup
    if (-not (Test-ContainerStartup)) {
        Write-Host ""
        Write-Warning "Container startup failed. Check the logs above."
        exit 1
    }
    
    # Step 5: Test API endpoints
    if (-not (Test-ApiEndpoints)) {
        Write-Host ""
        Write-Warning "API endpoints are not responding properly."
        exit 1
    }
    
    Write-Host ""
    Write-Status $true "All Docker dependency issues have been resolved!"
    Write-Host ""
    Write-Info "Your application is now running:"
    Write-Host "  • Frontend: http://localhost:5173" -ForegroundColor White
    Write-Host "  • Backend API: http://localhost:5000" -ForegroundColor White
    Write-Host "  • Health Check: http://localhost:5000/api/health/ping" -ForegroundColor White
    Write-Host ""
    Write-Info "To stop the application:"
    Write-Host "  docker-compose -f docker-compose.local.yml down" -ForegroundColor White
    Write-Host ""
    Write-Info "To view logs:"
    Write-Host "  docker-compose -f docker-compose.local.yml logs -f" -ForegroundColor White
}

# Run main function
Main
