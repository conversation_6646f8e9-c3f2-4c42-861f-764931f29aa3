import{j as e,b as t,a,u as n,k as s,r}from"./index-Nnj1g72A.js";import{e as i,r as o}from"./react-vendor-tYPmozCJ.js";import{a9 as l,S as d,T as c,f as p,z as u,q as h,A as m,O as x,a6 as y,a7 as f,ar as v,Z as g,as as R,an as j,aJ as b,c as Y,e as A,aM as C,ad as M,d as D,a as w,s as E,aN as k,D as S,am as _,bg as L,aY as T,bh as I,a5 as P,E as B,a$ as U,a2 as $,ae as z,B as q,R as O,M as G,af as H,aQ as N,bi as F,a8 as V,y as K,x as Q,w as W,a1 as J,aj as X,Y as Z}from"./antd-vendor-4OvKHZ_k.js";import{u as ee}from"./useMobile-BeW-phh2.js";import{a as te,e as ae,f as ne}from"./numberFormatter-5BSX8Tmh.js";import{u as se}from"./useDailyTableGraphQL-kyfCYKRH.js";var re,ie={exports:{}};var oe,le=(re||(re=1,oe=ie,function(){function e(e,t){return void 0===t?t={autoBom:!1}:"object"!=typeof t&&(t={autoBom:!t}),t.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\ufeff",e],{type:e.type}):e}function t(e,t,a){var n=new XMLHttpRequest;n.open("GET",e),n.responseType="blob",n.onload=function(){o(n.response,t,a)},n.onerror=function(){},n.send()}function a(e){var t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(a){}return 200<=t.status&&299>=t.status}function n(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(a){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}var s="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof i&&i.global===i?i:void 0,r=s.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),o=s.saveAs||("object"!=typeof window||window!==s?function(){}:"download"in HTMLAnchorElement.prototype&&!r?function(e,r,i){var o=s.URL||s.webkitURL,l=document.createElement("a");r=r||e.name||"download",l.download=r,l.rel="noopener","string"==typeof e?(l.href=e,l.origin===location.origin?n(l):a(l.href)?t(e,r,i):n(l,l.target="_blank")):(l.href=o.createObjectURL(e),setTimeout((function(){o.revokeObjectURL(l.href)}),4e4),setTimeout((function(){n(l)}),0))}:"msSaveOrOpenBlob"in navigator?function(s,r,i){if(r=r||s.name||"download","string"!=typeof s)navigator.msSaveOrOpenBlob(e(s,i),r);else if(a(s))t(s,r,i);else{var o=document.createElement("a");o.href=s,o.target="_blank",setTimeout((function(){n(o)}))}}:function(e,a,n,i){if((i=i||open("","_blank"))&&(i.document.title=i.document.body.innerText="downloading..."),"string"==typeof e)return t(e,a,n);var o="application/octet-stream"===e.type,l=/constructor/i.test(s.HTMLElement)||s.safari,d=/CriOS\/[\d]+/.test(navigator.userAgent);if((d||o&&l||r)&&"undefined"!=typeof FileReader){var c=new FileReader;c.onloadend=function(){var e=c.result;e=d?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=e:location=e,i=null},c.readAsDataURL(e)}else{var p=s.URL||s.webkitURL,u=p.createObjectURL(e);i?i.location=u:location.href=u,i=null,setTimeout((function(){p.revokeObjectURL(u)}),4e4)}});s.saveAs=o.saveAs=o,oe.exports=o}()),ie.exports);const{Text:de}=c,{Option:ce}=v,{RangePicker:pe}=R,ue=o.memo((({activeReportType:a,dateRange:n,selectedShift:s,selectedMachines:r,selectedModels:i,searchText:o,machines:c,models:D,shifts:w,onReportTypeChange:E,onDateRangeChange:k,onShiftChange:S,onMachineChange:_,onModelChange:L,onSearchChange:T,onClearFilters:I,machinesLoading:P=!1,modelsLoading:B=!1,existingReports:U=[],onCheckReportExists:$})=>{var z;const q=s||r.length>0||i.length>0||o,O=[s,r.length>0,i.length>0,o].filter(Boolean).length,G="shift"===a&&(null==n?void 0:n[0])&&s&&U.some((e=>e.date===n[0].format("YYYY-MM-DD")&&e.shift===s)),H="shift"!==a||(null==n?void 0:n[0])&&s&&r.length>0;return e.jsxs(l,{title:e.jsxs(d,{children:[e.jsx(M,{style:{color:t.SECONDARY_BLUE}}),e.jsx(de,{strong:!0,children:"Filtres Avancés"}),O>0&&e.jsxs(p,{color:t.PRIMARY_BLUE,style:{marginLeft:8},children:[O," actif",O>1?"s":""]})]}),extra:e.jsx(d,{children:q&&e.jsx(Y,{title:"Effacer tous les filtres",children:e.jsx(A,{type:"text",icon:e.jsx(C,{}),onClick:I,style:{color:t.LIGHT_GRAY},size:"small",children:"Effacer"})})}),style:{marginBottom:16,boxShadow:"0 2px 8px rgba(0,0,0,0.06)",border:`1px solid ${t.PRIMARY_BLUE}20`},bodyStyle:{paddingBottom:16},children:[q&&e.jsxs(e.Fragment,{children:[e.jsx("div",{style:{padding:"8px 12px",backgroundColor:"#f0f8ff",borderRadius:"6px",border:`1px solid ${t.SECONDARY_BLUE}20`,marginBottom:"16px"},children:e.jsxs(d,{wrap:!0,size:"small",children:[e.jsx(de,{style:{fontSize:"12px",color:t.SECONDARY_BLUE,fontWeight:500},children:"Filtres actifs:"}),s&&e.jsxs(p,{closable:!0,onClose:()=>S(null),color:t.SECONDARY_BLUE,size:"small",children:[e.jsx(u,{})," Équipe: ",null==(z=w.find((e=>e.key===s)))?void 0:z.label]}),r.length>0&&e.jsxs(p,{closable:!0,onClose:()=>_([]),color:t.PRIMARY_BLUE,size:"small",children:[e.jsx(h,{})," Machines: ",r.length]}),i.length>0&&e.jsxs(p,{closable:!0,onClose:()=>L([]),color:t.CHART_TERTIARY,size:"small",children:[e.jsx(m,{})," Modèles: ",i.length]}),o&&e.jsxs(p,{closable:!0,onClose:()=>T(""),color:"orange",size:"small",children:['Recherche: "',o,'"']})]})}),e.jsx(x,{style:{margin:"16px 0"}})]}),e.jsxs(y,{gutter:[16,16],children:[e.jsxs(f,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(de,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(m,{style:{marginRight:4}}),"Modèles",i.length>0&&e.jsx(p,{size:"small",color:t.CHART_TERTIARY,style:{marginLeft:4},children:i.length})]})}),e.jsx(v,{mode:"multiple",placeholder:"Tous les modèles",style:{width:"100%"},allowClear:!0,onChange:L,value:i,maxTagCount:"responsive",showSearch:!0,loading:B,filterOption:(e,t)=>{var a;return(null==(a=t.children)?void 0:a.toLowerCase().indexOf(e.toLowerCase()))>=0},notFoundContent:B?"Chargement...":"Aucun modèle trouvé",children:D.map((t=>e.jsx(ce,{value:t.id||t.name,children:t.name},t.id||t.name)))})]}),e.jsxs(f,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(de,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(h,{style:{marginRight:4}}),"Machines",r.length>0&&e.jsx(p,{size:"small",color:t.PRIMARY_BLUE,style:{marginLeft:4},children:r.length})]})}),e.jsx(v,{mode:"shift"===a?"single":"multiple",placeholder:"shift"===a?"Sélectionner une machine":"Toutes les machines",style:{width:"100%"},allowClear:!0,onChange:e=>{if("shift"===a){const t=Array.isArray(e)?e[0]:e;_(t?[t]:[]),t&&!i.includes("IPS")&&L(["IPS"])}else _(e||[])},value:"shift"===a?r[0]:r,maxTagCount:"responsive",showSearch:!0,loading:P,filterOption:(e,t)=>{var a;return(null==(a=t.children)?void 0:a.toLowerCase().indexOf(e.toLowerCase()))>=0},notFoundContent:P?"Chargement...":"Aucune machine trouvée",children:c.map((t=>e.jsx(ce,{value:t.id||t.name,children:t.name},t.id||t.name)))})]}),("shift"===a||"production"===a)&&e.jsxs(f,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(de,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(u,{style:{marginRight:4}}),"Équipe","shift"===a&&e.jsx(de,{style:{color:"#ff4d4f",fontSize:"12px"},children:" *"})]})}),e.jsx(v,{value:s,onChange:S,placeholder:"shift"===a?"Sélectionner une équipe":"Toutes les équipes",allowClear:"shift"!==a,style:{width:"100%"},children:w.map((t=>e.jsx(ce,{value:t.key,children:e.jsxs(d,{children:[e.jsx("div",{style:{width:8,height:8,borderRadius:"50%",backgroundColor:t.color}}),t.label," (",t.hours,")"]})},t.key)))})]}),e.jsxs(f,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(de,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(g,{style:{marginRight:4}}),"shift"===a?"Date":"Période","shift"===a&&e.jsx(de,{style:{color:"#ff4d4f",fontSize:"12px"},children:" *"})]})}),"shift"===a?e.jsx(R,{value:n[0],onChange:e=>k([e,e]),format:"DD/MM/YYYY",placeholder:"Sélectionner une date",style:{width:"100%"},allowClear:!1}):e.jsx(pe,{value:n,onChange:k,format:"DD/MM/YYYY",placeholder:["Date début","Date fin"],style:{width:"100%"},allowClear:!1})]}),e.jsxs(f,{xs:24,sm:12,md:8,lg:6,children:[e.jsx("div",{style:{marginBottom:8},children:e.jsxs(de,{strong:!0,style:{color:t.DARK_GRAY},children:[e.jsx(j,{style:{marginRight:4}}),"Recherche"]})}),e.jsx(b,{placeholder:"Rechercher par type, machine, date, utilisateur...",prefix:e.jsx(j,{}),allowClear:!0,onChange:e=>T(e.target.value),value:o,style:{width:"100%"}})]})]}),q&&e.jsx("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"},children:e.jsx(de,{style:{fontSize:"12px",color:"#52c41a"},children:"✓ Filtres appliqués - Les données sont filtrées selon vos critères"})}),"shift"===a&&e.jsxs(e.Fragment,{children:[G&&e.jsx("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff7e6",border:"1px solid #ffd666",borderRadius:"4px"},children:e.jsx(de,{style:{fontSize:"12px",color:"#d48806"},children:"⚠️ Un rapport existe déjà pour cette date et équipe"})}),!H&&e.jsx("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#fff2f0",border:"1px solid #ffccc7",borderRadius:"4px"},children:e.jsx(de,{style:{fontSize:"12px",color:"#cf1322"},children:"❌ Requis pour créer un rapport de quart: Date, Équipe, et Machine"})}),H&&!G&&e.jsx("div",{style:{marginTop:16,padding:"8px 12px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f",borderRadius:"4px"},children:e.jsx(de,{style:{fontSize:"12px",color:"#52c41a"},children:"✅ Prêt à créer un nouveau rapport de quart (Modèle par défaut: IPS)"})})]})]})}));ue.displayName="ReportFilters";D.locale("fr");const{Title:he,Text:me,Paragraph:xe}=c,{Option:ye}=v,{RangePicker:fe}=R,ve=[{key:"matin",label:"Équipe Matin",hours:"06:00-14:00",color:t.SECONDARY_BLUE},{key:"apres-midi",label:"Équipe Après-midi",hours:"14:00-22:00",color:t.PRIMARY_BLUE},{key:"nuit",label:"Équipe Nuit",hours:"22:00-06:00",color:t.DARK_GRAY}],ge={}.REACT_APP_API_URL||"/api",Re=[{key:"shift",label:"Rapports de quart",icon:e.jsx(V,{}),description:"Rapports par équipe de travail",endpoint:"/reports/shift",color:t.PRIMARY_BLUE,priority:1},{key:"daily",label:"Rapports journaliers",icon:e.jsx(g,{}),description:"Rapports quotidiens de production",endpoint:"/reports/daily",color:t.SECONDARY_BLUE,priority:2},{key:"weekly",label:"Rapports hebdomadaires",icon:e.jsx(K,{}),description:"Rapports de performance hebdomadaire",endpoint:"/reports/weekly",color:t.CHART_TERTIARY,priority:3},{key:"monthly",label:"Rapports mensuels",icon:e.jsx(Q,{}),description:"Rapports mensuels et tendances",endpoint:"/reports/monthly",color:t.CHART_QUATERNARY,priority:4},{key:"machine",label:"Rapports par machine",icon:e.jsx(h,{}),description:"Performance individuelle des machines",endpoint:"/reports/machine",color:t.PRIMARY_BLUE,priority:5},{key:"production",label:"Rapports de production",icon:e.jsx(W,{}),description:"Rapports de production quotidienne et performance",endpoint:"/reports/production",color:t.SECONDARY_BLUE,priority:6},{key:"maintenance",label:"Rapports de maintenance",icon:e.jsx(m,{}),description:"Maintenance préventive et corrective",endpoint:"/reports/maintenance",color:t.CHART_TERTIARY,priority:7},{key:"quality",label:"Rapports de qualité",icon:e.jsx(J,{}),description:"Contrôle qualité et rejets",endpoint:"/reports/quality",color:t.CHART_QUATERNARY,priority:8},{key:"financial",label:"Rapports financiers",icon:e.jsx(X,{}),description:"Rapports financiers et coûts",endpoint:"/reports/financial",color:t.PRIMARY_BLUE,priority:9},{key:"custom",label:"Rapports personnalisés",icon:e.jsx(m,{}),description:"Rapports configurables sur mesure",endpoint:"/reports/custom",color:t.SECONDARY_BLUE,priority:10}],je=[{key:"pdf",label:"PDF",icon:e.jsx(N,{}),description:"Document PDF formaté",mimeType:"application/pdf"},{key:"excel",label:"Excel",icon:e.jsx(F,{}),description:"Fichier Excel avec données",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},{key:"csv",label:"CSV",icon:e.jsx(z,{}),description:"Données CSV pour analyse",mimeType:"text/csv"}],be={pending:{color:"processing",text:"En cours"},generating:{color:"processing",text:"Génération..."},completed:{color:"success",text:"Terminé"},failed:{color:"error",text:"Échec"},cancelled:{color:"default",text:"Annulé"}},Ye=()=>{var i,c,u;const{user:h}=a(),{darkMode:m}=n(),x=ee(),{settings:v}=s(),{getAllDailyProduction:g,getMachinePerformance:R,getMachineModels:j,getMachineNames:b}=se(),[C,N]=o.useState("shift"),[F,V]=o.useState([D().subtract(7,"day"),D()]),[K,Q]=o.useState(null),[W,J]=o.useState([]),[X,Z]=o.useState([]),[re,ie]=o.useState(""),[oe,de]=o.useState(!1),[ce,pe]=o.useState(!0),[xe,ye]=o.useState([]),[fe,Ye]=o.useState([]),[Ce,Me]=o.useState([]),[De,we]=o.useState(!1),[Ee,ke]=o.useState(!1),[Se,_e]=o.useState({current:1,pageSize:10,total:0}),[Le,Te]=o.useState(null),[Ie,Pe]=o.useState(!1),[Be,Ue]=o.useState(!1),[$e,ze]=o.useState(null),[qe,Oe]=o.useState(!1),[Ge,He]=o.useState(0),[Ne,Fe]=o.useState(null),[Ve,Ke]=o.useState(!0),[Qe,We]=o.useState([]),Je=o.useMemo((()=>((e,t,a,n,s)=>{if("shift"!==e)return{isValid:!0,canCreate:!0,reportExists:!1};const r=(null==t?void 0:t[0])&&a&&s.some((e=>e.date===t[0].format("YYYY-MM-DD")&&e.shift===a)),i=(null==t?void 0:t[0])&&a&&n.length>0;return{isValid:i,canCreate:i&&!r,reportExists:r}})(C,F,K,W,Qe)),[C,F,K,W,Qe]),Xe=o.useMemo((()=>({async request(e,t={}){var a;try{const n=`${ge}${e}`;let s=r[(null==(a=t.method)?void 0:a.toLowerCase())||"get"](n).retry(2).set("Content-Type","application/json").set("Authorization",(null==h?void 0:h.token)?`Bearer ${h.token}`:"");t.headers&&Object.entries(t.headers).forEach((([e,t])=>{s=s.set(e,t)})),t.body&&"GET"!==t.method&&(s=s.send(t.body));return(await s).body}catch(n){throw n}},async getMachines(){try{const e=(await b()).getMachineNames||[];return e.map((e=>({id:e.Machine_Name,name:e.Machine_Name})))}catch(e){return[]}},async getModels(){try{const e=(await j()).getMachineModels||[];return e.map((e=>({id:e.model,name:e.model})))}catch(e){return[]}},async getReports(e){try{de(!0);let t={};"shift"===e.type?(t={date:e.startDate,dateRangeType:"day"},e.machines&&(t.machine=e.machines.split(",")[0]),e.models&&(t.model=e.models.split(",")[0])):(t={startDate:e.startDate,endDate:e.endDate,dateRangeType:"day"},e.machines&&(t.machines=e.machines.split(",")),e.models&&(t.models=e.models.split(",")));let a=[];if("shift"===e.type){const n=await R(t);let s=(null==n?void 0:n.getMachinePerformance)||[];e.shift&&(s=s.filter((t=>t.Shift&&t.Shift.toLowerCase()===e.shift.toLowerCase()))),a=s.map(((t,a)=>({id:a+1,type:"shift",date:e.startDate,endDate:e.startDate,generatedAt:(new Date).toISOString(),generatedBy:(null==h?void 0:h.name)||"Système",status:"completed",size:Math.floor(5e5*Math.random())+1e5,shiftData:{machine:t.Machine_Name,shift:t.Shift,production:t.production||0,performance:t.performance||0,availability:t.availability||0,quality:t.quality||0,oee:t.oee||0,downtime:t.downtime||0,rejects:t.rejects||0}})))}else if("daily"===e.type){const e=await g();a=((null==e?void 0:e.getAllDailyProduction)||[]).slice(0,10).map(((e,t)=>({id:t+1,type:"daily",date:e.Date_Insert_Day,endDate:e.Date_Insert_Day,generatedAt:(new Date).toISOString(),generatedBy:(null==h?void 0:h.name)||"Système",status:"completed",size:Math.floor(3e5*Math.random())+15e4,dailyData:{machine:e.Machine_Name,production:e.Good_QTY_Day||0,rejects:e.Rejects_QTY_Day||0,availability:e.Availability_Rate_Day||0,performance:e.Performance_Rate_Day||0,quality:e.Quality_Rate_Day||0,oee:e.OEE_Day||0,runHours:e.Run_Hours_Day||0,downHours:e.Down_Hours_Day||0}})))}else a=[{id:1,type:e.type,date:e.startDate,endDate:e.endDate,generatedAt:(new Date).toISOString(),generatedBy:(null==h?void 0:h.name)||"Système",status:"completed",size:Math.floor(4e5*Math.random())+2e5}];if(e.search){const t=e.search.toLowerCase();a=a.filter((e=>{var a,n;return e.type.toLowerCase().includes(t)||((null==(a=e.shiftData)?void 0:a.machine)||"").toLowerCase().includes(t)||((null==(n=e.dailyData)?void 0:n.machine)||"").toLowerCase().includes(t)||(e.generatedBy||"").toLowerCase().includes(t)||D(e.date).format("DD/MM/YYYY").includes(t)}))}if(e.machines){const t=e.machines.split(",").map((e=>e.toLowerCase()));a=a.filter((e=>t.some((t=>{var a,n;return((null==(a=e.shiftData)?void 0:a.machine)||"").toLowerCase().includes(t)||((null==(n=e.dailyData)?void 0:n.machine)||"").toLowerCase().includes(t)}))))}if(e.models){const t=e.models.split(",").map((e=>e.toLowerCase()));a=a.filter((e=>t.some((t=>{var a,n;return((null==(a=e.shiftData)?void 0:a.model)||"").toLowerCase().includes(t)||((null==(n=e.dailyData)?void 0:n.model)||"").toLowerCase().includes(t)}))))}const n=parseInt(e.page)||1,s=parseInt(e.pageSize)||10,r=(n-1)*s,i=r+s;return{reports:a.slice(r,i),total:a.length,page:n,pageSize:s}}catch(t){throw t}finally{de(!1)}},async generateReport(e,t=!1){var a,n,s,i,o,l;try{if("shift"===e.type){const l=t?"/shift-reports/generate-enhanced":"/shift-reports/generate",d=await r.post(`${ge}${l}`).withCredentials().send({machineId:(null==(n=null==(a=e.filters)?void 0:a.machines)?void 0:n[0])||"ALL",date:(null==(s=e.dateRange)?void 0:s.start)||D().format("YYYY-MM-DD"),shift:(null==(i=e.filters)?void 0:i.shift)||"Current"}).timeout(6e4).retry(2);if(d.body&&d.body.success){const e=d.body.version||"standard";return d.body.filePath&&window.open(d.body.filePath,"_blank"),{success:!0,reportId:d.body.reportId||Date.now(),filePath:d.body.filePath,downloadPath:d.body.downloadPath||d.body.filePath,fileSize:d.data.fileSize,version:e,performance:null==(o=d.data.reportData)?void 0:o.performance,message:`Rapport de quart ${"enhanced"===e?"amélioré":"standard"} généré avec succès`}}throw new Error("Erreur lors de la génération du rapport de quart")}return await new Promise((e=>setTimeout(e,3e3))),{success:!0,reportId:Date.now(),message:`Rapport ${e.type} généré avec succès`}}catch(d){throw"ECONNABORTED"===d.code?new Error("La génération du rapport a pris trop de temps. Veuillez réessayer."):d.response?new Error(`Erreur ${d.response.status}: ${(null==(l=d.response.data)?void 0:l.error)||d.response.statusText}`):d.request?new Error("Aucune réponse du serveur. Vérifiez votre connexion réseau."):d}},exportReport:async(e,t)=>new Response(new Blob(["Mock export data"],{type:"text/plain"})),deleteReport:async e=>({success:!0})})),[null==h?void 0:h.token,null==h?void 0:h.name,b,j,R,g]),Ze=o.useCallback((async()=>{try{we(!0);const e=await Xe.getMachines();Array.isArray(e)?Ye(e):Ye([]),Fe(null)}catch(e){Ye([]),Fe("Impossible de charger la liste des machines"),w.error({message:"Erreur de chargement",description:"Impossible de charger la liste des machines",duration:4})}finally{we(!1)}}),[Xe]),et=o.useCallback((async()=>{try{ke(!0);const e=await Xe.getModels();Array.isArray(e)?Me(e):Me([])}catch(e){Me([]),w.error({message:"Erreur de chargement",description:"Impossible de charger la liste des modèles",duration:4})}finally{ke(!1)}}),[Xe]),tt=o.useCallback((async()=>{try{de(!0),Fe(null);const e={type:C,startDate:F[0].format("YYYY-MM-DD"),endDate:F[1].format("YYYY-MM-DD"),page:Se.current,pageSize:Se.pageSize,...K&&{shift:K},...W.length>0&&{machines:W.join(",")},...X.length>0&&{models:X.join(",")},...re&&{search:re}},t=await Xe.getReports(e);ye(Array.isArray(t)?t:t.reports||[]),_e((e=>{var a;return{...e,total:t.total||(null==(a=t.reports)?void 0:a.length)||0}}))}catch(e){Fe("Impossible de charger les rapports"),ye([]),w.error({message:"Erreur de chargement",description:"Impossible de charger les rapports. Vérifiez votre connexion.",duration:4})}finally{de(!1),pe(!1)}}),[C,F,K,W,X,re,Se.current,Se.pageSize,Xe]),at=o.useCallback((async()=>{try{We([{date:"2025-07-13",shift:"matin",machine:"IPS01"},{date:"2025-07-13",shift:"apres-midi",machine:"IPS02"}])}catch(e){We([])}}),[]);o.useEffect((()=>{Ze(),et(),tt(),at()}),[Ze,et,tt,at]),o.useEffect((()=>{const e=xe.filter((e=>["pending","generating"].includes(e.status)));if(e.length>0&&!$e){const e=setInterval(tt,5e3);ze(e)}else 0===e.length&&$e&&(clearInterval($e),ze(null));return()=>{$e&&clearInterval($e)}}),[xe,$e,tt]);const nt=o.useCallback((e=>{if(N(e),_e((e=>({...e,current:1}))),"shift"===e&&F){const e=F[0];e&&V([e,e])}}),[F]),st=o.useCallback((e=>{V(e||[D().subtract(7,"day"),D()]),_e((e=>({...e,current:1})))}),[]),rt=o.useCallback((e=>{Q(e),_e((e=>({...e,current:1})))}),[]),it=o.useCallback((e=>{if("shift"===C){const t=Array.isArray(e)?e:[e];J(t.filter(Boolean)),t.length>0&&0===X.length&&Z(["IPS"])}else J(e||[]);_e((e=>({...e,current:1})))}),[C,X.length]),ot=o.useCallback((e=>{Z(e||[]),_e((e=>({...e,current:1})))}),[]),lt=o.useCallback((e=>{ie(e),_e((e=>({...e,current:1})))}),[]),dt=o.useCallback((()=>{Q(null),J([]),Z([]),ie(""),_e((e=>({...e,current:1})))}),[]),ct=o.useCallback((e=>{_e(e)}),[]),pt=o.useCallback((e=>{Te(e),Pe(!0)}),[]),ut=o.useCallback((async(e,t)=>{try{Ue(!0);const a=await Xe.exportReport(e.id,t),n=je.find((e=>e.key===t));if(a instanceof Response){const s=await a.blob(),r=`rapport_${e.id}_${D().format("YYYY-MM-DD_HH-mm")}.${t}`;le.saveAs(s,r),E.success(`Rapport exporté en ${(null==n?void 0:n.label)||t}`)}}catch(a){w.error({message:"Erreur d'exportation",description:`Impossible d'exporter le rapport: ${a.message}`,duration:4})}finally{Ue(!1)}}),[Xe]),ht=o.useCallback((async e=>{try{if("shift"===C&&!Je.canCreate)return void(Je.reportExists?w.warning({message:"Rapport déjà existant",description:"Un rapport existe déjà pour cette date et équipe.",duration:4}):w.error({message:"Informations manquantes",description:"Veuillez sélectionner la date, l'équipe et la machine pour créer un rapport de quart.",duration:4}));Oe(!0),He(0);const t=setInterval((()=>{He((e=>Math.min(e+10,90)))}),500),a=await Xe.generateReport({type:C,dateRange:{start:F[0].format("YYYY-MM-DD"),end:F[1].format("YYYY-MM-DD")},filters:{shift:K,machines:W,models:X.length>0?X:["IPS"]},...e},Ve);clearInterval(t),He(100),setTimeout((()=>{var e;Oe(!1),He(0),"shift"===C&&at(),tt(),E.success(`Rapport ${"enhanced"===a.version?"amélioré":"standard"} généré avec succès`),"enhanced"===a.version&&a.performance&&w.info({message:"Résumé Performance",description:`OEE: ${a.performance.totalProduction} unités produites, Qualité: ${null==(e=a.performance.qualityRate)?void 0:e.toFixed(1)}%`,duration:6})}),1e3)}catch(t){Oe(!1),He(0),w.error({message:"Erreur de génération",description:`Impossible de générer le rapport: ${t.message}`,duration:4})}}),[C,F,K,W,X,Xe,tt,Ve,Je,at]),mt=o.useCallback((e=>{const a=window.open("","_blank");if(!a)return void w.error({message:"Erreur d'impression",description:"Impossible d'ouvrir la fenêtre d'impression. Vérifiez les paramètres de votre navigateur."});const n=xt(e),s=Re.find((t=>t.key===e.type));a.document.write(`\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <title>Rapport ${(null==s?void 0:s.label)||e.type} #${e.id}</title>\n          <meta charset="utf-8">\n          <style>\n            body { \n              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; \n              margin: 20px; \n              line-height: 1.6;\n              color: #333;\n            }\n            .header { \n              display: flex; \n              justify-content: space-between; \n              align-items: center; \n              border-bottom: 2px solid ${t.PRIMARY_BLUE};\n              padding-bottom: 15px;\n              margin-bottom: 20px;\n            }\n            .header h1 { \n              color: ${t.PRIMARY_BLUE}; \n              margin: 0;\n              font-size: 24px;\n            }\n            .header .logo {\n              font-weight: bold;\n              color: ${t.SECONDARY_BLUE};\n              font-size: 18px;\n            }\n            table { \n              border-collapse: collapse; \n              width: 100%; \n              margin: 20px 0;\n              box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n            }\n            th, td { \n              border: 1px solid #ddd; \n              padding: 12px 8px; \n              text-align: left; \n            }\n            th { \n              background-color: ${t.PRIMARY_BLUE}; \n              color: white;\n              font-weight: 600;\n            }\n            tr:nth-child(even) { \n              background-color: #f9f9f9; \n            }\n            .statistics {\n              display: grid;\n              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n              gap: 15px;\n              margin: 20px 0;\n            }\n            .stat-card {\n              background: #f8f9fa;\n              padding: 15px;\n              border-radius: 8px;\n              border-left: 4px solid ${t.SECONDARY_BLUE};\n            }\n            .stat-title {\n              font-size: 14px;\n              color: #666;\n              margin-bottom: 5px;\n            }\n            .stat-value {\n              font-size: 24px;\n              font-weight: bold;\n              color: ${t.PRIMARY_BLUE};\n            }\n            .footer { \n              margin-top: 40px; \n              font-size: 12px; \n              color: #888; \n              text-align: center; \n              border-top: 1px solid #eee;\n              padding-top: 15px;\n            }\n            .section {\n              margin: 25px 0;\n            }\n            .section-title {\n              font-size: 18px;\n              color: ${t.PRIMARY_BLUE};\n              border-bottom: 1px solid #eee;\n              padding-bottom: 5px;\n              margin-bottom: 15px;\n            }\n            @media print {\n              button { display: none !important; }\n              .no-print { display: none !important; }\n              body { margin: 0; }\n              .header { page-break-after: avoid; }\n              table { page-break-inside: avoid; }\n            }\n          </style>\n        </head>\n        <body>\n          <div class="header">\n            <div>\n              <h1>Rapport ${(null==s?void 0:s.label)||e.type} #${e.id}</h1>\n              <p style="margin: 5px 0; color: #666;">\n                ${D(e.date).format("DD MMMM YYYY")} | \n                Généré le ${D(e.generatedAt).format("DD/MM/YYYY à HH:mm")}\n              </p>\n            </div>\n            <div class="logo">SOMIPEM</div>\n          </div>\n          ${n}\n          <div class="footer">\n            <p><strong>SOMIPEM Dashboard</strong> - Rapport généré automatiquement</p>\n            <p>Généré par: ${e.generatedBy||(null==h?void 0:h.name)||"Système"} | ${D().format("DD/MM/YYYY à HH:mm")}</p>\n          </div>\n        </body>\n      </html>\n    `),a.document.close(),setTimeout((()=>{a.print()}),500)}),[null==h?void 0:h.name]),xt=o.useCallback((e=>{var t,a,n,s,r,i,o,l,d,c,p,u;const h=Re.find((t=>t.key===e.type));switch(e.type){case"production":return`\n          <div class="section">\n            <h2 class="section-title">Résumé de Production</h2>\n            <div class="statistics">\n              <div class="stat-card">\n                <div class="stat-title">Production Totale</div>\n                <div class="stat-value">${te((null==(t=e.production)?void 0:t.total)||0)} unités</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Taux de Performance</div>\n                <div class="stat-value">${ne(((null==(a=e.production)?void 0:a.performance)||0)/100)}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Qualité</div>\n                <div class="stat-value">${ne(((null==(n=e.quality)?void 0:n.rate)||0)/100)}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Rejets</div>\n                <div class="stat-value">${te((null==(s=e.quality)?void 0:s.rejects)||0)} unités</div>\n              </div>\n            </div>\n          </div>\n          ${e.machineData?`\n            <div class="section">\n              <h2 class="section-title">Performance par Machine</h2>\n              <table>\n                <thead>\n                  <tr>\n                    <th>Machine</th>\n                    <th>Production</th>\n                    <th>Performance</th>\n                    <th>Disponibilité</th>\n                    <th>Rejets</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  ${e.machineData.map((e=>`\n                    <tr>\n                      <td>${e.name}</td>\n                      <td>${te(e.production)} unités</td>\n                      <td>${ne(e.performance/100)}</td>\n                      <td>${ne(e.availability/100)}</td>\n                      <td>${te(e.rejects)} unités</td>\n                    </tr>\n                  `)).join("")}\n                </tbody>\n              </table>\n            </div>\n          `:""}\n        `;case"arrets":return`\n          <div class="section">\n            <h2 class="section-title">Analyse des Arrêts</h2>\n            <div class="statistics">\n              <div class="stat-card">\n                <div class="stat-title">Total Arrêts</div>\n                <div class="stat-value">${te((null==(r=e.arrets)?void 0:r.total)||0)}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Durée Totale</div>\n                <div class="stat-value">${ae(((null==(i=e.arrets)?void 0:i.totalDuration)||0)/60,1)} heures</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">MTTR Moyen</div>\n                <div class="stat-value">${ae((null==(o=e.arrets)?void 0:o.averageMTTR)||0,1)} min</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Disponibilité</div>\n                <div class="stat-value">${ne(((null==(l=e.arrets)?void 0:l.availability)||0)/100)}</div>\n              </div>\n            </div>\n          </div>\n        `;case"shift":return`\n          <div class="section">\n            <h2 class="section-title">Rapport d'Équipe</h2>\n            <div class="statistics">\n              <div class="stat-card">\n                <div class="stat-title">Équipe</div>\n                <div class="stat-value">${e.shift||"N/A"}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Production</div>\n                <div class="stat-value">${te((null==(d=e.production)?void 0:d.total)||0)} unités</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Alertes</div>\n                <div class="stat-value">${te((null==(c=e.alerts)?void 0:c.total)||0)}</div>\n              </div>\n              <div class="stat-card">\n                <div class="stat-title">Machines Actives</div>\n                <div class="stat-value">${te((null==(p=e.production)?void 0:p.activeMachines)||0)}</div>\n              </div>\n            </div>\n          </div>\n        `;default:return`\n          <div class="section">\n            <h2 class="section-title">Détails du Rapport</h2>\n            <p>Type: ${(null==h?void 0:h.label)||e.type}</p>\n            <p>Période: ${D(e.startDate).format("DD/MM/YYYY")} - ${D(e.endDate).format("DD/MM/YYYY")}</p>\n            <p>Statut: ${(null==(u=be[e.status])?void 0:u.text)||e.status}</p>\n          </div>\n        `}}),[]),yt=o.useCallback((()=>[{title:"ID",dataIndex:"id",key:"id",width:100,render:a=>e.jsxs(me,{code:!0,style:{color:t.PRIMARY_BLUE},children:["#",a]}),sorter:(e,t)=>e.id-t.id},{title:"Type",dataIndex:"type",key:"type",width:150,render:a=>{const n=Re.find((e=>e.key===a));return e.jsx(p,{icon:null==n?void 0:n.icon,color:(null==n?void 0:n.color)||t.LIGHT_GRAY,style:{borderRadius:"4px"},children:(null==n?void 0:n.label)||a})},filters:Re.map((e=>({text:e.label,value:e.key}))),onFilter:(e,t)=>t.type===e},{title:"Période",dataIndex:"date",key:"date",width:180,render:(t,a)=>e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:500},children:D(t).format("DD/MM/YYYY")}),a.endDate&&a.endDate!==t&&e.jsxs(me,{type:"secondary",style:{fontSize:"12px"},children:["au ",D(a.endDate).format("DD/MM/YYYY")]})]}),sorter:(e,t)=>new Date(e.date)-new Date(t.date),defaultSortOrder:"descend"},{title:"Statut",dataIndex:"status",key:"status",width:120,render:t=>{const a=be[t]||{color:"default",text:t};return e.jsx(p,{color:a.color,style:{borderRadius:"4px"},children:a.text})},filters:Object.keys(be).map((e=>({text:be[e].text,value:e}))),onFilter:(e,t)=>t.status===e},{title:"Généré le",dataIndex:"generatedAt",key:"generatedAt",width:160,render:t=>e.jsxs("div",{children:[e.jsx("div",{children:D(t).format("DD/MM/YYYY")}),e.jsx(me,{type:"secondary",style:{fontSize:"12px"},children:D(t).format("HH:mm")})]}),responsive:["md"],sorter:(e,t)=>new Date(e.generatedAt)-new Date(t.generatedAt)},{title:"Généré par",dataIndex:"generatedBy",key:"generatedBy",width:140,render:a=>e.jsx(me,{style:{color:t.DARK_GRAY},children:a||"Système"}),responsive:["lg"]},{title:"Taille",dataIndex:"size",key:"size",width:100,render:t=>e.jsx(me,{type:"secondary",children:t?`${ae(t/1024,1)} KB`:"N/A"}),responsive:["xl"],sorter:(e,t)=>(e.size||0)-(t.size||0)},{title:"Actions",key:"actions",width:160,fixed:"right",render:(a,n)=>e.jsxs(d,{size:"small",children:[e.jsx(Y,{title:"Voir le rapport",children:e.jsx(A,{type:"text",icon:e.jsx(k,{}),onClick:()=>pt(n),style:{color:t.PRIMARY_BLUE}})}),e.jsx(S,{menu:{items:je.map((t=>({key:t.key,icon:t.icon,label:e.jsxs(d,{children:[t.label,e.jsx(me,{type:"secondary",style:{fontSize:"11px"},children:t.description})]}),onClick:()=>ut(n,t.key),disabled:"completed"!==n.status})))},trigger:["click"],disabled:"completed"!==n.status,children:e.jsx(Y,{title:"completed"===n.status?"Exporter":"Rapport non terminé",children:e.jsx(A,{type:"text",icon:e.jsx(_,{}),loading:Be,disabled:"completed"!==n.status,style:{color:"completed"===n.status?t.SECONDARY_BLUE:t.LIGHT_GRAY}})})}),e.jsx(Y,{title:"Imprimer",children:e.jsx(A,{type:"text",icon:e.jsx(L,{}),onClick:()=>mt(n),disabled:"completed"!==n.status,style:{color:"completed"===n.status?t.DARK_GRAY:t.LIGHT_GRAY}})})]})}]),[pt,ut,mt,Be]);return Ne&&ce?e.jsx(T,{status:"error",title:"Erreur de chargement",subTitle:Ne,extra:e.jsx(A,{type:"primary",onClick:()=>window.location.reload(),children:"Recharger la page"})}):e.jsxs("div",{className:"reports-page",style:{padding:x?"16px":"24px"},children:[e.jsxs(l,{title:e.jsxs(d,{children:[e.jsx(z,{style:{color:t.PRIMARY_BLUE}}),e.jsx(he,{level:4,style:{margin:0,color:t.PRIMARY_BLUE},children:"Rapports de Production"})]}),extra:e.jsxs(d,{children:["shift"===C&&e.jsxs(d,{children:[e.jsx(me,{style:{fontSize:"12px",color:t.LIGHT_GRAY},children:"Format:"}),e.jsxs(A.Group,{size:"small",children:[e.jsx(A,{type:Ve?"default":"primary",onClick:()=>Ke(!1),style:{backgroundColor:Ve?"transparent":t.PRIMARY_BLUE,borderColor:t.PRIMARY_BLUE,color:Ve?t.PRIMARY_BLUE:"white"},children:"Standard"}),e.jsx(A,{type:Ve?"primary":"default",onClick:()=>Ke(!0),style:{backgroundColor:Ve?t.SECONDARY_BLUE:"transparent",borderColor:t.SECONDARY_BLUE,color:Ve?"white":t.SECONDARY_BLUE},children:"Amélioré"})]})]}),e.jsx(A,{icon:e.jsx(U,{}),type:"primary",onClick:()=>ht(),disabled:"shift"===C&&!Je.canCreate,style:{backgroundColor:"shift"!==C||Je.canCreate?t.PRIMARY_BLUE:"#d9d9d9",borderColor:"shift"!==C||Je.canCreate?t.PRIMARY_BLUE:"#d9d9d9"},title:"shift"!==C||Je.canCreate?"":Je.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine",children:"Nouveau Rapport"}),e.jsx(A,{icon:e.jsx(O,{}),onClick:tt,loading:oe,children:"Actualiser"})]}),style:{background:m?"#141414":"#fff",boxShadow:m?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"},children:[e.jsx(I,{items:[{title:"Accueil"},{title:"Rapports"},{title:(null==(i=Re.find((e=>e.key===C)))?void 0:i.label)||"Tous les rapports"}],style:{marginBottom:16}}),e.jsxs(y,{gutter:[16,16],children:[e.jsx(f,{xs:24,md:6,lg:5,xl:4,children:e.jsx(l,{title:e.jsxs(d,{children:[e.jsx(M,{style:{color:t.SECONDARY_BLUE}}),e.jsx(me,{strong:!0,children:"Types de rapports"})]}),size:"small",bodyStyle:{padding:0},style:{marginBottom:x?16:0},children:e.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"4px",padding:"8px"},children:Re.map((a=>e.jsxs("div",{onClick:()=>nt(a.key),style:{display:"flex",alignItems:"flex-start",gap:"12px",padding:"12px 8px",borderRadius:"6px",cursor:"pointer",backgroundColor:C===a.key?t.HOVER_BLUE:"transparent",border:C===a.key?`1px solid ${t.PRIMARY_BLUE}`:"1px solid transparent",transition:"all 0.2s ease"},onMouseEnter:e=>{C!==a.key&&(e.currentTarget.style.backgroundColor="#f8f9fa")},onMouseLeave:e=>{C!==a.key&&(e.currentTarget.style.backgroundColor="transparent")},children:[e.jsx("span",{style:{color:a.color,fontSize:"16px",marginTop:"2px"},children:a.icon}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{fontWeight:500,color:C===a.key?t.PRIMARY_BLUE:t.DARK_GRAY,fontSize:"14px",marginBottom:"2px",lineHeight:"1.3"},children:a.label}),e.jsx("div",{style:{fontSize:"11px",color:t.LIGHT_GRAY,lineHeight:"1.2"},children:a.description})]})]},a.key)))})})}),e.jsxs(f,{xs:24,md:18,lg:19,xl:20,children:[e.jsx(ue,{activeReportType:C,dateRange:F,selectedShift:K,selectedMachines:W,selectedModels:X,searchText:re,machines:fe,models:Ce,shifts:ve,onReportTypeChange:N,onDateRangeChange:st,onShiftChange:rt,onMachineChange:it,onModelChange:ot,onSearchChange:lt,onClearFilters:dt,machinesLoading:De,modelsLoading:Ee,existingReports:Qe,onCheckReportExists:at}),e.jsx(l,{title:e.jsxs(d,{children:[e.jsx(z,{style:{color:t.SECONDARY_BLUE}}),e.jsx(me,{strong:!0,children:"Rapports disponibles"}),e.jsx(q,{count:Se.total,style:{backgroundColor:t.PRIMARY_BLUE}})]}),extra:$e&&e.jsxs(d,{children:[e.jsx($,{spin:!0}),e.jsx(me,{type:"secondary",children:"Actualisation automatique..."})]}),children:e.jsx(P,{columns:yt(),dataSource:xe,rowKey:"id",loading:oe,pagination:{...Se,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["10","20","50","100"],showTotal:(e,t)=>`${t[0]}-${t[1]} sur ${te(e)} rapports`},onChange:ct,locale:{emptyText:e.jsx(B,{image:B.PRESENTED_IMAGE_SIMPLE,description:"Aucun rapport trouvé",style:{color:t.LIGHT_GRAY},children:e.jsxs(A,{type:"primary",icon:e.jsx(U,{}),onClick:()=>ht(),disabled:"shift"===C&&!Je.canCreate,style:{backgroundColor:"shift"!==C||Je.canCreate?t.PRIMARY_BLUE:"#d9d9d9",borderColor:"shift"!==C||Je.canCreate?t.PRIMARY_BLUE:"#d9d9d9"},title:"shift"!==C||Je.canCreate?"":Je.reportExists?"Un rapport existe déjà pour cette date et équipe":"Veuillez sélectionner la date, l'équipe et la machine",children:["Générer ","shift"===C&&Ve?"Rapport Amélioré":"Rapport"]})})},scroll:{x:1200},size:"middle"})})]})]})]}),e.jsx(G,{title:"Génération du rapport",open:qe,footer:null,closable:!1,centered:!0,children:e.jsxs("div",{style:{textAlign:"center",padding:"20px 0"},children:[e.jsx(H,{type:"circle",percent:Ge,strokeColor:t.PRIMARY_BLUE}),e.jsx("div",{style:{marginTop:16},children:e.jsx(me,{children:"Génération en cours..."})})]})}),e.jsx(G,{title:Le&&e.jsxs(d,{children:[e.jsx(z,{style:{color:t.PRIMARY_BLUE}}),e.jsxs("span",{children:["Rapport #",Le.id]}),e.jsx(p,{color:null==(c=be[Le.status])?void 0:c.color,children:null==(u=be[Le.status])?void 0:u.text})]}),open:Ie,onCancel:()=>Pe(!1),width:800,footer:Le&&[e.jsx(A,{onClick:()=>Pe(!1),children:"Fermer"},"close"),e.jsx(A,{icon:e.jsx(L,{}),onClick:()=>mt(Le),disabled:"completed"!==Le.status,children:"Imprimer"},"print"),e.jsx(S,{menu:{items:je.map((e=>({key:e.key,icon:e.icon,label:e.label,onClick:()=>ut(Le,e.key)})))},disabled:"completed"!==Le.status,children:e.jsx(A,{icon:e.jsx(_,{}),loading:Be,type:"primary",disabled:"completed"!==Le.status,style:{backgroundColor:t.PRIMARY_BLUE,borderColor:t.PRIMARY_BLUE},children:"Exporter"})},"export")],children:Le&&e.jsx(Ae,{report:Le})})]})},Ae=({report:a})=>{var n,s,r,i,o,l,d;if(!a)return null;const c=Re.find((e=>e.key===a.type));return e.jsxs("div",{children:[e.jsxs(y,{gutter:[16,16],style:{marginBottom:20},children:[e.jsx(f,{span:8,children:e.jsx(Z,{title:"Type de rapport",value:(null==c?void 0:c.label)||a.type,prefix:null==c?void 0:c.icon})}),e.jsx(f,{span:8,children:e.jsx(Z,{title:"Date du rapport",value:D(a.date).format("DD/MM/YYYY")})}),e.jsx(f,{span:8,children:e.jsx(Z,{title:"Généré le",value:D(a.generatedAt).format("DD/MM/YYYY à HH:mm")})})]}),e.jsx(x,{}),"production"===a.type&&e.jsxs("div",{children:[e.jsx(he,{level:5,style:{color:t.PRIMARY_BLUE},children:"Production"}),e.jsxs(y,{gutter:[16,16],children:[e.jsx(f,{span:8,children:e.jsx(Z,{title:"Production Totale",value:te((null==(n=a.production)?void 0:n.total)||0),suffix:"unités",valueStyle:{color:t.PRIMARY_BLUE}})}),e.jsx(f,{span:8,children:e.jsx(Z,{title:"Performance",value:ne(((null==(s=a.production)?void 0:s.performance)||0)/100),valueStyle:{color:((null==(r=a.production)?void 0:r.performance)||0)>=90?"#52c41a":"#faad14"}})}),e.jsx(f,{span:8,children:e.jsx(Z,{title:"Qualité",value:ne(((null==(i=a.quality)?void 0:i.rate)||0)/100),valueStyle:{color:((null==(o=a.quality)?void 0:o.rate)||0)>=95?"#52c41a":"#ff4d4f"}})})]})]}),"arrets"===a.type&&e.jsxs("div",{children:[e.jsx(he,{level:5,style:{color:t.PRIMARY_BLUE},children:"Arrêts et Pannes"}),e.jsxs(y,{gutter:[16,16],children:[e.jsx(f,{span:12,children:e.jsx(Z,{title:"Nombre d'arrêts",value:te((null==(l=a.arrets)?void 0:l.total)||0),valueStyle:{color:"#ff4d4f"}})}),e.jsx(f,{span:12,children:e.jsx(Z,{title:"MTTR Moyen",value:ae((null==(d=a.arrets)?void 0:d.averageMTTR)||0,1),suffix:"min",valueStyle:{color:t.SECONDARY_BLUE}})})]})]}),e.jsx(x,{}),e.jsxs(xe,{style:{color:t.DARK_GRAY},children:["Ce rapport a été généré le ",D(a.generatedAt).format("DD MMMM YYYY à HH:mm"),"par ",a.generatedBy||"le système automatique","."]}),a.notes&&e.jsxs(e.Fragment,{children:[e.jsx(he,{level:5,style:{color:t.PRIMARY_BLUE},children:"Notes"}),e.jsx(xe,{children:a.notes})]})]})};export{Ye as default};
