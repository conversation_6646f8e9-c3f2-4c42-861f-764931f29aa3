const testGraphQL = async () => {
  try {
    console.log('Testing GraphQL endpoint...');
    
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getStopMachineModels {
              model
            }
          }
        `
      })
    });
    
    const result = await response.json();
    console.log('✅ GraphQL Response:', result);
    
    if (result.data && result.data.getStopMachineModels) {
      console.log('✅ Machine models found:', result.data.getStopMachineModels);
    } else {
      console.log('⚠️ No machine models in response');
    }
    
  } catch (error) {
    console.error('❌ GraphQL test failed:', error);
  }
};

testGraphQL();
