import { WebSocketServer } from "ws";
import { WebSocket } from "ws";
import db from "../db.js";
import MachineAlertService from "../services/MachineAlertService.js";

// Store active WebSocket connections
const clients = new Set();

// Initialize WebSocket server for machine data
const initMachineDataWebSocket = (server) => {
  const wss = new WebSocketServer({ noServer: true });

  server.on("upgrade", (request, socket, head) => {
    // Handle machine data WebSocket connections on a different path than notifications
    if (request.url === "/api/machine-data-ws") {
      wss.handleUpgrade(request, socket, head, (ws) => {
        wss.emit("connection", ws, request);
      });
    }
  });

  wss.on("connection", (ws) => {
    console.log("Machine data WebSocket client connected");

    // Add the client to our set of connected clients
    clients.add(ws);

    // Send initial machine data
    sendInitialMachineData(ws);

    // Set up a ping interval to keep the connection alive
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      }
    }, 30000);

    ws.on("message", (message) => {
      try {
        const data = JSON.parse(message);
        console.log("Received message from client:", data);

        // Handle client messages if needed
        if (data.type === "requestUpdate") {
          sendInitialMachineData(ws);
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    });

    ws.on("close", () => {
      // Remove the client when connection is closed
      clients.delete(ws);
      clearInterval(pingInterval);
      console.log("Machine data WebSocket client disconnected");
    });

    ws.on("error", (error) => {
      console.error("WebSocket error:", error);
      clients.delete(ws);
      clearInterval(pingInterval);
    });
  });

  return wss;
};

// Function to send initial machine data to a client
const sendInitialMachineData = async (ws) => {
  try {
    // Get machine data from database
    const [machineData] = await db.execute(`
      SELECT
        id,
        Machine_Name,
        Ordre_Fabrication,
        Article,
        Quantite_Planifier,
        Quantite_Bon,
        Quantite_Rejet,
        Poids_Purge,
        Stop_Time,
        Regleur_Prenom,
        Etat,
        Code_arret,
        TRS,
        cycle,
        Poid_unitaire,
        cycle_theorique,
        empreint
      FROM real_time_table
    `);

    // Get active sessions
    const [activeSessions] = await db.execute(`
      SELECT *
      FROM machine_sessions
      WHERE session_end IS NULL
    `);

    // Get side cards data
    const [sideCardData] = await db.execute(`
      SELECT
        COUNT(*) as total_machines,
        SUM(CASE WHEN Etat = 'on' THEN 1 ELSE 0 END) as active_machines,
        AVG(CAST(TRS AS DECIMAL(10,2))) as avg_trs,
        SUM(CAST(Quantite_Bon AS UNSIGNED)) as total_production,
        SUM(CAST(Quantite_Rejet AS UNSIGNED)) as total_rejects
      FROM real_time_table
    `);

    // Get daily stats
    const [dailyStats] = await db.execute(`
      SELECT
        DATE_FORMAT(session_start, '%H:00') as time_bucket,
        AVG(CAST(TRS AS DECIMAL(10,2))) as trs,
        SUM(CAST(Quantite_Bon AS UNSIGNED)) as production
      FROM machine_sessions
      WHERE DATE(session_start) = CURDATE()
      GROUP BY time_bucket
      ORDER BY time_bucket
    `);

    // Send the data to the client
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: "initialData",
        machineData,
        activeSessions,
        sideCardData: sideCardData[0] || {},
        dailyStats
      }));
    }
  } catch (error) {
    console.error("Error fetching initial machine data:", error);
  }
};

// Function to broadcast machine data updates to all connected clients
const broadcastMachineUpdate = (data) => {
  clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({
        type: "update",
        data
      }));
    }
  });
};

// Function to broadcast session updates to all connected clients
const broadcastSessionUpdate = (sessionData, updateType) => {
  clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({
        type: "sessionUpdate",
        updateType, // 'created', 'updated', or 'stopped'
        sessionData
      }));
    }
  });
};

// Setup database triggers or polling to detect changes and broadcast updates
const setupDataChangeDetection = () => {
  // Poll the database every few seconds to detect changes
  // This is a fallback method if database triggers are not available
  let lastMachineData = null;

  // Start machine alert monitoring
  MachineAlertService.startMonitoring(60000); // Check every minute for alerts

  const pollInterval = setInterval(async () => {
    try {
      // Only poll if we have active clients
      if (clients.size === 0) return;

      const [currentMachineData] = await db.execute(`
        SELECT
          id,
          Machine_Name,
          Ordre_Fabrication,
          Article,
          Quantite_Planifier,
          Quantite_Bon,
          Quantite_Rejet,
          Poids_Purge,
          Stop_Time,
          Regleur_Prenom,
          Etat,
          Code_arret,
          TRS,
          cycle,
          Poid_unitaire,
          cycle_theorique,
          empreint
        FROM real_time_table
      `);

      // If this is the first poll, just store the data
      if (!lastMachineData) {
        lastMachineData = currentMachineData;
        return;
      }

      // Check for changes
      const changes = detectChanges(lastMachineData, currentMachineData);

      // If there are changes, broadcast them
      if (changes.length > 0) {
        broadcastMachineUpdate({
          changedMachines: changes,
          fullData: currentMachineData
        });
      }

      // Update the stored data
      lastMachineData = currentMachineData;
    } catch (error) {
      console.error("Error polling for machine data changes:", error);
    }
  }, 3000); // Poll every 3 seconds

  return () => {
    clearInterval(pollInterval);
    MachineAlertService.stopMonitoring();
  };
};

// Helper function to detect changes between two sets of machine data
const detectChanges = (oldData, newData) => {
  const changes = [];

  // Create maps for faster lookup
  const oldMap = new Map(oldData.map(machine => [machine.id, machine]));
  const newMap = new Map(newData.map(machine => [machine.id, machine]));

  // Check for updated or new machines
  newData.forEach(newMachine => {
    const oldMachine = oldMap.get(newMachine.id);

    // If machine is new or has changed
    if (!oldMachine || hasChanged(oldMachine, newMachine)) {
      changes.push({
        type: oldMachine ? 'updated' : 'new',
        machine: newMachine
      });
    }
  });

  // Check for removed machines
  oldData.forEach(oldMachine => {
    if (!newMap.has(oldMachine.id)) {
      changes.push({
        type: 'removed',
        machine: oldMachine
      });
    }
  });

  return changes;
};

// Helper function to check if a machine's data has changed
const hasChanged = (oldMachine, newMachine) => {
  // Define fields to compare
  const fieldsToCompare = [
    'Etat', 'Quantite_Bon', 'Quantite_Rejet', 'TRS', 'Regleur_Prenom',
    'Ordre_Fabrication', 'Article', 'Quantite_Planifier', 'cycle',
    'Poid_unitaire', 'cycle_theorique', 'empreint', 'Code_arret'
  ];

  // Check if any of the fields have changed
  return fieldsToCompare.some(field => oldMachine[field] !== newMachine[field]);
};

export {
  initMachineDataWebSocket,
  broadcastMachineUpdate,
  broadcastSessionUpdate,
  setupDataChangeDetection
};