const axios = require('axios');

const testStopsData = async () => {
  try {
    console.log('🔍 Testing stops data with duration information...');
    
    const response = await axios.post('http://localhost:5000/api/graphql', {
      query: `
        query {
          getAllMachineStops {
            Date_Insert
            Machine_Name
            Part_NO
            Code_Stop
            Debut_Stop
            Fin_Stop_Time
            Regleur_Prenom
            duration_minutes
          }
        }
      `
    });
    
    console.log('✅ Backend GraphQL response received');
    
    if (response.data.data && response.data.data.getAllMachineStops) {
      const stops = response.data.data.getAllMachineStops;
      console.log('📊 Sample stops data:', stops.slice(0, 3));
      
      // Check for duration information
      const stopsWithDuration = stops.filter(stop => stop.duration_minutes && stop.duration_minutes > 0);
      console.log('📊 Stops with duration:', {
        totalStops: stops.length,
        stopsWithDuration: stopsWithDuration.length,
        sampleDurations: stopsWithDuration.slice(0, 5).map(s => s.duration_minutes)
      });
      
      // Calculate total duration
      const totalDuration = stops.reduce((sum, stop) => {
        return sum + (parseFloat(stop.duration_minutes) || 0);
      }, 0);
      
      console.log('📊 Total duration calculated:', Math.round(totalDuration), 'minutes');
      
      // Check operator data
      const operatorCounts = {};
      stops.forEach(stop => {
        const operator = stop.Regleur_Prenom || 'Non assigné';
        operatorCounts[operator] = (operatorCounts[operator] || 0) + 1;
      });
      
      console.log('📊 Operator interventions:', operatorCounts);
      
    } else {
      console.log('❌ No stops data received');
    }
  } catch (error) {
    console.error('❌ Error testing stops data:', error.message);
  }
};

testStopsData().catch(console.error);
