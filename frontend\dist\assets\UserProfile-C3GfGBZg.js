import{a as e,j as s}from"./index-Nnj1g72A.js";import{r as l}from"./react-vendor-tYPmozCJ.js";import a from"./user-management-CjwWAgRm.js";import{aR as r,a6 as i,a7 as t,a9 as n,_ as o,F as d,T as c,O as u,aS as m,S as x,c as p,e as h,aT as j,aU as f,a4 as b,aV as v,aJ as g,aW as y,aX as I,f as N,B as w,G as C,aG as k,z as A,s as S,ar as P}from"./antd-vendor-4OvKHZ_k.js";const{Title:z,Text:E,Paragraph:T}=c,{TabPane:L}=b,{Option:V}=P,M=()=>{const{user:c,updateProfile:P,changePassword:V}=e(),[M]=r.useForm(),[F]=r.useForm(),[D,q]=l.useState(!1),[B,U]=l.useState(!1),[R,$]=l.useState(!1);l.useEffect((()=>{const e=document.documentElement.classList.contains("dark")||document.body.classList.contains("dark")||"dark"===localStorage.getItem("theme");$(e);const s=new MutationObserver((e=>{e.forEach((e=>{if("class"===e.attributeName){const e=document.documentElement.classList.contains("dark")||document.body.classList.contains("dark");$(e)}}))}));return s.observe(document.documentElement,{attributes:!0}),s.observe(document.body,{attributes:!0}),()=>s.disconnect()}),[]);const G={backgroundColor:R?"#1f1f1f":"#ffffff",boxShadow:R?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.05)",borderRadius:"12px",border:"none",transition:"all 0.3s ease"},O={backgroundColor:"#1890ff",boxShadow:R?"0 4px 8px rgba(24, 144, 255, 0.5)":"0 4px 8px rgba(24, 144, 255, 0.2)",padding:"4px",border:"4px solid",borderColor:R?"#141414":"#ffffff",transition:"all 0.3s ease"},W=()=>{D||M.setFieldsValue({username:null==c?void 0:c.username,email:null==c?void 0:c.email,fullName:(null==c?void 0:c.fullName)||"",phone:(null==c?void 0:c.phone)||""}),q(!D)};return s.jsx("div",{style:{padding:24},children:s.jsxs(i,{gutter:[24,24],children:[s.jsx(t,{xs:24,md:8,children:s.jsxs(n,{bordered:!1,style:G,className:"profile-card",children:[s.jsxs("div",{style:{textAlign:"center",marginBottom:24},children:[s.jsx(o,{size:120,icon:s.jsx(d,{}),style:O,className:"profile-avatar"}),s.jsx(z,{level:3,style:{marginTop:16,marginBottom:4},children:(null==c?void 0:c.fullName)||(null==c?void 0:c.username)}),s.jsx("div",{style:{marginBottom:8},children:c?"admin"===c.role?s.jsx(w,{status:"success",text:s.jsx(E,{strong:!0,style:{color:"#52c41a"},children:"Administrateur"})}):c.active?s.jsx(w,{status:"processing",text:s.jsx(E,{children:"Utilisateur actif"})}):s.jsx(w,{status:"default",text:s.jsx(E,{type:"secondary",children:"Utilisateur inactif"})}):null}),s.jsxs(T,{type:"secondary",style:{fontSize:"14px"},children:["Membre depuis ",(null==c?void 0:c.createdAt)?new Date(c.createdAt).toLocaleDateString():"N/A"]})]}),s.jsx(u,{style:{margin:"12px 0 24px"}}),s.jsxs(m,{title:s.jsx(E,{strong:!0,children:"Informations"}),column:1,bordered:!1,size:"small",labelStyle:{fontWeight:"500",color:R?"rgba(255,255,255,0.85)":"rgba(0,0,0,0.85)"},contentStyle:{color:R?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)"},children:[s.jsx(m.Item,{label:"Nom d'utilisateur",children:null==c?void 0:c.username}),s.jsx(m.Item,{label:"Email",children:null==c?void 0:c.email}),s.jsx(m.Item,{label:"Téléphone",children:(null==c?void 0:c.phone)||"Non renseigné"}),s.jsx(m.Item,{label:"Rôle",children:"admin"===(null==c?void 0:c.role)?"Administrateur":"Utilisateur"}),s.jsx(m.Item,{label:"Statut",children:(null==c?void 0:c.active)?"Actif":"Inactif"})]}),s.jsx("div",{style:{marginTop:24,textAlign:"center"},children:s.jsxs(x,{children:[s.jsx(p,{title:"Modifier le profil",children:s.jsx(h,{type:"primary",icon:s.jsx(j,{}),onClick:W,shape:"round",children:"Modifier"})}),s.jsx(p,{title:"Changer le mot de passe",children:s.jsx(h,{icon:s.jsx(f,{}),onClick:()=>document.getElementById("security-tab").click(),shape:"round",children:"Mot de passe"})})]})})]})}),s.jsx(t,{xs:24,md:16,children:s.jsx(n,{bordered:!1,style:G,className:"profile-tabs-card",children:s.jsxs(b,{defaultActiveKey:"profile",children:[s.jsxs(L,{tab:s.jsxs("span",{children:[s.jsx(d,{}),"Profil"]}),children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:16},children:[s.jsx(z,{level:4,children:"Informations du profil"}),s.jsx(h,{type:D?"primary":"default",icon:D?s.jsx(v,{}):s.jsx(j,{}),onClick:W,children:D?"Enregistrer":"Modifier"})]}),D?s.jsxs(r,{form:M,layout:"vertical",onFinish:async e=>{U(!0);try{(await P(e)).success&&(q(!1),S.success("Profil mis à jour avec succès!"))}finally{U(!1)}},initialValues:{username:null==c?void 0:c.username,email:null==c?void 0:c.email,fullName:(null==c?void 0:c.fullName)||"",phone:(null==c?void 0:c.phone)||""},children:[s.jsxs(i,{gutter:16,children:[s.jsx(t,{span:12,children:s.jsx(r.Item,{name:"fullName",label:"Nom complet",rules:[{required:!0,message:"Veuillez entrer votre nom complet"}],children:s.jsx(g,{prefix:s.jsx(d,{}),placeholder:"Nom complet"})})}),s.jsx(t,{span:12,children:s.jsx(r.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer votre nom d'utilisateur"}],children:s.jsx(g,{prefix:s.jsx(d,{}),placeholder:"Nom d'utilisateur"})})})]}),s.jsxs(i,{gutter:16,children:[s.jsx(t,{span:12,children:s.jsx(r.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer votre email"},{type:"email",message:"Veuillez entrer un email valide"}],children:s.jsx(g,{prefix:s.jsx(y,{}),placeholder:"Email"})})}),s.jsx(t,{span:12,children:s.jsx(r.Item,{name:"phone",label:"Téléphone",rules:[{pattern:/^[0-9+\s-]{8,15}$/,message:"Format de téléphone invalide"}],children:s.jsx(g,{prefix:s.jsx(I,{}),placeholder:"Téléphone"})})})]}),s.jsx(r.Item,{children:s.jsx(h,{type:"primary",htmlType:"submit",loading:B,children:"Mettre à jour le profil"})})]}):s.jsxs(m,{bordered:!0,column:{xxl:2,xl:2,lg:2,md:1,sm:1,xs:1},labelStyle:{fontWeight:"500",color:R?"rgba(255,255,255,0.85)":"rgba(0,0,0,0.85)"},children:[s.jsx(m.Item,{label:"Nom complet",children:(null==c?void 0:c.fullName)||"Non renseigné"}),s.jsx(m.Item,{label:"Nom d'utilisateur",children:null==c?void 0:c.username}),s.jsx(m.Item,{label:"Email",children:null==c?void 0:c.email}),s.jsx(m.Item,{label:"Téléphone",children:(null==c?void 0:c.phone)||"Non renseigné"}),s.jsx(m.Item,{label:"Rôle",span:2,children:s.jsx(N,{color:"admin"===(null==c?void 0:c.role)?"green":"blue",children:"admin"===(null==c?void 0:c.role)?"Administrateur":"Utilisateur"})}),s.jsx(m.Item,{label:"Statut",span:2,children:s.jsx(w,{status:(null==c?void 0:c.active)?"success":"default",text:(null==c?void 0:c.active)?"Actif":"Inactif"})}),s.jsx(m.Item,{label:"Compte créé",span:2,children:(null==c?void 0:c.createdAt)?new Date(c.createdAt).toLocaleDateString():"N/A"}),s.jsx(m.Item,{label:"Dernière connexion",span:2,children:(null==c?void 0:c.lastLogin)?new Date(c.lastLogin).toLocaleString():"N/A"})]})]},"profile"),s.jsxs(L,{tab:s.jsxs("span",{id:"security-tab",children:[s.jsx(C,{}),"Sécurité"]}),children:[s.jsx(z,{level:4,children:"Changer le mot de passe"}),s.jsxs(r,{form:F,layout:"vertical",onFinish:async e=>{U(!0);try{(await V(e)).success&&(F.resetFields(),S.success("Mot de passe changé avec succès!"))}finally{U(!1)}},children:[s.jsx(r.Item,{name:"currentPassword",label:"Mot de passe actuel",rules:[{required:!0,message:"Veuillez entrer votre mot de passe actuel"}],children:s.jsx(g.Password,{prefix:s.jsx(C,{}),placeholder:"Mot de passe actuel"})}),s.jsx(r.Item,{name:"newPassword",label:"Nouveau mot de passe",rules:[{required:!0,message:"Veuillez entrer votre nouveau mot de passe"},{min:8,message:"Le mot de passe doit contenir au moins 8 caractères"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,message:"Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"}],children:s.jsx(g.Password,{prefix:s.jsx(C,{}),placeholder:"Nouveau mot de passe",autoComplete:"new-password"})}),s.jsx(r.Item,{name:"confirmPassword",label:"Confirmer le mot de passe",dependencies:["newPassword"],rules:[{required:!0,message:"Veuillez confirmer votre mot de passe"},({getFieldValue:e})=>({validator:(s,l)=>l&&e("newPassword")!==l?Promise.reject(new Error("Les deux mots de passe ne correspondent pas")):Promise.resolve()})],children:s.jsx(g.Password,{prefix:s.jsx(C,{}),placeholder:"Confirmer le mot de passe",autoComplete:"new-password"})}),s.jsx(r.Item,{children:s.jsx(h,{type:"primary",htmlType:"submit",loading:B,children:"Changer le mot de passe"})})]}),s.jsx(u,{}),s.jsx(z,{level:4,children:"Paramètres de sécurité"}),s.jsxs(m,{bordered:!0,column:1,children:[s.jsxs(m.Item,{label:"Authentification à deux facteurs",children:[s.jsx(k,{checkedChildren:"Activée",unCheckedChildren:"Désactivée",defaultChecked:null==c?void 0:c.twoFactorEnabled,disabled:!0}),s.jsx(h,{type:"link",disabled:!0,children:"Configurer"})]}),s.jsx(m.Item,{label:"Notifications de connexion",children:s.jsx(k,{checkedChildren:"Activées",unCheckedChildren:"Désactivées",defaultChecked:null==c?void 0:c.loginNotifications,disabled:!0})}),s.jsx(m.Item,{label:"Sessions actives",children:s.jsx(h,{type:"link",disabled:!0,children:"Voir les sessions (1 active)"})})]})]},"security"),c&&"admin"===(null==c?void 0:c.role)&&s.jsx(L,{tab:s.jsxs("span",{children:[s.jsx(A,{}),"Gestion des utilisateurs"]}),children:s.jsx(a,{darkMode:R})},"users")]})})})]})})};export{M as default};
