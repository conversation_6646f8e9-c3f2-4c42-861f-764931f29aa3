/**
 * Simple script to fix the reports.version column size issue
 * Directly executes the ALTER TABLE statement to extend VARCHAR(10) to VARCHAR(50)
 */

import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || 'root',
  database: process.env.DB_NAME || 'Testingarea51'
};

async function fixVersionColumn() {
  let connection;
  
  try {
    console.log('🔧 Fixing reports.version column size issue');
    console.log('==========================================\n');

    // Connect to database
    console.log('📡 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection established\n');

    // Check current column structure
    console.log('🔍 Checking current version column...');
    const [currentSchema] = await connection.execute(`
      SHOW COLUMNS FROM reports LIKE 'version'
    `);

    if (currentSchema.length === 0) {
      console.log('❌ Version column not found in reports table');
      console.log('💡 The table might need to be created first');
      return;
    }

    const currentColumn = currentSchema[0];
    console.log('📊 Current column definition:', {
      field: currentColumn.Field,
      type: currentColumn.Type,
      null: currentColumn.Null,
      key: currentColumn.Key,
      default: currentColumn.Default,
      extra: currentColumn.Extra
    });

    // Check if already fixed
    if (currentColumn.Type.includes('varchar(50)') || currentColumn.Type.includes('varchar(255)')) {
      console.log('✅ Version column already has sufficient size');
      console.log('🎯 No migration needed');
      return;
    }

    // Show existing data before migration
    console.log('\n📋 Checking existing reports...');
    const [existingData] = await connection.execute(`
      SELECT version, COUNT(*) as count 
      FROM reports 
      GROUP BY version 
      ORDER BY count DESC
    `);

    console.log('📊 Current version distribution:');
    existingData.forEach(row => {
      console.log(`  - ${row.version}: ${row.count} reports`);
    });

    // Execute the ALTER TABLE statement
    console.log('\n🔧 Extending version column from VARCHAR(10) to VARCHAR(50)...');
    await connection.execute(`
      ALTER TABLE reports 
      MODIFY COLUMN version VARCHAR(50) DEFAULT 'standard'
    `);
    console.log('✅ Column successfully extended');

    // Verify the change
    console.log('\n🔍 Verifying the change...');
    const [newSchema] = await connection.execute(`
      SHOW COLUMNS FROM reports LIKE 'version'
    `);

    const newColumn = newSchema[0];
    console.log('📊 Updated column definition:', {
      field: newColumn.Field,
      type: newColumn.Type,
      null: newColumn.Null,
      key: newColumn.Key,
      default: newColumn.Default,
      extra: newColumn.Extra
    });

    // Test inserting the problematic value
    console.log('\n🧪 Testing "enhanced-react" insertion...');
    
    // Use a simple query without prepared statements
    const testQuery = `
      INSERT INTO reports (
        type, title, description, date, status, generated_at, version
      ) VALUES (
        'test', 'Migration Test', 'Testing enhanced-react version', 
        CURDATE(), 'completed', NOW(), 'enhanced-react'
      )
    `;

    const [insertResult] = await connection.execute(testQuery);
    const testId = insertResult.insertId;
    console.log(`✅ Test insertion successful (ID: ${testId})`);

    // Verify the test record
    const [testRecord] = await connection.execute(`
      SELECT id, version, LENGTH(version) as version_length 
      FROM reports 
      WHERE id = ?
    `, [testId]);

    console.log('📊 Test record:', {
      id: testRecord[0].id,
      version: testRecord[0].version,
      length: testRecord[0].version_length
    });

    // Clean up test record
    await connection.execute(`DELETE FROM reports WHERE id = ?`, [testId]);
    console.log('🧹 Test record cleaned up');

    // Final summary
    console.log('\n🎉 Migration completed successfully!');
    console.log('✅ reports.version column extended to VARCHAR(50)');
    console.log('✅ "enhanced-react" version identifier now supported');
    console.log('🚀 PDF generation system should now work without database errors');

    // Show final column info
    console.log('\n📈 Final column specification:');
    console.log(`  Type: ${newColumn.Type}`);
    console.log(`  Can store: Up to 50 characters`);
    console.log(`  Current max needed: 13 characters ("enhanced-react")`);
    console.log(`  Future capacity: 37 additional characters available`);

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    
    if (error.code === 'ER_BAD_FIELD_ERROR') {
      console.error('💡 The reports table might not exist yet');
      console.error('   Try running the application first to create the table');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 Database access denied - check credentials in config.env');
    } else {
      console.error('📋 Full error details:', error);
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n📡 Database connection closed');
    }
  }
}

// Load environment variables from config.env
const configPath = path.join(__dirname, '../config.env');
if (fs.existsSync(configPath)) {
  console.log('📄 Loading configuration from config.env...');
  const envContent = fs.readFileSync(configPath, 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
  console.log('✅ Configuration loaded\n');
} else {
  console.log('⚠️ config.env not found, using default values\n');
}

// Run the fix
fixVersionColumn().catch(error => {
  console.error('❌ Script failed:', error.message);
  process.exit(1);
});
