// Test script to verify apicache works
console.log('Testing apicache dependency...');

try {
    const apicache = require('apicache');
    console.log('SUCCESS: apicache successfully imported');

    // Test basic functionality
    const cache = apicache.middleware('5 minutes');
    console.log('SUCCESS: apicache middleware created successfully');

    // Test cache instance
    const cacheInstance = apicache.newInstance();
    console.log('SUCCESS: apicache instance created successfully');

    console.log('SUCCESS: All apicache tests passed!');
    process.exit(0);
} catch (error) {
    console.error('ERROR: apicache test failed:', error.message);
    process.exit(1);
}
