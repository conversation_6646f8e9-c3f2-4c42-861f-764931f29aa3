/**
 * Test the FINAL comprehensive GraphQL query
 */

import fetch from 'node-fetch';

async function testFinalComprehensiveQuery() {
  console.log('🧪 Testing FINAL comprehensive stop data query...');
  
  const query = `
    query GetFinalComprehensiveStopData($filters: FinalOptimizedStopFilterInput) {
      getFinalComprehensiveStopData(filters: $filters) {
        totalRecords
        queryExecutionTime
        cacheHit
        
        sidecards {
          Arret_Totale
          Arret_Totale_nondeclare
        }
        
        allStops {
          Machine_Name
          Date_Insert
          Code_Stop
          Debut_Stop
          Fin_Stop_Time
          Regleur_Prenom
          duration_minutes
        }
        
        topStops {
          stopName
          count
        }
        
        stopReasons {
          reason
          count
        }
        
        machineComparison {
          Machine_Name
          stops
          totalDuration
        }
        
        operatorStats {
          operator
          interventions
          totalDuration
        }
        
        durationTrend {
          hour
          avgDuration
        }
        
        stopStats {
          Stop_Date
          Total_Stops
        }
      }
    }
  `;

  const variables = {
    filters: {
      limit: 100
    }
  };

  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, variables })
    });

    if (!response.ok) {
      console.error(`❌ HTTP error! status: ${response.status}`);
      return false;
    }

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return false;
    }

    const data = result.data.getFinalComprehensiveStopData;
    console.log('✅ Success! FINAL comprehensive data retrieved:');
    console.log(`   📊 Query Time: ${data.queryExecutionTime}ms`);
    console.log(`   📈 Total Records: ${data.totalRecords}`);
    console.log(`   💾 Cache Status: ${data.cacheHit}`);
    console.log(`   🛑 Total Stops: ${data.sidecards.Arret_Totale}`);
    console.log(`   ⚠️  Non-declared: ${data.sidecards.Arret_Totale_nondeclare}`);
    console.log(`   🔝 Top Stops: ${data.topStops.length} categories`);
    console.log(`   🔧 Machine Comparisons: ${data.machineComparison.length} machines`);
    console.log(`   👤 Operator Stats: ${data.operatorStats.length} operators`);
    console.log(`   📈 Duration Trend: ${data.durationTrend.length} hours`);
    console.log(`   📅 Stop Stats: ${data.stopStats.length} days`);
    console.log(`   📋 Raw Stops: ${data.allStops.length} records`);
    
    // Show sample data
    console.log('\n📋 Sample Raw Stops:');
    data.allStops.slice(0, 3).forEach((stop, i) => {
      console.log(`   ${i+1}. ${stop.Machine_Name} - ${stop.Code_Stop} (${stop.duration_minutes}min)`);
    });
    
    console.log('\n🔝 Top Stop Reasons:');
    data.topStops.slice(0, 3).forEach((stop, i) => {
      console.log(`   ${i+1}. ${stop.stopName}: ${stop.count} occurrences`);
    });
    
    console.log('\n🏭 Machine Comparison:');
    data.machineComparison.slice(0, 3).forEach((machine, i) => {
      console.log(`   ${i+1}. ${machine.Machine_Name}: ${machine.stops} stops, ${machine.totalDuration?.toFixed(1) || 0}min total`);
    });

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Test with filters
async function testFinalWithFilters() {
  console.log('\n🔍 Testing FINAL with IPS machine filter...');
  
  const query = `
    query GetFinalComprehensiveStopData($filters: FinalOptimizedStopFilterInput) {
      getFinalComprehensiveStopData(filters: $filters) {
        totalRecords
        queryExecutionTime
        sidecards {
          Arret_Totale
          Arret_Totale_nondeclare
        }
        allStops {
          Machine_Name
          Code_Stop
          duration_minutes
        }
        topStops {
          stopName
          count
        }
        machineComparison {
          Machine_Name
          stops
          totalDuration
        }
      }
    }
  `;

  const variables = {
    filters: {
      model: 'IPS',
      limit: 50
    }
  };

  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, variables })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return false;
    }

    const data = result.data.getFinalComprehensiveStopData;
    console.log(`✅ IPS Filter Success!`);
    console.log(`   📊 Query Time: ${data.queryExecutionTime}ms`);
    console.log(`   🛑 Total IPS Stops: ${data.sidecards.Arret_Totale}`);
    console.log(`   📋 Records Retrieved: ${data.totalRecords}`);
    
    // Verify all machines start with IPS
    const allIPS = data.allStops.every(stop => stop.Machine_Name.startsWith('IPS'));
    console.log(`   🔍 All machines are IPS: ${allIPS ? '✅' : '❌'}`);
    
    // Show IPS machine comparison
    console.log(`   🏭 IPS Machines: ${data.machineComparison.length} found`);
    data.machineComparison.forEach(machine => {
      console.log(`      - ${machine.Machine_Name}: ${machine.stops} stops`);
    });
    
    return allIPS;

  } catch (error) {
    console.error('❌ Filter test failed:', error.message);
    return false;
  }
}

async function runFinalTests() {
  console.log('🚀 Starting FINAL Optimized GraphQL Tests');
  console.log('='.repeat(60));

  const basicTest = await testFinalComprehensiveQuery();
  const filterTest = await testFinalWithFilters();

  console.log('\n📊 FINAL Test Results:');
  console.log(`   Basic Comprehensive Query: ${basicTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   IPS Filter Query: ${filterTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (basicTest && filterTest) {
    console.log('\n🎉 ALL TESTS PASSED! The FINAL optimized resolver is working perfectly!');
    console.log('✨ This resolver provides ALL dashboard data in a single query.');
    console.log('⚡ Performance: Multiple data sets retrieved efficiently in parallel.');
    console.log('🔄 Next step: Update the frontend to use this optimized resolver.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.');
  }
  
  return basicTest && filterTest;
}

// Test machine models and names
async function testUtilityResolvers() {
  console.log('\n🔧 Testing utility resolvers...');
  
  // Test machine models
  const modelsQuery = `
    query {
      getFinalStopMachineModels {
        model
      }
    }
  `;

  // Test machine names
  const namesQuery = `
    query GetFinalStopMachineNames($filters: FinalOptimizedStopFilterInput) {
      getFinalStopMachineNames(filters: $filters) {
        Machine_Name
      }
    }
  `;

  try {
    // Test models
    const modelsResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: modelsQuery })
    });
    const modelsResult = await modelsResponse.json();

    // Test names for IPS
    const namesResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query: namesQuery, 
        variables: { filters: { model: 'IPS' } }
      })
    });
    const namesResult = await namesResponse.json();

    if (!modelsResult.errors && !namesResult.errors) {
      console.log(`✅ Machine Models: ${modelsResult.data.getFinalStopMachineModels.length} found`);
      console.log(`✅ IPS Machine Names: ${namesResult.data.getFinalStopMachineNames.length} found`);
      return true;
    } else {
      console.log('❌ Utility resolvers failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Utility test failed:', error.message);
    return false;
  }
}

async function main() {
  const mainTestResult = await runFinalTests();
  const utilityTestResult = await testUtilityResolvers();
  
  console.log('\n' + '='.repeat(60));
  console.log('🏁 FINAL TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`Comprehensive Queries: ${mainTestResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Utility Queries: ${utilityTestResult ? '✅ PASS' : '❌ FAIL'}`);
  
  if (mainTestResult && utilityTestResult) {
    console.log('\n🚀 READY FOR FRONTEND INTEGRATION!');
    console.log('📋 The optimized GraphQL resolver is complete and tested.');
    console.log('🔄 Next: Update frontend to use getFinalComprehensiveStopData');
  }
}

main();
