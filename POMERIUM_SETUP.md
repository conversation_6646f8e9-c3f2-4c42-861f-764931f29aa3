# Pomerium Integration Setup Guide

## Overview

This guide explains how to replace ngrok with Pomerium for secure, identity-aware access to your LOCQL application. Pomerium provides enterprise-grade security, authentication, and access control.

## Benefits of Pomerium over ngrok

### ✅ **Security Advantages**
- **Identity-aware access**: Authenticate users before granting access
- **Policy-based authorization**: Fine-grained access control
- **Zero-trust architecture**: Every request is verified
- **Built-in SSL/TLS**: Automatic certificate management

### ✅ **Production Benefits**
- **No bandwidth limits**: Unlike ngrok free tier
- **Self-hosted**: Full control over your infrastructure
- **Enterprise features**: Audit logs, session management
- **High availability**: Designed for production workloads

### ✅ **Integration Benefits**
- **Identity provider support**: Google, GitHub, Azure AD, OIDC
- **WebSocket support**: Real-time features work seamlessly
- **API protection**: Secure your backend APIs
- **CORS handling**: Built-in cross-origin request support

## Architecture

```
External Users → Pomerium Proxy → Docker Containers → Host MySQL
                      ↓
              Identity Provider
              (Google, GitHub, etc.)
```

### URL Structure
- **Frontend**: `https://locql.adapted-osprey-5307.pomerium.app`
- **API**: `https://api.adapted-osprey-5307.pomerium.app`
- **WebSocket**: `wss://ws.adapted-osprey-5307.pomerium.app`

## Quick Start

### 1. Prerequisites
- Docker Desktop running
- MySQL database on host machine
- Valid domain name (using Pomerium Zero for this example)

### 2. Start with Pomerium
```powershell
# Run the Pomerium startup script
.\start-with-pomerium.ps1

# Or start manually
docker-compose -f docker-compose.pomerium.yml up --build
```

### 3. Access the Application
1. Navigate to `https://locql.adapted-osprey-5307.pomerium.app`
2. You'll be redirected to authenticate (currently configured for Gmail)
3. After authentication, you'll have access to the application

## Configuration Details

### Pomerium Configuration
The Pomerium configuration is embedded in `docker-compose.pomerium.yml`:

```yaml
routes:
  # Frontend route
  - from: https://locql.adapted-osprey-5307.pomerium.app
    to: http://frontend:5173
    policy:
      - allow:
          and:
            - domain:
                is: gmail.com
    
  # API route
  - from: https://api.adapted-osprey-5307.pomerium.app
    to: http://backend:5000
    
  # WebSocket route
  - from: https://ws.adapted-osprey-5307.pomerium.app
    to: http://backend:5000
    allow_websockets: true
```

### Authentication Policy
Currently configured to allow users with Gmail accounts. You can modify this in the `docker-compose.pomerium.yml` file:

```yaml
policy:
  - allow:
      and:
        - domain:
            is: your-domain.com  # Change this
```

### Environment Variables
Key environment variables in `pomerium.env`:

```env
# Pomerium URLs
POMERIUM_FRONTEND_URL=https://locql.adapted-osprey-5307.pomerium.app
POMERIUM_API_URL=https://api.adapted-osprey-5307.pomerium.app
POMERIUM_WS_URL=wss://ws.adapted-osprey-5307.pomerium.app

# WebSocket configuration
WS_EXTERNAL_URL=wss://ws.adapted-osprey-5307.pomerium.app
```

## WebSocket Integration

### Updated WebSocket Service
A Pomerium-compatible WebSocket service is provided in:
`frontend/src/utils/websocketService.pomerium.js`

Key changes:
- Uses Pomerium WebSocket URL
- Supports environment variable configuration
- Maintains all existing functionality

### Switching to Pomerium WebSocket Service
To use the Pomerium WebSocket service, update your imports:

```javascript
// Replace this import
import websocketService from "../utils/websocketService";

// With this import
import websocketService from "../utils/websocketService.pomerium";
```

## Customization

### 1. Change Authentication Provider
To use a different identity provider, update the Pomerium configuration:

```yaml
# For GitHub
policy:
  - allow:
      and:
        - domain:
            is: github.com

# For Azure AD
policy:
  - allow:
      and:
        - domain:
            is: your-company.com
```

### 2. Custom Domain
To use your own domain:

1. Update the domain in `docker-compose.pomerium.yml`
2. Configure DNS to point to your server
3. Update environment variables in `pomerium.env`

### 3. Advanced Policies
Pomerium supports complex access policies:

```yaml
policy:
  - allow:
      and:
        - domain:
            is: your-company.com
        - groups:
            has: admin
  - allow:
      and:
        - email:
            is: <EMAIL>
```

## Monitoring and Logs

### View Container Logs
```powershell
# All services
docker-compose -f docker-compose.pomerium.yml logs -f

# Specific services
docker-compose -f docker-compose.pomerium.yml logs -f pomerium
docker-compose -f docker-compose.pomerium.yml logs -f backend
docker-compose -f docker-compose.pomerium.yml logs -f frontend
```

### Health Checks
```powershell
# Backend health
Invoke-WebRequest -Uri "http://localhost:5000/api/health/ping"

# Frontend (local)
Invoke-WebRequest -Uri "http://localhost:5173/"

# Pomerium proxy (may require authentication)
Invoke-WebRequest -Uri "https://locql.adapted-osprey-5307.pomerium.app"
```

## Troubleshooting

### 1. Certificate Issues
Pomerium automatically generates SSL certificates. Initial access may show certificate warnings:
- Wait a few minutes for certificate generation
- Refresh the page
- Check Pomerium logs for certificate errors

### 2. Authentication Loops
If you get stuck in authentication loops:
- Clear browser cookies for the domain
- Check the authentication policy configuration
- Verify your email domain matches the policy

### 3. WebSocket Connection Issues
- Ensure WebSocket route is configured with `allow_websockets: true`
- Check browser console for WebSocket connection errors
- Verify the WebSocket URL in the frontend configuration

### 4. Database Connection
Same as before - ensure MySQL is accessible via `host.docker.internal`

## Migration from ngrok

### 1. Update Frontend Code
Replace ngrok WebSocket service imports with Pomerium version:

```javascript
// Old ngrok import
import websocketService from "../utils/websocketService";

// New Pomerium import
import websocketService from "../utils/websocketService.pomerium";
```

### 2. Update Environment Variables
Replace ngrok URLs with Pomerium URLs in your configuration.

### 3. Test Functionality
1. Verify authentication works
2. Test WebSocket connections
3. Confirm all API endpoints are accessible
4. Check database operations

## Production Deployment

### 1. Custom Domain Setup
- Register a domain name
- Configure DNS A records
- Update Pomerium configuration

### 2. Identity Provider Integration
- Set up OAuth application with your identity provider
- Configure Pomerium with client ID and secret
- Test authentication flow

### 3. Security Hardening
- Review and tighten access policies
- Enable audit logging
- Configure session timeouts
- Set up monitoring and alerting

## Support and Resources

- **Pomerium Documentation**: https://www.pomerium.com/docs/
- **Pomerium Zero**: https://console.pomerium.app/
- **Community Support**: https://discuss.pomerium.com/

The Pomerium integration provides enterprise-grade security while maintaining all the functionality of your LOCQL application with improved access control and authentication.
