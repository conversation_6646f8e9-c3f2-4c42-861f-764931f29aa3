import fetch from 'node-fetch';

// Test script to check backend data flow
const testBackendData = async () => {
  try {
    console.log('🔍 Testing backend data flow...');
    
    // Test essential data query
    const essentialQuery = `
      query GetStopSidecards($filters: StopFilterInput) {
        getStopSidecards(filters: $filters) {
          Arret_Totale
          Arret_Totale_nondeclare
        }
      }
    `;
    
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: essentialQuery,
        variables: {
          filters: {
            model: 'IPS'
          }
        }
      })
    });
    
    const result = await response.json();
    console.log('📊 Backend Essential Data Response:', result);
    
    // Test full stops data query
    const stopsQuery = `
      query GetAllMachineStops($filters: StopFilterInput) {
        getAllMachineStops(filters: $filters) {
          Machine_Name
          Code_Stop
          Raison_Arret
          duration_minutes
          Debut_Stop
          Fin_Stop_Time
          Date_Insert
        }
      }
    `;
    
    const stopsResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: stopsQuery,
        variables: {
          filters: {
            model: 'IPS'
          }
        }
      })
    });
    
    const stopsResult = await stopsResponse.json();
    console.log('📊 Backend Stops Data Response:', {
      totalStops: stopsResult.data?.getAllMachineStops?.length || 0,
      firstStop: stopsResult.data?.getAllMachineStops?.[0] || null,
      sampleStops: stopsResult.data?.getAllMachineStops?.slice(0, 3) || [],
      errors: stopsResult.errors || null
    });
    
    // Calculate undeclared stops manually
    if (stopsResult.data?.getAllMachineStops) {
      const allStops = stopsResult.data.getAllMachineStops;
      const declaredStops = allStops.filter(stop => {
        const codeStop = stop.Code_Stop || stop.Raison_Arret;
        // A stop is declared if it has a code/reason AND it's not "Arrêt non déclaré"
        return codeStop && 
               codeStop !== 'Arrêt non déclaré' && 
               codeStop !== 'Non déclaré' &&
               codeStop !== 'Undeclared';
      });
      const undeclaredStops = allStops.length - declaredStops.length;
      
      console.log('📊 Manual Calculation:', {
        totalStops: allStops.length,
        declaredStops: declaredStops.length,
        undeclaredStops: undeclaredStops,
        percentageUndeclared: ((undeclaredStops / allStops.length) * 100).toFixed(1)
      });
    }
    
  } catch (error) {
    console.error('❌ Error testing backend:', error);
  }
};

testBackendData();
