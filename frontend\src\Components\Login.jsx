"use client"

import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { Form, Input, Button, Card, Modal, message, Image, Typography, Checkbox } from "antd"
import { MailOutlined, LockOutlined } from "@ant-design/icons"
import logoLight from "../assets/logo.jpg"
import logoDark from "../assets/logo_for_DarkMode.jpg"
import { useTheme } from "../theme-context"
import { useAuth } from "../hooks/useAuth"
import "./login.css"

const { Title, Text } = Typography

const Login = () => {
  const [form] = Form.useForm()
  const [forgotForm] = Form.useForm()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [forgotPasswordVisible, setForgotPasswordVisible] = useState(false)
  const [forgotLoading, setForgotLoading] = useState(false)
  const { darkMode } = useTheme()
  const { login, forgotPassword, isAuthenticated, redirectPath } = useAuth()

  // Check if user is already authenticated and redirect if needed
  useEffect(() => {
    if (isAuthenticated) {
      // Use the redirectPath from the auth context instead of hardcoding "/home"
      navigate(redirectPath, { replace: true })
    }
  }, [isAuthenticated, navigate, redirectPath])

  const onFinish = async (values) => {
    setLoading(true)
    try {
      const result = await login(values)
      if (result.success) {
        message.success("Connexion réussie ! Redirection en cours...")
        // Use the redirectPath returned from the login function
        setTimeout(() => navigate(result.redirectPath || redirectPath), 1500)
      }
    } finally {
      setLoading(false)
    }
  }

  const handleForgotPassword = async () => {
    try {
      setForgotLoading(true)
      const values = await forgotForm.validateFields()

      // Show loading and disable button
      message.loading("Envoi des instructions...", 2)

      const result = await forgotPassword(values.email)

      if (result.success) {
        // Show success message and reset form
        setForgotPasswordVisible(false)
        forgotForm.resetFields()

        // Show countdown timer
        let seconds = 30
        const timer = setInterval(() => {
          message.info(`Vous pouvez demander un nouveau lien dans ${seconds} secondes...`, 1)
          seconds--

          if (seconds < 0) {
            clearInterval(timer)
            message.destroy()
          }
        }, 1000)
      }
    } catch (error) {
      if (error.errorFields) {
        message.error("Veuillez corriger les erreurs dans le formulaire")
        return
      }
      console.error("Forgot password error:", error)
    } finally {
      setForgotLoading(false)
    }
  }

  // Styles adaptés au mode sombre
  const darkModeStyles = {
    loginContainer: {
      background: darkMode
        ? "linear-gradient(135deg, #1f1f1f 0%, #141414 100%)"
        : "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
    },
    card: {
      backgroundColor: darkMode ? "#1f1f1f" : "#ffffff",
      boxShadow: darkMode ? "0 12px 40px rgba(0, 0, 0, 0.5)" : "0 12px 40px rgba(0, 0, 0, 0.15)",
    },
    title: {
      color: darkMode ? "rgba(255, 255, 255, 0.85)" : "#2c3e50",
    },
    input: {
      backgroundColor: darkMode ? "#141414" : "#ffffff",
      borderColor: darkMode ? "#434343" : "#e8e8e8",
      color: darkMode ? "rgba(255, 255, 255, 0.85)" : "rgba(0, 0, 0, 0.85)",
    },
    checkbox: {
      color: darkMode ? "rgba(255, 255, 255, 0.65)" : "#5a6673",
    },
    logoFilter: {
      filter: darkMode
        ? "drop-shadow(0 4px 12px rgba(255, 255, 255, 0.15))"
        : "drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1))",
      transition: "filter 0.3s ease",
    },
  }

  return (
    <div className={`login-container ${darkMode ? "dark" : "light"}`} style={darkModeStyles.loginContainer}>
      <div className="centered-wrapper">
        <Card className="login-card" style={darkModeStyles.card} hoverable>
          <div className="decorative-line" />

          {/* Logo et Ligne de présentation */}
          <div
            className="logo-container"
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              marginBottom: "24px",
              padding: "16px",
            }}
          >
            <div
              style={{
                width: "280px",
                height: "140px",
                overflow: "hidden",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginBottom: "16px",
              }}
            >
              <Image
                src={darkMode ? logoDark : logoLight}
                alt="SOMIPEM Logo"
                preview={false}
                className="logo-hover"
                style={{
                  ...darkModeStyles.logoFilter,
                  width: "100%",
                  objectFit: "cover",
                  objectPosition: "center",
                  transition: "all 0.3s ease",
                }}
              />
            </div>
            <Title
              level={3}
              className="company-tagline"
              style={{
                ...darkModeStyles.title,
                textAlign: "center",
                marginTop: "8px",
              }}
            >
              Perfemance 4.0
            </Title>
          </div>

          {/* Formulaire de connexion */}
          <Form
            form={form}
            name="login"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="email"
              rules={[
                { required: true, message: "Veuillez entrer votre email" },
                { type: "email", message: "Veuillez entrer un email valide" },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Email"
                className="form-input"
                style={darkModeStyles.input}
              />
            </Form.Item>

            <Form.Item name="password" rules={[{ required: true, message: "Veuillez entrer votre mot de passe" }]}>
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Mot de passe"
                className="form-input"
                style={darkModeStyles.input}
              />
            </Form.Item>

            <div className="login-actions">
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox style={darkModeStyles.checkbox}>Se souvenir de moi</Checkbox>
              </Form.Item>

              <Button
                type="link"
                className="forgot-password"
                onClick={() => setForgotPasswordVisible(true)}
                style={{ color: darkMode ? "#1890ff" : "#1890ff" }}
              >
                Mot de passe oublié?
              </Button>
            </div>

            <Form.Item>
              <Button type="primary" htmlType="submit" className="login-button" block loading={loading}>
                Connexion
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>

      {/* Modal de mot de passe oublié */}
      <Modal
        title="Réinitialisation du mot de passe"
        open={forgotPasswordVisible}
        onCancel={() => {
          setForgotPasswordVisible(false)
          forgotForm.resetFields()
        }}
        // Update the Modal footer to disable button during cooldown
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setForgotPasswordVisible(false)
              forgotForm.resetFields()
            }}
          >
            Annuler
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={forgotLoading}
            disabled={forgotLoading}
            onClick={handleForgotPassword}
          >
            {forgotLoading ? "Envoi en cours..." : "Envoyer le lien"}
          </Button>,
        ]}
      >
        <Form form={forgotForm} layout="vertical">
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: "Veuillez entrer votre email" },
              { type: "email", message: "Veuillez entrer un email valide" },
            ]}
          >
            <Input prefix={<MailOutlined />} placeholder="Entrez votre email" />
          </Form.Item>
          <Text type="secondary">Nous vous enverrons un lien pour réinitialiser votre mot de passe.</Text>
        </Form>
      </Modal>
    </div>
  )
}

export default Login
