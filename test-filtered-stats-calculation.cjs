const axios = require('axios');

const testFilteredStatsCalculation = async () => {
  try {
    console.log('🔍 Testing filtered stats calculation (IPS model)...');
    
    const response = await axios.post('http://localhost:5000/api/graphql', {
      query: `
        query($filters: StopFilterInput) {
          getAllMachineStops(filters: $filters) {
            Date_Insert
            Machine_Name
            Part_NO
            Code_Stop
            Debut_Stop
            Fin_Stop_Time
            Regleur_Prenom
            duration_minutes
          }
        }
      `,
      variables: {
        filters: {
          model: "IPS"
        }
      }
    });
    
    if (response.data.data && response.data.data.getAllMachineStops) {
      const stops = response.data.data.getAllMachineStops;
      console.log('📊 Total filtered stops (IPS model):', stops.length);
      
      // Parse date format: DD/MM/YYYY HH:mm
      const parseDateTime = (dateStr) => {
        if (!dateStr) return null;
        try {
          const [datePart, timePart] = dateStr.split(' ');
          const [day, month, year] = datePart.split('/');
          const [hours, minutes] = timePart.split(':');
          return new Date(year, month - 1, day, hours, minutes);
        } catch (error) {
          return null;
        }
      };
      
      // Calculate total duration
      let totalDuration = 0;
      let stopsWithDuration = 0;
      
      stops.forEach(stop => {
        if (stop.Debut_Stop && stop.Fin_Stop_Time) {
          const startTime = parseDateTime(stop.Debut_Stop);
          const endTime = parseDateTime(stop.Fin_Stop_Time);
          
          if (startTime && endTime && !isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
            const durationMs = endTime - startTime;
            const durationMinutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
            totalDuration += durationMinutes;
            if (durationMinutes > 0) stopsWithDuration++;
          }
        }
      });
      
      const avgDuration = stops.length > 0 ? totalDuration / stops.length : 0;
      
      console.log('📊 Duration calculations:', {
        totalDuration: Math.round(totalDuration),
        avgDuration: avgDuration.toFixed(1),
        stopsWithDuration: stopsWithDuration,
        totalStops: stops.length
      });
      
      // Calculate declared vs non-declared
      const declaredStops = stops.filter(stop => 
        stop.Code_Stop && 
        stop.Code_Stop.trim() !== '' && 
        stop.Code_Stop !== 'Arrêt non déclaré'
      ).length;
      
      const nonDeclaredStops = stops.length - declaredStops;
      const percentageNonDeclared = stops.length > 0 ? ((nonDeclaredStops / stops.length) * 100).toFixed(1) : 0;
      
      console.log('📊 Declaration stats:', {
        totalStops: stops.length,
        declaredStops: declaredStops,
        nonDeclaredStops: nonDeclaredStops,
        percentageNonDeclared: percentageNonDeclared + '%'
      });
      
      // Calculate operator interventions
      const operatorCounts = {};
      stops.forEach(stop => {
        const operator = stop.Regleur_Prenom && stop.Regleur_Prenom.trim() !== '' 
          ? stop.Regleur_Prenom 
          : 'Non assigné';
        operatorCounts[operator] = (operatorCounts[operator] || 0) + 1;
      });
      
      const totalInterventions = Object.values(operatorCounts).reduce((sum, count) => sum + count, 0);
      console.log('📊 Interventions:', {
        totalInterventions: totalInterventions,
        operatorCount: Object.keys(operatorCounts).length,
        topOperators: Object.entries(operatorCounts)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 5)
          .map(([operator, count]) => ({ operator, count }))
      });
      
      console.log('\n🎯 Expected Stats Cards for IPS filter:');
      console.log('1. Total Arrêts:', stops.length);
      console.log('2. Arrêts Non Déclarés:', nonDeclaredStops, `(${percentageNonDeclared}%)`);
      console.log('3. Durée Totale:', Math.round(totalDuration), 'min');
      console.log('4. Durée Moyenne:', avgDuration.toFixed(1), 'min');
      console.log('5. Interventions:', totalInterventions);
      
    } else {
      console.log('❌ No stops data received');
    }
  } catch (error) {
    console.error('❌ Error testing stats calculation:', error.message);
  }
};

testFilteredStatsCalculation().catch(console.error);
