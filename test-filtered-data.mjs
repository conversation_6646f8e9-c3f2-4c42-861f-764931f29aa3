/**
 * Test specific filter scenario: IPS01 + April 2025
 */

import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:5000/api/graphql';

const testQuery = `
  query GetAllMachineStops($filter: StopFilterInput) {
    getAllMachineStops(filter: $filter) {
      ID_Stop
      Date_Insert
      Machine_Name
      Raison_Stop
      Status_Stop
      duration_minutes
      ID_Duree
      Debut_Stop
      Fin_Stop_Time
    }
  }
`;

async function testFilteredData() {
  try {
    console.log('🔍 Testing filtered query: IPS01 + April 2025...');
    
    const variables = {
      filter: {
        selectedMachine: "IPS01",
        selectedDate: "2025-04-01T00:00:00.000Z",
        dateRangeType: "month"
      }
    };
    
    console.log('📊 Variables:', JSON.stringify(variables, null, 2));
    
    const response = await fetch(BACKEND_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: testQuery,
        variables: variables
      })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ Errors:', result.errors);
      return;
    }

    const stops = result.data?.getAllMachineStops || [];
    console.log(`\n✅ Filtered query returned ${stops.length} stops`);
    
    if (stops.length > 0) {
      console.log('\n📅 Sample data:');
      stops.slice(0, 5).forEach((stop, index) => {
        console.log(`  ${index + 1}. Date: ${stop.Date_Insert}, Machine: ${stop.Machine_Name}, Duration: ${stop.duration_minutes}min`);
      });
      
      // Test date parsing like frontend would do
      const grouped = {};
      stops.forEach(stop => {
        const dateField = stop.Date_Insert;
        let dateKey = 'unknown';
        
        if (dateField && dateField.includes('/')) {
          const [day, month, year] = dateField.split(' ')[0].split('/');
          dateKey = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
        
        if (!grouped[dateKey]) {
          grouped[dateKey] = [];
        }
        grouped[dateKey].push(stop);
      });
      
      console.log('\n📊 Grouped by date:');
      Object.entries(grouped).forEach(([date, dailyStops]) => {
        console.log(`  ${date}: ${dailyStops.length} stops`);
      });
      
      console.log(`\n📈 Chart would show ${Object.keys(grouped).length} data points`);
      
    } else {
      console.log('⚠️ No filtered data returned - this explains the "Aucune donnée disponible" message');
      
      // Test without date filter to see if machine filter works
      console.log('\n🔍 Testing just machine filter...');
      
      const machineOnlyVariables = {
        filter: {
          selectedMachine: "IPS01"
        }
      };
      
      const machineResponse = await fetch(BACKEND_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: testQuery,
          variables: machineOnlyVariables
        })
      });

      const machineResult = await machineResponse.json();
      const machineStops = machineResult.data?.getAllMachineStops || [];
      
      console.log(`📊 Machine only filter returned ${machineStops.length} stops`);
      
      if (machineStops.length > 0) {
        console.log('📅 Date range in data:');
        const dates = machineStops.map(s => s.Date_Insert).slice(0, 10);
        dates.forEach(date => console.log(`  ${date}`));
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testFilteredData();
