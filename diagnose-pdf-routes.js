/**
 * Diagnostic script to check PDF routes using Puppeteer
 * This will help us see what's actually happening in the browser
 */

import puppeteer from 'puppeteer';

const FRONTEND_URL = 'http://localhost:5173';

async function diagnosePDFRoutes() {
  console.log('🔍 Diagnosing PDF Routes with Puppeteer...');
  
  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false, // Show browser for debugging
      devtools: true,  // Open dev tools
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      console.log(`🖥️ Browser Console [${msg.type()}]:`, msg.text());
    });
    
    // Enable error logging
    page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
    });

    // Test 1: Simple PDF Test Route
    console.log('\n1️⃣ Testing Simple PDF Route...');
    try {
      await page.goto(`${FRONTEND_URL}/reports/pdf-test-simple`, {
        waitUntil: 'networkidle0',
        timeout: 15000
      });
      
      await page.waitForTimeout(2000); // Wait for React to render
      
      const content = await page.evaluate(() => {
        return {
          title: document.title,
          hasContent: document.body.innerText.includes('SOMIPEM'),
          hasReport: document.body.innerText.includes('Rapport'),
          bodyText: document.body.innerText.substring(0, 200),
          hasDataReady: document.body.hasAttribute('data-pdf-ready'),
          dataReadyValue: document.body.getAttribute('data-pdf-ready')
        };
      });
      
      console.log('📊 Simple Route Results:', content);
      
    } catch (error) {
      console.error('❌ Simple route failed:', error.message);
    }

    // Test 2: Complex PDF Test Route
    console.log('\n2️⃣ Testing Complex PDF Route...');
    try {
      await page.goto(`${FRONTEND_URL}/reports/pdf-test`, {
        waitUntil: 'networkidle0',
        timeout: 15000
      });
      
      await page.waitForTimeout(3000); // Wait for charts to load
      
      const content = await page.evaluate(() => {
        return {
          hasContent: document.body.innerText.includes('SOMIPEM'),
          hasReport: document.body.innerText.includes('Rapport'),
          hasCharts: document.querySelectorAll('canvas').length > 0,
          bodyText: document.body.innerText.substring(0, 200),
          hasDataReady: document.body.hasAttribute('data-pdf-ready'),
          dataReadyValue: document.body.getAttribute('data-pdf-ready'),
          errors: Array.from(document.querySelectorAll('[class*="error"]')).map(el => el.textContent)
        };
      });
      
      console.log('📊 Complex Route Results:', content);
      
    } catch (error) {
      console.error('❌ Complex route failed:', error.message);
    }

    // Test 3: PDF Preview Route
    console.log('\n3️⃣ Testing PDF Preview Route...');
    try {
      const testData = {
        machine: { name: 'Test Machine' },
        shift: 'Matin',
        date: '2025-01-16',
        performance: { oee: 85.5 },
        production: { totalProduction: 850 },
        sessions: []
      };
      
      const encodedData = Buffer.from(JSON.stringify(testData)).toString('base64');
      const previewUrl = `${FRONTEND_URL}/reports/pdf-preview?data=${encodedData}`;
      
      await page.goto(previewUrl, {
        waitUntil: 'networkidle0',
        timeout: 15000
      });
      
      await page.waitForTimeout(3000);
      
      const content = await page.evaluate(() => {
        return {
          hasContent: document.body.innerText.includes('SOMIPEM'),
          hasReport: document.body.innerText.includes('Rapport'),
          hasLoading: document.body.innerText.includes('Chargement'),
          hasError: document.body.innerText.includes('Erreur'),
          bodyText: document.body.innerText.substring(0, 300),
          hasDataReady: document.body.hasAttribute('data-pdf-ready'),
          dataReadyValue: document.body.getAttribute('data-pdf-ready')
        };
      });
      
      console.log('📊 Preview Route Results:', content);
      
    } catch (error) {
      console.error('❌ Preview route failed:', error.message);
    }

    console.log('\n✅ Diagnosis complete. Check the browser window for visual inspection.');
    console.log('Press Ctrl+C to close the browser and exit.');
    
    // Keep browser open for manual inspection
    await new Promise(resolve => {
      process.on('SIGINT', () => {
        console.log('\n🔄 Closing browser...');
        resolve();
      });
    });

  } catch (error) {
    console.error('❌ Diagnosis failed:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run diagnosis
diagnosePDFRoutes().catch(error => {
  console.error('❌ Diagnostic script failed:', error);
  process.exit(1);
});
