# Enhanced PDF Integration Test - PowerShell Version

Write-Host "🧪 Enhanced PDF Integration Test" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan
Write-Host ""

# Test 1: Verify servers are running
Write-Host "🔍 Step 1: Checking server status..." -ForegroundColor Yellow

try {
    $backendResponse = Invoke-WebRequest -Uri "http://localhost:5000" -Method GET -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($backendResponse.StatusCode -eq 200 -or $backendResponse.StatusCode -eq 404) {
        Write-Host "✅ Backend server is running on port 5000" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Backend server not accessible" -ForegroundColor Red
}

try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:5173" -Method GET -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend server is running on port 5173" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Frontend server not accessible" -ForegroundColor Red
}

Write-Host ""

# Test 2: Check component files exist
Write-Host "🔍 Step 2: Checking enhanced components..." -ForegroundColor Yellow

$backendDir = "c:\Users\<USER>\Desktop\TEST\locql project\backend"

if (Test-Path "$backendDir\utils\pdfTemplate.js") {
    Write-Host "✅ PDFTemplate component exists" -ForegroundColor Green
} else {
    Write-Host "❌ PDFTemplate component missing" -ForegroundColor Red
}

if (Test-Path "$backendDir\services\reportDataService.js") {
    Write-Host "✅ ReportDataService component exists" -ForegroundColor Green
} else {
    Write-Host "❌ ReportDataService component missing" -ForegroundColor Red
}

if (Test-Path "$backendDir\utils\pdfGenerator.js") {
    Write-Host "✅ PDFGenerator component exists" -ForegroundColor Green
} else {
    Write-Host "❌ PDFGenerator component missing" -ForegroundColor Red
}

Write-Host ""

# Test 3: Check frontend integration
Write-Host "🔍 Step 3: Checking frontend integration..." -ForegroundColor Yellow

$frontendFile = "c:\Users\<USER>\Desktop\TEST\locql project\frontend\src\Pages\reports.jsx"

if (Test-Path $frontendFile) {
    $content = Get-Content $frontendFile -Raw
    
    if ($content -match "useEnhancedReports") {
        Write-Host "✅ Enhanced reports state implemented" -ForegroundColor Green
    } else {
        Write-Host "❌ Enhanced reports state missing" -ForegroundColor Red
    }
    
    if ($content -match "generate-enhanced") {
        Write-Host "✅ Enhanced API endpoint integrated" -ForegroundColor Green
    } else {
        Write-Host "❌ Enhanced API endpoint missing" -ForegroundColor Red
    }
    
    if ($content -match "Button.Group") {
        Write-Host "✅ Enhanced toggle UI implemented" -ForegroundColor Green
    } else {
        Write-Host "❌ Enhanced toggle UI missing" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Reports.jsx file not found" -ForegroundColor Red
}

Write-Host ""

# Test 4: Check dependencies
Write-Host "🔍 Step 4: Checking dependencies..." -ForegroundColor Yellow

$packageJsonPath = "$backendDir\package.json"
if (Test-Path $packageJsonPath) {
    $packageContent = Get-Content $packageJsonPath -Raw | ConvertFrom-Json
    if ($packageContent.dependencies.pdfkit) {
        Write-Host "✅ PDFKit dependency installed" -ForegroundColor Green
    } else {
        Write-Host "❌ PDFKit dependency missing" -ForegroundColor Red
    }
}

Write-Host ""

# Manual testing instructions
Write-Host "📋 Step 5: Ready for manual testing!" -ForegroundColor Yellow
Write-Host ""
Write-Host "🌐 To complete testing:" -ForegroundColor Cyan
Write-Host "1. Open: http://localhost:5173" -ForegroundColor White
Write-Host "2. Log in to the application" -ForegroundColor White
Write-Host "3. Navigate to Reports page" -ForegroundColor White
Write-Host "4. Look for Standard/Enhanced toggle for shift reports" -ForegroundColor White
Write-Host "5. Test both report generation modes" -ForegroundColor White
Write-Host ""
Write-Host "📊 Expected results:" -ForegroundColor Cyan
Write-Host "• Enhanced reports should be larger files" -ForegroundColor White
Write-Host "• Enhanced reports should have SOMIPEM branding" -ForegroundColor White
Write-Host "• Enhanced reports should include French formatting (1.234,56)" -ForegroundColor White
Write-Host "• Enhanced reports should show performance recommendations" -ForegroundColor White
Write-Host "• Enhanced reports should have color-coded OEE indicators" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Test complete! Proceed with manual UI testing." -ForegroundColor Green
