# Reports Page - Documentation

## Overview
The Reports Page is a comprehensive reporting system for the SOMIPEM production dashboard that allows users to generate, view, export, and manage various types of reports.

## Features

### 🔧 Production-Ready Features
- **Environment-aware API**: Automatically switches between mock data (development) and real API (production)
- **Real-time updates**: Auto-refresh for pending reports
- **French localization**: All numbers formatted according to French standards
- **Responsive design**: Works seamlessly on desktop, tablet, and mobile
- **Error handling**: Comprehensive error management with user-friendly notifications
- **Performance optimized**: Lazy loading and efficient data handling

### 📊 Report Types
1. **Production Reports** - Daily production metrics and performance
2. **Arrêts & Pannes** - Downtime analysis and failure tracking
3. **Équipes (Shifts)** - Team-based performance reports
4. **Machines** - Individual machine performance analysis
5. **Qualité (Quality)** - Quality control and reject analysis
6. **Maintenance** - Preventive and corrective maintenance tracking

### 💾 Export Formats
- **PDF**: Formatted documents with SOMIPEM branding
- **Excel**: Spreadsheet data for further analysis
- **CSV**: Raw data for import into other systems

### 🎛️ Advanced Filtering
- Date range selection
- Report type filtering
- Shift-specific filtering
- Machine selection (multi-select)
- Real-time search across all reports

## Technical Implementation

### Environment Configuration
```javascript
// Development Mode
- Uses mock data from mockReportsApi.js
- Simulates API delays and responses
- No backend dependency

// Production Mode
- Connects to real API endpoints
- Environment variables for configuration
- Token-based authentication
```

### API Endpoints (Production)
```
GET  /api/reports              - List reports with filtering
POST /api/reports/generate     - Generate new report
GET  /api/reports/:id/export   - Export report in specified format
DELETE /api/reports/:id        - Delete report
GET  /api/machines             - List available machines
```

### Key Components

#### 1. ReportsPage (Main Component)
- State management for filters and data
- Real-time auto-refresh for pending reports
- Comprehensive error handling
- Progress tracking for report generation

#### 2. ReportDetail (Modal Component)
- Dynamic content based on report type
- French-formatted statistics
- Export and print functionality

#### 3. Enhanced Table
- Sortable columns
- Advanced filtering
- Responsive design
- Action buttons (View, Export, Print)

#### 4. Print Functionality
- Professional formatting with SOMIPEM branding
- Print-optimized CSS
- Auto-print capability

### French Number Formatting
All numeric values use the French formatting utility:
- **Integers**: `12.345` (dots for thousands)
- **Decimals**: `12,50` (comma for decimal separator)
- **Percentages**: `85,5 %` (French percentage format)

## Installation & Setup

### Dependencies
```bash
npm install file-saver xlsx dayjs antd @ant-design/icons
```

### Environment Variables
```env
# Production
REACT_APP_API_URL=https://your-production-api.com
NODE_ENV=production

# Development (uses mock data automatically)
NODE_ENV=development
```

## Usage Guide

### For Developers

#### Adding New Report Types
1. Add new type to `reportTypes` array:
```javascript
{
  key: "new_type",
  label: "New Report Type",
  icon: <SomeIcon />,
  description: "Description of the new report",
  endpoint: "/reports/new_type",
  color: SOMIPEM_COLORS.PRIMARY_BLUE,
  priority: 7
}
```

2. Update `generateReportHTML` function for print formatting
3. Add specific filtering logic if needed

#### Customizing Export Formats
Add new format to `exportFormats` array and implement in backend API.

#### Adding Mock Data
Update `mockReportsApi.js` to include new report structures for development testing.

### For End Users

#### Generating Reports
1. Select report type from sidebar
2. Configure filters (date range, machines, shifts)
3. Click "Nouveau Rapport" button
4. Monitor progress in generation modal

#### Viewing Reports
- Click eye icon to view detailed report
- Status indicators show report completion
- Real-time updates for pending reports

#### Exporting Reports
- Choose from PDF, Excel, or CSV formats
- Click export dropdown on completed reports
- Files automatically download with French naming

#### Printing Reports
- Click print icon for completed reports
- Opens formatted print preview
- Includes SOMIPEM branding and proper formatting

## Best Practices

### Performance
- Use pagination for large datasets
- Implement lazy loading for heavy components
- Optimize re-renders with useCallback and useMemo

### Error Handling
- Always provide user-friendly error messages
- Implement retry mechanisms for failed requests
- Log errors for debugging while hiding technical details

### Accessibility
- Proper ARIA labels for screen readers
- Keyboard navigation support
- High contrast colors for readability

### Internationalization
- French date and number formatting
- Consistent terminology across components
- Cultural considerations for business hours and date formats

## Production Deployment

### Backend Requirements
1. **API Endpoints**: Implement all required endpoints
2. **Authentication**: Token-based auth system
3. **File Generation**: PDF/Excel generation capabilities
4. **Database**: Report storage and metadata tracking

### Frontend Build
```bash
npm run build
# Ensure environment variables are set correctly
# Deploy to production server
```

### Monitoring
- Track report generation performance
- Monitor export success rates
- Log user interaction patterns
- Alert on API failures

## Troubleshooting

### Common Issues

#### Reports Not Loading
- Check API connectivity
- Verify authentication tokens
- Ensure backend endpoints are responding

#### Export Failures
- Verify file generation services
- Check browser download settings
- Ensure proper MIME type handling

#### Print Issues
- Test in different browsers
- Verify CSS print styles
- Check popup blocker settings

### Debug Mode
Enable console logging by setting:
```javascript
localStorage.setItem('debug', 'true')
```

## Future Enhancements

### Planned Features
- **Report Scheduling**: Automatic report generation
- **Email Integration**: Send reports via email
- **Advanced Charts**: Interactive visualizations in reports
- **Template System**: Customizable report templates
- **Audit Trail**: Track report access and modifications

### Technical Improvements
- **Caching Strategy**: Implement intelligent caching
- **Offline Support**: Progressive Web App capabilities
- **Real-time Collaboration**: Multiple users working on reports
- **Advanced Analytics**: Usage patterns and optimization insights

## Support

For technical support or feature requests:
- Check console errors first
- Review network requests in browser dev tools
- Verify API responses and status codes
- Contact development team with detailed error descriptions

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Compatible with**: React 18+, Ant Design 5+  
**Browser Support**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
