// Test backend GraphQL endpoint
console.log('🧪 Testing backend GraphQL endpoint...');

const testQuery = `
  query {
    getStopSidecards {
      Arret_Totale
      Arret_Totale_nondeclare
    }
  }
`;

fetch('http://localhost:5000/api/graphql', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({ query: testQuery })
})
.then(response => response.json())
.then(result => {
  console.log('📊 GraphQL Response:', JSON.stringify(result, null, 2));
  if (result.errors) {
    console.error('❌ GraphQL Errors:', result.errors);
  } else {
    console.log('✅ GraphQL working correctly');
  }
})
.catch(error => {
  console.error('❌ Error testing GraphQL:', error.message);
});
