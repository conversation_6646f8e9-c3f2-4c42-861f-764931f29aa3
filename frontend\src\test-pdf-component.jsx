import React from 'react';
import PDFReportTemplate from './components/reports/PDFReportTemplate';

// Mock data for testing
const mockReportData = {
  machine: { id: 'M001', name: 'Machine Test' },
  shift: 'Matin',
  date: '2025-07-16',
  performance: {
    oee: 75.5,
    availability: 85.2,
    performanceRate: 92.1,
    qualityRate: 96.3,
    runTime: 8,
    downTime: 0.5,
    cycleTime: 45.2
  },
  sessions: [
    {
      session_start: '2025-07-16T06:00:00Z',
      session_end: '2025-07-16T14:00:00Z',
      Article: 'ART001',
      Quantite_Bon: 150,
      Quantite_Rejet: 5
    }
  ],
  production: {
    goodParts: 150,
    rejects: 5,
    totalProduction: 155
  },
  period: {
    start: '2025-07-16T06:00:00Z',
    end: '2025-07-16T14:00:00Z'
  }
};

const TestPDFComponent = () => {
  return (
    <div>
      <h1>PDF Component Test</h1>
      <PDFReportTemplate reportData={mockReportData} />
    </div>
  );
};

export default TestPDFComponent;
