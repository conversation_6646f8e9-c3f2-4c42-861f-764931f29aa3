const axios = require('axios');

const testBackendData = async () => {
  try {
    console.log('🔍 Testing backend GraphQL data...');
    
    const response = await axios.post('http://localhost:5000/api/graphql', {
      query: `
        query {
          getStopSidecards {
            Arret_Totale
            Arret_Totale_nondeclare
          }
        }
      `
    });
    
    console.log('✅ Backend GraphQL response:', response.data);
    
    if (response.data.data && response.data.data.getStopSidecards) {
      const data = response.data.data.getStopSidecards;
      console.log('📊 Stats from backend:', {
        totalStops: data.Arret_Totale,
        nonDeclaredStops: data.Arret_Totale_nondeclare,
        percentage: data.Arret_Totale > 0 ? ((data.Arret_Totale_nondeclare / data.Arret_Totale) * 100).toFixed(1) : 0
      });
    }
  } catch (error) {
    console.error('❌ Error testing backend:', error.message);
  }
};

testBackendData().catch(console.error);
