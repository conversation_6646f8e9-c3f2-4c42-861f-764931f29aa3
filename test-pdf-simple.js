/**
 * Simple PDF System Test - Focus on Core Issues
 */

import fetch from 'node-fetch';

const FRONTEND_URL = 'http://localhost:5173';
const BACKEND_URL = 'http://localhost:5000';

// Test data for PDF preview
const testReportData = {
  machine: { id: 'TEST01', name: 'Machine Test 01' },
  shift: 'Matin',
  date: '2025-01-16',
  performance: { oee: 85.5, availability: 92.3, performanceRate: 88.7, qualityRate: 95.2 },
  production: { totalProduction: 850, goodParts: 810, rejects: 40 },
  sessions: [
    {
      session_start: '2025-01-16T06:00:00Z',
      session_end: '2025-01-16T07:00:00Z',
      Quantite_Bon: 105,
      Quantite_Rejet: 5,
      cycle: 32.5,
      TRS: 87.2,
      Article: 'TEST-PART-001'
    }
  ]
};

async function testServices() {
  console.log('🧪 Simple PDF System Test');
  console.log('==========================\n');

  // Test 1: Backend Health
  console.log('1️⃣ Testing Backend Health...');
  try {
    const healthResponse = await fetch(`${BACKEND_URL}/api/health/ping`, { timeout: 5000 });
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log(`✅ Backend is healthy (uptime: ${Math.floor(healthData.uptime)}s)`);
    } else {
      console.log(`❌ Backend health check failed: ${healthResponse.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Backend not accessible: ${error.message}`);
    return false;
  }

  // Test 2: Frontend Accessibility
  console.log('\n2️⃣ Testing Frontend...');
  try {
    const frontendResponse = await fetch(FRONTEND_URL, { timeout: 5000 });
    if (frontendResponse.ok) {
      console.log('✅ Frontend is accessible');
    } else {
      console.log(`❌ Frontend returned: ${frontendResponse.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Frontend not accessible: ${error.message}`);
    return false;
  }

  // Test 3: PDF Test Route
  console.log('\n3️⃣ Testing PDF Test Route...');
  try {
    const testRouteResponse = await fetch(`${FRONTEND_URL}/reports/pdf-test`, { timeout: 10000 });
    if (testRouteResponse.ok) {
      const html = await testRouteResponse.text();
      if (html.includes('PDF Template Test') || html.includes('SOMIPEM')) {
        console.log('✅ PDF test route is working');
      } else {
        console.log('⚠️ PDF test route accessible but content may be incomplete');
      }
    } else {
      console.log(`❌ PDF test route failed: ${testRouteResponse.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ PDF test route error: ${error.message}`);
    return false;
  }

  // Test 4: PDF Preview Route with Data
  console.log('\n4️⃣ Testing PDF Preview Route...');
  try {
    const encodedData = Buffer.from(JSON.stringify(testReportData)).toString('base64');
    const previewUrl = `${FRONTEND_URL}/reports/pdf-preview?data=${encodedData}`;
    
    console.log(`🔗 Testing URL: ${previewUrl.substring(0, 80)}...`);
    
    const previewResponse = await fetch(previewUrl, { timeout: 15000 });
    if (previewResponse.ok) {
      const html = await previewResponse.text();
      
      // Check for key elements
      const hasReport = html.includes('Rapport de Quart');
      const hasSomipem = html.includes('SOMIPEM');
      const hasCharts = html.includes('chart') || html.includes('Chart');
      
      console.log(`📊 Report content: ${hasReport ? '✅' : '❌'}`);
      console.log(`🏢 SOMIPEM branding: ${hasSomipem ? '✅' : '❌'}`);
      console.log(`📈 Charts: ${hasCharts ? '✅' : '❌'}`);
      
      if (hasReport && hasSomipem) {
        console.log('✅ PDF preview route is working correctly');
      } else {
        console.log('⚠️ PDF preview route has content issues');
      }
    } else {
      console.log(`❌ PDF preview route failed: ${previewResponse.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ PDF preview route error: ${error.message}`);
    return false;
  }

  console.log('\n🎉 All core tests passed!');
  console.log('\n📋 Next Steps:');
  console.log('1. Visit http://localhost:5173/reports/pdf-test to see the PDF template');
  console.log('2. Check browser console for "PDF template ready" message');
  console.log('3. Use Ctrl+P to see print preview');
  console.log('4. Test the backend PDF generation from the reports page');
  
  return true;
}

// Run the test
testServices().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
