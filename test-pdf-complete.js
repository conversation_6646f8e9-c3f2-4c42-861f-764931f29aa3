/**
 * Complete PDF Generation System Test
 * This script tests the entire PDF generation pipeline
 */

import { spawn } from 'child_process';
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';

const FRONTEND_URL = 'http://localhost:5173';
const BACKEND_URL = 'http://localhost:5000';

// Test data
const testReportData = {
  machine: { id: 'TEST01', name: 'Machine Test 01' },
  shift: 'Matin',
  date: '2025-01-16',
  performance: { oee: 85.5, availability: 92.3, performanceRate: 88.7, qualityRate: 95.2 },
  production: { totalProduction: 850, goodParts: 810, rejects: 40 },
  sessions: [
    {
      session_start: '2025-01-16T06:00:00Z',
      session_end: '2025-01-16T07:00:00Z',
      Quantite_Bon: 105,
      Quantite_Rejet: 5,
      cycle: 32.5,
      TRS: 87.2,
      Article: 'TEST-PART-001'
    }
  ]
};

async function checkService(url, name) {
  try {
    // For backend, check the health ping endpoint
    const checkUrl = name === 'Backend' ? `${url}/api/health/ping` : url;
    const response = await fetch(checkUrl, { timeout: 5000 });

    if (response.ok) {
      if (name === 'Backend') {
        const healthData = await response.json();
        console.log(`✅ ${name} is running (status: ${healthData.status}, uptime: ${Math.floor(healthData.uptime)}s)`);
      } else {
        console.log(`✅ ${name} is running`);
      }
      return true;
    } else {
      console.log(`❌ ${name} returned status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${name} is not accessible: ${error.message}`);
    return false;
  }
}

async function testPDFRoute() {
  console.log('\n🧪 Testing PDF Routes...');
  
  // Test the PDF test route
  const testUrl = `${FRONTEND_URL}/reports/pdf-test`;
  console.log(`🔍 Testing: ${testUrl}`);
  
  try {
    const response = await fetch(testUrl);
    if (response.ok) {
      console.log('✅ PDF test route is accessible');
      return true;
    } else {
      console.log(`❌ PDF test route returned: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ PDF test route failed: ${error.message}`);
    return false;
  }
}

async function testPDFPreviewRoute() {
  console.log('\n🧪 Testing PDF Preview Route...');
  
  // Encode test data
  const encodedData = Buffer.from(JSON.stringify(testReportData)).toString('base64');
  const previewUrl = `${FRONTEND_URL}/reports/pdf-preview?data=${encodedData}`;
  
  console.log(`🔍 Testing: ${previewUrl.substring(0, 80)}...`);
  
  try {
    const response = await fetch(previewUrl);
    if (response.ok) {
      const html = await response.text();
      if (html.includes('Rapport de Quart') || html.includes('SOMIPEM')) {
        console.log('✅ PDF preview route is working');
        return true;
      } else {
        console.log('❌ PDF preview route returned unexpected content');
        return false;
      }
    } else {
      console.log(`❌ PDF preview route returned: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ PDF preview route failed: ${error.message}`);
    return false;
  }
}

async function testBackendPDFGeneration() {
  console.log('\n🧪 Testing Backend PDF Generation...');
  
  const testPayload = {
    machineId: 'TEST01',
    date: '2025-01-16',
    shift: 'Matin'
  };
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/shift-reports/generate-enhanced`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add any required auth headers here
      },
      body: JSON.stringify(testPayload)
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Backend PDF generation endpoint is working');
      console.log('📊 Response:', result);
      return true;
    } else {
      console.log(`❌ Backend PDF generation failed: ${response.status}`);
      const errorText = await response.text();
      console.log('Error details:', errorText);
      return false;
    }
  } catch (error) {
    console.log(`❌ Backend PDF generation failed: ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Complete PDF System Test');
  console.log('=====================================\n');
  
  // Check if services are running
  console.log('🔍 Checking Services...');
  const frontendRunning = await checkService(FRONTEND_URL, 'Frontend');
  const backendRunning = await checkService(BACKEND_URL, 'Backend');
  
  if (!frontendRunning) {
    console.log('\n💡 To start frontend: cd frontend && npm run dev');
  }
  
  if (!backendRunning) {
    console.log('\n💡 To start backend: cd backend && npm run dev');
  }
  
  if (!frontendRunning || !backendRunning) {
    console.log('\n❌ Please start the required services and run the test again');
    process.exit(1);
  }
  
  // Test PDF routes
  const testRouteResult = await testPDFRoute();
  const previewRouteResult = await testPDFPreviewRoute();
  
  // Test backend PDF generation (optional - requires auth)
  // const backendResult = await testBackendPDFGeneration();
  
  console.log('\n📋 Test Results Summary:');
  console.log('========================');
  console.log(`Frontend Service: ${frontendRunning ? '✅' : '❌'}`);
  console.log(`Backend Service: ${backendRunning ? '✅' : '❌'}`);
  console.log(`PDF Test Route: ${testRouteResult ? '✅' : '❌'}`);
  console.log(`PDF Preview Route: ${previewRouteResult ? '✅' : '❌'}`);
  
  if (frontendRunning && backendRunning && testRouteResult && previewRouteResult) {
    console.log('\n🎉 All tests passed! PDF system is ready.');
    console.log('\n📖 Next steps:');
    console.log('1. Visit http://localhost:5173/reports/pdf-test to see the PDF template');
    console.log('2. Use browser print preview to see how the PDF will look');
    console.log('3. Test the full PDF generation from the reports page');
  } else {
    console.log('\n❌ Some tests failed. Please check the issues above.');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
