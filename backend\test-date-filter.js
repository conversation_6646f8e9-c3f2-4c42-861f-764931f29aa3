const query = `{
  getAllMachineStops(filters: {model: "IPS", startDate: "2025-05-02", endDate: "2025-05-03"}) {
    Machine_Name
    Date_Insert
    Code_Stop
  }
}`;

const body = JSON.stringify({ query });

console.log('Testing GraphQL date filter...');
fetch('http://localhost:5000/api/graphql', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body
})
.then(r => r.json())
.then(result => {
  console.log('Date filter result:', JSON.stringify(result, null, 2));
})
.catch(error => {
  console.error('Error:', error);
});
