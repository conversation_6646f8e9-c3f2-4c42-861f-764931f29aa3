/**
 * Test Script: ArretLineChart Data Flow
 * 
 * This script replicates the exact backend query and filtering logic
 * used by the dashboard to feed data to ArretLineChart.jsx
 * 
 * Data Flow:
 * 1. Backend query: getAllMachineStops (via graphQLInterface.getTableData)
 * 2. Filter by machine, model, date range (in queuedDataManager)
 * 3. Process filteredStopsData into daily aggregates
 * 4. Pass as chartData to ArretLineChart
 */

import { GraphQLClient } from 'graphql-request';

async function testEvolutionChartDataFlow() {
  console.log('🚀 Testing ArretLineChart Data Flow...\n');

  const client = new GraphQLClient('http://localhost:5000/api/graphql');

  // Test with same query used by the dashboard
  const query = `
    query GetAllMachineStops($filters: StopFilterInput) {
      getAllMachineStops(filters: $filters) {
        Date_Insert
        Machine_Name
        Part_NO
        Code_Stop
        Debut_Stop
        Fin_Stop_Time
        Regleur_Prenom
        duration_minutes
      }
    }
  `;

  // Test different filter scenarios using the actual dashboard filter format
  const testScenarios = [
    {
      name: "No Filters (Default)",
      filters: {
        model: "IPS",
        machine: null,
        date: null,
        startDate: null,
        endDate: null,
        dateRangeType: "month"
      }
    },
    {
      name: "Machine Filter Only",
      filters: {
        model: null,
        machine: "IPS01",
        date: null,
        startDate: null,
        endDate: null,
        dateRangeType: "month"
      }
    },
    {
      name: "Machine + Model Filter",
      filters: {
        model: "IPS",
        machine: "IPS01",
        date: null,
        startDate: null,
        endDate: null,
        dateRangeType: "month"
      }
    },
    {
      name: "Date Range Filter",
      filters: {
        model: null,
        machine: null,
        date: "2024-12-01",
        startDate: "2024-12-01",
        endDate: "2024-12-31",
        dateRangeType: "month"
      }
    },
    {
      name: "Full Filters (Machine + Model + Date)",
      filters: {
        model: "IPS",
        machine: "IPS01",
        date: "2024-12-01",
        startDate: "2024-12-01",
        endDate: "2024-12-31",
        dateRangeType: "month"
      }
    }
  ];

  for (const scenario of testScenarios) {
    console.log(`\n📊 === ${scenario.name} ===`);
    console.log('Filters:', JSON.stringify(scenario.filters, null, 2));

    try {
      const result = await client.request(query, { filters: scenario.filters });
      const stopsData = result.getAllMachineStops || [];

      console.log(`\n📈 Backend Response:`);
      console.log(`- Total stops found: ${stopsData.length}`);
      
      if (stopsData.length > 0) {
        console.log(`- Date range: ${stopsData[0]?.Date_Insert} to ${stopsData[stopsData.length - 1]?.Date_Insert}`);
        console.log(`- Sample stop:`, {
          machine: stopsData[0]?.Machine_Name,
          date: stopsData[0]?.Date_Insert,
          duration: stopsData[0]?.duration_minutes
        });
      }

      // Process data exactly like queuedDataManager.jsx does
      const chartData = processStopsDataToEvolutionChart(stopsData, scenario.filters);
      
      console.log(`\n📊 Evolution Chart Data (chartData):`);
      console.log(`- Chart points: ${chartData.length}`);
      
      if (chartData.length > 0) {
        console.log('- Sample chart points:');
        chartData.slice(0, 3).forEach((point, index) => {
          console.log(`  ${index + 1}. Date: ${point.date} | Display: ${point.displayDate} | Stops: ${point.stops} | Duration: ${point.duration}min`);
        });
        
        console.log('- Full chart data:', JSON.stringify(chartData, null, 2));
      } else {
        console.log('⚠️ No chart data generated!');
        
        // Debug why no chart data was generated
        console.log('\n🔍 Debug Info:');
        console.log('- Raw stops sample:', stopsData.slice(0, 2));
        
        if (stopsData.length > 0) {
          const uniqueDates = [...new Set(stopsData.map(stop => stop.Date_Insert))];
          console.log('- Unique dates in data:', uniqueDates.slice(0, 5));
          
          const uniqueMachines = [...new Set(stopsData.map(stop => stop.Machine_Name))];
          console.log('- Unique machines in data:', uniqueMachines);
        }
      }

    } catch (error) {
      console.error(`❌ Error in ${scenario.name}:`, error.message);
    }
  }
}

/**
 * Process stops data into evolution chart format
 * This replicates the logic from queuedDataManager.jsx
 */
function processStopsDataToEvolutionChart(stopsData, appliedFilters) {
  console.log('\n🔄 Processing stops data to evolution chart format...');
  
  // Step 1: Filter data (simulate client-side filtering like in queuedDataManager)
  let filteredStopsData = stopsData.filter(stop => {
    // Date filter simulation (if startDate and endDate were provided)
    if (appliedFilters.startDate && appliedFilters.endDate) {
      const stopDate = parseStopDate(stop.Date_Insert);
      if (stopDate) {
        const startDate = new Date(appliedFilters.startDate);
        const endDate = new Date(appliedFilters.endDate);
        if (stopDate < startDate || stopDate > endDate) {
          return false;
        }
      }
    }
    
    // Machine filter (additional client-side validation)
    if (appliedFilters.machine && stop.Machine_Name !== appliedFilters.machine) {
      return false;
    }
    
    return true;
  });

  console.log(`🔍 Filtered ${stopsData.length} -> ${filteredStopsData.length} stops`);

  // Step 2: Aggregate by date (exactly like queuedDataManager.jsx)
  const dailyStats = {};
  
  filteredStopsData.forEach(stop => {
    if (stop.Date_Insert) {
      const originalDate = stop.Date_Insert.toString();
      let date;
      
      // Parse date exactly like queuedDataManager.jsx
      if (originalDate.match(/^\d{4} \d{2}:\d{2}:\d{2}-\d{1,2}-\s*\d{1,2}$/)) {
        const match = originalDate.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);
        if (match) {
          const [_, year, month, day] = match;
          date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        } else {
          date = originalDate;
        }
      } else {
        // Try to extract just the date part
        const dateMatch = originalDate.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
        if (dateMatch) {
          const [_, year, month, day] = dateMatch;
          date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        } else {
          date = originalDate;
        }
      }
      
      if (!dailyStats[date]) {
        dailyStats[date] = { date, stops: 0, duration: 0 };
      }
      dailyStats[date].stops++;
      
      if (stop.duration_minutes && stop.duration_minutes > 0) {
        dailyStats[date].duration += parseFloat(stop.duration_minutes);
      }
    }
  });

  console.log('📊 Daily stats generated:', Object.keys(dailyStats).length, 'days');

  // Step 3: Convert to evolution data array
  let evolutionData = Object.values(dailyStats)
    .sort((a, b) => new Date(a.date) - new Date(b.date));

  // Step 4: Limit to last 7 days (like the dashboard does when no specific date filter)
  if (!appliedFilters.startDate || !appliedFilters.endDate) {
    evolutionData = evolutionData.slice(-7);
  }

  // Step 5: Add displayDate (like queuedDataManager.jsx)
  evolutionData = evolutionData.map(item => {
    let displayDate;
    
    try {
      const dateObj = new Date(item.date);
      if (!isNaN(dateObj.getTime())) {
        displayDate = dateObj.toLocaleDateString('fr-FR', { 
          day: '2-digit', 
          month: '2-digit' 
        });
      } else {
        displayDate = item.date.slice(0, 10);
      }
    } catch (error) {
      displayDate = item.date.slice(0, 10);
    }
    
    return {
      ...item,
      displayDate
    };
  });

  return evolutionData;
}

/**
 * Parse stop date handling the custom database format
 */
function parseStopDate(dateString) {
  if (!dateString) return null;
  
  const str = dateString.toString();
  
  // Handle format: "2024 10:35:13-12- 3"
  const match = str.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);
  if (match) {
    const [_, year, month, day] = match;
    return new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
  }
  
  // Handle standard ISO format
  if (str.match(/^\d{4}-\d{2}-\d{2}/)) {
    return new Date(str);
  }
  
  return null;
}

// Run the test
testEvolutionChartDataFlow().catch(console.error);
