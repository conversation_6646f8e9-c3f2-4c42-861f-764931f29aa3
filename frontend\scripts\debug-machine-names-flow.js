// Debug machine names loading in context
console.log('Running machine names debug script');

// Mock the GraphQL API response for machine names
const mockMachineNamesResponse = {
  data: {
    getFinalStopMachineNames: [
      { Machine_Name: "Machine1", machine_id: 1 },
      { Machine_Name: "Machine2", machine_id: 2 },
      { Machine_Name: "Machine3", machine_id: 3 }
    ]
  }
};

// Function to simulate fetching machine names with a model filter
async function fetchMachineNames(modelFilter) {
  console.log(`Fetching machine names with filter: ${modelFilter || 'none'}`);
  
  // Simulate API response
  return mockMachineNamesResponse.data.getFinalStopMachineNames;
}

// Demo filter function
function filterMachinesByModel(machines, model) {
  console.log(`Filtering ${machines.length} machines by model: ${model}`);
  
  // THIS IS WRONG - machines don't have 'modele' property
  const wrongFiltered = machines.filter(machine => machine.modele === model);
  console.log('Wrong filtering result (using "modele"):', wrongFiltered);
  
  // THIS IS CORRECT - we need to add model info to machines when fetching
  const correctFiltered = machines.filter(machine => machine.model === model);
  console.log('Correct filtering (if model property existed):', correctFiltered);
  
  return wrongFiltered;
}

// Test the flow
async function testMachineNamesFlow() {
  // 1. Initial fetch (no filter)
  const allMachines = await fetchMachineNames();
  console.log('All machines:', allMachines);
  
  // 2. Set some state with these machines
  console.log('Setting state with all machines');
  
  // 3. User selects a model filter
  const selectedModel = 'IPS';
  console.log(`User selected model: ${selectedModel}`);
  
  // 4. Wrong approach: Filter existing machines by model
  const wrongFiltered = filterMachinesByModel(allMachines, selectedModel);
  console.log(`Wrong approach result: ${wrongFiltered.length} machines`);
  
  // 5. Correct approach: Fetch machines with model filter
  const correctFiltered = await fetchMachineNames(selectedModel);
  console.log(`Correct approach result: ${correctFiltered.length} machines`);
  
  // 6. Add model info to machines when fetching
  const enhancedMachines = correctFiltered.map(machine => ({
    ...machine,
    model: selectedModel  // Add model info to each machine
  }));
  console.log('Enhanced machines with model info:', enhancedMachines);
}

// Run the test
testMachineNamesFlow();
