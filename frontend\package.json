{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "react-scripts start", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.25.2", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "d3-format": "^3.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "lodash": "^4.17.21", "projet": "file:..", "prop-types": "^15.8.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-router-dom": "^6.30.0", "react-window": "^1.8.11", "recharts": "^2.15.3", "superagent": "^10.2.2", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.27.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.1.4", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-remove-debugger": "^6.9.4", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.1.0", "jsdom": "^26.1.0", "rollup-plugin-visualizer": "^5.14.0", "terser": "^5.39.2", "vite": "^6.3.5", "vitest": "^3.1.3"}}