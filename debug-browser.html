<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Routes Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        iframe { width: 100%; height: 400px; border: 1px solid #ccc; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>PDF Routes Debug Tool</h1>
    
    <div class="test-section">
        <h2>Route Tests</h2>
        <button onclick="testRoute('/reports/pdf-test-simple')">Test Simple Route</button>
        <button onclick="testRoute('/reports/pdf-test')">Test Complex Route</button>
        <button onclick="testRoute('/reports/pdf-preview?data=eyJtYWNoaW5lIjp7Im5hbWUiOiJUZXN0In0sInNoaWZ0IjoiTWF0aW4iLCJkYXRlIjoiMjAyNS0wMS0xNiJ9')">Test Preview Route</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Live Route Preview</h2>
        <select id="route-selector" onchange="loadRoute()">
            <option value="">Select a route...</option>
            <option value="/reports/pdf-test-simple">Simple PDF Test</option>
            <option value="/reports/pdf-test">Complex PDF Test</option>
            <option value="/reports/pdf-preview?data=eyJtYWNoaW5lIjp7Im5hbWUiOiJUZXN0In0sInNoaWZ0IjoiTWF0aW4iLCJkYXRlIjoiMjAyNS0wMS0xNiJ9">PDF Preview</option>
        </select>
        <iframe id="route-frame" src="about:blank"></iframe>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console-output" style="background: #f5f5f5; padding: 10px; height: 200px; overflow-y: scroll; font-family: monospace;"></div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <script>
        const FRONTEND_URL = 'http://localhost:5173';
        const consoleOutput = document.getElementById('console-output');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'success';
            consoleOutput.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        async function testRoute(route) {
            const url = FRONTEND_URL + route;
            log(`Testing route: ${route}`);
            
            try {
                const response = await fetch(url);
                if (response.ok) {
                    const html = await response.text();
                    const hasContent = html.includes('SOMIPEM') || html.includes('Rapport');
                    const isBasicShell = html.length < 1000;
                    
                    log(`✅ Status: ${response.status}, Length: ${html.length} chars`, 'success');
                    
                    if (isBasicShell) {
                        log(`⚠️ Basic HTML shell returned - React may not be rendering`, 'warning');
                    }
                    
                    if (hasContent) {
                        log(`✅ Content found in response`, 'success');
                    } else {
                        log(`❌ Expected content not found`, 'error');
                    }
                } else {
                    log(`❌ HTTP ${response.status}: ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        function loadRoute() {
            const selector = document.getElementById('route-selector');
            const frame = document.getElementById('route-frame');
            const route = selector.value;
            
            if (route) {
                const url = FRONTEND_URL + route;
                log(`Loading route in iframe: ${route}`);
                frame.src = url;
                
                // Listen for iframe load events
                frame.onload = function() {
                    try {
                        const iframeDoc = frame.contentDocument || frame.contentWindow.document;
                        const hasContent = iframeDoc.body.innerText.includes('SOMIPEM') || 
                                         iframeDoc.body.innerText.includes('Rapport');
                        
                        if (hasContent) {
                            log(`✅ Route loaded successfully with content`, 'success');
                        } else {
                            log(`⚠️ Route loaded but content may be missing`, 'warning');
                            log(`Body text preview: ${iframeDoc.body.innerText.substring(0, 100)}...`);
                        }
                        
                        // Check for data-pdf-ready attribute
                        const hasDataReady = iframeDoc.body.hasAttribute('data-pdf-ready');
                        if (hasDataReady) {
                            log(`✅ data-pdf-ready attribute found: ${iframeDoc.body.getAttribute('data-pdf-ready')}`, 'success');
                        } else {
                            log(`❌ data-pdf-ready attribute not found`, 'error');
                        }
                        
                    } catch (error) {
                        log(`❌ Cannot access iframe content (CORS): ${error.message}`, 'error');
                    }
                };
                
                frame.onerror = function() {
                    log(`❌ Failed to load route in iframe`, 'error');
                };
            }
        }

        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        // Initial log
        log('PDF Routes Debug Tool initialized');
        log('Click buttons above to test routes or select from dropdown to preview');
    </script>
</body>
</html>
