const t=(t,e=null,s=!1)=>{if(null==t||""===t)return"0";const r="string"==typeof t?parseFloat(t):t;if(isNaN(r))return"0";let n=e;null===n&&(n=Number.isInteger(r)?0:r<10?2:1);const o=(Math.round(r*Math.pow(10,n))/Math.pow(10,n)).toFixed(n).split(".");let a=o[0];const i=o[1];if(a=a.replace(/\B(?=(\d{3})+(?!\d))/g,"."),n>0&&(s||"00"!==i)){const t=s?i:i.replace(/0+$/,"");if(t.length>0)return`${a},${t}`}return a},e=e=>t(e,0,!1),s=(e,s=1)=>t(e,s,!1),r=(e,s=2)=>t(e,s,!1),n=(e,s="h")=>{const r="string"==typeof e?parseFloat(e):e;return isNaN(r)?"0":t(e,2,!1)},o=(r,o="")=>{const a=o.toLowerCase();return"%"===a?s(r,1):"kg"===a?(e=>{const s="string"==typeof e?parseFloat(e):e;if(isNaN(s)||0===s)return"0";const r=Number.isInteger(s)?0:1;return t(e,r,!1)})(r):"pcs"===a||"pieces"===a?e(r):"h"===a||"hours"===a||"min"===a||"minutes"===a||"sec"===a||"seconds"===a?n(r):t(r)};export{e as a,r as b,o as c,n as d,t as e,s as f};
