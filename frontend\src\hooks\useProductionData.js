import { useState, useCallback, useEffect } from "react";
import request from "superagent";
// import { notification } from "antd"; // DISABLED: Notifications disabled for ProductionDashboard
import dayjs from "dayjs";
import { extractResponseData } from "../utils/apiUtils";
import { normalizePercentage, transformData } from "../utils/dataUtils";

/**
 * Custom hook for fetching and managing production data
 * @param {Object} options - Hook options
 * @param {string} options.selectedMachineModel - Selected machine model
 * @param {string} options.selectedMachine - Selected machine
 * @param {Object} options.dateFilter - Date filter
 * @param {string} options.dateRangeType - Date range type
 * @param {Function} options.buildDateQueryParams - Function to build date query params
 * @returns {Object} Production data and related functions
 */
const useProductionData = ({
  selectedMachineModel,
  selectedMachine,
  dateFilter,
  dateRangeType,
  buildDateQueryParams
}) => {
  // Helper function for authenticated requests
  const createAuthRequest = (method, url) => {
    const baseURL = process.env.NODE_ENV === "production" 
      ? "https://charming-hermit-intense.ngrok-free.app" 
      : "http://localhost:5000";
      
    return request[method](`${baseURL}${url}`)
      .retry(2)
      .withCredentials() // ✅ FIXED: Correct SuperAgent syntax
      .timeout(30000);   // ✅ ADDED: Consistent timeout
  };

  // State for production data
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState([]);
  const [machinePerformance, setMachinePerformance] = useState([]);
  const [mergedData, setMergedData] = useState([]);
  const [uniqueDates, setUniqueDates] = useState([]);
  const [goodQty, setGoodQty] = useState(0);
  const [rejetQty, setRejetQty] = useState(0);
  const [oeeTrends, setOeeTrends] = useState([]);
  const [speedTrends, setSpeedTrends] = useState([]);
  const [hourlyTrends, setHourlyTrends] = useState([]);
  const [shiftComparison, setShiftComparison] = useState([]);

  // Function to build query parameters for API requests
  const buildQueryParams = useCallback(() => {
    const queryParams = new URLSearchParams();

    // Add machine filtering
    if (selectedMachineModel && !selectedMachine) {
      queryParams.append("model", selectedMachineModel);
    } else if (selectedMachine) {
      queryParams.append("machine", selectedMachine);
    }

    // Add performance-optimized pagination parameters
    queryParams.append("limit", "100"); // Limit records for tables
    queryParams.append("chartLimit", "200"); // Limit chart data points for performance
    queryParams.append("page", "1");

    // Add date parameters from buildDateQueryParams
    const dateParams = buildDateQueryParams();
    Object.entries(dateParams).forEach(([key, value]) => {
      queryParams.append(key, value);
    });

    // Smart default filtering: If no date filter is applied, default to last 7 days for performance
    if (!dateParams.date && !dateParams.dateRangeType) {
      const sevenDaysAgo = dayjs().subtract(7, 'days').format('YYYY-MM-DD');
      queryParams.append("date", sevenDaysAgo);
      queryParams.append("dateRangeType", "week");

      // Add performance hint for backend
      queryParams.append("defaultFilter", "true");
    }

    // Add data aggregation hints for large date ranges
    const dateRange = dateParams.dateRangeType;
    if (dateRange === 'month') {
      queryParams.append("aggregateBy", "day");
    } else if (dateRange === 'year') {
      queryParams.append("aggregateBy", "week");
    }

    return queryParams.toString() ? `?${queryParams.toString()}` : "";
  }, [selectedMachineModel, selectedMachine, buildDateQueryParams]);

  // Helper function to generate sample data for demonstration
  const generateSampleData = useCallback(() => {
    // Ensure we're using a valid date object
    const today = dayjs();
    const sampleData = [];



    // Generate 10 days of sample data
    for (let i = 9; i >= 0; i--) {
      // Create a proper date string in YYYY-MM-DD format
      const date = today.subtract(i, 'day').format('YYYY-MM-DD');

      // Validate the date is correct
      if (!dayjs(date).isValid()) {
        console.error("Invalid date generated:", date);
        continue; // Skip this iteration
      }

      // Create sample data point with valid date
      sampleData.push({
        date,
        good: Math.floor(Math.random() * 1000) + 500,
        reject: Math.floor(Math.random() * 100) + 10,
        oee: Math.floor(Math.random() * 30) + 70, // 70-100% (already in 0-100 range)
        speed: Math.floor(Math.random() * 5) + 5, // 5-10
        Machine_Name: selectedMachine || (selectedMachineModel ? `${selectedMachineModel}01` : 'IPS01'),
        Shift: ['Matin', 'Après-midi', 'Nuit'][Math.floor(Math.random() * 3)]
      });
    }


    return sampleData;
  }, [selectedMachine, selectedMachineModel]);

  // Fetch production data
  const fetchData = useCallback(async () => {
    if (!selectedMachineModel) {
      console.log("No machine model selected, skipping data fetch");
      return;
    }

    setLoading(true);
    try {
      // Build query parameters
      const queryString = buildQueryParams();
      console.log("API query string:", queryString);

      // Use Promise.allSettled instead of Promise.all to handle individual request failures
      const results = await Promise.allSettled([
        createAuthRequest('get', `/api/testing-chart-production${queryString}`),
        createAuthRequest('get', '/api/unique-dates-production').catch(() => ({ body: [] })),
        createAuthRequest('get', `/api/sidecards-prod${queryString}`),
        createAuthRequest('get', `/api/sidecards-prod-rejet${queryString}`),
        createAuthRequest('get', `/api/machine-performance${queryString}`),
        createAuthRequest('get', `/api/hourly-trends${queryString}`),
        createAuthRequest('get', `/api/machine-oee-trends${queryString}`),
        createAuthRequest('get', `/api/speed-trends${queryString}`),
        createAuthRequest('get', `/api/shift-comparison${queryString}`),
        createAuthRequest('get', `/api/machine-daily-mould${queryString}`), // New endpoint for machine_daily_table_mould data
      ]);

      // Process results safely
      const [
        chartRes,
        datesRes,
        prodRes,
        rejetRes,
        machinesRes,
        hourlyRes,
        oeeTrendsRes,
        speedTrendsRes,
        shiftRes,
        mouldDataRes,
      ] = results;

      // Update state with data from successful requests
      if (chartRes.status === "fulfilled" && chartRes.value.body) {
        // Extract data using our utility function
        const responseData = extractResponseData(chartRes.value);

        // Ensure responseData is an array before calling map
        const dataArray = Array.isArray(responseData) ? responseData : [];

        const transformedData = dataArray.map(transformData);
        setChartData(transformedData);
      } else {
        console.log("No chart data available");
        setChartData([]);
      }

      if (datesRes.status === "fulfilled") {
        // Extract data using our utility function
        const datesData = extractResponseData(datesRes.value);
        setUniqueDates(datesData || []);
      }

      if (prodRes.status === "fulfilled") {
        // Extract data using our utility function
        const prodData = extractResponseData(prodRes.value);
        setGoodQty(prodData[0]?.goodqty || 0);
      } else {
        setGoodQty(0);
      }

      if (rejetRes.status === "fulfilled") {
        // Extract data using our utility function
        const rejetData = extractResponseData(rejetRes.value);
        setRejetQty(rejetData[0]?.rejetqty || 0);
      } else {
        setRejetQty(0);
      }

      if (machinesRes.status === "fulfilled" && machinesRes.value.body) {
        // Extract data using our utility function
        const machinesData = extractResponseData(machinesRes.value);
        setMachinePerformance(machinesData || []);
      } else {
        console.log("No machine performance data available");
        setMachinePerformance([]);
      }

      if (hourlyRes.status === "fulfilled") {
        // Extract data using our utility function
        const hourlyData = extractResponseData(hourlyRes.value);
        setHourlyTrends(hourlyData || []);
      }

      // Process OEE and speed trends
      const oeeMap =
        oeeTrendsRes.status === "fulfilled" && oeeTrendsRes.value.body
          ? extractResponseData(oeeTrendsRes.value).reduce((acc, item) => {
              acc[item.date] = parseFloat(item.oee) || 0;
              return acc;
            }, {})
          : {};

      const speedMap =
        speedTrendsRes.status === "fulfilled" && speedTrendsRes.value.body
          ? extractResponseData(speedTrendsRes.value).reduce((acc, item) => {
              // Ensure speed is a valid number
              const speedValue = parseFloat(item.speed);
              if (!isNaN(speedValue) && speedValue > 0) {
                acc[item.date] = speedValue;
              }
              return acc;
            }, {})
          : {};

      const allDates = [...new Set([...Object.keys(oeeMap), ...Object.keys(speedMap)])];

      // Process dates to ensure they're in chronological order
      const sortedDates = [...allDates].sort((a, b) => dayjs(a).diff(dayjs(b)));

      // Filter out dates that are too far apart (more than 60 days from the most recent date)
      let filteredDates = sortedDates;
      if (sortedDates.length > 0) {
        const mostRecentDate = dayjs(sortedDates[sortedDates.length - 1]);
        filteredDates = sortedDates.filter(date => {
          const diff = mostRecentDate.diff(dayjs(date), 'day');
          return diff <= 60; // Only show data from the last 60 days
        });
      }

      const mergedRes = filteredDates
        .map((date) => ({
          date,
          oee: oeeMap[date] || 0,
          speed: speedMap[date] || null, // Use null for missing values to avoid connecting lines
        }))
        .sort((a, b) => dayjs(a.date).diff(dayjs(b.date)));

      // Use machine_daily_table_mould data if available, otherwise fallback to merged OEE/speed data
      if (mouldDataRes && mouldDataRes.status === "fulfilled" && mouldDataRes.value.body) {
        // Extract data using our utility function
        const mouldData = extractResponseData(mouldDataRes.value);

        if (mouldData.length > 0) {
          try {
            // Process the mould data to ensure it has the correct format for charts
            const processedMouldData = mouldData.map(item => {
              // Ensure all numeric values are properly parsed
              const goodQty = parseFloat(item.Good_QTY_Day || item.good || 0);
              const rejectQty = parseFloat(item.Rejects_QTY_Day || item.reject || 0);
              const oeeValue = parseFloat(item.OEE_Day || item.oee || 0);
              const speedValue = parseFloat(item.Speed_Day || item.speed || 0);
              const availabilityValue = parseFloat(item.Availability_Rate_Day || item.availability || 0);
              const performanceValue = parseFloat(item.Performance_Rate_Day || item.performance || 0);
              const qualityValue = parseFloat(item.Quality_Rate_Day || item.quality || 0);

              // Ensure date is in a valid format (YYYY-MM-DD)
              let formattedDate = null;
              try {
                // Try to parse the date from the API
                const rawDate = item.Date_Insert_Day || item.date;
                if (rawDate) {
                  // Check if it's already a valid date string
                  if (dayjs(rawDate).isValid()) {
                    formattedDate = dayjs(rawDate).format('YYYY-MM-DD');
                  } else {
                    // Try to parse different date formats
                    const formats = ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD', 'YYYY/MM/DD', 'DD-MM-YYYY'];
                    for (const format of formats) {
                      const parsed = dayjs(rawDate, format);
                      if (parsed.isValid()) {
                        formattedDate = parsed.format('YYYY-MM-DD');
                        break;
                      }
                    }
                  }
                }

                // If we still don't have a valid date, use today's date
                if (!formattedDate) {
                  console.warn(`Invalid date found: ${item.Date_Insert_Day || item.date}, using today's date instead`);
                  formattedDate = dayjs().format('YYYY-MM-DD');
                }
              } catch (error) {
                console.error("Error parsing date:", error);
                formattedDate = dayjs().format('YYYY-MM-DD');
              }

              // Normalize percentage values
              const normalizedOee = normalizePercentage(oeeValue);
              const normalizedAvailability = normalizePercentage(availabilityValue);
              const normalizedPerformance = normalizePercentage(performanceValue);
              const normalizedQuality = normalizePercentage(qualityValue);

              return {
                date: formattedDate,
                oee: normalizedOee,
                speed: !isNaN(speedValue) ? speedValue : 0,
                good: !isNaN(goodQty) ? goodQty : 0,
                reject: !isNaN(rejectQty) ? rejectQty : 0,
                Machine_Name: item.Machine_Name || 'N/A',
                Shift: item.Shift || 'N/A',
                availability: normalizedAvailability,
                performance: normalizedPerformance,
                quality: normalizedQuality
              };
            }).sort((a, b) => dayjs(a.date).diff(dayjs(b.date)));

            // Check if we have any valid data points with non-zero values
            const hasValidData = processedMouldData.some(item =>
              item.good > 0 || item.reject > 0 || item.oee > 0 || item.speed > 0
            );

            if (hasValidData) {
              setMergedData(processedMouldData);
            } else {
              console.warn("No valid data points found in processed mould data");
              // Use sample data for demonstration if needed
              const sampleData = generateSampleData();
              setMergedData(sampleData);

              // DISABLED: Notification disabled for ProductionDashboard which uses GraphQL
              // if ((selectedMachineModel && selectedMachineModel !== "IPS") || selectedMachine || dateFilter) {
              //   notification.info({
              //     message: "Données de démonstration",
              //     description: "Aucune donnée valide n'a été trouvée. Des données de démonstration sont affichées.",
              //     duration: 5,
              //   });
              // }
            }
          } catch (error) {
            console.error("Error processing mould data:", error);
            setMergedData(mergedRes);
          }
        } else {
          console.log("Machine daily mould API returned empty array, using merged OEE/speed data as fallback");

          // Check if mergedRes has any data
          if (mergedRes.length > 0) {
            setMergedData(mergedRes);
          } else {
            // Generate sample data for demonstration
            const sampleData = generateSampleData();
            setMergedData(sampleData);

            // DISABLED: Notification disabled for ProductionDashboard which uses GraphQL
            // if ((selectedMachineModel && selectedMachineModel !== "IPS") || selectedMachine || dateFilter) {
            //   notification.info({
            //     message: "Données de démonstration",
            //     description: "Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",
            //     duration: 5,
            //   });
            // }
          }

          // DISABLED: Notification disabled for ProductionDashboard which uses GraphQL
          // The ProductionDashboard now uses GraphQL and has its own proper "no data" handling
          // This REST API notification was causing false "no data" alerts
          // if ((selectedMachineModel && selectedMachineModel !== "IPS") || selectedMachine || dateFilter) {
          //   notification.info({
          //     message: "Aucune donnée disponible",
          //     description: "Aucune donnée n'a été trouvée pour les filtres sélectionnés. Essayez de modifier vos critères de recherche.",
          //     duration: 5,
          //   });
          // }
        }
      } else {
        // Handle API error or rejected promise
        console.log("Machine daily mould API request failed or returned invalid data");
        if (mouldDataRes && mouldDataRes.status === "rejected") {
          console.error("API error:", mouldDataRes.reason);
        }

        // Check if mergedRes has any data
        if (mergedRes.length > 0) {
          setMergedData(mergedRes);
        } else {
          // Generate sample data for demonstration
          const sampleData = generateSampleData();
          setMergedData(sampleData);

          // DISABLED: Notification disabled for ProductionDashboard which uses GraphQL
          // if ((selectedMachineModel && selectedMachineModel !== "IPS") || selectedMachine || dateFilter) {
          //   notification.info({
          //     message: "Données de démonstration",
          //     description: "Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",
          //     duration: 5,
          //   });
          // } else {
          //   console.log("Using sample data for default dashboard state (IPS model)");
          // }
          console.log("Using sample data for default dashboard state (IPS model)");
        }
      }

      // Set OEE and speed trends
      if (oeeTrendsRes.status === "fulfilled") {
        setOeeTrends(extractResponseData(oeeTrendsRes.value) || []);
      }

      if (speedTrendsRes.status === "fulfilled") {
        setSpeedTrends(extractResponseData(speedTrendsRes.value) || []);
      }

      if (shiftRes.status === "fulfilled") {
        setShiftComparison(extractResponseData(shiftRes.value) || []);
      }
    } catch (error) {
      console.error("Error loading data:", error);
      // Set default values for critical components
      setGoodQty(0);
      setRejetQty(0);
      setChartData([]);
      setMachinePerformance([]);
    } finally {
      setLoading(false);
    }
  }, [selectedMachineModel, selectedMachine, dateFilter, dateRangeType, buildQueryParams, generateSampleData]);

  // Fetch general data for good quantity and rejected quantity
  const fetchGeneralData = useCallback(async () => {
    try {
      // Always fetch general data, regardless of machine model selection
      setLoading(true);

      // Use Promise.allSettled to handle individual request failures
      const results = await Promise.allSettled([
        createAuthRequest('get', '/api/sidecards-prod'),
        createAuthRequest('get', '/api/sidecards-prod-rejet'),
      ]);

      const [prodRes, rejetRes] = results;

      // Update good quantity
      if (prodRes.status === "fulfilled") {
        const prodData = extractResponseData(prodRes.value);
        setGoodQty(prodData[0]?.goodqty || 15000);
      } else {
        console.error("Failed to fetch good quantity:", prodRes.reason);
        setGoodQty(15000); // Use sample data if the request fails
      }

      // Update rejected quantity
      if (rejetRes.status === "fulfilled") {
        const rejetData = extractResponseData(rejetRes.value);
        setRejetQty(rejetData[0]?.rejetqty || 750);
      } else {
        console.error("Failed to fetch rejected quantity:", rejetRes.reason);
        setRejetQty(750); // Use sample data if the request fails
      }
    } catch (error) {
      console.error("Error loading general data:", error);
      // Use sample data if an error occurs
      setGoodQty(15000);
      setRejetQty(750);
    } finally {
      setLoading(false);
    }
  }, []);

  // Calculate statistics
  const calculateStatistics = useCallback(() => {
    // Calculate average TRS (OEE) with normalization
    let avgTRS = 0;
    if (chartData.length > 0) {
      const sum = chartData.reduce((acc, item) => {
        // Get the OEE value and ensure it's a number
        let oeeValue = parseFloat(item.oee || 0);
        // Normalize it (convert from 0-1 to 0-100 if needed)
        oeeValue = normalizePercentage(oeeValue);
        return acc + oeeValue;
      }, 0);
      avgTRS = sum / chartData.length;
    }

    const rejectRate = goodQty + rejetQty > 0 ? (rejetQty / (goodQty + rejetQty)) * 100 : 0;
    const qualityRate = goodQty + rejetQty > 0 ? (goodQty / (goodQty + rejetQty)) * 100 : 0;

    // Calculate average availability with normalization
    let avgAvailability = 0;
    if (chartData.length > 0) {
      const sum = chartData.reduce((acc, item) => {
        // Get the availability value and ensure it's a number
        let availValue = parseFloat(item.availability || 0);
        // Normalize it (convert from 0-1 to 0-100 if needed)
        availValue = normalizePercentage(availValue);
        return acc + availValue;
      }, 0);
      avgAvailability = sum / chartData.length;
    }

    // Calculate average performance with normalization
    let avgPerformance = 0;
    if (chartData.length > 0) {
      const sum = chartData.reduce((acc, item) => {
        // Get the performance value and ensure it's a number
        let perfValue = parseFloat(item.performance || 0);
        // Normalize it (convert from 0-1 to 0-100 if needed)
        perfValue = normalizePercentage(perfValue);
        return acc + perfValue;
      }, 0);
      avgPerformance = sum / chartData.length;
    }

    // Calculate average quality with normalization
    let avgQuality = 0;
    if (chartData.length > 0) {
      const sum = chartData.reduce((acc, item) => {
        // Get the quality value and ensure it's a number
        let qualValue = parseFloat(item.quality || 0);
        // Normalize it (convert from 0-1 to 0-100 if needed)
        qualValue = normalizePercentage(qualValue);
        return acc + qualValue;
      }, 0);
      avgQuality = sum / chartData.length;
    }

    return {
      avgTRS,
      rejectRate,
      qualityRate,
      avgAvailability,
      avgPerformance,
      avgQuality
    };
  }, [chartData, goodQty, rejetQty]);

  // Effect to fetch data when dependencies change
  useEffect(() => {
    console.log("🔄 Data fetch effect triggered:", {
      selectedMachineModel,
      selectedMachine,
      dateFilter,
      dateRangeType
    });

    if (selectedMachineModel) {
      console.log("📊 Fetching production data for model:", selectedMachineModel);
      fetchData();
    } else {
      console.log("📊 Fetching general data (no machine model selected)");
      // Fetch general data even if no machine model is selected
      fetchGeneralData();
    }
  }, [selectedMachineModel, selectedMachine, dateFilter, dateRangeType, fetchData, fetchGeneralData]);

  return {
    loading,
    chartData,
    machinePerformance,
    mergedData,
    uniqueDates,
    goodQty,
    rejetQty,
    oeeTrends,
    speedTrends,
    hourlyTrends,
    shiftComparison,
    fetchData,
    fetchGeneralData,
    calculateStatistics
  };
};

export default useProductionData;
