import React, { useState, useCallback, useEffect, useMemo } from "react";
import { Card, Button, Tooltip, Spin, Empty, Space } from "antd";
import {
  ExpandOutlined,
  CompressOutlined,
  FullscreenOutlined,
} from "@ant-design/icons";
import PropTypes from "prop-types";
import ChartModal from "./ChartModal";
import { optimizeChartData, calculateChartDimensions } from "../../../utils/chartDataUtils";
import "./ExpandableChart.css";

/**
 * Simplified ExpandableChart component with dedicated modal
 * Focuses on normal chart display and delegates modal functionality to ChartModal
 */
const ExpandableChart = ({
  children,
  title,
  data,
  chartType = "bar",
  expandMode = "modal", // "modal" or "inline"
  onExpand,
  onCollapse,
  exportEnabled,
  zoomEnabled,
  ...cardProps
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Data optimization for normal view only
  const optimizedData = useMemo(() => {
    if (!data || data.length === 0) {
      return { data: [], config: {} };
    }

    const result = optimizeChartData(data, isExpanded, chartType);
    return result;
  }, [data, isExpanded, chartType]);

  // Chart dimensions for normal view
  const chartDimensions = useMemo(() => {
    return calculateChartDimensions(optimizedData.data, isExpanded, chartType);
  }, [optimizedData.data, isExpanded, chartType]);

  // Handle expansion toggle
  const handleExpansion = useCallback(async () => {
    setIsLoading(true);

    try {
      // Small delay for large datasets
      if (data && data.length > 100) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      if (expandMode === "modal") {
        setIsModalVisible(true);
        if (onExpand) onExpand();
      } else {
        const newExpandedState = !isExpanded;
        setIsExpanded(newExpandedState);

        if (newExpandedState && onExpand) {
          onExpand();
        } else if (!newExpandedState && onCollapse) {
          onCollapse();
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [expandMode, isExpanded, onExpand, onCollapse, data]);

  // Handle modal close
  const handleModalClose = useCallback(() => {
    setIsModalVisible(false);
    if (onCollapse) {
      onCollapse();
    }
  }, [onCollapse]);

  // Keyboard events for inline expansion
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === "Escape" && isExpanded && expandMode === "inline") {
        setIsExpanded(false);
        if (onCollapse) onCollapse();
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [isExpanded, expandMode, onCollapse]);

  // Chart controls for inline expansion
  const renderChartControls = () => (
    expandMode === "inline" && (
      <Tooltip title={isExpanded ? "Réduire" : "Agrandir"}>
        <Button
          icon={isExpanded ? <CompressOutlined /> : <ExpandOutlined />}
          onClick={handleExpansion}
          type="primary"
        />
      </Tooltip>
    )
  );

  // Render normal chart
  const renderNormalChart = (height = 300) => {
    if (isLoading) {
      return (
        <div
          style={{
            height,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Spin size="large" tip="Chargement du graphique..." />
        </div>
      );
    }

    if (!data || data.length === 0) {
      return (
        <div
          style={{
            height,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column'
          }}
        >
          <Empty description="Aucune donnée disponible" />
        </div>
      );
    }

    const chartData = optimizedData?.data?.length > 0 ? optimizedData.data : data;
    const actualHeight = isExpanded ? chartDimensions.height : height;

    return (
      <div
        id={isExpanded ? "expanded-chart" : "normal-chart"}
        className={`chart-container ${isExpanded ? "expanded" : ""}`}
        style={{
          height: actualHeight,
          width: "100%",
          position: "relative",
          overflow: "visible",
        }}
      >
        {React.cloneElement(children, {
          ...children.props,
          height: actualHeight,
          data: chartData,
          chartConfig: optimizedData?.config || {},
          dimensions: chartDimensions,
          enhanced: isExpanded,
          expanded: isExpanded,
          isModal: false,
        })}
      </div>
    );
  };

  return (
    <>
      {/* Normal/Inline Chart Card */}
      <Card
        {...cardProps}
        title={title}
        className={`expandable-chart-card ${isExpanded ? "expanded" : ""}`}
        extra={
          <Space>
            {isExpanded && renderChartControls()}
            <Tooltip title={expandMode === "modal" ? "Ouvrir en plein écran" : (isExpanded ? "Réduire" : "Agrandir")}>
              <Button
                icon={expandMode === "modal" ? <FullscreenOutlined /> : (isExpanded ? <CompressOutlined /> : <ExpandOutlined />)}
                onClick={handleExpansion}
                type={isExpanded ? "primary" : "default"}
                className="expand-button"
              />
            </Tooltip>
          </Space>
        }
        hoverable
        style={{
          cursor: "pointer",
          transition: "all 0.3s ease",
          ...(isExpanded && {
            position: "relative",
            zIndex: 10,
            boxShadow: "0 8px 24px rgba(0,0,0,0.15)",
          }),
        }}
        onClick={(e) => {
          // Only trigger expansion if clicking on the card itself, not controls
          if (e.target.closest('.ant-card-extra') || e.target.closest('.chart-container')) {
            return;
          }
          handleExpansion();
        }}
      >
        {renderNormalChart(isExpanded ? 600 : 300)}
      </Card>

      {/* Dedicated Modal Component */}
      <ChartModal
        visible={isModalVisible}
        onClose={handleModalClose}
        title={title}
        data={data}
        chartType={chartType}
      >
        {children}
      </ChartModal>
    </>
  );
};

ExpandableChart.propTypes = {
  children: PropTypes.element.isRequired,
  title: PropTypes.string.isRequired,
  data: PropTypes.array.isRequired,
  chartType: PropTypes.string,
  expandMode: PropTypes.oneOf(["modal", "inline"]),
  onExpand: PropTypes.func,
  onCollapse: PropTypes.func,
};

export default ExpandableChart;
