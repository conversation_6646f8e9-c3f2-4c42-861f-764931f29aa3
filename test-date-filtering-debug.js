/**
 * Test Script: Debug Date Filtering Issues in ArretLineChart
 * 
 * This script helps identify why the chart shows "No data available" 
 * when date filters are applied.
 */

import { GraphQLClient } from 'graphql-request';

async function debugDateFilteringIssues() {
  console.log('🔍 Debugging Date Filtering Issues...\n');

  const client = new GraphQLClient('http://localhost:5000/api/graphql');

  // Test with the exact scenario from screenshot: IPS01 + April 2025
  const query = `
    query GetAllMachineStops($filters: StopFilterInput) {
      getAllMachineStops(filters: $filters) {
        Date_Insert
        Machine_Name
        duration_minutes
      }
    }
  `;

  // Simulate the exact filters from the screenshot
  const filters = {
    model: "IPS",
    machine: "IPS01",
    date: "2025-04-01", // April 2025 as shown in screenshot
    startDate: "2025-04-01",
    endDate: "2025-04-30",
    dateRangeType: "month"
  };

  console.log('📋 Testing Exact Scenario from Screenshot:');
  console.log('- Machine Model: IPS');
  console.log('- Machine: IPS01');
  console.log('- Date: April 2025');
  console.log('- Date Range Type: month\n');

  try {
    const result = await client.request(query, { filters });
    const stopsData = result.getAllMachineStops || [];

    console.log('🔍 Backend Query Result:');
    console.log(`- Total stops found: ${stopsData.length}`);
    
    if (stopsData.length > 0) {
      console.log(`- Date range: ${stopsData[0]?.Date_Insert} to ${stopsData[stopsData.length - 1]?.Date_Insert}`);
      
      // Show all unique dates to see what's available
      const uniqueDates = [...new Set(stopsData.map(stop => stop.Date_Insert))];
      console.log('\n📅 All unique dates in the data:');
      uniqueDates.slice(0, 10).forEach((date, index) => {
        console.log(`  ${index + 1}. ${date}`);
      });
      if (uniqueDates.length > 10) {
        console.log(`  ... and ${uniqueDates.length - 10} more dates`);
      }
    }

    // Process data exactly like the dashboard does
    console.log('\n🔄 Processing data like dashboard...');
    const chartData = processDataWithDateFiltering(stopsData, filters);
    
    console.log(`\n📊 Final Chart Data:`);
    console.log(`- Chart points generated: ${chartData.length}`);
    
    if (chartData.length > 0) {
      console.log('\n📈 Chart Data Points:');
      chartData.forEach((point, index) => {
        console.log(`  ${index + 1}. ${point.displayDate}: ${point.stops} stops`);
      });
      
      console.log('\n✅ Data would display in chart');
    } else {
      console.log('\n❌ NO CHART DATA - This explains "Aucune donnée disponible"');
      
      // Debug why no data was generated
      console.log('\n🔍 Debugging empty result:');
      
      // Step 1: Check daily aggregation
      const dailyStats = aggregateByDate(stopsData);
      console.log(`- Daily aggregation created ${Object.keys(dailyStats).length} days`);
      
      if (Object.keys(dailyStats).length > 0) {
        console.log('- Sample daily stats:');
        Object.entries(dailyStats).slice(0, 3).forEach(([date, stats]) => {
          console.log(`  ${date}: ${stats.stops} stops`);
        });
        
        // Step 2: Check date filtering
        console.log('\n- Testing date filtering...');
        const allEvolutionData = Object.values(dailyStats).sort((a, b) => new Date(a.date) - new Date(b.date));
        console.log(`- Before date filter: ${allEvolutionData.length} points`);
        
        // Apply same filtering logic as dashboard
        const filteredData = applyDateFilter(allEvolutionData, filters);
        console.log(`- After date filter: ${filteredData.length} points`);
        
        if (filteredData.length === 0) {
          console.log('⚠️ DATE FILTERING IS REMOVING ALL DATA!');
          console.log('- Available date range:', 
            allEvolutionData[0]?.date, 
            'to', 
            allEvolutionData[allEvolutionData.length - 1]?.date
          );
          console.log('- Filter expecting date range:', filters.startDate, 'to', filters.endDate);
        }
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

/**
 * Aggregate stops by date exactly like dashboard (FIXED VERSION)
 */
function aggregateByDate(stopsData) {
  const dailyStats = {};
  
  console.log('\n🔄 Aggregating stops by date...');
  
  stopsData.forEach((stop, index) => {
    if (stop.Date_Insert) {
      let date = parseDate(stop.Date_Insert);
      
      if (date) { // Only process valid dates
        if (!dailyStats[date]) {
          dailyStats[date] = { date, stops: 0, duration: 0 };
          console.log(`    📊 Created new day: ${date}`);
        }
        dailyStats[date].stops++;
        
        if (stop.duration_minutes && stop.duration_minutes > 0) {
          dailyStats[date].duration += parseFloat(stop.duration_minutes);
        }
        
        if (index < 5) { // Show first 5 for debugging
          console.log(`    ➕ Added stop to ${date}: now ${dailyStats[date].stops} stops`);
        }
      }
    }
  });
  
  console.log(`📊 Aggregation complete: ${Object.keys(dailyStats).length} unique days`);
  
  return dailyStats;
}

/**
 * Parse date exactly like dashboard (FIXED VERSION)
 */
function parseDate(dateString) {
  let originalDate = dateString.toString();
  let date;
  
  if (originalDate.includes('/')) {
    // Format: DD/MM/YYYY HH:MM:SS
    const parts = originalDate.split(' ');
    const datePart = parts[0]; // DD/MM/YYYY
    const [day, month, year] = datePart.split('/');
    date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    console.log(`    📅 Parsed DD/MM/YYYY: ${originalDate} -> ${date}`);
  } else if (originalDate.includes('T')) {
    // Format: YYYY-MM-DDTHH:MM:SS
    date = originalDate.split('T')[0];
    console.log(`    📅 Parsed ISO: ${originalDate} -> ${date}`);
  } else if (originalDate.match(/^\d{4} \d{2}:\d{2}:\d{2}-\d{1,2}-\s*\d{1,2}$/)) {
    // Format: "2024 10:35:13-12- 3" -> "YYYY HH:MM:SS-MM-DD"
    const match = originalDate.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);
    if (match) {
      const [_, year, month, day] = match;
      date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      console.log(`    📅 Parsed custom: ${originalDate} -> ${date}`);
    } else {
      console.warn(`    ❌ Failed to parse custom format: ${originalDate}`);
      return null; // Skip invalid dates
    }
  } else {
    const dateMatch = originalDate.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
    if (dateMatch) {
      const [_, year, month, day] = dateMatch;
      date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      console.log(`    📅 Parsed regex: ${originalDate} -> ${date}`);
    } else {
      console.warn(`    ❌ Unrecognized format: ${originalDate}`);
      return null; // Skip invalid dates
    }
  }
  
  return date;
}

/**
 * Apply date filtering exactly like dashboard
 */
function applyDateFilter(evolutionData, filters) {
  if (!filters.date || !filters.dateRangeType) {
    return evolutionData;
  }
  
  if (filters.dateRangeType === 'month') {
    const filterDate = new Date(filters.date);
    const filterMonth = filterDate.getMonth();
    const filterYear = filterDate.getFullYear();
    
    console.log(`  - Filtering for month ${filterMonth + 1} of year ${filterYear}`);
    
    return evolutionData.filter(item => {
      const itemDate = new Date(item.date);
      const matches = itemDate.getMonth() === filterMonth && itemDate.getFullYear() === filterYear;
      if (!matches) {
        console.log(`    - Excluded: ${item.date} (month: ${itemDate.getMonth() + 1}, year: ${itemDate.getFullYear()})`);
      }
      return matches;
    });
  }
  
  return evolutionData;
}

/**
 * Process data with date filtering
 */
function processDataWithDateFiltering(stopsData, filters) {
  const dailyStats = aggregateByDate(stopsData);
  let evolutionData = Object.values(dailyStats).sort((a, b) => new Date(a.date) - new Date(b.date));
  
  // Apply date filtering
  evolutionData = applyDateFilter(evolutionData, filters);
  
  // Add display dates
  return evolutionData.map(item => ({
    ...item,
    displayDate: formatDisplayDate(item.date)
  }));
}

/**
 * Format display date
 */
function formatDisplayDate(dateString) {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return dateString.slice(0, 10);
    }
    return date.toLocaleDateString('fr-FR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  } catch (error) {
    return dateString.slice(0, 10);
  }
}

// Run the debug test
debugDateFilteringIssues().catch(console.error);
