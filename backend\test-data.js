import mysql from 'mysql2/promise';

async function testData() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: 'root',
      database: 'Testingarea51'
    });
    
    console.log('=== Sample Machine Stop Data ===');
    const [rows] = await connection.execute(`
      SELECT Machine_Name, Debut_Stop, Fin_Stop_Time, Code_Stop
      FROM machine_stop_table_mould 
      WHERE Debut_Stop IS NOT NULL AND Fin_Stop_Time IS NOT NULL
      ORDER BY Debut_Stop DESC 
      LIMIT 3
    `);
    
    console.table(rows);    console.log('\n=== Availability Calculation Test (All Data) ===');
    const [availability] = await connection.execute(`
      SELECT 
        DATE(STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i')) as stop_date,
        COUNT(*) as stop_count,
        SUM(TIMESTAMPDIFF(MINUTE, 
          STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i'),
          STR_TO_DATE(Fin_Stop_Time, '%d/%m/%Y %H:%i')
        )) as total_downtime
      FROM machine_stop_table_mould 
      WHERE Debut_Stop IS NOT NULL AND Fin_Stop_Time IS NOT NULL
        AND Debut_Stop != Fin_Stop_Time
      GROUP BY DATE(STR_TO_DATE(Debut_Stop, '%d/%m/%Y %H:%i'))
      ORDER BY stop_date DESC
      LIMIT 5
    `);
    
    console.table(availability);
    
    await connection.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testData();
