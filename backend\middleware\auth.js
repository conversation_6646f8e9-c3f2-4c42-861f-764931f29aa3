/**
 * Authentication middleware
 */
import jwt from 'jsonwebtoken';
import { executeQuery } from '../utils/dbUtils.js';
import { sendError } from '../utils/responseUtils.js';
import { parsePermissions } from '../utils/permissionUtils.js';

/**
 * Authentication middleware
 */
const auth = async (req, res, next) => {
  // Prioritize token from HTTP-only cookie
  // Fall back to headers for backward compatibility during transition
  const token = req.cookies.token ||
                req.header('Authorization')?.replace('Bearer ', '') ||
                req.header('x-auth-token');

  // Check if no token
  if (!token) {
    return sendError(res, 'No token, authorization denied', 401);
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Set user id in req.user
    req.user = { id: decoded.id };

    // Get user from database with role and department information
    const { success, data: users, error } = await executeQuery(
      `SELECT u.id, u.username, u.email, u.role, u.department_id, u.permissions,
              r.name as role_name, r.permissions as role_permissions,
              d.name as department_name
       FROM users u
       LEFT JOIN roles r ON u.role_id = r.id
       LEFT JOIN departments d ON u.department_id = d.id
       WHERE u.id = ?`,
      [decoded.id]
    );

    if (!success) {
      return sendError(res, 'Database error', 500, error);
    }

    if (users.length === 0) {
      return sendError(res, 'User not found', 404);
    }

    const user = users[0];

    // Parse permissions
    user.permissions = parsePermissions(user.permissions);
    user.role_permissions = parsePermissions(user.role_permissions);

    try {
      // Import dynamically to avoid circular dependency
      const { getAllRolePermissions } = await import('../utils/roleHierarchy.js');

      // Get role permissions from hierarchy
      let hierarchyPermissions = [];
      if (user.role) {
        hierarchyPermissions = getAllRolePermissions(user.role);
      }

      // Add hierarchy permissions to user object
      user.hierarchy_permissions = hierarchyPermissions;

      // Combine all permissions
      const allPermissions = [
        ...user.permissions,
        ...user.role_permissions,
        ...hierarchyPermissions
      ].filter(Boolean); // Remove null/undefined values

      // Add combined permissions to user object
      user.all_permissions = allPermissions;

      // Set userContext
      req.userContext = {
        departmentId: user.department_id,
        departmentName: user.department_name,
        roleName: user.role,
        permissions: allPermissions
      };
    } catch (error) {
      console.error('Error setting up permissions in auth middleware:', error);
      // If there's an error, set empty arrays to avoid undefined errors
      user.hierarchy_permissions = [];
      user.all_permissions = [...user.permissions, ...user.role_permissions].filter(Boolean);
    }

    // Set user in request
    req.user = user;

    next();
  } catch (err) {
    if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
      return sendError(res, 'Token is not valid', 401);
    }
    return sendError(res, 'Authentication error', 500, err);
  }
};


export default auth;