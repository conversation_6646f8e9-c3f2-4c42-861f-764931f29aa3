# Working Dockerfile without problematic native dependencies
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Create package.json without problematic native dependencies
RUN echo "=== Removing problematic native dependencies ===" && \
    cp package.json package.json.backup && \
    node -e "
const fs = require('fs');
const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
// Remove problematic native dependencies that require Python/build tools
delete pkg.dependencies.canvas;
delete pkg.dependencies['chartjs-node-canvas'];
delete pkg.dependencies.puppeteer;
delete pkg.dependencies['puppeteer-core'];
delete pkg.dependencies.pdfkit;
// Keep all other dependencies including apicache
fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
console.log('Removed problematic dependencies, keeping apicache and core deps');
"

# Install dependencies with verbose logging
RUN echo "=== Installing Node.js dependencies ===" && \
    if [ -f package-lock.json ]; then \
        echo "Using npm ci with package-lock.json" && \
        npm ci --verbose; \
    else \
        echo "No package-lock.json found, using npm install" && \
        npm install --verbose; \
    fi && \
    echo "=== Verifying core dependencies ===" && \
    npm list apicache && \
    npm list express && \
    npm list mysql2 && \
    echo "=== Testing dependency imports ===" && \
    node -e "try { require('apicache'); console.log('✅ apicache: SUCCESS'); } catch(e) { console.error('❌ apicache:', e.message); process.exit(1); }" && \
    node -e "try { require('express'); console.log('✅ express: SUCCESS'); } catch(e) { console.error('❌ express:', e.message); }" && \
    node -e "try { require('mysql2'); console.log('✅ mysql2: SUCCESS'); } catch(e) { console.error('❌ mysql2:', e.message); }" && \
    node -e "try { require('cors'); console.log('✅ cors: SUCCESS'); } catch(e) { console.error('❌ cors:', e.message); }" && \
    node -e "try { require('jsonwebtoken'); console.log('✅ jsonwebtoken: SUCCESS'); } catch(e) { console.error('❌ jsonwebtoken:', e.message); }" && \
    node -e "try { require('bcrypt'); console.log('✅ bcrypt: SUCCESS'); } catch(e) { console.error('❌ bcrypt:', e.message); }" && \
    npm cache clean --force

# Copy source code
COPY . .

# Create a test server to demonstrate apicache functionality
RUN echo "=== Creating test server ===" && \
    cat > test-apicache-server.js << 'EOF'
const express = require('express');
const apicache = require('apicache');
const cors = require('cors');

const app = express();
const cache = apicache.middleware;

// Enable CORS
app.use(cors());
app.use(express.json());

// Apply caching middleware to specific routes
app.use('/api/cached', cache('5 minutes'));

// Test routes
app.get('/', (req, res) => {
    res.json({
        message: 'LOCQL Backend Test Server',
        status: 'running',
        dependencies: {
            apicache: 'working',
            express: 'working',
            cors: 'working'
        },
        timestamp: new Date().toISOString()
    });
});

app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        apicache: 'enabled',
        cache_stats: apicache.getPerformance(),
        uptime: process.uptime()
    });
});

// Cached endpoint to test apicache functionality
app.get('/api/cached/data', (req, res) => {
    console.log('Processing cached request at:', new Date().toISOString());
    res.json({
        message: 'This response is cached for 5 minutes',
        generated_at: new Date().toISOString(),
        cache_key: req.originalUrl,
        random_data: Math.random()
    });
});

// Non-cached endpoint for comparison
app.get('/api/uncached/data', (req, res) => {
    console.log('Processing uncached request at:', new Date().toISOString());
    res.json({
        message: 'This response is NOT cached',
        generated_at: new Date().toISOString(),
        random_data: Math.random()
    });
});

// Cache management endpoints
app.get('/api/cache/clear', (req, res) => {
    apicache.clear();
    res.json({ message: 'Cache cleared successfully' });
});

app.get('/api/cache/status', (req, res) => {
    res.json({
        performance: apicache.getPerformance(),
        index: apicache.getIndex()
    });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, '0.0.0.0', () => {
    console.log('🚀 LOCQL Test Server started successfully!');
    console.log(`📡 Server running on port ${PORT}`);
    console.log('✅ apicache middleware enabled');
    console.log('🔗 Test endpoints:');
    console.log(`   • Health: http://localhost:${PORT}/health`);
    console.log(`   • Cached: http://localhost:${PORT}/api/cached/data`);
    console.log(`   • Uncached: http://localhost:${PORT}/api/uncached/data`);
    console.log(`   • Cache Status: http://localhost:${PORT}/api/cache/status`);
});
EOF

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:5000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the test application
CMD ["node", "test-apicache-server.js"]
