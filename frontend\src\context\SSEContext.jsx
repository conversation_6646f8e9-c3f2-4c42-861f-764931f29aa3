/**
 * SSE Context Provider for Somipem
 * Manages a single SSE connection shared across all components
 * Prevents multiple connections per user and connection limit issues
 */

import React, { createContext, useContext } from 'react';
import useSSENotifications from '../hooks/useSSENotifications';

// Create the SSE context
const SSEContext = createContext();

// SSE Provider component
export const SSEProvider = ({ children }) => {
  // Single SSE connection for the entire application
  const sseData = useSSENotifications({
    enableBrowserNotifications: true,
    enableAntNotifications: false, // Prevent duplicate notifications
    maxNotificationsInMemory: 50
  });

  return (
    <SSEContext.Provider value={sseData}>
      {children}
    </SSEContext.Provider>
  );
};

// Custom hook to use SSE context
export const useSSE = () => {
  const context = useContext(SSEContext);
  
  if (!context) {
    throw new Error('useSSE must be used within an SSEProvider');
  }
  
  return context;
};

export default SSEContext;
