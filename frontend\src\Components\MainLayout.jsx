"use client"

import { useState } from "react"
import { Link, useLocation, useNavigate } from "react-router-dom"
import {
  Layout,
  <PERSON>u,
  But<PERSON>,
  Statistic,
  Drawer,
  Tooltip,
  Badge,
  Avatar,
  Dropdown,
  Space,
  Divider,
  Typography,
  Image,
  notification
} from "antd"

import {
  UserOutlined,
  SettingOutlined,
  TeamOutlined,
  <PERSON>uFoldOutlined,
  MenuUnfoldOutlined,
  <PERSON>boardOutlined,
  <PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  CalendarOutlined,
  HomeOutlined,
  CloseOutlined,
  BellOutlined,
  LogoutOutlined,
  QuestionCircleOutlined,
  GlobalOutlined,
  ToolOutlined,
  AppstoreOutlined,
  MoonOutlined,
  SunOutlined,
  LockOutlined,
} from "@ant-design/icons"
import logoLight from "../assets/logo.jpg"
import logoDark from "../assets/logo_for_DarkMode.jpg"
import { Outlet } from "react-router-dom"
import { useTheme } from "../theme-context"
import { useAuth } from "../hooks/useAuth"
import { usePermission } from "../hooks/usePermission"
import PermissionNavLink from "./PermissionNavLink"
import { menuPermissions } from "../config/permissionConfig"
const { Header, Sider, Content, Footer } = Layout
const { Text, Title } = Typography
import SSENotificationBell from "./SSENotificationBell"
const MainLayout = ({
  currentDate = new Date().toLocaleDateString("fr-FR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  }),
}) => {
  const [collapsed, setCollapsed] = useState(false)
  const [broken, setBroken] = useState(false)
  const [notifications, setNotifications] = useState(3)
  const { darkMode, toggleDarkMode } = useTheme()
  const location = useLocation()
  const navigate = useNavigate()
  const { user, logout } = useAuth() // Add this line to get user data

  const isMobile = broken
  const sidebarWidth = 260
  const collapsedWidth = 80
  // Déterminer la clé de menu active en fonction de l'URL actuelle
  const getActiveMenuKey = () => {
    const path = location.pathname
    if (path.includes("/home")) return "1"
    if (path.includes("/production")) return "2"
    if (path === "/arrets") return "3-1"
    if (path === "/arrets-dashboard") return "3-2"
    if (path.includes("/arrets")) return "3"
    if (path.includes("/admin/users")) return "admin"
    if (path.includes("/profile")) return "/profile"
    return "1" // Par défaut
  }

  // Handle user menu actions
  const handleUserMenuClick = ({ key }) => {
    if (key === "1") {
      navigate("/profile")
    } else if (key === "3") {
      // Handle help action
    } else if (key === "4") {
      logout()
      navigate("/login")
    }
  }

  // Get permission checking functions
  const { hasPermission, hasRole } = usePermission();

  // Function to check if a menu item should be visible based on permissions
  const isMenuItemVisible = (item) => {
    if (!item) return false;

    // If no permissions or roles specified, show the item
    if (!item.permissions && !item.roles) return true;

    // Check permissions and roles
    return (
      (!item.permissions || hasPermission(item.permissions)) &&
      (!item.roles || hasRole(item.roles))
    );
  };

  // Éléments du menu principal with permission checks
  const menuItems = [
    // Dashboard
    {
      key: "1",
      icon: <HomeOutlined />,
      label: <PermissionNavLink to="/home" permissions={menuPermissions.dashboard.permissions}>
        Accueil
      </PermissionNavLink>,
      permissions: menuPermissions.dashboard.permissions,
    },

    // Production
    {
      key: "2",
      icon: <DashboardOutlined />,
      label: <PermissionNavLink to="/production" permissions={menuPermissions.production.permissions}>
        Production
      </PermissionNavLink>,
      permissions: menuPermissions.production.permissions,
    },    // Stops
    {
      key: "3",
      icon: <AlertOutlined />,
      label: "Arrêts",
      permissions: menuPermissions.stops.permissions,
      children: [
        {
          key: "3-1",
          label: <PermissionNavLink to="/arrets" permissions={menuPermissions.stops.permissions}>
            Arrêts (Classique)
          </PermissionNavLink>,
          permissions: menuPermissions.stops.permissions,
        },
        {
          key: "3-2",
          label: <PermissionNavLink to="/arrets-dashboard" permissions={menuPermissions.stops.permissions}>
            Tableau de Bord Modulaire
          </PermissionNavLink>,
          permissions: menuPermissions.stops.permissions,
        },
      ],
    },

    {
      type: "divider",
    },

    // Analytics group
    {
      key: "group-1",
      type: "group",
      label: "Analyses",
      children: [
        {
          key: "4",
          icon: <LineChartOutlined />,
          label: <PermissionNavLink to="/analytics" permissions={menuPermissions.analytics.permissions}>
            Analyses
          </PermissionNavLink>,
          permissions: menuPermissions.analytics.permissions,
        },
        {
          key: "5",
          icon: <BarChartOutlined />,
          label: <PermissionNavLink to="/reports" permissions={menuPermissions.reports.permissions}>
            Rapports
          </PermissionNavLink>,
          permissions: menuPermissions.reports.permissions,
        },
      ],
    },

    {
      type: "divider",
    },

    // Configuration group
    {
      key: "group-2",
      type: "group",
      label: "Configuration",
      children: [
        {
          key: "7",
          icon: <ToolOutlined />,
          label: <PermissionNavLink to="/maintenance" permissions={menuPermissions.maintenance.permissions}>
            Maintenance
          </PermissionNavLink>,
          permissions: menuPermissions.maintenance.permissions,
        },
        {
          key: "notifications",
          icon: <BellOutlined />,
          label: <PermissionNavLink to="/notifications" permissions={menuPermissions.notifications.permissions}>
            Notifications
          </PermissionNavLink>,
          permissions: menuPermissions.notifications.permissions,
        },
      ],
    },

    // Admin section
    {
      key: "admin",
      icon: <SettingOutlined />,
      label: "Administration",
      roles: menuPermissions.admin.roles,
      children: [
        {
          key: "/admin/users",
          icon: <TeamOutlined />,
          label: <PermissionNavLink to="/admin/users" permissions={['manage_users']} roles={['admin']}>
            Gestion des utilisateurs
          </PermissionNavLink>,
          permissions: ['manage_users'],
          roles: ['admin'],
        },
      ],
    },

    // User profile (always visible to authenticated users)
    {
      key: "/profile",
      icon: <UserOutlined />,
      label: <Link to="/profile">Mon profil</Link>,
    },

    // Permission test page (for development/testing)
    {
      key: "/permission-test",
      icon: <LockOutlined />,
      label: <Link to="/permission-test">Test des permissions</Link>,
    },
  ]

  // Filter menu items based on permissions
  .filter(item => {
    // Keep dividers and group headers
    if (item.type === 'divider' || item.type === 'group') {
      // For groups, filter their children
      if (item.type === 'group' && item.children) {
        item.children = item.children.filter(child => isMenuItemVisible(child));
        // Only keep the group if it has visible children
        return item.children.length > 0;
      }
      return true;
    }

    // Filter regular menu items
    return isMenuItemVisible(item);
  })

  // Menu déroulant pour les notifications


  // Menu déroulant pour le profil utilisateur
  const userMenu = {
    items: [
      {
        key: "1",
        label: "Mon profil",
        icon: <UserOutlined />,
      },
      {
        type: "divider",
      },
      {
        key: "3",
        label: "Aide",
        icon: <QuestionCircleOutlined />,
      },
      {
        key: "4",
        label: "Déconnexion",
        icon: <LogoutOutlined />,
        danger: true,
      },
    ],
    onClick: handleUserMenuClick,
  }

  // Style du menu
  const menuStyle = {
    borderRight: 0,
    padding: isMobile ? "8px 0" : "16px 0",
    fontSize: isMobile ? "14px" : "15px",
  }
  // Titre de la page en fonction de l'URL
  const getPageTitle = () => {
    const path = location.pathname
    if (path.includes("/home")) return "Tableau de Bord"
    if (path.includes("/production")) return "Production"
    if (path === "/arrets-dashboard") return "Tableau de Bord des Arrêts (Modulaire)"
    if (path.includes("/arrets")) return "Gestion des Arrêts"
    if (path.includes("/analytics")) return "Analyses"
    if (path.includes("/reports")) return "Rapports"
    if (path.includes("/settings")) return "Paramètres"
    if (path.includes("/maintenance")) return "Maintenance"
    if (path.includes("/admin/users")) return "Gestion des Utilisateurs"
    if (path.includes("/profile")) return "Mon Profil"
    return "Tableau de Bord"
  }

  return (
    <Layout style={{ minHeight: "100vh" }}>
      {/* Drawer pour mobile */}
      {isMobile && (
        <Drawer
          placement="left"
          closable={false}
          onClose={() => setCollapsed(true)}
          open={!collapsed}
          bodyStyle={{ padding: 0 }}
          width={sidebarWidth}
          style={{
            zIndex: 1001,
            position: "fixed",
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: 16,
              borderBottom: `1px solid ${darkMode ? "#303030" : "#f0f0f0"}`,
              height: "120px",
            }}
          >
            <div style={{ display: "flex", alignItems: "center", width: "100%", justifyContent: "center" }}>
              <Image
                src={darkMode ? logoDark : logoLight}
                alt="SOMIPEM Logo"
                preview={false}
                style={{
                  height: 100,
                  maxWidth: "90%",
                  objectFit: "contain",
                }}
              />
            </div>
            <Button
              icon={<CloseOutlined />}
              onClick={() => setCollapsed(true)}
              type="text"
              style={{ position: "absolute", right: 10, top: 10 }}
            />
          </div>
          <Menu
            theme={darkMode ? "dark" : "light"}
            mode="inline"
            items={menuItems}
            style={{ padding: "8px 0" }}
            defaultSelectedKeys={[getActiveMenuKey()]}
            selectedKeys={[getActiveMenuKey()]}
          />
          <Divider style={{ margin: "8px 0" }} />
          <div style={{ padding: "0 16px 16px" }}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Button icon={<GlobalOutlined />} block>
                Changer de langue
              </Button>
              <Button icon={<QuestionCircleOutlined />} block>
                Aide et support
              </Button>
              <Button icon={darkMode ? <SunOutlined /> : <MoonOutlined />} block onClick={toggleDarkMode}>
                {darkMode ? "Mode clair" : "Mode sombre"}
              </Button>
            </Space>
          </div>
        </Drawer>
      )}

      {/* Sidebar pour desktop */}
      {!isMobile && (
        <Sider
          collapsible
          collapsed={collapsed}
          trigger={null}
          breakpoint="lg"
          theme={darkMode ? "dark" : "light"}
          onBreakpoint={(broken) => {
            setBroken(broken)
            if (broken) setCollapsed(true)
          }}
          width={sidebarWidth}
          collapsedWidth={collapsedWidth}
          style={{
            overflow: "auto",
            height: "100vh",
            position: "fixed",
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 1001,
            boxShadow: darkMode ? "2px 0 8px rgba(0,0,0,0.2)" : "2px 0 8px rgba(0,0,0,0.06)",
          }}
        >
          <div
            className="logo"
            style={{
              padding: collapsed ? "16px 8px" : "24px 16px",
              transition: "all 0.3s",
              borderBottom: `1px solid ${darkMode ? "#303030" : "#f0f0f0"}`,
              display: "flex",
              alignItems: "center",
              justifyContent: collapsed ? "center" : "center",
              height: collapsed ? "120px" : "180px",
            }}
          >
            <Image
              src={darkMode ? logoDark : logoLight}
              alt="SOMIPEM Logo"
              preview={false}
              style={{
                height: collapsed ? 100 : 160,
                maxWidth: "100%",
                objectFit: "contain",
                transition: "all 0.3s",
              }}
            />
          </div>

          <Menu
            theme={darkMode ? "dark" : "light"}
            mode="inline"
            defaultSelectedKeys={[getActiveMenuKey()]}
            selectedKeys={[getActiveMenuKey()]}
            items={menuItems}
            inlineCollapsed={collapsed}
            style={menuStyle}
          />

          {!collapsed && (
            <>
              <Divider style={{ margin: "8px 0" }} />
              <div style={{ padding: "0 16px 16px" }}>
                <Space direction="vertical" style={{ width: "100%" }}>
                  <Button icon={<GlobalOutlined />} block>
                    Changer de langue
                  </Button>
                  <Button icon={<QuestionCircleOutlined />} block>
                    Aide et support
                  </Button>
                  <Button icon={darkMode ? <SunOutlined /> : <MoonOutlined />} block onClick={toggleDarkMode}>
                    {darkMode ? "Mode clair" : "Mode sombre"}
                  </Button>
                </Space>
              </div>
            </>
          )}
        </Sider>
      )}

      <Layout
        style={{
          marginLeft: !isMobile ? (collapsed ? collapsedWidth : sidebarWidth) : 0,
          transition: "margin 0.2s, padding 0.2s",
        }}
      >
        <Header
          style={{
            padding: "0 24px",
            background: darkMode ? "#1f1f1f" : "#fff",
            position: "sticky",
            top: 0,
            zIndex: 1000,
            boxShadow: darkMode ? "0 2px 8px rgba(0,0,0,0.2)" : "0 2px 8px rgba(0,0,0,0.06)",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            height: 64,
          }}
        >
          <div style={{ display: "flex", alignItems: "center" }}>
            <Button
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              type="text"
              style={{
                fontSize: 16,
                width: 48,
                height: 48,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            />

            {!isMobile && (
              <Title level={4} style={{ margin: 0, marginLeft: 16 }}>
                {getPageTitle()}
              </Title>
            )}
          </div>

          <Space size={16}>
            <Statistic
              className="header-date"
              value={currentDate}
              valueStyle={{
                fontSize: isMobile ? 12 : 14,
                fontWeight: 500,
                color: darkMode ? "rgba(255,255,255,0.65)" : "rgba(0,0,0,0.65)",
              }}
              prefix={<CalendarOutlined style={{ marginRight: 8 }} />}
            />

            <Space size={16}>
              <Tooltip title={darkMode ? "Passer en mode clair" : "Passer en mode sombre"}>
                <Button
                  type="text"
                  icon={darkMode ? <SunOutlined /> : <MoonOutlined />}
                  onClick={toggleDarkMode}
                  style={{
                    width: 40,
                    height: 40,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                />
              </Tooltip>
              <SSENotificationBell />


              <Dropdown menu={userMenu} trigger={["click"]} placement="bottomRight">
                <Button
                  type="text"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "0 8px",
                  }}
                >
                  <Space>
                    <Avatar icon={<UserOutlined />} style={{ backgroundColor: "#1890ff" }} />
                    {!isMobile && <Text>{user?.username || "Utilisateur"}</Text>}
                  </Space>
                </Button>
              </Dropdown>
            </Space>
          </Space>
        </Header>

        <Content
          style={{
            margin: isMobile ? "16px 8px" : "24px 16px",
            padding: isMobile ? 16 : 24,
            minHeight: 280,
            background: darkMode ? "#141414" : "#fff",
            borderRadius: 8,
            position: "relative",
            boxShadow: darkMode ? "0 1px 4px rgba(0,0,0,0.15)" : "0 1px 4px rgba(0,0,0,0.05)",
          }}
        >
          {/* Overlay pour capturer les clics sur mobile */}
          {isMobile && !collapsed && (
            <div
              style={{
                position: "fixed",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: "rgba(0,0,0,0.3)",
                zIndex: 999,
                cursor: "pointer",
              }}
              onClick={() => setCollapsed(true)}
            />
          )}

          <Outlet />
        </Content>

        <Footer
          style={{
            textAlign: "center",
            padding: isMobile ? "12px 8px" : "16px 24px",
            background: "transparent",
          }}
        >
          <Text type="secondary">SOMIPEM ©{new Date().getFullYear()} Caps and Preforms</Text>
        </Footer>
      </Layout>
    </Layout>
  )
}

export default MainLayout
