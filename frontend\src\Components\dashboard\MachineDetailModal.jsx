/**
 * MachineDetailModal Component
 * Modal for displaying machine session history and details
 */
import React from 'react';
import { Modal, Tabs, Table, Button, Empty, Row, Col, Card, Typography, Statistic, Divider, Tag, Badge } from 'antd';
import {
  LineChartOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  Clock<PERSON>ircleOutlined,
  DashboardOutlined,
  HistoryOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { Bar, Line } from 'react-chartjs-2';
import { getBaseChartOptions } from '../chart-config.jsx';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

/**
 * MachineDetailModal component - Displays machine session history and details
 * @param {boolean} visible - Whether the modal is visible
 * @param {Object} machine - The selected machine
 * @param {Array} machineHistory - History data for the machine
 * @param {boolean} loading - Whether the history data is loading
 * @param {string} error - Error message if any
 * @param {Function} onClose - Function to call when closing the modal
 * @param {Function} onRefresh - Function to call when refreshing the data
 * @param {boolean} darkMode - Whether dark mode is enabled
 */
const MachineDetailModal = ({
  visible,
  machine,
  machineHistory,
  loading,
  error,
  onClose,
  onRefresh,
  darkMode
}) => {
  if (!machine) return null;

  // Define columns for the history table
  const historyColumns = [
    {
      title: 'Start Time',
      dataIndex: 'session_start',
      key: 'session_start',
      render: (text) => new Date(text).toLocaleString(),
      sorter: (a, b) => new Date(b.session_start) - new Date(a.session_start),
    },
    {
      title: 'End Time',
      dataIndex: 'session_end',
      key: 'session_end',
      render: (text) => text ? new Date(text).toLocaleString() : <Tag color="processing">Active</Tag>,
    },
    {
      title: 'Duration',
      key: 'duration',
      render: (_, record) => {
        const start = new Date(record.session_start);
        const end = record.session_end ? new Date(record.session_end) : new Date();
        const durationMs = end - start;
        const hours = Math.floor(durationMs / 3600000);
        const minutes = Math.floor((durationMs % 3600000) / 60000);
        return `${hours}h ${minutes}m`;
      },
    },
    {
      title: 'TRS',
      dataIndex: 'TRS',
      key: 'TRS',
      render: (text) => {
        const value = parseFloat(text || 0);
        let color = 'red';
        if (value > 80) color = 'green';
        else if (value > 60) color = 'orange';

        return <span style={{ color }}>{value.toFixed(1)}%</span>;
      },
      sorter: (a, b) => parseFloat(a.TRS || 0) - parseFloat(b.TRS || 0),
    },
    {
      title: 'Production',
      dataIndex: 'Quantite_Bon',
      key: 'Quantite_Bon',
      sorter: (a, b) => parseFloat(a.Quantite_Bon || 0) - parseFloat(b.Quantite_Bon || 0),
    },
    {
      title: 'Rejects',
      dataIndex: 'Quantite_Rejet',
      key: 'Quantite_Rejet',
      sorter: (a, b) => parseFloat(a.Quantite_Rejet || 0) - parseFloat(b.Quantite_Rejet || 0),
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => (
        <Tag color={record.session_end ? 'default' : 'processing'}>
          {record.session_end ? 'Completed' : 'Active'}
        </Tag>
      ),
    },
  ];

  // Get chart options based on dark mode
  const baseOptions = getBaseChartOptions(darkMode);

  // Get current shift
  const getCurrentShift = () => {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 14) return 'morning';
    if (hour >= 14 && hour < 22) return 'afternoon';
    return 'night';
  };

  // Check if a session is in the current shift
  const isSessionInShift = (session, shift) => {
    if (shift === 'all') return true;

    const sessionDate = new Date(session.session_start);
    const hour = sessionDate.getHours();

    if (shift === 'morning' && hour >= 6 && hour < 14) return true;
    if (shift === 'afternoon' && hour >= 14 && hour < 22) return true;
    if (shift === 'night' && (hour >= 22 || hour < 6)) return true;

    return false;
  };

  // Helper functions for French number formatting (copied from enhanced-machine-card.jsx)
  const safeParseFloat = (value) => {
    if (value === undefined || value === null || value === "") return 0;
    const sanitized = String(value).replace(/,/g, ".");
    const parsed = Number.parseFloat(sanitized);
    return isNaN(parsed) ? 0 : parsed;
  };
  const formatDecimal = (value, decimals = 1) => {
    if (value === undefined || value === null || value === "") return "0";
    const num = safeParseFloat(value);
    return num.toFixed(decimals).replace(/\./g, ",");
  };
  const formatThousands = (value) => {
    if (value === undefined || value === null || value === "") return "0";
    const num = safeParseFloat(value);
    return num.toLocaleString('en-US').replace(/,/g, ".");
  };

  // Render modal content based on loading and error states
  const renderModalContent = () => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <div className="ant-spin ant-spin-lg ant-spin-spinning">
            <span className="ant-spin-dot ant-spin-dot-spin">
              <i className="ant-spin-dot-item"></i>
              <i className="ant-spin-dot-item"></i>
              <i className="ant-spin-dot-item"></i>
              <i className="ant-spin-dot-item"></i>
            </span>
          </div>
          <div style={{ marginTop: 16 }}>Chargement de l'historique...</div>
        </div>
      );
    }

    if (error) {
      return (
        <Empty
          description={
            <>
              <p>Erreur de chargement: {error}</p>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={onRefresh}
              >
                Réessayer
              </Button>
            </>
          }
        />
      );
    }

    if (!machineHistory || machineHistory.length === 0) {
      return (
        <Empty
          description={
            <>
              <p>Aucune session trouvée pour cette machine</p>
              <p>La table machine_sessions est vide ou aucune donnée n'est disponible</p>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={onRefresh}
              >
                Rafraîchir
              </Button>
            </>
          }
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    // Prepare data for history charts
    const historyTrsChartData = {
      labels: machineHistory.map((h) => {
        const date = new Date(h.session_start);
        return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
      }),
      datasets: [
        {
          label: "TRS (%)",
          data: machineHistory.map((h) => parseFloat(h.TRS) || 0),
          backgroundColor: "rgba(153, 102, 255, 0.2)",
          borderColor: "rgba(153, 102, 255, 1)",
          borderWidth: 2,
          fill: true,
        },
      ],
    };

    const historyProductionChartData = {
      labels: machineHistory.map((h) => {
        const date = new Date(h.session_start);
        return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
      }),
      datasets: [
        {
          label: "Good Production",
          data: machineHistory.map((h) => parseFloat(h.Quantite_Bon) || 0),
          backgroundColor: "rgba(75, 192, 192, 0.6)",
          borderColor: "rgba(75, 192, 192, 1)",
          borderWidth: 1,
        },
      ],
    };

    const historyRejectChartData = {
      labels: machineHistory.map((h) => {
        const date = new Date(h.session_start);
        return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
      }),
      datasets: [
        {
          label: "Rejected Production",
          data: machineHistory.map((h) => parseFloat(h.Quantite_Rejet) || 0),
          backgroundColor: "rgba(255, 99, 132, 0.6)",
          borderColor: "rgba(255, 99, 132, 1)",
          borderWidth: 1,
        },
      ],
    };

    const historyDurationChartData = {
      labels: machineHistory.map((h) => {
        const date = new Date(h.session_start);
        return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
      }),
      datasets: [
        {
          label: "Session Duration (min)",
          data: machineHistory.map((h) => {
            const start = new Date(h.session_start);
            const end = h.session_end ? new Date(h.session_end) : new Date();
            return Math.round((end - start) / 60000); // Convert ms to minutes
          }),
          backgroundColor: "rgba(255, 159, 64, 0.6)",
          borderColor: "rgba(255, 159, 64, 1)",
          borderWidth: 1,
        },
      ],
    };

    return (
      <Tabs defaultActiveKey="1" className={darkMode ? "dark-mode" : ""}>
        <TabPane tab="Sessions" key="1">
          <Table
            columns={historyColumns}
            dataSource={machineHistory.map((item, index) => ({ ...item, key: index }))}
            pagination={{ pageSize: 5 }}
            scroll={{ x: true }}
          />
        </TabPane>
        <TabPane tab="Charts" key="2">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <div className="chart-container">
                <h3 className="chart-title">
                  <LineChartOutlined /> TRS (%)
                </h3>
                <div style={{ height: 200 }}>
                  <Line
                    data={historyTrsChartData}
                    options={{
                      ...baseOptions,
                      scales: {
                        ...baseOptions.scales,
                        y: {
                          ...baseOptions.scales.y,
                          beginAtZero: true,
                          max: 100,
                          grid: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                        x: {
                          ...baseOptions.scales.x,
                          grid: {
                            display: false,
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                      plugins: {
                        ...baseOptions.plugins,
                        legend: {
                          ...baseOptions.plugins.legend,
                          labels: {
                            ...baseOptions.plugins.legend.labels,
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="chart-container">
                <h3 className="chart-title">
                  <CheckCircleOutlined /> Production (pcs)
                </h3>
                <div style={{ height: 200 }}>
                  <Bar
                    data={historyProductionChartData}
                    options={{
                      ...baseOptions,
                      scales: {
                        ...baseOptions.scales,
                        y: {
                          ...baseOptions.scales.y,
                          beginAtZero: true,
                          grid: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                        x: {
                          ...baseOptions.scales.x,
                          grid: {
                            display: false,
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                      plugins: {
                        ...baseOptions.plugins,
                        legend: {
                          ...baseOptions.plugins.legend,
                          labels: {
                            ...baseOptions.plugins.legend.labels,
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="chart-container">
                <h3 className="chart-title">
                  <CloseCircleOutlined /> Rejects (pcs)
                </h3>
                <div style={{ height: 200 }}>
                  <Bar
                    data={historyRejectChartData}
                    options={{
                      ...baseOptions,
                      scales: {
                        ...baseOptions.scales,
                        y: {
                          ...baseOptions.scales.y,
                          beginAtZero: true,
                          grid: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                        x: {
                          ...baseOptions.scales.x,
                          grid: {
                            display: false,
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                      plugins: {
                        ...baseOptions.plugins,
                        legend: {
                          ...baseOptions.plugins.legend,
                          labels: {
                            ...baseOptions.plugins.legend.labels,
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="chart-container">
                <h3 className="chart-title">
                  <ClockCircleOutlined /> Session Duration (min)
                </h3>
                <div style={{ height: 200 }}>
                  <Bar
                    data={historyDurationChartData}
                    options={{
                      ...baseOptions,
                      scales: {
                        ...baseOptions.scales,
                        y: {
                          ...baseOptions.scales.y,
                          beginAtZero: true,
                          grid: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                        x: {
                          ...baseOptions.scales.x,
                          grid: {
                            display: false,
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                      plugins: {
                        ...baseOptions.plugins,
                        legend: {
                          ...baseOptions.plugins.legend,
                          labels: {
                            ...baseOptions.plugins.legend.labels,
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </TabPane>
        <TabPane
          tab={
            <span>
              <InfoCircleOutlined style={{ marginRight: 8 }} />
              Informations
            </span>
          }
          key="3"
        >
          <div style={{ padding: "16px 0" }}>
            <Row gutter={[24, 24]}>
              {/* Machine Info Card */}
              <Col xs={24} md={12}>
                <Card
                  title={
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <DashboardOutlined style={{ color: "#1890ff", marginRight: 8 }} />
                      <span>Détails de la machine</span>
                    </div>
                  }
                  bordered
                  style={{ height: "100%" }}
                >
                  <div style={{ display: "flex", alignItems: "center", marginBottom: 16 }}>
                    <div
                      style={{
                        width: 64,
                        height: 64,
                        borderRadius: 8,
                        background: "rgba(24, 144, 255, 0.1)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        marginRight: 16,
                      }}
                    >
                      <DashboardOutlined style={{ fontSize: 32, color: "#1890ff" }} />
                    </div>
                    <div>
                      <Title level={4} style={{ margin: 0 }}>
                        {machine.Machine_Name}
                      </Title>
                      <Text type="secondary">
                        {machineHistory.length > 0 && machineHistory[0].Ordre_Fabrication
                          ? `OF : ${machineHistory[0].Ordre_Fabrication}`
                          : "Aucun ordre de fabrication"}
                      </Text>
                    </div>
                  </div>

                  <Divider style={{ margin: "16px 0" }} />

                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Statistic
                        title={<Text style={{ fontSize: 14 }}>Sessions {getCurrentShift() === 'morning' ? 'matin' : getCurrentShift() === 'afternoon' ? 'après-midi' : 'nuit'}</Text>}
                        value={formatThousands(machineHistory.filter(session => isSessionInShift(session, getCurrentShift())).length)}
                        prefix={<HistoryOutlined />}
                        valueStyle={{ color: "#1890ff", fontSize: 20 }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title={<Text style={{ fontSize: 14 }}>Sessions actives {getCurrentShift() === 'morning' ? 'matin' : getCurrentShift() === 'afternoon' ? 'après-midi' : 'nuit'}</Text>}
                        value={formatThousands(machineHistory.filter(s => !s.session_end && isSessionInShift(s, getCurrentShift())).length)}
                        prefix={<ClockCircleOutlined />}
                        valueStyle={{
                          color: machineHistory.filter(s => !s.session_end && isSessionInShift(s, getCurrentShift())).length > 0 ? "#52c41a" : "#8c8c8c",
                          fontSize: 20,
                        }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>

              {/* Session Info Card */}
              <Col xs={24} md={12}>
                <Card
                  title={
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <HistoryOutlined style={{ color: "#1890ff", marginRight: 8 }} />
                      <span>Historique des sessions</span>
                    </div>
                  }
                  bordered
                  style={{ height: "100%" }}
                >
                  {machineHistory.length > 0 ? (
                    <>
                      <div style={{ marginBottom: 16 }}>
                        <Text strong>Dernière session :</Text>
                        <div
                          style={{
                            background: "rgba(0,0,0,0.02)",
                            padding: "12px",
                            borderRadius: "8px",
                            marginTop: "8px",
                          }}
                        >
                          <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 8 }}>
                            <Text>Début :</Text>
                            <Text strong>{machineHistory[0].session_start ? new Date(machineHistory[0].session_start).toLocaleString('fr-FR') : ''}</Text>
                          </div>
                          <div style={{ display: "flex", justifyContent: "space-between" }}>
                            <Text>Fin :</Text>
                            <Text strong>
                              {machineHistory[0].session_end ? (
                                new Date(machineHistory[0].session_end).toLocaleString('fr-FR')
                              ) : (
                                <Tag color="processing">En cours</Tag>
                              )}
                            </Text>
                          </div>
                        </div>
                      </div>

                      <Divider style={{ margin: "16px 0" }} />

                      <Row gutter={[16, 16]}>
                        <Col span={8}>
                          <Statistic
                            title={<Text style={{ fontSize: 14 }}>TRS moyen</Text>}
                            value={(() => {
                              const trsValues = machineHistory
                                .map((s) => safeParseFloat(s.TRS || 0))
                                .filter((v) => !isNaN(v));
                              return trsValues.length
                                ? formatDecimal(trsValues.reduce((a, b) => a + b, 0) / trsValues.length)
                                : "N/A";
                            })()}
                            suffix="%"
                            valueStyle={{ fontSize: 18 }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title={<Text style={{ fontSize: 14 }}>Bons produits</Text>}
                            value={formatThousands(machineHistory.reduce((sum, s) => sum + safeParseFloat(s.Quantite_Bon || 0), 0))}
                            valueStyle={{ color: "#52c41a", fontSize: 18 }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title={<Text style={{ fontSize: 14 }}>Produits rejetés</Text>}
                            value={formatThousands(machineHistory.reduce((sum, s) => sum + safeParseFloat(s.Quantite_Rejet || 0), 0))}
                            valueStyle={{ color: "#ff4d4f", fontSize: 18 }}
                          />
                        </Col>
                      </Row>
                    </>
                  ) : (
                    <Empty description="Aucune donnée de session disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </Card>
              </Col>

              {/* Performance Metrics */}
              <Col xs={24}>
                <Card
                  title={
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <LineChartOutlined style={{ color: "#1890ff", marginRight: 8 }} />
                      <span>Métriques de performance</span>
                    </div>
                  }
                  bordered
                >
                  {machineHistory.length > 0 ? (
                    <Row gutter={[24, 24]}>
                      <Col xs={24} md={8}>
                        <Card style={{ background: "rgba(0,0,0,0.02)" }}>
                          <Statistic
                            title="Durée moyenne des sessions"
                            value={(() => {
                              const durations = machineHistory.map((s) => {
                                const start = new Date(s.session_start);
                                const end = s.session_end ? new Date(s.session_end) : new Date();
                                return end - start;
                              });
                              const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
                              const hours = Math.floor(avgDuration / 3600000);
                              const minutes = Math.floor((avgDuration % 3600000) / 60000);
                              return `${hours}h ${minutes}m`;
                            })()}
                            prefix={<ClockCircleOutlined />}
                          />
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card style={{ background: "rgba(0,0,0,0.02)" }}>
                          <Statistic
                            title="Taux de rejet moyen"
                            value={(() => {
                              const totalBon = machineHistory.reduce(
                                (sum, s) => sum + safeParseFloat(s.Quantite_Bon || 0),
                                0
                              );
                              const totalRejet = machineHistory.reduce(
                                (sum, s) => sum + safeParseFloat(s.Quantite_Rejet || 0),
                                0
                              );
                              return totalBon + totalRejet > 0
                                ? formatDecimal((totalRejet / (totalBon + totalRejet)) * 100)
                                : "0,0";
                            })()}
                            suffix="%"
                            prefix={<CloseCircleOutlined />}
                            valueStyle={{ color: "#ff4d4f" }}
                          />
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card style={{ background: "rgba(0,0,0,0.02)" }}>
                          <Statistic
                            title="Production totale"
                            value={formatThousands(machineHistory.reduce((sum, s) => sum + safeParseFloat(s.Quantite_Bon || 0), 0))}
                            prefix={<CheckCircleOutlined />}
                            valueStyle={{ color: "#52c41a" }}
                          />
                        </Card>
                      </Col>
                    </Row>
                  ) : (
                    <Empty description="Aucune donnée de performance disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </Card>
              </Col>
            </Row>
          </div>
        </TabPane>
      </Tabs>
    );
  };

  return (
    <Modal
      title={`Sessions de ${machine.Machine_Name}`}
      open={visible}
      width={800}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          Fermer
        </Button>,
        <Button key="allSessions" type="primary" onClick={() => window.open("/sessions-report", "_blank")}>
          Voir toutes les sessions
        </Button>,
      ]}
      destroyOnClose
    >
      {renderModalContent()}
    </Modal>
  );
};

export default MachineDetailModal;
