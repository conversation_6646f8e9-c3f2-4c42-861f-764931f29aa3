#!/bin/bash

# LOCQL Docker + ngrok Startup Script
# This script ensures ngrok tunnel is active before starting Docker containers

echo "🚀 LOCQL Docker + ngrok Startup"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NGROK_DOMAIN="charming-hermit-intense.ngrok-free.app"
NGROK_HTTP_URL="https://${NGROK_DOMAIN}"
NGROK_WS_URL="wss://${NGROK_DOMAIN}"
BACKEND_PORT=5000
FRONTEND_PORT=5173

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if ngrok tunnel is active
check_ngrok_tunnel() {
    print_info "Checking ngrok tunnel status..."
    
    # Test HTTP endpoint
    if curl -s --max-time 10 "${NGROK_HTTP_URL}/api/health/ping" > /dev/null 2>&1; then
        print_status 0 "ngrok HTTP tunnel is active: ${NGROK_HTTP_URL}"
        return 0
    else
        print_status 1 "ngrok HTTP tunnel is not accessible: ${NGROK_HTTP_URL}"
        return 1
    fi
}

# Check if Docker is running
check_docker() {
    print_info "Checking Docker status..."
    if docker --version > /dev/null 2>&1 && docker ps > /dev/null 2>&1; then
        print_status 0 "Docker is running"
        return 0
    else
        print_status 1 "Docker is not running or accessible"
        return 1
    fi
}

# Check if MySQL is accessible
check_mysql() {
    print_info "Checking MySQL connectivity..."
    if command -v mysql &> /dev/null; then
        if mysql -h localhost -u root -proot -e "SELECT 1;" > /dev/null 2>&1; then
            print_status 0 "MySQL is accessible"
            return 0
        else
            print_status 1 "MySQL connection failed"
            return 1
        fi
    else
        print_warning "MySQL client not found, skipping database check"
        return 0
    fi
}

# Check if ports are available
check_ports() {
    print_info "Checking port availability..."
    
    local ports_ok=0
    
    if ! netstat -tuln 2>/dev/null | grep -q ":${BACKEND_PORT} "; then
        print_status 0 "Port ${BACKEND_PORT} (backend) is available"
    else
        print_warning "Port ${BACKEND_PORT} is already in use"
        ports_ok=1
    fi
    
    if ! netstat -tuln 2>/dev/null | grep -q ":${FRONTEND_PORT} "; then
        print_status 0 "Port ${FRONTEND_PORT} (frontend) is available"
    else
        print_warning "Port ${FRONTEND_PORT} is already in use"
        ports_ok=1
    fi
    
    return $ports_ok
}

# Start Docker containers
start_containers() {
    print_info "Starting Docker containers..."
    
    # Stop any existing containers
    docker-compose -f docker-compose.app.yml down > /dev/null 2>&1
    
    # Start containers
    if docker-compose -f docker-compose.app.yml up --build -d; then
        print_status 0 "Docker containers started successfully"
        return 0
    else
        print_status 1 "Failed to start Docker containers"
        return 1
    fi
}

# Wait for services to be ready
wait_for_services() {
    print_info "Waiting for services to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:${BACKEND_PORT}/api/health/ping > /dev/null 2>&1; then
            print_status 0 "Backend service is ready"
            break
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_status 1 "Backend service failed to start within timeout"
        return 1
    fi
    
    # Check frontend
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:${FRONTEND_PORT}/ > /dev/null 2>&1; then
            print_status 0 "Frontend service is ready"
            break
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_status 1 "Frontend service failed to start within timeout"
        return 1
    fi
    
    return 0
}

# Main execution
main() {
    echo ""
    print_info "Starting pre-flight checks..."
    
    # Check Docker
    if ! check_docker; then
        echo ""
        print_warning "Please start Docker Desktop and try again"
        exit 1
    fi
    
    # Check MySQL
    check_mysql
    
    # Check ports
    check_ports
    
    # Check ngrok tunnel
    if ! check_ngrok_tunnel; then
        echo ""
        print_warning "ngrok tunnel is not active. Please start ngrok tunnel:"
        print_info "ngrok http ${BACKEND_PORT} --domain=${NGROK_DOMAIN}"
        echo ""
        read -p "Press Enter after starting ngrok tunnel, or Ctrl+C to exit..."
        
        # Re-check tunnel
        if ! check_ngrok_tunnel; then
            print_status 1 "ngrok tunnel is still not accessible"
            exit 1
        fi
    fi
    
    echo ""
    print_info "All checks passed! Starting containers..."
    
    # Start containers
    if ! start_containers; then
        exit 1
    fi
    
    # Wait for services
    if ! wait_for_services; then
        print_warning "Some services may not be fully ready"
    fi
    
    echo ""
    print_status 0 "LOCQL application is running!"
    echo ""
    print_info "Access URLs:"
    echo "  • Frontend (Local):  http://localhost:${FRONTEND_PORT}"
    echo "  • Backend (Local):   http://localhost:${BACKEND_PORT}"
    echo "  • External (ngrok):  ${NGROK_HTTP_URL}"
    echo "  • WebSocket (ngrok): ${NGROK_WS_URL}/api/machine-data-ws"
    echo ""
    print_info "To stop the application:"
    echo "  docker-compose -f docker-compose.app.yml down"
    echo ""
    print_info "To view logs:"
    echo "  docker-compose -f docker-compose.app.yml logs -f"
}

# Run main function
main "$@"
