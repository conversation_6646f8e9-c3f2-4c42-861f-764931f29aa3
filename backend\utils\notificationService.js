import db from "../db.js"

/**
 * Service for creating and managing notifications
 */
class NotificationService {
  /**
   * Create a new notification
   * @param {Object} notification - The notification object
   * @param {string} notification.title - Notification title
   * @param {string} notification.message - Notification message
   * @param {string} notification.category - Category (alert, maintenance, update, info, machine_alert, production, quality)
   * @param {string} notification.priority - Priority (low, medium, high, critical)
   * @param {string} notification.severity - Severity (info, warning, error, critical)
   * @param {string} notification.source - Source of notification (system, machine_monitoring, user, etc.)
   * @param {number|null} notification.machine_id - Machine ID (for machine-related notifications)
   * @param {number|null} notification.userId - User ID (null for all users)
   * @returns {Promise<Object>} - The created notification
   */
  static async createNotification(notification) {
    const {
      title,
      message,
      category,
      priority = 'medium',
      severity = 'info',
      source = 'system',
      machine_id = null,
      userId
    } = notification

    // Validate category
    const validCategories = ['alert', 'maintenance', 'update', 'info', 'machine_alert', 'production', 'quality'];
    if (!validCategories.includes(category)) {
      throw new Error(`Invalid category: ${category}`);
    }

    // Validate priority
    const validPriorities = ['low', 'medium', 'high', 'critical'];
    if (!validPriorities.includes(priority)) {
      throw new Error(`Invalid priority: ${priority}`);
    }

    // Validate severity
    const validSeverities = ['info', 'warning', 'error', 'critical'];
    if (!validSeverities.includes(severity)) {
      throw new Error(`Invalid severity: ${severity}`);
    }

    console.log(`🔔 Creating notification: ${title} (${priority})`);

    const insertQuery = `INSERT INTO notifications
       (title, message, category, priority, severity, source, machine_id, user_id, timestamp, \`read\`)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), false)`;
    
    const insertParams = [title, message, category, priority, severity, source, machine_id, userId || null];
    
    console.log(`🔍 Executing insert query with params:`, insertParams);    try {
      // Use async/await with the promise-based database connection
      const [result] = await db.execute(insertQuery, insertParams);
      const notificationId = result.insertId;
      console.log(`✅ Notification inserted with ID: ${notificationId}`);

      // Get the created notification
      const selectQuery = "SELECT * FROM notifications WHERE id = ?";
      console.log(`🔍 Executing select query for notification ID: ${notificationId}`);
        const [selectResult] = await db.execute(selectQuery, [notificationId]);
      
      if (selectResult.length === 0) {
        console.error(`❌ Notification not found after creation: ${notificationId}`);
        throw new Error("Notification not found");
      }

      console.log(`✅ Notification retrieved successfully:`, selectResult[0]);
      return selectResult[0];
      
    } catch (error) {
      console.error("❌ Database error in createNotification:", error);
      throw error;
    }
  }

  /**
   * Create a system notification for all users
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {string} category - Category (alert, maintenance, update, info)
   * @returns {Promise<Object>} - The created notification
   */
  static async createSystemNotification(title, message, category = "info") {
    return this.createNotification({
      title,
      message,
      category,
      userId: null, // null means for all users
    })
  }

  /**
   * Create a maintenance notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @returns {Promise<Object>} - The created notification
   */
  static async createMaintenanceNotification(title, message) {
    return this.createSystemNotification(title, message, "maintenance")
  }

  /**
   * Create an alert notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @returns {Promise<Object>} - The created notification
   */
  static async createAlertNotification(title, message) {
    return this.createSystemNotification(title, message, "alert")
  }

  /**
   * Create an update notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @returns {Promise<Object>} - The created notification
   */
  static async createUpdateNotification(title, message) {
    return this.createSystemNotification(title, message, "update")
  }

  /**
   * Create a machine alert notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @param {number} machineId - Machine ID
   * @returns {Promise<Object>} - The created notification
   */
  static async createMachineAlertNotification(title, message, priority = 'high', machineId = null) {
    return this.createNotification({
      title,
      message,
      category: 'machine_alert',
      priority,
      severity: priority === 'critical' ? 'critical' : 'warning',
      source: 'machine_monitoring',
      machine_id: machineId,
      userId: null // Broadcast to all users
    })
  }

  /**
   * Create a production notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<Object>} - The created notification
   */
  static async createProductionNotification(title, message, priority = 'medium') {
    return this.createNotification({
      title,
      message,
      category: 'production',
      priority,
      severity: priority === 'critical' ? 'critical' : 'info',
      source: 'production_monitoring',
      userId: null // Broadcast to all users
    })
  }

  /**
   * Create a quality alert notification
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @param {number} machineId - Machine ID
   * @returns {Promise<Object>} - The created notification
   */
  static async createQualityAlertNotification(title, message, priority = 'high', machineId = null) {
    return this.createNotification({
      title,
      message,
      category: 'quality',
      priority,
      severity: priority === 'critical' ? 'critical' : 'warning',
      source: 'quality_monitoring',
      machine_id: machineId,
      userId: null // Broadcast to all users
    })
  }

  /**
   * Acknowledge a notification
   * @param {number} notificationId - Notification ID
   * @param {number} userId - User ID who acknowledged
   * @returns {Promise<boolean>} - Success status
   */  static async acknowledgeNotification(notificationId, userId) {
    try {
      const [result] = await db.execute(
        "UPDATE notifications SET acknowledged = true, acknowledged_by = ?, acknowledged_at = NOW() WHERE id = ?",
        [userId, notificationId]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error("Database error:", error);
      throw error;
    }
  }
}

export default NotificationService

