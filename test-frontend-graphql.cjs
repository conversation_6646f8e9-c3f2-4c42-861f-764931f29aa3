// Test frontend GraphQL integration
// This script tests if the frontend can properly call the GraphQL interface

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// Frontend GraphQL URL (through Vite dev server proxy)
const FRONTEND_URL = 'http://localhost:5173/api/graphql';

// Test the same queries that the frontend uses
const frontendQueries = {
  essentialData: `
    query($filters: StopFilterInput) {
      getStopSidecards(filters: $filters) {
        Arret_Totale
        Arret_Totale_nondeclare
      }
    }
  `,
  
  chartData: `
    query($filters: StopFilterInput) {
      getTop5Stops(filters: $filters) {
        stopName
        count
      }
    }
  `,
  
  machineComparison: `
    query($filters: StopFilterInput) {
      getMachineStopComparison(filters: $filters) {
        Machine_Name
        stops
        totalDuration
      }
    }
  `,
  
  tableData: `
    query($filters: StopFilterInput) {
      getAllMachineStops(filters: $filters) {
        Date_Insert
        Machine_Name
        Code_Stop
        Debut_Stop
        Fin_Stop_Time
        Regleur_Prenom
        duration_minutes
      }
    }
  `,
  
  machineModels: `
    query {
      getStopMachineModels {
        model
      }
    }
  `,
  
  machineNames: `
    query {
      getStopMachineNames {
        Machine_Name
      }
    }
  `
};

async function testFrontendGraphQL(query, variables = {}) {
  try {
    console.log(`\n🔍 Testing frontend GraphQL endpoint...`);
    console.log('Variables:', variables);
    
    const response = await fetch(FRONTEND_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, variables })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return null;
    }
    
    console.log('✅ Success! Data received:');
    console.log(JSON.stringify(result.data, null, 2));
    
    return result.data;
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return null;
  }
}

async function runFrontendTests() {
  console.log('🚀 Testing frontend GraphQL integration...\n');
  
  // Test machine models first
  console.log('=== Testing Machine Models ===');
  const machineModels = await testFrontendGraphQL(frontendQueries.machineModels);
  
  // Test machine names
  console.log('\n=== Testing Machine Names ===');
  const machineNames = await testFrontendGraphQL(frontendQueries.machineNames);
  
  // Test with IPS filter (default frontend filter)
  const ipsFilter = { model: 'IPS' };
  
  // Test essential data (sidecards)
  console.log('\n=== Testing Essential Data (Sidecards) ===');
  const essentialData = await testFrontendGraphQL(frontendQueries.essentialData, {
    filters: ipsFilter
  });
  
  // Test chart data (top stops)
  console.log('\n=== Testing Chart Data (Top Stops) ===');
  const chartData = await testFrontendGraphQL(frontendQueries.chartData, {
    filters: ipsFilter
  });
  
  // Test machine comparison
  console.log('\n=== Testing Machine Comparison ===');
  const machineComparison = await testFrontendGraphQL(frontendQueries.machineComparison, {
    filters: ipsFilter
  });
  
  // Test table data (limited)
  console.log('\n=== Testing Table Data (Limited) ===');
  const tableData = await testFrontendGraphQL(frontendQueries.tableData, {
    filters: { ...ipsFilter, limit: 5 }
  });
  
  // Test with specific machine if we have data
  if (machineNames && machineNames.getStopMachineNames && machineNames.getStopMachineNames.length > 0) {
    const ipsMachine = machineNames.getStopMachineNames.find(m => m.Machine_Name.startsWith('IPS'));
    if (ipsMachine) {
      console.log(`\n=== Testing with Specific Machine: ${ipsMachine.Machine_Name} ===`);
      
      const specificFilter = { model: 'IPS', machine: ipsMachine.Machine_Name };
      
      const specificEssentialData = await testFrontendGraphQL(frontendQueries.essentialData, {
        filters: specificFilter
      });
      
      const specificChartData = await testFrontendGraphQL(frontendQueries.chartData, {
        filters: specificFilter
      });
    }
  }
  
  console.log('\n📊 Frontend Test Summary:');
  console.log('- Machine Models:', machineModels ? '✅ Success' : '❌ Failed');
  console.log('- Machine Names:', machineNames ? '✅ Success' : '❌ Failed');
  console.log('- Essential Data:', essentialData ? '✅ Success' : '❌ Failed');
  console.log('- Chart Data:', chartData ? '✅ Success' : '❌ Failed');
  console.log('- Machine Comparison:', machineComparison ? '✅ Success' : '❌ Failed');
  console.log('- Table Data:', tableData ? '✅ Success' : '❌ Failed');
  
  // Analysis
  if (chartData && chartData.getTop5Stops) {
    console.log('\n📈 Chart Data Analysis:');
    console.log('- Top stops count:', chartData.getTop5Stops.length);
    console.log('- First stop:', chartData.getTop5Stops[0]);
  }
  
  if (machineComparison && machineComparison.getMachineStopComparison) {
    console.log('\n📊 Machine Comparison Analysis:');
    console.log('- Machines count:', machineComparison.getMachineStopComparison.length);
    console.log('- First machine:', machineComparison.getMachineStopComparison[0]);
  }
  
  if (essentialData && essentialData.getStopSidecards) {
    console.log('\n📋 Essential Data Analysis:');
    console.log('- Sidecards data:', essentialData.getStopSidecards);
  }
}

// Run the tests
runFrontendTests().catch(console.error);
