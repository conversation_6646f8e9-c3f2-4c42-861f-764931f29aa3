# Database Configuration for Docker with Pomerium
# Use host.docker.internal to connect to host MySQL from containers
DB_HOST=host.docker.internal
DB_USER=root
DB_PASS=root
DB_NAME=Testingarea51
PORT=5000

# JWT Configuration
JWT_SECRET=dadfba2957b57678f0bdf5bb2f27b1938b9a0b472cd95a49a7f01397af5db005b9072f3d5b0a42268571b735187e2a7aedc532ae79797206fe5789718711d719
JWT_EXPIRE=8h
JWT_COOKIE_EXPIRE=1

# Cache Configuration
DISABLE_CACHE=false

# Email Configuration
SMTP_HOST=sandbox.smtp.mailtrap.io
SMTP_PORT=587
SMTP_EMAIL=b43b394d59fa77
SMTP_PASSWORD=60513ef904592c
FROM_NAME=Your DASHBOARD
FROM_EMAIL=<EMAIL>

# Frontend URL for Pomerium environment
FRONTEND_URL=https://locql.adapted-osprey-5307.pomerium.app

# Node Environment
NODE_ENV=development

# Pomerium Configuration
POMERIUM_FRONTEND_URL=https://locql.adapted-osprey-5307.pomerium.app
POMERIUM_API_URL=https://api.adapted-osprey-5307.pomerium.app
POMERIUM_WS_URL=wss://ws.adapted-osprey-5307.pomerium.app

# WebSocket Configuration for Pomerium
WS_EXTERNAL_URL=wss://ws.adapted-osprey-5307.pomerium.app

# CORS Origins for Pomerium
CORS_ORIGINS=https://locql.adapted-osprey-5307.pomerium.app,https://api.adapted-osprey-5307.pomerium.app,https://ws.adapted-osprey-5307.pomerium.app

# Pomerium Authentication Configuration
POMERIUM_ENABLED=true
POMERIUM_VERIFY_URL=https://verify.adapted-osprey-5307.pomerium.app
POMERIUM_AUDIENCE=adapted-osprey-5307.pomerium.app

# Session Configuration for Pomerium
SESSION_SECRET=your-session-secret-change-this-in-production-pomerium
SESSION_DOMAIN=.adapted-osprey-5307.pomerium.app
TRUST_PROXY=true

# Security Configuration
SECURE_COOKIES=true
SAME_SITE_COOKIES=lax

# Logging
LOG_LEVEL=debug
