// Debug script to log machine names structure
console.log('Debugging machine names structure');

// Mock fetch to simulate API response
global.fetch = async () => ({
  ok: true,
  json: async () => ({
    data: {
      getFinalStopMachineNames: [
        { Machine_Name: 'Machine1' },
        { Machine_Name: 'Machine2' },
        { Machine_Name: 'Machine3' }
      ]
    }
  })
});

// Log sample data structure
console.log('Sample machine names structure:');
console.log([
  { Machine_Name: 'Machine1' },
  { Machine_Name: 'Machine2' },
  { Machine_Name: 'Machine3' }
]);

// Compare with what's being rendered
console.log('How it should be rendered:');
const sampleMachines = [
  { Machine_Name: 'Machine1' },
  { Machine_Name: 'Machine2' },
  { Machine_Name: 'Machine3' }
];
console.log(sampleMachines.map(machine => machine.Machine_Name));
