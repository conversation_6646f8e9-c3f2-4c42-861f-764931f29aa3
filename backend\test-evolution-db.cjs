const db = require('./db.js');

console.log('Testing database connection...');

// Test query to get evolution data
const query = `
  SELECT 
    DATE(STR_TO_DATE(Date_Insert, '%d/%m/%Y')) as date_only,
    COUNT(*) as count 
  FROM machine_stop_table_mould 
  GROUP BY DATE(STR_TO_DATE(Date_Insert, '%d/%m/%Y')) 
  ORDER BY DATE(STR_TO_DATE(Date_Insert, '%d/%m/%Y')) DESC 
  LIMIT 10
`;

db.query(query, (err, results) => {
  if (err) {
    console.error('Error:', err);
  } else {
    console.log('Sample evolution data from database:');
    console.log(results);
  }
  process.exit(0);
});
