import React, { useEffect, useState } from 'react';
import { Card, Typography, Divider, Table, Alert } from 'antd';
import useStopTableGraphQL from '../hooks/useStopTableGraphQL';

const { Title, Text } = Typography;

const DiagnosticPage = () => {
  const [machineModels, setMachineModels] = useState([]);
  const [machineNames, setMachineNames] = useState([]);
  const [filteredNames, setFilteredNames] = useState([]);
  const [selectedModel, setSelectedModel] = useState('IPS');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const graphQL = useStopTableGraphQL();
  
  useEffect(() => {
    async function fetchDiagnosticData() {
      setLoading(true);
      try {
        // Fetch machine models
        const models = await graphQL.getMachineModels();
        setMachineModels(models);
        console.log('Fetched models:', models);
        
        // Fetch all machine names
        const allMachines = await graphQL.getMachineNames();
        setMachineNames(allMachines);
        console.log('Fetched all machines:', allMachines);
        
        // Fetch filtered machine names for default model
        const filtered = await graphQL.getMachineNames({ model: selectedModel });
        setFilteredNames(filtered);
        console.log(`Fetched machines for model ${selectedModel}:`, filtered);
        
        setError(null);
      } catch (err) {
        console.error('Diagnostic error:', err);
        setError(err.message || 'Failed to fetch diagnostic data');
      } finally {
        setLoading(false);
      }
    }
    
    fetchDiagnosticData();
  }, [graphQL, selectedModel]);
  
  // Columns for the machine names table
  const columns = [
    {
      title: 'Machine Name',
      dataIndex: 'Machine_Name',
      key: 'name',
      render: (text, record) => text || JSON.stringify(record)
    },
    {
      title: 'Raw Data',
      key: 'raw',
      render: (_, record) => (
        <pre style={{ maxHeight: '100px', overflow: 'auto' }}>
          {JSON.stringify(record, null, 2)}
        </pre>
      )
    }
  ];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>GraphQL Data Diagnostic</Title>
      
      {error && (
        <Alert
          message="Error Loading Data"
          description={error}
          type="error"
          style={{ marginBottom: 24 }}
        />
      )}
      
      <Card loading={loading} title="Machine Models">
        <Text>Total Models: {machineModels.length}</Text>
        <pre>{JSON.stringify(machineModels, null, 2)}</pre>
      </Card>
      
      <Divider />
      
      <Card loading={loading} title="All Machine Names">
        <Text>Total Machines: {machineNames.length}</Text>
        <Table 
          dataSource={machineNames} 
          columns={columns}
          rowKey={(record) => record.Machine_Name || Math.random().toString()}
          pagination={{ pageSize: 5 }}
        />
      </Card>
      
      <Divider />
      
      <Card loading={loading} title={`Filtered Machine Names (${selectedModel})`}>
        <Text>Filtered Machines: {filteredNames.length}</Text>
        <Table 
          dataSource={filteredNames} 
          columns={columns}
          rowKey={(record) => record.Machine_Name || Math.random().toString()}
          pagination={{ pageSize: 5 }}
        />
      </Card>
    </div>
  );
};

export default DiagnosticPage;
