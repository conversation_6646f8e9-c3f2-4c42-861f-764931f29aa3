/**
 * Test script to verify that chart data is properly processed with the new fixes
 * This tests the scenario: IPS01 machine + April 2025 date filter
 */

import fs from 'fs';
import path from 'path';
import dayjs from 'dayjs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const dataProcessingPath = path.join(__dirname, 'frontend/src/context/arret/modules/dataProcessing.jsx');

console.log('✅ Path resolved:', dataProcessingPath);

const dataProcessingContent = fs.readFileSync(dataProcessingPath, 'utf8');

// Mock dayjs for the frontend modules
global.dayjs = dayjs;

// Create a simple module evaluation context
const moduleExports = {};
const module = { exports: moduleExports };

// Mock the import/export system
const mockRequire = (moduleName) => {
  if (moduleName === 'dayjs') {
    return require('dayjs');
  }
  return {};
};

// Evaluate the module content in a controlled way
try {
  // Extract the parseDate function
const parseDateMatch = dataProcessingContent.match(/export const parseDate\s*=\s*(.*);/s);
  if (parseDateMatch) {
    const parseDateCode = `const parseDate = (${parseDateMatch[1]};`;
    eval(parseDateCode);
    
    console.log('✅ parseDate function loaded successfully');
    
    // Test the date parsing with actual backend data format
    const testDates = [
      "16/04/2025 10:30:00",
      "16/04/2025 14:45:00", 
      "17/04/2025 08:15:00",
      "17/04/2025 16:20:00"
    ];
    
    console.log('\n📅 Testing date parsing:');
    testDates.forEach(dateStr => {
      const parsed = parseDate(dateStr);
      if (parsed && parsed.isValid()) {
        console.log(`  ${dateStr} -> ${parsed.format('YYYY-MM-DD')} ✅`);
      } else {
        console.log(`  ${dateStr} -> FAILED ❌`);
      }
    });
    
    // Test chart data transformation with mock data
    console.log('\n📊 Testing chart data transformation:');
    
    const mockStopsData = [
      {
        ID_Stop: 1,
        Date_Insert: "16/04/2025 10:30:00",
        Machine_Name: "IPS01",
        duree_arret: "01:30:00"
      },
      {
        ID_Stop: 2,
        Date_Insert: "16/04/2025 14:45:00",
        Machine_Name: "IPS01", 
        duree_arret: "00:45:00"
      },
      {
        ID_Stop: 3,
        Date_Insert: "17/04/2025 08:15:00",
        Machine_Name: "IPS01",
        duree_arret: "02:15:00"
      }
    ];
    
    // Group by date
    const grouped = {};
    mockStopsData.forEach(stop => {
      const dateField = stop.Date_Insert;
      let dateKey = 'unknown';
      if (dateField) {
        const parsedDate = parseDate(dateField);
        if (parsedDate && parsedDate.isValid()) {
          dateKey = parsedDate.format('YYYY-MM-DD');
        }
      }
      
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(stop);
    });
    
    console.log('Grouped data:', grouped);
    
    // Transform to chart format
    const chartData = Object.entries(grouped).map(([date, stops]) => {
      const displayDate = global.dayjs(date).format('DD/MM/YYYY');
      
      return {
        date: displayDate,
        dateKey: date,
        stops: stops.length,
        totalDuration: stops.reduce((sum, stop) => {
          if (stop.duree_arret) {
            const parts = stop.duree_arret.split(':');
            const hours = parseInt(parts[0]) || 0;
            const minutes = parseInt(parts[1]) || 0;
            return sum + (hours * 60) + minutes;
          }
          return sum;
        }, 0),
        details: stops
      };
    }).sort((a, b) => global.dayjs(a.dateKey).diff(global.dayjs(b.dateKey)));
    
    console.log('\n📈 Final chart data:');
    chartData.forEach(item => {
      console.log(`  ${item.date}: ${item.stops} stops, ${item.totalDuration}min total`);
    });
    
    if (chartData.length > 0) {
      console.log('\n✅ Chart data transformation successful!');
      console.log(`📊 Generated ${chartData.length} data points for chart display`);
    } else {
      console.log('\n❌ No chart data generated - check date parsing');
    }
    
  } else {
    console.log('❌ Could not extract parseDate function from dataProcessing.jsx');
  }
  
} catch (error) {
  console.error('❌ Error testing chart data fix:', error);
}
