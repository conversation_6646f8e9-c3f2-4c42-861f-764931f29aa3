#!/bin/bash

# LOCQL Docker Test Script
# This script tests the Docker setup and connectivity

echo "🐳 LOCQL Docker Setup Test"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "ℹ️  $1"
}

# Check if Docker is running
echo "1. Checking Docker..."
docker --version > /dev/null 2>&1
print_status $? "Docker is installed and running"

# Check if docker-compose is available
echo "2. Checking Docker Compose..."
docker-compose --version > /dev/null 2>&1
print_status $? "Docker Compose is available"

# Check ngrok tunnel
echo "3. Checking ngrok tunnel..."
if curl -s --max-time 5 "https://charming-hermit-intense.ngrok-free.app/api/health/ping" > /dev/null 2>&1; then
    print_status 0 "ngrok tunnel is active and accessible"
else
    print_warning "ngrok tunnel is not accessible. Please ensure ngrok is running."
fi

# Check if MySQL is running on host
echo "4. Checking MySQL connectivity..."
if command -v mysql &> /dev/null; then
    mysql -h localhost -u root -proot -e "SELECT 1;" > /dev/null 2>&1
    print_status $? "MySQL is accessible with configured credentials"
else
    print_warning "MySQL client not found. Cannot test database connectivity."
fi

# Check if ports are available
echo "5. Checking port availability..."
if ! netstat -tuln 2>/dev/null | grep -q ":5000 "; then
    print_status 0 "Port 5000 (backend) is available"
else
    print_warning "Port 5000 is already in use"
fi

if ! netstat -tuln 2>/dev/null | grep -q ":5173 "; then
    print_status 0 "Port 5173 (frontend) is available"
else
    print_warning "Port 5173 is already in use"
fi

# Check if required files exist
echo "6. Checking Docker configuration files..."
files=(
    "docker-compose.app.yml"
    "docker.env"
    "backend/Dockerfile"
    "frontend/Dockerfile"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        print_status 0 "$file exists"
    else
        print_status 1 "$file is missing"
    fi
done

echo ""
print_info "Test completed. If all checks passed, you can run:"
print_info "docker-compose -f docker-compose.app.yml up --build"

echo ""
print_info "To monitor the application:"
print_info "- Frontend (Local): http://localhost:5173"
print_info "- Backend API (Local): http://localhost:5000"
print_info "- External Access (ngrok): https://charming-hermit-intense.ngrok-free.app"
print_info "- Health Check: http://localhost:5000/api/health/ping"
print_info "- WebSocket (ngrok): wss://charming-hermit-intense.ngrok-free.app/api/machine-data-ws"
