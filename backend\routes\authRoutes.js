/**
 * Authentication routes
 */
import express from 'express';
import { check, validationResult } from 'express-validator';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import nodemailer from 'nodemailer';
import crypto from 'crypto';
import db from '../db.js';
import rateLimit from 'express-rate-limit';
import { sendSuccess, sendError, sendValidationError, asyncHandler } from '../utils/responseUtils.js';
import { executeQuery, handleDbError } from '../utils/dbUtils.js';

const router = express.Router();

// Configure email transporter
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: false,
  auth: {
    user: process.env.SMTP_EMAIL,
    pass: process.env.SMTP_PASSWORD
  },
  debug: process.env.NODE_ENV === 'development'
});

// Verify email connection in development mode
if (process.env.NODE_ENV === 'development') {
  transporter.verify((error, success) => {
    if (error) {
      console.error("SMTP verification error:", error);
    } else {
      console.log("SMTP server is ready");
    }
  });
}

/**
 * @route   POST /api/login
 * @desc    Authenticate user & get token
 * @access  Public
 */
router.post('/login', [
  check('email', 'Please include a valid email').isEmail(),
  check('password', 'Password is required').exists()
], asyncHandler(async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return sendValidationError(res, errors.array());
  }

  const { email, password } = req.body;

  // Check if user exists
  const { success, data: users, error } = await executeQuery(
    'SELECT * FROM users WHERE email = ?',
    [email]
  );

  if (!success) {
    return sendError(res, 'Database error', 500, error);
  }

  if (users.length === 0) {
    return sendError(res, 'Invalid credentials', 401);
  }

  const user = users[0];

  // Compare passwords
  const isMatch = await bcrypt.compare(password, user.password);
  if (!isMatch) {
    // Log failed attempt
    await executeQuery(
      'INSERT INTO login_attempts (email, ip_address, success) VALUES (?, ?, ?)',
      [email, req.ip, false]
    );
    return sendError(res, 'Invalid credentials', 401);
  }

  // Log successful attempt
  await executeQuery(
    'INSERT INTO login_attempts (email, ip_address, success) VALUES (?, ?, ?)',
    [email, req.ip, true]
  );

  // Create JWT token
  const token = jwt.sign(
    { id: user.id, role: user.role },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE }
  );

  // Store token in sessions table
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + parseInt(process.env.JWT_COOKIE_EXPIRE || '7'));

  await executeQuery(
    'INSERT INTO sessions (user_id, token, expires_at) VALUES (?, ?, ?)',
    [user.id, token, expiresAt]
  );

  // Set cookie
  const cookieOptions = {
    expires: expiresAt,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production'
  };

  res.cookie('token', token, cookieOptions);

  // Return user data without password
  const { password: _, ...userData } = user;

  return sendSuccess(res, { user: userData, token }, 'Login successful', 200);
}));

/**
 * @route   POST /api/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', [
  check('username', 'Username is required').not().isEmpty(),
  check('email', 'Please include a valid email').isEmail(),
  check('password', 'Password must be at least 8 characters with at least one uppercase, one lowercase, and one number')
    .matches(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9]{8,}$/)
], asyncHandler(async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return sendValidationError(res, errors.array());
  }

  const { username, email, password, role_id } = req.body;

  // Check if user already exists
  const { success, data: users, error } = await executeQuery(
    'SELECT * FROM users WHERE email = ?',
    [email]
  );

  if (!success) {
    return sendError(res, 'Database error', 500, error);
  }

  if (users.length > 0) {
    return sendError(res, 'User already exists', 400);
  }

  // Hash password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(password, salt);

  // Get default role if role_id is not provided
  let roleIdToUse = role_id;
  let roleName = 'user'; // Default role name

  if (!roleIdToUse) {
    const { success: roleSuccess, data: roleData } = await executeQuery(
      'SELECT id, name FROM roles WHERE name = ?',
      ['user']
    );
    if (roleSuccess && roleData.length > 0) {
      roleIdToUse = roleData[0].id;
      roleName = roleData[0].name;
    }
  } else {
    // If role_id is provided, get the role name
    const { success: roleSuccess, data: roleData } = await executeQuery(
      'SELECT name FROM roles WHERE id = ?',
      [roleIdToUse]
    );
    if (roleSuccess && roleData.length > 0) {
      roleName = roleData[0].name;
    }
  }

  // Create user with both role and role_id
  const result = await executeQuery(
    'INSERT INTO users (username, email, password, role, role_id) VALUES (?, ?, ?, ?, ?)',
    [username, email, hashedPassword, roleName, roleIdToUse]
  );

  if (!result.success) {
    return sendError(res, 'Failed to create user', 500, result.error);
  }

  return sendSuccess(res, null, 'User registered successfully', 201);
}));

/**
 * Configure rate limiting for password reset
 */
const resetLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // 3 attempts per window
  message: { success: false, message: 'Too many password reset attempts, please try again later' }
});

/**
 * @route   POST /api/forgot-password
 * @desc    Send password reset email
 * @access  Public
 */
router.post('/forgot-password', resetLimiter, [
  check('email', 'Please include a valid email').isEmail()
], asyncHandler(async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return sendValidationError(res, errors.array());
  }

  const { email } = req.body;

  // Check if user exists
  const { success, data: users, error } = await executeQuery(
    'SELECT * FROM users WHERE email = ?',
    [email]
  );

  if (!success) {
    return sendError(res, 'Database error', 500, error);
  }

  // Always return the same response regardless of whether the email exists
  // This prevents user enumeration attacks
  if (users.length === 0) {
    return sendSuccess(res, null, 'If your email is registered, you will receive reset instructions');
  }

  const user = users[0];

  // Generate reset token
  const resetToken = crypto.randomBytes(32).toString('hex');
  const tokenHash = crypto.createHash('sha256').update(resetToken).digest('hex');
  const resetTokenExpiry = Date.now() + 3600000; // 1 hour from now

  // Store the hash instead of plain token
  const tokenResult = await executeQuery(
    'INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?) ' +
    'ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at)',
    [user.id, tokenHash, new Date(resetTokenExpiry)]
  );

  if (!tokenResult.success) {
    return sendError(res, 'Failed to create reset token', 500, tokenResult.error);
  }

  // Create reset URL
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

  // Send email
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('Sending password reset email to:', email);
    }

    const info = await transporter.sendMail({
      from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
      to: email,
      subject: 'Password Reset Request',
      headers: {
        'X-Priority': '1',
        'X-MSMail-Priority': 'High',
        'X-Content-Type-Options': 'nosniff'
      },
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <p style="color: #4a5568; font-size: 16px;">
            We received a password reset request. The link will expire in 1 hour:
          </p>
          <a href="${resetUrl}"
             style="color: #4299e1; text-decoration: underline;">
            Reset Password
          </a>
          <p style="color: #718096; font-size: 14px; margin-top: 20px;">
            If you didn't request this, please contact support immediately.
          </p>
        </div>
      `
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('Email sent successfully:', info.messageId);
    }

    return sendSuccess(res, null, 'Reset email sent');
  } catch (error) {
    console.error('Email error:', error);

    // Delete the reset token if email fails
    await executeQuery('DELETE FROM password_resets WHERE user_id = ?', [user.id]);

    return sendError(res, 'Email could not be sent', 500, error);
  }
}));

/**
 * @route   GET /api/verify-reset-token/:token
 * @desc    Verify password reset token
 * @access  Public
 */
router.get('/verify-reset-token/:token', asyncHandler(async (req, res) => {
  const { token } = req.params;

  // Hash the token for database comparison
  const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

  // Check if token exists and is not expired
  const { success, data: tokens, error } = await executeQuery(
    'SELECT * FROM password_resets WHERE token = ? AND expires_at > NOW()',
    [tokenHash]
  );

  if (!success) {
    return sendError(res, 'Database error', 500, error);
  }

  if (tokens.length === 0) {
    return sendError(res, 'Invalid or expired token', 400);
  }

  return sendSuccess(res, null, 'Token is valid');
}));

/**
 * @route   POST /api/reset-password
 * @desc    Reset password with token
 * @access  Public
 */
router.post('/reset-password', [
  check('token', 'Token is required').not().isEmpty(),
  check('password', 'Password must be at least 8 characters with at least one uppercase, one lowercase, one number, and one special character')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/)
], asyncHandler(async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return sendValidationError(res, errors.array());
  }

  const { token, password } = req.body;

  // Hash the token for database comparison
  const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

  // Find valid reset token
  const { success, data: tokens, error } = await executeQuery(
    'SELECT * FROM password_resets WHERE token = ? AND expires_at > NOW()',
    [tokenHash]
  );

  if (!success) {
    return sendError(res, 'Database error', 500, error);
  }

  if (tokens.length === 0) {
    return sendError(res, 'Invalid or expired token', 400);
  }

  const resetRecord = tokens[0];

  // Hash new password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(password, salt);

  // Update user password
  const updateResult = await executeQuery(
    'UPDATE users SET password = ? WHERE id = ?',
    [hashedPassword, resetRecord.user_id]
  );

  if (!updateResult.success) {
    return sendError(res, 'Failed to update password', 500, updateResult.error);
  }

  // Delete used reset token
  const deleteResult = await executeQuery(
    'DELETE FROM password_resets WHERE token = ?',
    [tokenHash]
  );

  if (!deleteResult.success) {
    // Log error but don't fail the request - password was updated successfully
    console.error('Failed to delete reset token:', deleteResult.error);
  }

  return sendSuccess(res, null, 'Password has been reset successfully');
}));

/**
 * @route   GET /api/logout
 * @desc    Logout user and clear cookie
 * @access  Private
 */
router.get('/logout', asyncHandler(async (req, res) => {
  // Get token from cookie
  const token = req.cookies.token;

  if (!token) {
    return sendSuccess(res, null, 'Already logged out');
  }

  // Remove token from sessions table
  const result = await executeQuery(
    'DELETE FROM sessions WHERE token = ?',
    [token]
  );

  if (!result.success) {
    // Log error but continue with logout
    console.error('Failed to delete session:', result.error);
  }

  // Clear cookie
  res.clearCookie('token');

  return sendSuccess(res, null, 'Logged out successfully');
}));

/**
 * @route   GET /api/me
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/me', asyncHandler(async (req, res) => {
  // Get token from cookie or authorization header
  const token = req.cookies.token || req.headers.authorization?.split(' ')[1];

  if (!token) {
    return sendError(res, 'Not authenticated', 401);
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from database with role permissions
    const { success, data: users, error } = await executeQuery(
      `SELECT u.*, r.permissions as role_permissions
       FROM users u
       LEFT JOIN roles r ON u.role_id = r.id
       WHERE u.id = ?`,
      [decoded.id]
    );

    if (!success) {
      return sendError(res, 'Database error', 500, error);
    }

    if (users.length === 0) {
      return sendError(res, 'User not found', 404);
    }

    const user = users[0];

    // Parse permissions
    const parsePermissions = (perms) => {
      if (!perms) return [];
      if (Array.isArray(perms)) return perms;
      try {
        return JSON.parse(perms);
      } catch (e) {
        return [];
      }
    };

    user.permissions = parsePermissions(user.permissions);
    user.role_permissions = parsePermissions(user.role_permissions);

    try {
      // Import dynamically to avoid circular dependency
      const { getAllRolePermissions } = await import('../utils/roleHierarchy.js');

      // Get role permissions from hierarchy
      let hierarchyPermissions = [];
      if (user.role) {
        hierarchyPermissions = getAllRolePermissions(user.role);
      }

      // Add hierarchy permissions to user object
      user.hierarchy_permissions = hierarchyPermissions;

      // Combine all permissions
      const allPermissions = [
        ...user.permissions,
        ...user.role_permissions,
        ...hierarchyPermissions
      ].filter(Boolean); // Remove null/undefined values

      // Add combined permissions to user object
      user.all_permissions = [...new Set(allPermissions)]; // Remove duplicates
    } catch (error) {
      console.error('Error setting up permissions in /me endpoint:', error);
      // If there's an error, set empty arrays to avoid undefined errors
      user.hierarchy_permissions = [];
      user.all_permissions = [...user.permissions, ...user.role_permissions].filter(Boolean);
    }

    // Return user data without sensitive fields
    const { password, reset_token, reset_token_expiry, ...userData } = user;

    return sendSuccess(res, userData);
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return sendError(res, 'Not authenticated', 401);
    }
    return sendError(res, 'Server error', 500, error);
  }
}));

/**
 * @route   GET /api/refresh-session
 * @desc    Refresh user session by extending cookie expiration
 * @access  Private
 */
router.get('/refresh-session', asyncHandler(async (req, res) => {
  // Get token from cookie
  const token = req.cookies.token;

  if (!token) {
    return sendError(res, 'Not authenticated', 401);
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Create a new token with the same payload but new expiration
    const newToken = jwt.sign(
      { id: decoded.id, role: decoded.role },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRE }
    );

    // Update token in sessions table
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + parseInt(process.env.JWT_COOKIE_EXPIRE || '7'));

    await executeQuery(
      'UPDATE sessions SET token = ?, expires_at = ? WHERE token = ?',
      [newToken, expiresAt, token]
    );

    // Set new cookie
    const cookieOptions = {
      expires: expiresAt,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production'
    };

    res.cookie('token', newToken, cookieOptions);

    return sendSuccess(res, null, 'Session refreshed successfully');
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return sendError(res, 'Not authenticated', 401);
    }
    return sendError(res, 'Server error', 500, error);
  }
}));


/**
 * @route   GET /api/sse-token
 * @desc    Get temporary token for SSE connection (uses HTTP-only cookie for auth)
 * @access  Private
 */
router.get('/sse-token', asyncHandler(async (req, res) => {
  // Get token from cookie
  const token = req.cookies.token;

  if (!token) {
    return sendError(res, 'Not authenticated', 401);
  }

  try {
    // Verify the HTTP-only cookie token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Create a short-lived token specifically for SSE (5 minutes)
    const sseToken = jwt.sign(
      { id: decoded.id, role: decoded.role, purpose: 'sse' },
      process.env.JWT_SECRET,
      { expiresIn: '5m' }
    );

    return sendSuccess(res, { sseToken }, 'SSE token generated successfully');
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return sendError(res, 'Not authenticated', 401);
    }
    return sendError(res, 'Server error', 500, error);
  }
}));

/**
 * @route   POST /api/test-email
 * @desc    Send a test email to verify SMTP configuration
 * @access  Admin
 */
router.post('/test-email', [
  check('email', 'Please include a valid email').isEmail()
], asyncHandler(async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return sendValidationError(res, errors.array());
  }

  const { email } = req.body;

  try {
    await transporter.sendMail({
      from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
      to: email,
      subject: 'LOCQL Email Test',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1>Email Configuration Test</h1>
          <p>This is a test email to confirm your SMTP configuration is working correctly.</p>
          <p>If you received this email, your email service is properly configured!</p>
        </div>
      `
    });

    return sendSuccess(res, null, 'Test email sent successfully');
  } catch (error) {
    return sendError(res, 'Failed to send test email', 500, error);
  }
}));

export default router;
