/**
 * Comprehensive Filter Application Test for ArretLineChart
 * 
 * This test verifies that the getAllMachineStops query properly handles
 * all filter combinations and that the data flows correctly to ArretLineChart.
 */

import { GraphQLClient } from 'graphql-request';

async function testFilterApplications() {
  console.log('🧪 Testing Filter Applications for ArretLineChart\n');

  const client = new GraphQLClient('http://localhost:5000/api/graphql');

  const query = `
    query GetAllMachineStops($filters: StopFilterInput) {
      getAllMachineStops(filters: $filters) {
        Date_Insert
        Machine_Name
        Part_NO
        Code_Stop
        Debut_Stop
        Fin_Stop_Time
        Regleur_Prenom
        duration_minutes
      }
    }
  `;

  // Test scenarios that simulate real dashboard usage
  const testScenarios = [
    {
      name: "1. Default State (Model Only)",
      description: "Default dashboard state - only model filter active",
      filters: {
        model: "IPS",
        machine: null,
        date: null,
        startDate: null,
        endDate: null,
        dateRangeType: "month"
      },
      expectedBehavior: "Should show all IPS machines data without date restriction"
    },
    {
      name: "2. Model + Machine Selection",
      description: "User selects specific machine within model",
      filters: {
        model: "IPS",
        machine: "IPS01",
        date: null,
        startDate: null,
        endDate: null,
        dateRangeType: "month"
      },
      expectedBehavior: "Should show only IPS01 machine data"
    },
    {
      name: "3. Model + Date Range",
      description: "User applies date filter with model",
      filters: {
        model: "IPS",
        machine: null,
        date: "2024-12-01",
        startDate: "2024-12-01",
        endDate: "2024-12-31",
        dateRangeType: "month"
      },
      expectedBehavior: "Should show IPS machines data for December 2024"
    },
    {
      name: "4. All Filters Applied",
      description: "User applies machine, model, and date filters",
      filters: {
        model: "IPS",
        machine: "IPS01",
        date: "2024-12-01",
        startDate: "2024-12-01",
        endDate: "2024-12-31",
        dateRangeType: "month"
      },
      expectedBehavior: "Should show IPS01 data for December 2024 only"
    },
    {
      name: "5. Week Filter",
      description: "User selects week range",
      filters: {
        model: "IPS",
        machine: "IPS01",
        date: "2024-12-03",
        startDate: null,
        endDate: null,
        dateRangeType: "week"
      },
      expectedBehavior: "Should show IPS01 data for week containing Dec 3, 2024"
    },
    {
      name: "6. Day Filter",
      description: "User selects specific day",
      filters: {
        model: "IPS",
        machine: "IPS01",
        date: "2024-12-03",
        startDate: null,
        endDate: null,
        dateRangeType: "day"
      },
      expectedBehavior: "Should show IPS01 data for Dec 3, 2024 only"
    }
  ];

  for (const scenario of testScenarios) {
    console.log(`\n📊 === ${scenario.name} ===`);
    console.log(`📋 ${scenario.description}`);
    console.log(`🎯 Expected: ${scenario.expectedBehavior}`);
    console.log('Filters:', JSON.stringify(scenario.filters, null, 2));

    try {
      const startTime = Date.now();
      const result = await client.request(query, { filters: scenario.filters });
      const executionTime = Date.now() - startTime;
      
      const stopsData = result.getAllMachineStops || [];

      console.log(`\n⏱️ Query executed in ${executionTime}ms`);
      console.log(`📈 Backend Response:`);
      console.log(`- Total stops found: ${stopsData.length}`);
      
      if (stopsData.length > 0) {
        console.log(`- Date range: ${stopsData[0]?.Date_Insert} to ${stopsData[stopsData.length - 1]?.Date_Insert}`);
        
        // Analyze machines in result
        const uniqueMachines = [...new Set(stopsData.map(stop => stop.Machine_Name))];
        console.log(`- Machines found: ${uniqueMachines.join(', ')}`);
        
        // Analyze date range if date filter was applied
        if (scenario.filters.date || scenario.filters.startDate) {
          const dates = stopsData.map(stop => extractDateFromStop(stop.Date_Insert)).filter(Boolean);
          const uniqueDates = [...new Set(dates)].sort();
          console.log(`- Date span: ${uniqueDates[0]} to ${uniqueDates[uniqueDates.length - 1]}`);
          console.log(`- Days covered: ${uniqueDates.length}`);
        }
        
        // Sample data
        console.log(`- Sample stop:`, {
          machine: stopsData[0]?.Machine_Name,
          date: stopsData[0]?.Date_Insert,
          duration: stopsData[0]?.duration_minutes
        });
        
        // Verify filter application
        console.log(`\n🔍 Filter Verification:`);
        
        // Check machine filter
        if (scenario.filters.machine) {
          const wrongMachines = stopsData.filter(stop => stop.Machine_Name !== scenario.filters.machine);
          console.log(`- Machine filter: ${wrongMachines.length === 0 ? '✅ PASS' : '❌ FAIL'} (${wrongMachines.length} wrong machines)`);
        } else if (scenario.filters.model) {
          const wrongModel = stopsData.filter(stop => !stop.Machine_Name.startsWith(scenario.filters.model));
          console.log(`- Model filter: ${wrongModel.length === 0 ? '✅ PASS' : '❌ FAIL'} (${wrongModel.length} wrong models)`);
        }
        
        // Check date filter
        if (scenario.filters.startDate && scenario.filters.endDate) {
          const startDate = new Date(scenario.filters.startDate);
          const endDate = new Date(scenario.filters.endDate);
          const outOfRange = stopsData.filter(stop => {
            const stopDate = new Date(extractDateFromStop(stop.Date_Insert));
            return stopDate < startDate || stopDate > endDate;
          });
          console.log(`- Date range filter: ${outOfRange.length === 0 ? '✅ PASS' : '❌ FAIL'} (${outOfRange.length} out of range)`);
        }
        
      } else {
        console.log('⚠️ No data returned - check if filters are too restrictive');
      }

      // Process data for ArretLineChart simulation
      const chartData = processStopsForLineChart(stopsData);
      
      console.log(`\n📊 Chart Data (ArretLineChart input):`);
      console.log(`- Chart points: ${chartData.length}`);
      
      if (chartData.length > 0) {
        console.log('- Preview (first 3 points):');
        chartData.slice(0, 3).forEach((point, index) => {
          console.log(`  ${index + 1}. ${point.displayDate}: ${point.stops} stops`);
        });
        
        // Performance check
        if (chartData.length > 50) {
          console.log(`⚠️ Large dataset: ${chartData.length} points - may impact chart performance`);
        }
      } else {
        console.log('❌ No chart data generated');
      }

      // Performance warning
      if (executionTime > 3000) {
        console.log(`⚠️ Slow query: ${executionTime}ms - consider optimization`);
      }

    } catch (error) {
      console.error(`❌ Error in ${scenario.name}:`, error.message);
    }
  }
  
  console.log('\n🎯 Filter Test Summary Complete!');
}

/**
 * Process stops data for line chart (simplified)
 */
function processStopsForLineChart(stopsData) {
  const dailyStats = {};
  
  stopsData.forEach(stop => {
    if (!stop.Date_Insert) return;
    
    const date = extractDateFromStop(stop.Date_Insert);
    if (!date) return;
    
    if (!dailyStats[date]) {
      dailyStats[date] = { date, stops: 0 };
    }
    
    dailyStats[date].stops++;
  });

  return Object.values(dailyStats)
    .sort((a, b) => new Date(a.date) - new Date(b.date))
    .map(item => ({
      ...item,
      displayDate: formatDateForDisplay(item.date)
    }));
}

/**
 * Extract date from database format
 */
function extractDateFromStop(dateString) {
  if (!dateString) return null;
  
  const str = dateString.toString().trim();
  
  // Format: " 3/12/2024 09:55:38" or "03/12/2024 09:55:38"
  const match1 = str.match(/^\s*(\d{1,2})\/(\d{1,2})\/(\d{4})/);
  if (match1) {
    const [_, day, month, year] = match1;
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  // Format: "2024-12-03" (ISO format)
  if (str.match(/^\d{4}-\d{2}-\d{2}/)) {
    return str.substring(0, 10);
  }
  
  return null;
}

/**
 * Format date for display
 */
function formatDateForDisplay(dateString) {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return dateString.slice(0, 10);
    }
    return date.toLocaleDateString('fr-FR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  } catch (error) {
    return dateString.slice(0, 10);
  }
}

// Run the comprehensive test
testFilterApplications().catch(console.error);
