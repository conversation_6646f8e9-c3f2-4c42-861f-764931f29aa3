import React from 'react';
import PDFReportTemplate from '../../components/reports/PDFReportTemplate';

/**
 * Test page for PDF template - accessible at /reports/pdf-test
 * This allows manual testing of the PDF template without P<PERSON>peteer
 */
const PDFTestPage = () => {
  // Sample test data
  const testReportData = {
    machine: {
      id: 'TEST01',
      name: 'Machine Test 01',
      model: 'Test Model'
    },
    shift: 'Matin',
    date: '2025-01-16',
    period: {
      start: '2025-01-16T06:00:00Z',
      end: '2025-01-16T14:00:00Z'
    },
    performance: {
      oee: 85.5,
      availability: 92.3,
      performanceRate: 88.7,
      qualityRate: 95.2,
      runTime: 7.2,
      downTime: 0.8,
      theoreticalRate: 120,
      actualRate: 106,
      cycleTime: 34
    },
    production: {
      totalProduction: 850,
      goodParts: 810,
      rejects: 40
    },
    sessions: [
      {
        session_start: '2025-01-16T06:00:00Z',
        session_end: '2025-01-16T07:00:00Z',
        Quantite_Bon: 105,
        Quantite_Rejet: 5,
        cycle: 32.5,
        TRS: 87.2,
        Article: 'TEST-PART-001'
      },
      {
        session_start: '2025-01-16T07:00:00Z',
        session_end: '2025-01-16T08:00:00Z',
        Quantite_Bon: 110,
        Quantite_Rejet: 3,
        cycle: 31.8,
        TRS: 89.5,
        Article: 'TEST-PART-001'
      },
      {
        session_start: '2025-01-16T08:00:00Z',
        session_end: '2025-01-16T09:00:00Z',
        Quantite_Bon: 108,
        Quantite_Rejet: 4,
        cycle: 33.1,
        TRS: 86.8,
        Article: 'TEST-PART-002'
      }
    ]
  };

  return (
    <div>
      <div className="no-print bg-blue-100 p-4 mb-4 border-l-4 border-blue-500">
        <h2 className="text-lg font-semibold text-blue-800">PDF Template Test Page</h2>
        <p className="text-blue-700">
          This page shows how the PDF template will look. 
          Use browser print preview to see the final PDF layout.
        </p>
        <p className="text-sm text-blue-600 mt-2">
          <strong>data-pdf-ready attribute:</strong> {document.body.getAttribute('data-pdf-ready') || 'Not set'}
        </p>
      </div>
      
      <PDFReportTemplate reportData={testReportData} />
    </div>
  );
};

export default PDFTestPage;
