import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import PDFReportTemplate from '../../components/reports/PDFReportTemplate';

/**
 * PDF Preview Route for Puppeteer
 * This route is specifically designed to be accessed by Puppeteer
 * for PDF generation. It receives report data via URL parameters
 * and renders the PDF template without any navigation or UI chrome.
 */
const PDFPreviewPage = () => {
  const [searchParams] = useSearchParams();
  const [reportData, setReportData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    try {
      // Decode report data from URL parameters
      const encodedData = searchParams.get('data');
      if (!encodedData) {
        throw new Error('No report data provided');
      }

      // Decode base64 and parse JSON - Fix for browser environment
      const decodedData = atob(encodedData); // Use atob instead of <PERSON><PERSON><PERSON> in browser
      const parsedData = JSON.parse(decodedData);

      setReportData(parsedData);
      setLoading(false);

      console.log('📊 PDF preview data loaded:', {
        machine: parsedData.machine?.name,
        shift: parsedData.shift,
        date: parsedData.date
      });

    } catch (err) {
      console.error('❌ Failed to load PDF preview data:', err);
      setError(err.message);
      setLoading(false);
    }
  }, [searchParams]);

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto mb-4"></div>
          <div className="text-lg text-gray-600">Préparation du rapport PDF...</div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-2">Erreur de chargement</div>
          <div className="text-gray-600">{error}</div>
        </div>
      </div>
    );
  }

  // Render PDF template
  return <PDFReportTemplate reportData={reportData} />;
};

export default PDFPreviewPage;
