// Update the CORS configuration to allow requests from your frontend
import cors from 'cors';

// Read allowed origins from environment variable or use defaults
const allowedOrigins = [
  'http://localhost:5173',
  'http://localhost:5000',
  'https://charming-hermit-intense.ngrok-free.app',
  // Docker container frontend
  'http://frontend:5173',
  'http://locql-frontend:5173',
  // Pomerium URLs
  'https://locql.adapted-osprey-5307.pomerium.app',
  'https://api.adapted-osprey-5307.pomerium.app',
  'https://ws.adapted-osprey-5307.pomerium.app',
  // Allow all origins in development mode
  ...(process.env.NODE_ENV === 'development' ? ['*'] : [])
];

// CORS options configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl, Postman)
    if (!origin) {
      return callback(null, true);
    }

    // In development mode, allow all origins
    if (process.env.NODE_ENV === 'development' || allowedOrigins.includes('*')) {
      return callback(null, true);
    }

    // Check if the origin is allowed
    if (allowedOrigins.indexOf(origin) !== -1) {
      console.log('CORS allowed for origin:', origin);
      callback(null, true);
    } else {
      console.log('Origin not allowed by CORS:', origin, 'Allowed origins:', allowedOrigins);
      callback(new Error('Not allowed by CORS'));
    }
  },  credentials: true,  // Allow cookies and authentication headers
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'x-auth-token', 'ngrok-skip-browser-warning', 'withcredentials'],
  exposedHeaders: ['x-auth-token']
};

// Export the configured CORS middleware
const corsMiddleware = cors(corsOptions);
export default corsMiddleware;
// Export options for use elsewhere if needed
export { corsOptions };
