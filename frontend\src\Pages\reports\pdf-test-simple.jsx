import React, { useEffect } from 'react';

/**
 * Simple PDF Test Page - Minimal dependencies
 * This helps isolate import/dependency issues
 */
const PDFTestSimplePage = () => {
  useEffect(() => {
    // Set the PDF ready attribute for testing
    setTimeout(() => {
      document.body.setAttribute('data-pdf-ready', 'true');
      console.log('✅ Simple PDF template ready');
    }, 1000);

    return () => {
      document.body.removeAttribute('data-pdf-ready');
    };
  }, []);

  return (
    <div className="min-h-screen bg-white p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h1 className="text-3xl font-bold text-blue-900 mb-2">
                Rapport de Quart - Test Simple
              </h1>
              <div className="text-lg text-gray-600">
                Machine Test 01 - Équipe Matin
              </div>
              <div className="text-sm text-gray-500">
                16 janvier 2025
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-900">SOMIPEM</div>
              <div className="text-sm text-gray-500">
                Généré le 16/01/2025 à 14:30
              </div>
            </div>
          </div>
          
          <div className="h-1 bg-gradient-to-r from-blue-900 to-blue-600 rounded"></div>
        </div>

        {/* Test Info */}
        <div className="bg-blue-100 p-4 mb-8 border-l-4 border-blue-500">
          <h2 className="text-lg font-semibold text-blue-800">PDF Template Test - Simple Version</h2>
          <p className="text-blue-700">
            This is a simplified version to test basic functionality without complex dependencies.
          </p>
          <div className="mt-2 text-sm text-blue-600">
            <strong>Status:</strong> {document.body.getAttribute('data-pdf-ready') ? 'Ready' : 'Loading...'}
          </div>
        </div>

        {/* Executive Summary */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-blue-900 mb-4 border-b border-gray-200 pb-2">
            Résumé Exécutif
          </h2>
          
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-900">850</div>
              <div className="text-sm text-gray-600">Unités Produites</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-700">85.5%</div>
              <div className="text-sm text-gray-600">OEE Global</div>
            </div>
            
            <div className="bg-yellow-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-yellow-700">95.2%</div>
              <div className="text-sm text-gray-600">Taux de Qualité</div>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-purple-700">34s</div>
              <div className="text-sm text-gray-600">Temps de Cycle</div>
            </div>
          </div>
        </div>

        {/* Simple Table */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-blue-900 mb-4 border-b border-gray-200 pb-2">
            Données de Test
          </h2>
          
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-blue-900 text-white">
                <th className="border border-gray-300 p-3 text-left">Heure</th>
                <th className="border border-gray-300 p-3 text-right">Production</th>
                <th className="border border-gray-300 p-3 text-right">Rejets</th>
                <th className="border border-gray-300 p-3 text-right">TRS (%)</th>
              </tr>
            </thead>
            <tbody>
              <tr className="bg-gray-50">
                <td className="border border-gray-300 p-3">06:00 - 07:00</td>
                <td className="border border-gray-300 p-3 text-right">105</td>
                <td className="border border-gray-300 p-3 text-right">5</td>
                <td className="border border-gray-300 p-3 text-right">87.2</td>
              </tr>
              <tr className="bg-white">
                <td className="border border-gray-300 p-3">07:00 - 08:00</td>
                <td className="border border-gray-300 p-3 text-right">110</td>
                <td className="border border-gray-300 p-3 text-right">3</td>
                <td className="border border-gray-300 p-3 text-right">89.5</td>
              </tr>
              <tr className="bg-gray-50">
                <td className="border border-gray-300 p-3">08:00 - 09:00</td>
                <td className="border border-gray-300 p-3 text-right">108</td>
                <td className="border border-gray-300 p-3 text-right">4</td>
                <td className="border border-gray-300 p-3 text-right">86.8</td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Footer */}
        <div className="mt-8 pt-4 border-t border-gray-200 text-center text-xs text-gray-500">
          <p>Ce rapport a été généré automatiquement par le système SOMIPEM</p>
          <p>Version de test simplifiée - Pour toute question, contactez l'équipe technique</p>
        </div>
      </div>

      {/* Print styles */}
      <style jsx>{`
        @media print {
          .no-print { display: none !important; }
          body { margin: 0; padding: 0; }
          table { font-size: 10px; }
          th, td { padding: 4px 8px; }
        }
        
        @page {
          margin: 20mm 15mm;
          size: A4;
        }
      `}</style>
    </div>
  );
};

export default PDFTestSimplePage;
