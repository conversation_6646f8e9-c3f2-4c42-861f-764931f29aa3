import React from 'react';
import { Card, Select, Typography, Space, Divider, Table } from 'antd';
import { useMachineDataFixer } from '../hooks/useMachineDataFixer';

const { Title, Text } = Typography;
const { Option } = Select;

const MachineDataFixerTest = () => {
  const {
    machineModels,
    filteredMachineNames,
    selectedModel,
    selectedMachine,
    loading,
    error,
    handleModelChange,
    handleMachineChange
  } = useMachineDataFixer();
  
  const columns = [
    {
      title: 'Machine Name',
      dataIndex: 'Machine_Name',
      key: 'name',
      render: text => text || 'Unknown'
    },
    {
      title: 'Raw Data',
      key: 'raw',
      render: (_, record) => (
        <pre style={{ maxHeight: '100px', overflow: 'auto' }}>
          {JSON.stringify(record, null, 2)}
        </pre>
      )
    }
  ];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>Machine Data Fixer Test</Title>
      
      {error && <Text type="danger">{error}</Text>}
      
      <Card loading={loading}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Text strong>Machine Model:</Text>
            <Select
              style={{ width: 200, marginLeft: 16 }}
              value={selectedModel}
              onChange={handleModelChange}
              placeholder="Select a model"
              allowClear
              loading={loading}
            >
              {machineModels.map(model => (
                <Option key={model} value={model}>{model}</Option>
              ))}
            </Select>
          </div>
          
          <div>
            <Text strong>Machine:</Text>
            <Select
              style={{ width: 300, marginLeft: 16 }}
              value={selectedMachine}
              onChange={handleMachineChange}
              placeholder="Select a machine"
              allowClear
              loading={loading}
              disabled={filteredMachineNames.length === 0}
            >
              {filteredMachineNames.map(machine => (
                <Option 
                  key={machine.Machine_Name || String(machine)} 
                  value={machine.Machine_Name || String(machine)}
                >
                  {machine.Machine_Name || String(machine)}
                </Option>
              ))}
            </Select>
          </div>
        </Space>
      </Card>
      
      <Divider />
      
      <Title level={3}>Machines for {selectedModel || 'All Models'}</Title>
      <Table 
        dataSource={filteredMachineNames} 
        columns={columns}
        rowKey={(record) => record.Machine_Name || JSON.stringify(record)}
        pagination={{ pageSize: 5 }}
        loading={loading}
      />
    </div>
  );
};

export default MachineDataFixerTest;
