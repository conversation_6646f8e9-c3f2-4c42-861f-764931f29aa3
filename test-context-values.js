const { execSync } = require('child_process');

// Check if the context values are properly exposed
console.log('🔍 Testing context values...');

// Test direct API call to verify backend data
const axios = require('axios');

const testBackendData = async () => {
  try {
    const response = await axios.post('http://localhost:5000/graphql', {
      query: `
        query {
          getStopSidecards {
            Arret_Totale
            Arret_Totale_nondeclare
          }
        }
      `
    });
    
    console.log('✅ Backend GraphQL response:', response.data);
    
    if (response.data.data && response.data.data.getStopSidecards) {
      const data = response.data.data.getStopSidecards;
      console.log('📊 Stats from backend:', {
        totalStops: data.Arret_Totale,
        nonDeclaredStops: data.Arret_Totale_nondeclare,
        percentage: data.Arret_Totale > 0 ? ((data.Arret_Totale_nondeclare / data.Arret_Totale) * 100).toFixed(1) : 0
      });
    }
  } catch (error) {
    console.error('❌ Error testing backend:', error.message);
  }
};

testBackendData();
