const query = `{
  getStopsAnalysisData(filters: {model: "IPS"}) {
    durationTrend {
      hour
      avgDuration
    }
    operatorStats {
      operator
      interventions
      totalDuration
    }
    stopReasons {
      reason
      count
    }
    stopStats {
      Stop_Date
      Total_Stops
    }
  }
}`;

const body = JSON.stringify({ query });

console.log('Testing getStopsAnalysisData...');
fetch('http://localhost:5000/api/graphql', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body
})
.then(r => r.json())
.then(result => {
  if (result.errors) {
    console.error('GraphQL errors:', result.errors);
  } else {
    console.log('Analysis data result:');
    console.log('- Duration trend count:', result.data?.getStopsAnalysisData?.durationTrend?.length || 0);
    console.log('- Operator stats count:', result.data?.getStopsAnalysisData?.operatorStats?.length || 0);
    console.log('- Stop reasons count:', result.data?.getStopsAnalysisData?.stopReasons?.length || 0);
    console.log('- Stop stats count:', result.data?.getStopsAnalysisData?.stopStats?.length || 0);
  }
})
.catch(error => {
  console.error('Error:', error);
});
