// Debug script to log the output of GraphQL hook's getMachineNames
import { createMockGraphQLHook } from './mock-graphql-hook.js';

// Create a mock GraphQL hook
const mockGraphQLHook = createMockGraphQLHook();

// Log the expected structure from the GraphQL hook
console.log('Expected structure from GraphQL hook getMachineNames:');
mockGraphQLHook.getMachineNames().then(data => {
  console.log(JSON.stringify(data, null, 2));
  
  // Now try to filter as in ArretContext
  const selectedModel = 'IPS';
  console.log(`\nFiltering for model: ${selectedModel}`);
  
  // This is what's happening in ArretContext
  const filtered = data.filter(machine => machine.modele === selectedModel);
  console.log('Filtered result:', filtered);
  
  console.log('\nProblem: The property is "Machine_Name" but we\'re filtering by "modele"');
  
  // Show the correct way to filter
  console.log('\nCorrect way to filter would be:');
  console.log('data.filter(machine => {');
  console.log('  // Check if machine is an object with Machine_Name');
  console.log('  if (typeof machine === "object" && machine.Machine_Name) {');
  console.log('    // Here we would need a property that indicates the model');
  console.log('    return machine.model === selectedModel;');
  console.log('  }');
  console.log('  return false;');
  console.log('});');
});
