/**
 * Test script to verify evolution chart works with sample data
 */

// Test the fallback sample data generation
function generateSampleEvolutionData() {
  const today = new Date();
  const sampleData = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(today);
    date.setDate(today.getDate() - (6 - i));
    return {
      date: date.toISOString().split('T')[0],
      displayDate: date.toLocaleDateString('fr-FR', { 
        day: '2-digit', 
        month: '2-digit' 
      }),
      stops: Math.floor(Math.random() * 10) + 1,
      duration: Math.floor(Math.random() * 200) + 50
    };
  });
  
  return sampleData;
}

const sampleData = generateSampleEvolutionData();

console.log('📈 Sample Evolution Data Generated:');
console.log(JSON.stringify(sampleData, null, 2));

console.log('\n✅ Data validation:');
console.log('- Is Array:', Array.isArray(sampleData));
console.log('- Has data:', sampleData.length > 0);
console.log('- Has required fields:', sampleData[0] && sampleData[0].date && sampleData[0].displayDate && sampleData[0].stops);
console.log('- Chart ready:', Array.isArray(sampleData) && sampleData.length > 0);

console.log('\n🎯 Expected chart behavior:');
console.log('- X-axis labels:', sampleData.map(item => item.displayDate));
console.log('- Y-axis values:', sampleData.map(item => item.stops));
