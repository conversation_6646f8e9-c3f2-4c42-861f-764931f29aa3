#!/usr/bin/env node

// <PERSON>ript to help migrate remaining test files from fetch/axios to SuperAgent
// This script will update the common patterns found in test files

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = __dirname;

// Test file patterns to update
const testFiles = [
  'test-advanced-optimization.js',
  'test-backend-node.js',
  'test-comprehensive.js',
  'test-endpoint.js',
  'test-graphql-endpoint.js',
  'debug-backend-data.js'
];

const commonReplacements = [
  // Fetch to SuperAgent conversions
  {
    from: /import fetch from 'node-fetch';/g,
    to: "import request from 'superagent';"
  },
  {
    from: /const fetch = \(\.\.\.\args\) => import\('node-fetch'\)\.then\(\(\{default: fetch\}\) => fetch\(\.\.\.\args\)\);/g,
    to: "import request from 'superagent';"
  },
  {
    from: /await fetch\(([^,]+),\s*\{\s*method:\s*'POST',\s*headers:\s*\{\s*'Content-Type':\s*'application\/json'\s*\},\s*body:\s*JSON\.stringify\(([^}]+)\)\s*\}\)/g,
    to: 'await request.post($1).send($2).retry(2)'
  },
  {
    from: /await response\.json\(\)/g,
    to: 'response.body'
  },
  {
    from: /response\.data/g,
    to: 'response.body'
  }
];

function updateFile(filePath) {
  try {
    const fullPath = path.join(projectRoot, filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(fullPath, 'utf8');
    let modified = false;

    commonReplacements.forEach(({ from, to }) => {
      if (content.match(from)) {
        content = content.replace(from, to);
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
    return false;
  }
}

console.log('🔄 Starting SuperAgent migration for test files...\n');

let updatedCount = 0;
testFiles.forEach(file => {
  if (updateFile(file)) {
    updatedCount++;
  }
});

console.log(`\n✨ Migration complete! Updated ${updatedCount} files.`);
