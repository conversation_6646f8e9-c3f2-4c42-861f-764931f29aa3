import { useState, useCallback } from "react";
import dayjs from "dayjs";
import "dayjs/locale/fr";
import isoWeek from "dayjs/plugin/isoWeek";
import weekOfYear from "dayjs/plugin/weekOfYear";
import customParseFormat from "dayjs/plugin/customParseFormat";

// Simple date formatting function
const formatApiDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split('T')[0];
};

// Add plugins
dayjs.extend(isoWeek);
dayjs.extend(weekOfYear);
dayjs.extend(customParseFormat);

// Configure dayjs to use French locale
dayjs.locale("fr");

/**
 * Custom hook for managing date filters
 * @returns {Object} Date filter state and functions
 */
const useDateFilter = () => {
  const [dateFilter, setDateFilter] = useState(null);
  const [dateRangeType, setDateRangeType] = useState("day");
  const [dateRangeDescription, setDateRangeDescription] = useState("");
  const [dateFilterActive, setDateFilterActive] = useState(false);

  // Helper function to format date range for display
  const formatDateRange = useCallback((date, rangeType) => {
    if (!date) return { short: "", full: "" };

    try {
      const formattedDate = dayjs(date);
      if (!formattedDate.isValid()) {
        console.error("Invalid date in formatDateRange:", date);
        return { short: "Date invalide", full: "Date invalide" };
      }

      if (rangeType === "day") {
        return {
          short: formattedDate.format("DD/MM/YYYY"),
          full: `le ${formattedDate.format("DD MMMM YYYY")}`,
        };
      } else if (rangeType === "week") {
        // Ensure we're using ISO week (Monday to Sunday)
        const startOfWeek = formattedDate.startOf("isoWeek");
        const endOfWeek = formattedDate.endOf("isoWeek");
        const weekNumber = formattedDate.isoWeek();

        return {
          short: `S${weekNumber} ${formattedDate.format("YYYY")}`,
          full: `Semaine ${weekNumber} (du ${startOfWeek.format("DD MMMM")} au ${endOfWeek.format("DD MMMM YYYY")})`,
        };
      } else if (rangeType === "month") {
        // Capitalize the month name
        const monthName = formattedDate.format("MMMM");
        const capitalizedMonth = monthName.charAt(0).toUpperCase() + monthName.slice(1);

        return {
          short: `${capitalizedMonth} ${formattedDate.format("YYYY")}`,
          full: `${capitalizedMonth} ${formattedDate.format("YYYY")}`,
        };
      }

      return { short: "", full: "" };
    } catch (e) {
      console.error("Error in formatDateRange:", e);
      return { short: "Erreur de date", full: "Erreur de date" };
    }
  }, []);

  // Handle date change
  const handleDateChange = (date) => {
    if (!date) {
      resetDateFilter();
      return;
    }

    try {
      // Adjust the date based on the current date range type
      let adjustedDate = dayjs(date);

      // The date picker already returns the correct date based on the picker mode
      // For week view, the date picker returns the first day of the selected week
      // For month view, the date picker returns the first day of the selected month
      // We don't need to adjust the date anymore

      // Convert to JavaScript Date object
      const finalDate = adjustedDate.toDate();

      // Update state
      setDateFilter(finalDate);
      const { full } = formatDateRange(finalDate, dateRangeType);
      setDateRangeDescription(full);
      setDateFilterActive(true);

      console.log(`Date selected: ${adjustedDate.format('YYYY-MM-DD')}, Range type: ${dateRangeType}`);
    } catch (e) {
      console.error("Error handling date change:", e);
      // Use the original date as fallback
      setDateFilter(date);
      const { full } = formatDateRange(date, dateRangeType);
      setDateRangeDescription(full);
      setDateFilterActive(true);
    }
  };

  // Handle date range type change
  const handleDateRangeTypeChange = (type) => {
    setDateRangeType(type);

    // If a date is already selected, update the description and adjust the date
    if (dateFilter) {
      const date = dayjs(dateFilter);
      let adjustedDate = date;

      // For week view, ensure we're using the first day of the week
      if (type === "week") {
        adjustedDate = date.startOf("isoWeek");
      }
      // For month view, ensure we're using the first day of the month
      else if (type === "month") {
        adjustedDate = date.startOf("month");
      }

      // Update the date filter with the adjusted date
      const finalDate = adjustedDate.toDate();
      setDateFilter(finalDate);

      // Update the description
      const { full } = formatDateRange(finalDate, type);
      setDateRangeDescription(full);

      console.log(`Date range type changed to: ${type}, Adjusted date: ${adjustedDate.format('YYYY-MM-DD')}`);
    }
  };

  // Reset date filter
  const resetDateFilter = () => {
    setDateFilter(null);
    setDateRangeDescription("");
    setDateFilterActive(false);
  };

  // Build query parameters for API requests
  const buildDateQueryParams = useCallback(() => {
    const queryParams = new URLSearchParams();

    // Add date range type and date if a date is selected
    if (dateFilter) {
      try {
        // The date should already be adjusted based on the date range type
        // when it was selected in the date picker or when the date range type was changed
        const formattedDate = formatApiDate(dateFilter);

        if (formattedDate) {
          // Add the date and date range type to the query parameters
          queryParams.append("date", formattedDate);
          queryParams.append("dateRangeType", dateRangeType);

          console.log(`API request params: date=${formattedDate}, dateRangeType=${dateRangeType}`);
        } else {
          console.error("Failed to format date for API request:", dateFilter);
        }
      } catch (e) {
        console.error("Error building date query params:", e);
      }
    }

    return queryParams;
  }, [dateFilter, dateRangeType]);

  return {
    dateFilter,
    dateRangeType,
    dateRangeDescription,
    dateFilterActive,
    handleDateChange,
    handleDateRangeTypeChange,
    resetDateFilter,
    buildDateQueryParams,
    formatDateRange
  };
};

export default useDateFilter;
