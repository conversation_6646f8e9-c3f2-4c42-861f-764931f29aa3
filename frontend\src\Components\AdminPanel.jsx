import { useState, useEffect } from "react";
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  message,
  Space,
  Popconfirm,
  Card,
  Typography,
  Divider,
  Tabs
} from "antd";
import {
  UserOutlined,
  MailOutlined,
  LockOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  TeamOutlined,
  LockFilled,
  SettingOutlined
} from "@ant-design/icons";
import { useAuth } from "../hooks/useAuth";
import RoleManagement from "./RoleManagement";
import DepartmentManagement from "./DepartmentManagement";
import { extractResponseData, isResponseSuccessful, getErrorMessage } from "../utils/apiUtils";
import { secureHttp } from "../utils/superagentConfig";
const { Title } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

const AdminPanel = () => {
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [rolesLoading, setRolesLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingUser, setEditingUser] = useState(null);
  const { user: currentUser } = useAuth(); // isAuthenticated removed - using HTTP-only cookies

  useEffect(() => {
    console.log('🔍 [AdminPanel] Component mounted, current user:', currentUser);
    console.log('🔍 [AdminPanel] Fetching data regardless of user state...');
    fetchUsers();
    fetchRoles();
  }, []); // Run once on mount

  const fetchRoles = async () => {
    console.log('🔍 [AdminPanel] Starting fetchRoles...');
    setRolesLoading(true);
    try {
      console.log('🔍 [AdminPanel] Making request to /api/roles');
      const response = await secureHttp.get('/api/roles');
      console.log('🔍 [AdminPanel] Roles response:', response);

      console.log('🔍 [AdminPanel] Raw roles response structure:', {
        status: response.status,
        body: response.body,
        data: response.data
      });

      if (isResponseSuccessful(response)) {
        // Extract data from response (handles both old and new formats)
        const rolesData = extractResponseData(response);
        console.log('🔍 [AdminPanel] Extracted roles data:', rolesData);
        console.log('🔍 [AdminPanel] Setting roles state with:', rolesData || []);
        setRoles(rolesData || []);
      } else {
        console.error('🔍 [AdminPanel] Roles request failed:', response);
        const errorMessage = response.body?.message || "Échec du chargement des rôles";
        message.error(errorMessage);
      }
    } catch (error) {
      console.error('🔍 [AdminPanel] Roles request error:', error);
      const errorMessage = getErrorMessage(error) || "Échec du chargement des rôles";
      message.error(errorMessage);
    } finally {
      setRolesLoading(false);
    }
  };

  const fetchUsers = async () => {
    console.log('🔍 [AdminPanel] Starting fetchUsers...');
    console.log('🔍 [AdminPanel] Current user:', currentUser);
    setLoading(true);
    try {
      console.log('🔍 [AdminPanel] Making request to /api/users');
      const response = await secureHttp.get('/api/users');
      console.log('🔍 [AdminPanel] Users response:', response);

      console.log('🔍 [AdminPanel] Raw users response structure:', {
        status: response.status,
        body: response.body,
        data: response.data
      });

      if (isResponseSuccessful(response)) {
        // Extract data from response (handles both old and new formats)
        const usersData = extractResponseData(response);
        console.log('🔍 [AdminPanel] Extracted users data:', usersData);
        console.log('🔍 [AdminPanel] Setting users state with:', usersData || []);
        setUsers(usersData || []);
      } else {
        console.error('🔍 [AdminPanel] Users request failed:', response);
        const errorMessage = response.body?.message || "Échec du chargement des utilisateurs";
        message.error(errorMessage);
      }
    } catch (error) {
      console.error('🔍 [AdminPanel] Users request error:', error);
      const errorMessage = getErrorMessage(error) || "Échec du chargement des utilisateurs";
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = () => {
    setEditingUser(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      role_id: user.role_id || null,
    });
    setModalVisible(true);
  };

  const handleDeleteUser = async (userId) => {
    try {
      const response = await secureHttp.delete(`/api/users/${userId}`);

      if (isResponseSuccessful(response)) {
        const successMessage = response.body?.message || "Utilisateur supprimé avec succès";
        message.success(successMessage);
        fetchUsers();
      } else {
        const errorMessage = response.body?.message || "Échec de la suppression de l'utilisateur";
        message.error(errorMessage);
      }
    } catch (error) {
      console.error(error);
      const errorMessage = getErrorMessage(error) || "Échec de la suppression de l'utilisateur";
      message.error(errorMessage);
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingUser) {
        // Update existing user
        const response = await secureHttp.put(`/api/users/${editingUser.id}`, values);

        if (isResponseSuccessful(response)) {
          const successMessage = response.body?.message || "Utilisateur mis à jour avec succès";
          message.success(successMessage);
          setModalVisible(false);
          fetchUsers();
        } else {
          const errorMessage = response.body?.message || "Échec de la mise à jour de l'utilisateur";
          message.error(errorMessage);
        }
      } else {
        // Create new user
        const response = await secureHttp.post('/api/register', values);

        if (isResponseSuccessful(response)) {
          const successMessage = response.body?.message || "Utilisateur créé avec succès";
          message.success(successMessage);
          setModalVisible(false);
          fetchUsers();
        } else {
          const errorMessage = response.body?.message || "Échec de la création de l'utilisateur";
          message.error(errorMessage);
        }
      }
    } catch (error) {
      console.error(error);
      const errorMessage = getErrorMessage(error) || "Opération échouée";
      message.error(errorMessage);
    }
  };

  const columns = [
    {
      title: "Nom d'utilisateur",
      dataIndex: "username",
      key: "username",
      sorter: (a, b) => a.username.localeCompare(b.username),
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Rôle",
      dataIndex: "role_name",
      key: "role",
      render: (role_name, record) => role_name || (record.role ? record.role.charAt(0).toUpperCase() + record.role.slice(1) : ''),
      filters: roles.map(role => ({ text: role.name, value: role.name })),
      onFilter: (value, record) => record.role_name === value,
    },
    {
      title: "Date de création",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
            disabled={record.id === currentUser?.id} // Can't edit yourself
            title="Modifier l'utilisateur"
          />
          <Popconfirm
            title="Êtes-vous sûr de vouloir supprimer cet utilisateur ?"
            onConfirm={() => handleDeleteUser(record.id)}
            okText="Oui"
            cancelText="Non"
            disabled={record.id === currentUser?.id} // Can't delete yourself
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Button
              icon={<DeleteOutlined />}
              danger
              disabled={record.id === currentUser?.id} // Can't delete yourself
              title="Supprimer l'utilisateur"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Card bordered={false}>
        <Title level={2}>
          <SettingOutlined /> Panneau d'administration
        </Title>

        <Divider />

        <Tabs defaultActiveKey="1" type="card">
          <TabPane
            tab={<span><UserOutlined /> Utilisateurs</span>}
            key="1"
          >
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: 16 }}>
              <Title level={4}>Gestion des utilisateurs</Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddUser}
              >
                Ajouter un utilisateur
              </Button>
            </div>

            <Table
              columns={columns}
              dataSource={users}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showTotal: (total) => `Total ${total} utilisateurs`
              }}
            />
          </TabPane>

          <TabPane
            tab={<span><LockFilled /> Rôles et permissions</span>}
            key="2"
          >
            <RoleManagement />
          </TabPane>

          <TabPane
            tab={<span><TeamOutlined /> Départements</span>}
            key="3"
          >
            <DepartmentManagement />
          </TabPane>

          <TabPane
            tab={<span>🔍 Debug</span>}
            key="4"
          >
            <div style={{ padding: 20 }}>
              <Title level={4}>🔍 Authentication Debug</Title>
              <div style={{ background: '#f6f8fa', padding: 16, borderRadius: 8, marginBottom: 16 }}>
                <Text strong>Current User:</Text>
                <pre style={{ marginTop: 8, fontSize: '12px' }}>
                  {JSON.stringify(currentUser, null, 2)}
                </pre>
              </div>
              <Button
                type="primary"
                onClick={() => {
                  console.log('🔍 [Debug] Current user:', currentUser);
                  console.log('🔍 [Debug] Testing /api/users...');
                  secureHttp.get('/api/users')
                    .then(response => {
                      console.log('🔍 [Debug] /api/users success:', response);
                      message.success('Users API test successful');
                    })
                    .catch(error => {
                      console.error('🔍 [Debug] /api/users error:', error);
                      message.error(`Users API test failed: ${error.message}`);
                    });
                }}
              >
                Test Users API
              </Button>
              <Button
                style={{ marginLeft: 8 }}
                onClick={() => {
                  console.log('🔍 [Debug] Testing /api/roles...');
                  secureHttp.get('/api/roles')
                    .then(response => {
                      console.log('🔍 [Debug] /api/roles success:', response);
                      message.success('Roles API test successful');
                    })
                    .catch(error => {
                      console.error('🔍 [Debug] /api/roles error:', error);
                      message.error(`Roles API test failed: ${error.message}`);
                    });
                }}
              >
                Test Roles API
              </Button>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      <Modal
        title={editingUser ? "Modifier l'utilisateur" : "Ajouter un utilisateur"}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ role_id: roles.length > 0 ? roles.find(r => r.name === "user")?.id : null }}
        >
          <Form.Item
            name="username"
            label="Nom d'utilisateur"
            rules={[{ required: true, message: "Veuillez entrer un nom d'utilisateur" }]}
          >
            <Input prefix={<UserOutlined />} placeholder="Nom d'utilisateur" />
          </Form.Item>

          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: "Veuillez entrer un email" },
              { type: "email", message: "Veuillez entrer un email valide" },
            ]}
          >
            <Input prefix={<MailOutlined />} placeholder="Email" />
          </Form.Item>

          {!editingUser && (
            <Form.Item
              name="password"
              label="Mot de passe"
              rules={[
                { required: true, message: "Veuillez entrer un mot de passe" },
                { min: 6, message: "Le mot de passe doit contenir au moins 6 caractères" }
              ]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="Mot de passe" />
            </Form.Item>
          )}

          <Form.Item
            name="role_id"
            label="Rôle"
            rules={[{ required: true, message: "Veuillez sélectionner un rôle" }]}
          >
            <Select
              placeholder="Sélectionner un rôle"
              loading={rolesLoading}
            >
              {roles.map(role => (
                <Option key={role.id} value={role.id}>{role.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item>
            <div style={{ display: "flex", justifyContent: "flex-end", gap: 8 }}>
              <Button onClick={() => setModalVisible(false)}>
                Annuler
              </Button>
              <Button type="primary" htmlType="submit">
                {editingUser ? "Mettre à jour" : "Créer"}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminPanel;