// Quick test to check if the modular context is working
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testGraphQLQueries() {
  try {
    console.log('🧪 Testing GraphQL queries...');
    
    // Test the basic query
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getStopSidecards {
              Arret_Totale
              Arret_Totale_nondeclare
            }
          }
        `
      })
    });
    
    const result = await response.json();
    console.log('📊 GraphQL Response:', JSON.stringify(result, null, 2));
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
    } else {
      console.log('✅ GraphQL working correctly');
    }
    
  } catch (error) {
    console.error('❌ Error testing GraphQL:', error.message);
  }
}

testGraphQLQueries();
