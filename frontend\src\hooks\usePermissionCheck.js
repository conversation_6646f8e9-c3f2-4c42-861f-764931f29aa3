import { usePermission } from './usePermission';
import { createPer<PERSON><PERSON><PERSON><PERSON>, filterByPermission } from '../utils/permissionUtils';

/**
 * Custom hook for checking permissions for actions and filtering items
 * 
 * @returns {Object} Permission checking functions
 */
export function usePermissionCheck() {
  const { hasPermission, hasRole, hasDepartmentAccess } = usePermission();
  
  // Create a permission checker function
  const canPerformAction = createPermissionChecker(hasPermission, hasRole);
  
  // Create a function to filter items by permission
  const filterItemsByPermission = (items, permissionKey = 'permissions', roleKey = 'roles') => {
    return filterByPermission(items, hasPermission, hasRole, permissionKey, roleKey);
  };
  
  return {
    canPerformAction,
    filterItemsByPermission,
    hasPermission,
    hasRole,
    hasDepartmentAccess
  };
}

export default usePermissionCheck;
