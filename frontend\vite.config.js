import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    react({
      // Add babel plugins for production builds
      babel: {
        plugins: [
          process.env.NODE_ENV === 'production' && 'transform-remove-console',
          process.env.NODE_ENV === 'production' && 'transform-remove-debugger',
        ].filter(Boolean),
      },
    }),
    // Add bundle visualizer in analyze mode
    process.env.ANALYZE && visualizer({
      open: true,
      gzipSize: true,
      brotliSize: true,
    }),
  ].filter(Boolean),

  build: {
    outDir: 'dist',
    // Generate manifest for better caching
    manifest: true,
    // Configure esbuild for build process
    esbuildOptions: {
      loader: {
        '.js': 'jsx',  // Treat .js files as JSX during build
      },
    },
    rollupOptions: {
      output: {
        // Simplified chunking strategy to avoid dependency issues
        manualChunks: {
          // Group all React and related packages together
          'react-vendor': [
            'react',
            'react-dom',
            'react-router-dom',
            'react-router',
            'scheduler',
            '@remix-run/router'
          ],

          // Group Ant Design together
          'antd-vendor': [
            'antd',
            '@ant-design/icons'
          ],

          // Group chart libraries
          'chart-vendor': [
            'chart.js',
            'react-chartjs-2',
            'recharts'
          ],

          // Group utilities
          'utils-vendor': [
            'lodash',
            'dayjs',
            'date-fns'
          ]
        },
        // Optimize chunk size
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
      }
    },
    // Improve build performance
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production',
      },
    },
    sourcemap: process.env.NODE_ENV !== 'production',
    // Optimize CSS
    cssCodeSplit: true,
    // Adjust chunk size warning limit
    chunkSizeWarningLimit: 1200,
  },

  server: {
    // Ensure proper CORS headers
    cors: {
      origin: process.env.NODE_ENV === 'production'
        ? ['https://somipem.example.com'] // Replace with actual production domain
        : true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      credentials: true,
    },
    // Allow access from any host
    host: '0.0.0.0',
    port: 5173,
    // Allow iframes
    headers: {
      'X-Frame-Options': 'ALLOWALL',
      'Access-Control-Allow-Origin': '*',
    },
    // Proxy API requests to backend
    proxy: {
      '/api': {
        target: process.env.VITE_API_URL || 'http://localhost:5000',
        changeOrigin: true,
        secure: false,
        ws: true,
        configure: (proxy, options) => {
          // Handle SSE connections properly
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // Set headers for SSE
            if (req.url && req.url.includes('/notifications/stream')) {
              proxyReq.setHeader('Accept', 'text/event-stream');
              proxyReq.setHeader('Cache-Control', 'no-cache');
              proxyReq.setHeader('Connection', 'keep-alive');
            }
          });
          
          proxy.on('proxyRes', (proxyRes, req, res) => {
            // Set CORS headers for SSE
            if (req.url && req.url.includes('/notifications/stream')) {
              proxyRes.headers['access-control-allow-origin'] = req.headers.origin || '*';
              proxyRes.headers['access-control-allow-credentials'] = 'true';
              proxyRes.headers['access-control-allow-headers'] = 'authorization, content-type, accept';
            }
          });
          
          proxy.on('error', (err, req, res) => {
            console.log('Proxy error:', err);
          });
        }
      },
      '/socket.io': {
        target: process.env.VITE_API_URL || 'http://localhost:5000',
        changeOrigin: true,
        ws: true,
      }
    },
    // Improve HMR performance
    hmr: {
      overlay: true,
    },
  },

  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'antd',
      'lodash',
      'dayjs',
      'chart.js',
      'react-chartjs-2',
      'recharts',
    ],
    esbuildOptions: {
      target: 'es2020',
      loader: {
        '.js': 'jsx',  // Configure esbuild to treat .js files as JSX
      },
    },
  },

  // Improve resolve performance
  resolve: {
    alias: {
      '@': '/src',
    },
  },

  // Enable fast refresh
  experimental: {
    renderBuiltUrl: (filename) => ({ relative: true }),
  },
});
