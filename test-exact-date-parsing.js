/**
 * Test the exact date format we're seeing in console
 */

function testExactDateParsing() {
  console.log('🧪 Testing exact date parsing from console output');
  
  const testDates = [
    '2024 10:35:13-12- 3',
    '2024 10:33:28-12- 3', 
    '2024 09:55:38-12- 3',
    '2025 06:57:27-05- 2',
    '2025 23:20:28-05- 1',
    '2025 23:20:20-05- 1',
    '2025 17:08:13-05- 1'
  ];
  
  testDates.forEach(dateStr => {
    console.log('\n🔍 Processing:', dateStr);
    
    // Test our regex
    const match = dateStr.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);
    if (match) {
      const [_, year, month, day] = match;
      const isoDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      console.log('✅ Regex match - Year:', year, 'Month:', month, 'Day:', day);
      console.log('📅 ISO format:', isoDate);
      
      // Test Date object creation
      const dateObj = new Date(isoDate);
      const isValid = !isNaN(dateObj.getTime());
      console.log('🔄 Date object valid:', isValid);
      
      if (isValid) {
        const displayDate = dateObj.toLocaleDateString('fr-FR', { 
          day: '2-digit', 
          month: '2-digit' 
        });
        console.log('🎯 Display date:', displayDate);
      }
    } else {
      console.log('❌ No regex match');
    }
  });
  
  console.log('\n📊 Expected result: All dates should parse to proper DD/MM format');
}

testExactDateParsing();
