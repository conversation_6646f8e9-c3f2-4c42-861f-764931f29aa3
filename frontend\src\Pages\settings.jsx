"use client"

import { useState, useEffect } from "react"
import {
  Card,
  Tabs,
  Form,
  Switch,
  Select,
  InputNumber,
  Button,
  Divider,
  Row,
  Col,
  Typography,
  message,
  Space,
} from "antd"
import {
  UserOutlined,
  BellOutlined,
  MailOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  FileTextOutlined,
  Clock<PERSON>ircleOutlined,
  SaveOutlined,
  ReloadOutlined,
  SendOutlined,
} from "@ant-design/icons"
import { useSettings } from "../context/SettingsContext"
import { useTheme } from "../theme-context"

const { Title, Text } = Typography
const { TabPane } = Tabs
const { Option } = Select

const SettingsPage = () => {
  const {
    settings,
    loading,
    updateSetting,
    updateSettings,
    testEmailSettings,
    loadEmailSettings,
    loadShiftSettings,
    loadReportSettings,
  } = useSettings()
  const { darkMode, toggleDarkMode } = useTheme()
  const [form] = Form.useForm()
  const [activeTab, setActiveTab] = useState("interface")
  const [saving, setSaving] = useState(false)
  const [testingEmail, setTestingEmail] = useState(false)

  // Mettre à jour le formulaire quand les paramètres sont chargés
  useEffect(() => {
    if (!loading) {
      form.setFieldsValue(settings)
    }
  }, [form, settings, loading])

  // Charger les paramètres spécifiques en fonction de l'onglet actif
  useEffect(() => {
    if (activeTab === "email") {
      loadEmailSettings()
    } else if (activeTab === "shift") {
      loadShiftSettings()
    } else if (activeTab === "reports") {
      loadReportSettings()
    }
  }, [activeTab, loadEmailSettings, loadShiftSettings, loadReportSettings])

  const handleTabChange = (key) => {
    setActiveTab(key)
  }

  const handleSave = async (values) => {
    setSaving(true)
    try {
      const success = await updateSettings(values)
      if (success) {
        message.success("Paramètres enregistrés avec succès")
      }
    } finally {
      setSaving(false)
    }
  }

  const handleTestEmail = async () => {
    setTestingEmail(true)
    try {
      await testEmailSettings()
    } finally {
      setTestingEmail(false)
    }
  }

  // Gérer le changement de mode sombre
  const handleDarkModeChange = (checked) => {
    toggleDarkMode()
    updateSetting("darkMode", checked)
  }

  if (loading) {
    return (
      <Card loading={true} style={{ margin: "24px" }}>
        <div style={{ height: "400px" }}></div>
      </Card>
    )
  }

  return (
    <Card
      title={
        <Space>
          <SettingOutlined />
          <span>Paramètres</span>
        </Space>
      }
      style={{ margin: "24px" }}
    >
      <Form form={form} layout="vertical" initialValues={settings} onFinish={handleSave}>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          {/* Onglet Interface */}
          <TabPane
            tab={
              <span>
                <SettingOutlined /> Interface
              </span>
            }
            key="interface"
          >
            <Title level={4}>Apparence et comportement</Title>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item name="darkMode" label="Mode sombre" valuePropName="checked">
                  <Switch checked={darkMode} onChange={handleDarkModeChange} />
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item name="compactMode" label="Mode compact" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item name="animationsEnabled" label="Animations de l'interface" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item name="chartAnimations" label="Animations des graphiques" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Divider />

            <Title level={4}>Affichage des données</Title>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item name="dataDisplayMode" label="Mode d'affichage par défaut">
                  <Select>
                    <Option value="chart">Graphiques</Option>
                    <Option value="table">Tableaux</Option>
                    <Option value="mixed">Mixte</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item name="dashboardRefreshRate" label="Taux de rafraîchissement du tableau de bord (secondes)">
                  <InputNumber min={10} max={300} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item name="defaultView" label="Vue par défaut">
                  <Select>
                    <Option value="dashboard">Tableau de bord</Option>
                    <Option value="production">Production</Option>
                    <Option value="arrets">Arrêts</Option>
                    <Option value="reports">Rapports</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item name="tableRowsPerPage" label="Lignes par page dans les tableaux">
                  <Select>
                    <Option value={10}>10</Option>
                    <Option value={20}>20</Option>
                    <Option value={50}>50</Option>
                    <Option value={100}>100</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          {/* Onglet Notifications */}
          <TabPane
            tab={
              <span>
                <BellOutlined /> Notifications
              </span>
            }
            key="notifications"
          >
            <Title level={4}>Paramètres de notification</Title>

            <Form.Item name="notificationsEnabled" label="Activer les notifications" valuePropName="checked">
              <Switch />
            </Form.Item>

            <Divider />

            <Title level={4}>Types de notifications</Title>

            <Row gutter={24}>
              <Col xs={24} md={8}>
                <Form.Item name="notifyMachineAlerts" label="Alertes machines" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>

              <Col xs={24} md={8}>
                <Form.Item name="notifyMaintenance" label="Maintenance" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>

              <Col xs={24} md={8}>
                <Form.Item name="notifyUpdates" label="Mises à jour système" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          {/* Onglet Email */}
          <TabPane
            tab={
              <span>
                <MailOutlined /> Email
              </span>
            }
            key="email"
          >
            <Title level={4}>Notifications par email</Title>

            <Form.Item name="emailNotifications" label="Activer les notifications par email" valuePropName="checked">
              <Switch />
            </Form.Item>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item name="emailFormat" label="Format des emails">
                  <Select>
                    <Option value="html">HTML</Option>
                    <Option value="text">Texte brut</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item name="emailDigest" label="Recevoir un résumé quotidien" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Divider />

            <Button type="primary" icon={<SendOutlined />} onClick={handleTestEmail} loading={testingEmail}>
              Tester les paramètres d'email
            </Button>
          </TabPane>

          {/* Onglet Rapports de quart */}
          <TabPane
            tab={
              <span>
                <ClockCircleOutlined /> Rapports de quart
              </span>
            }
            key="shift"
          >
            <Title level={4}>Paramètres des rapports de quart</Title>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item name="defaultShift" label="Quart par défaut">
                  <Select>
                    <Option value="Matin">Matin (06:00 - 14:00)</Option>
                    <Option value="Après-midi">Après-midi (14:00 - 22:00)</Option>
                    <Option value="Nuit">Nuit (22:00 - 06:00)</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item
                  name="shiftReportNotifications"
                  label="Notifications pour les rapports de quart"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="shiftReportEmails"
              label="Recevoir les rapports de quart par email"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Divider />

            <Title level={4}>Paramètres par quart</Title>

            <Row gutter={24}>
              <Col xs={24} md={8}>
                <Title level={5}>Matin</Title>
                <Form.Item name="shift1Notifications" label="Notifications" valuePropName="checked">
                  <Switch />
                </Form.Item>
                <Form.Item name="shift1Emails" label="Emails" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>

              <Col xs={24} md={8}>
                <Title level={5}>Après-midi</Title>
                <Form.Item name="shift2Notifications" label="Notifications" valuePropName="checked">
                  <Switch />
                </Form.Item>
                <Form.Item name="shift2Emails" label="Emails" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>

              <Col xs={24} md={8}>
                <Title level={5}>Nuit</Title>
                <Form.Item name="shift3Notifications" label="Notifications" valuePropName="checked">
                  <Switch />
                </Form.Item>
                <Form.Item name="shift3Emails" label="Emails" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          {/* Onglet Rapports */}
          <TabPane
            tab={
              <span>
                <FileTextOutlined /> Rapports
              </span>
            }
            key="reports"
          >
            <Title level={4}>Paramètres des rapports</Title>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item name="defaultReportFormat" label="Format de rapport par défaut">
                  <Select>
                    <Option value="pdf">PDF</Option>
                    <Option value="excel">Excel</Option>
                    <Option value="csv">CSV</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item
                  name="reportAutoDownload"
                  label="Téléchargement automatique des rapports"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          {/* Onglet Sécurité */}
          <TabPane
            tab={
              <span>
                <SecurityScanOutlined /> Sécurité
              </span>
            }
            key="security"
          >
            <Title level={4}>Paramètres de sécurité</Title>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item name="sessionTimeout" label="Délai d'expiration de session (minutes)">
                  <InputNumber min={5} max={240} />
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item name="loginNotifications" label="Notifications de connexion" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item name="twoFactorAuth" label="Authentification à deux facteurs" valuePropName="checked">
              <Switch />
            </Form.Item>

            <Text type="secondary">
              L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à votre compte.
            </Text>
          </TabPane>

          {/* Onglet Profil */}
          <TabPane
            tab={
              <span>
                <UserOutlined /> Profil
              </span>
            }
            key="profile"
          >
            <Title level={4}>Paramètres du profil</Title>

            <Text>Les paramètres du profil sont gérés dans la page de profil utilisateur.</Text>

            <Divider />

            <Button type="primary" href="/profile">
              Accéder à mon profil
            </Button>
          </TabPane>
        </Tabs>

        <Divider />

        <Row justify="end" gutter={16}>
          <Col>
            <Button icon={<ReloadOutlined />} onClick={() => form.resetFields()}>
              Réinitialiser
            </Button>
          </Col>
          <Col>
            <Button type="primary" icon={<SaveOutlined />} htmlType="submit" loading={saving}>
              Enregistrer
            </Button>
          </Col>
        </Row>
      </Form>
    </Card>
  )
}

export default SettingsPage

