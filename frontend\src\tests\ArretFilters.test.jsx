import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ArretFilters from '../Components/arrets/ArretFilters';
import { ArretProvider } from '../context/arret/ArretContext';

// Create a test wrapper that includes the ArretProvider
const renderWithArretProvider = (ui) => {
  return render(
    <ArretProvider>
      {ui}
    </ArretProvider>
  );
};

describe('ArretFilters Component', () => {
  const mockOnFilterChange = vi.fn();
  
  beforeEach(() => {
    mockOnFilterChange.mockClear();
  });
  
  it('renders correctly with default values', async () => {
    renderWithArretProvider(<ArretFilters onFilterChange={mockOnFilterChange} />);
    
    // Check that basic elements are present
    await waitFor(() => {
      expect(screen.getByText(/Modèle de Machine/i)).toBeInTheDocument();
      expect(screen.getByText(/Machine Spécifique/i)).toBeInTheDocument();
      expect(screen.getByText(/Type de Période/i)).toBeInTheDocument();
      expect(screen.getByText(/Sélection de Date/i)).toBeInTheDocument();
    });
  });
  
  it('calls onFilterChange when filters change', async () => {
    renderWithArretProvider(<ArretFilters onFilterChange={mockOnFilterChange} />);
    
    // Wait for initial render
    await waitFor(() => {
      expect(mockOnFilterChange).toHaveBeenCalledTimes(1);
    });
    
    // Test will verify that our changes to ensure onFilterChange is always called work correctly
  });
});
