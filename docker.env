# Database Configuration for Docker
# Use host.docker.internal to connect to host MySQL from containers
DB_HOST=host.docker.internal
DB_USER=root
DB_PASS=root
DB_NAME=Testingarea51
PORT=5000

# JWT Configuration
JWT_SECRET=dadfba2957b57678f0bdf5bb2f27b1938b9a0b472cd95a49a7f01397af5db005b9072f3d5b0a42268571b735187e2a7aedc532ae79797206fe5789718711d719
JWT_EXPIRE=8h
JWT_COOKIE_EXPIRE=1

# Cache Configuration
DISABLE_CACHE=false

# Email Configuration
SMTP_HOST=sandbox.smtp.mailtrap.io
SMTP_PORT=587
SMTP_EMAIL=b43b394d59fa77
SMTP_PASSWORD=60513ef904592c
FROM_NAME=Your DASHBOARD
FROM_EMAIL=<EMAIL>

# Frontend URL for Docker environment (ngrok-compatible)
FRONTEND_URL=http://localhost:5173

# Node Environment
NODE_ENV=development

# ngrok WebSocket Configuration
NGROK_WS_URL=wss://charming-hermit-intense.ngrok-free.app
NGROK_HTTP_URL=https://charming-hermit-intense.ngrok-free.app

# WebSocket Configuration for Docker
WS_EXTERNAL_URL=wss://charming-hermit-intense.ngrok-free.app
