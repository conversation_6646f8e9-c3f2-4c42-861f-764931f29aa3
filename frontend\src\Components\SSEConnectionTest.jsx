import React, { useState, useRef } from 'react';
import { Card, Button, Typography, Space, Alert } from 'antd';
import superagent from 'superagent';

const { Text, Title } = Typography;

const SSEConnectionTest = () => {
  const [status, setStatus] = useState('disconnected');
  const [logs, setLogs] = useState([]);
  const [token, setToken] = useState('');
  const eventSourceRef = useRef(null);

  const addLog = (message) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    console.log(message);
  };

  const testGetToken = async () => {
    try {
      addLog('🔑 Testing token request...');
      const response = await superagent.get('/api/sse-token').withCredentials();
      const receivedToken = response.data?.data?.sseToken || response.data?.sseToken;
      
      if (receivedToken) {
        setToken(receivedToken);
        addLog('✅ Token received successfully');
        return receivedToken;
      } else {
        addLog('❌ No token in response');
        return null;
      }
    } catch (error) {
      addLog(`❌ Token request failed: ${error.response?.data?.message || error.message}`);
      return null;
    }
  };

  const testDirectConnection = async () => {
    try {
      addLog('🔌 Testing direct SSE connection...');
      const tokenToUse = token || await testGetToken();
      
      if (!tokenToUse) {
        addLog('❌ Cannot connect without token');
        return;
      }

      // Close existing connection
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      setStatus('connecting');
      
      // Try direct connection
      const sseUrl = `http://localhost:5000/api/notifications/stream?token=${encodeURIComponent(tokenToUse)}`;
      addLog(`📡 Connecting to: ${sseUrl}`);
      
      const eventSource = new EventSource(sseUrl);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        setStatus('connected');
        addLog('✅ SSE connection established!');
      };

      eventSource.onmessage = (event) => {
        addLog(`📨 Message received: ${event.data}`);
      };

      eventSource.onerror = (error) => {
        setStatus('error');
        addLog(`❌ SSE error: ${JSON.stringify(error)}`);
        addLog(`❌ EventSource state: ${eventSource.readyState}`);
      };

    } catch (error) {
      setStatus('error');
      addLog(`❌ Connection failed: ${error.message}`);
    }
  };

  const testProxyConnection = async () => {
    try {
      addLog('🔌 Testing proxy SSE connection...');
      const tokenToUse = token || await testGetToken();
      
      if (!tokenToUse) {
        addLog('❌ Cannot connect without token');
        return;
      }

      // Close existing connection
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      setStatus('connecting');
      
      // Try proxy connection
      const sseUrl = `/api/notifications/stream?token=${encodeURIComponent(tokenToUse)}`;
      addLog(`📡 Connecting via proxy to: ${sseUrl}`);
      
      const eventSource = new EventSource(sseUrl);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        setStatus('connected');
        addLog('✅ SSE proxy connection established!');
      };

      eventSource.onmessage = (event) => {
        addLog(`📨 Message received: ${event.data}`);
      };

      eventSource.onerror = (error) => {
        setStatus('error');
        addLog(`❌ SSE proxy error: ${JSON.stringify(error)}`);
        addLog(`❌ EventSource state: ${eventSource.readyState}`);
      };

    } catch (error) {
      setStatus('error');
      addLog(`❌ Proxy connection failed: ${error.message}`);
    }
  };

  const disconnect = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setStatus('disconnected');
    addLog('🔌 Connection closed');
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>SSE Connection Test</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="Connection Status">
          <Text strong>Status: </Text>
          <Text type={status === 'connected' ? 'success' : status === 'error' ? 'danger' : 'secondary'}>
            {status.toUpperCase()}
          </Text>
          {token && (
            <div>
              <Text strong>Token: </Text>
              <Text code>{token.substring(0, 20)}...</Text>
            </div>
          )}
        </Card>

        <Card title="Test Actions">
          <Space wrap>
            <Button onClick={testGetToken}>Get Token</Button>
            <Button type="primary" onClick={testDirectConnection}>Test Direct Connection</Button>
            <Button onClick={testProxyConnection}>Test Proxy Connection</Button>
            <Button danger onClick={disconnect}>Disconnect</Button>
            <Button onClick={clearLogs}>Clear Logs</Button>
          </Space>
        </Card>

        <Card title="Connection Logs">
          <div style={{ 
            height: '300px', 
            overflow: 'auto', 
            backgroundColor: '#f5f5f5', 
            padding: '10px',
            fontFamily: 'monospace',
            fontSize: '12px'
          }}>
            {logs.length === 0 ? (
              <Text type="secondary">No logs yet...</Text>
            ) : (
              logs.map((log, index) => (
                <div key={index}>{log}</div>
              ))
            )}
          </div>
        </Card>

        <Alert 
          message="Testing Instructions" 
          description="1. Click 'Get Token' first. 2. Try 'Test Direct Connection' - this bypasses the proxy. 3. If direct works, try 'Test Proxy Connection'. 4. Check logs for detailed error messages."
          type="info" 
        />
      </Space>
    </div>
  );
};

export default SSEConnectionTest;