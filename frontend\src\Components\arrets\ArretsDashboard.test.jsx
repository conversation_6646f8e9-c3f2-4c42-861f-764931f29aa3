import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { message } from 'antd';
import ArretsDashboard from '../../Pages/ArretsDashboard';

// Mock axios for testing
jest.mock('axios');

describe('ArretsDashboard Integration Tests', () => {
  beforeEach(() => {
    // Mock console.error to avoid context error noise in tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('should render dashboard with all main components', async () => {
    render(<ArretsDashboard />);
    
    // Check for main sections
    expect(screen.getByText(/Tableau de Bord des Arrêts/i)).toBeInTheDocument();
    
    // Check for filter controls
    expect(screen.getByText(/Modèle de Machine/i)).toBeInTheDocument();
    expect(screen.getByText(/Machine Spécifique/i)).toBeInTheDocument();
    expect(screen.getByText(/Type de Période/i)).toBeInTheDocument();
    
    // Check for auto/manual mode switch
    expect(screen.getByRole('switch')).toBeInTheDocument();
    
    // Check for action buttons
    expect(screen.getByText(/Effacer/i)).toBeInTheDocument();
    expect(screen.getByText(/Actualiser/i)).toBeInTheDocument();
  });

  test('should have filters positioned above stats cards', () => {
    const { container } = render(<ArretsDashboard />);
    
    const filterSection = container.querySelector('.ant-space');
    const statsSection = container.querySelector('.ant-row');
    
    // Filters should appear before stats in DOM order
    expect(filterSection.compareDocumentPosition(statsSection) & Node.DOCUMENT_POSITION_FOLLOWING).toBeTruthy();
  });

  test('should default to IPS machine model', async () => {
    render(<ArretsDashboard />);
    
    await waitFor(() => {
      const machineModelSelect = screen.getByDisplayValue(/IPS/i);
      expect(machineModelSelect).toBeInTheDocument();
    });
  });

  test('should have auto mode enabled by default', () => {
    render(<ArretsDashboard />);
    
    const autoSwitch = screen.getByRole('switch');
    expect(autoSwitch).toBeChecked();
  });

  test('should toggle between auto and manual mode', () => {
    render(<ArretsDashboard />);
    
    const autoSwitch = screen.getByRole('switch');
    expect(autoSwitch).toBeChecked();
    
    // Click to switch to manual mode
    fireEvent.click(autoSwitch);
    expect(autoSwitch).not.toBeChecked();
    
    // Click to switch back to auto mode
    fireEvent.click(autoSwitch);
    expect(autoSwitch).toBeChecked();
  });

  test('should display filter indicators when filters are active', async () => {
    render(<ArretsDashboard />);
    
    // Should show IPS model tag by default
    await waitFor(() => {
      expect(screen.getByText(/Modèle: IPS/i)).toBeInTheDocument();
    });
  });

  test('should clear filters when clear button is clicked', async () => {
    render(<ArretsDashboard />);
    
    const clearButton = screen.getByText(/Effacer/i);
    
    // Button should be enabled when filters are active (IPS default)
    await waitFor(() => {
      expect(clearButton).not.toBeDisabled();
    });
    
    fireEvent.click(clearButton);
    
    // After clearing, model tag should be removed
    await waitFor(() => {
      expect(screen.queryByText(/Modèle: IPS/i)).not.toBeInTheDocument();
    });
  });

  test('should have proper French labels', () => {
    render(<ArretsDashboard />);
    
    // Check for French labels
    expect(screen.getByText(/Modèle de Machine/i)).toBeInTheDocument();
    expect(screen.getByText(/Machine Spécifique/i)).toBeInTheDocument();
    expect(screen.getByText(/Type de Période/i)).toBeInTheDocument();
    expect(screen.getByText(/Sélection de Date/i)).toBeInTheDocument();
    expect(screen.getByText(/Jour/i)).toBeInTheDocument();
    expect(screen.getByText(/Semaine/i)).toBeInTheDocument();
    expect(screen.getByText(/Mois/i)).toBeInTheDocument();
  });

  test('should display stats cards with proper colors', async () => {
    render(<ArretsDashboard />);
    
    await waitFor(() => {
      // Check that stats cards are rendered
      const statsCards = screen.getAllByRole('img', { name: /anticon/ });
      expect(statsCards.length).toBeGreaterThan(0);
    });
  });
});

// Additional utility tests for context functionality
describe('ArretContext Functionality', () => {
  test('should maintain filter state correctly', () => {
    // This would test the context state management
    expect(true).toBe(true); // Placeholder
  });

  test('should handle API errors gracefully', () => {
    // This would test error handling
    expect(true).toBe(true); // Placeholder
  });

  test('should optimize re-renders', () => {
    // This would test performance optimizations
    expect(true).toBe(true); // Placeholder
  });
});
