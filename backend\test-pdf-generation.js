import pdfGenerationService from './services/pdfGenerationService.js';
import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';

/**
 * Test script for the new PDF generation service
 * Run with: node test-pdf-generation.js
 */

// Sample report data for testing
const sampleReportData = {
  machine: {
    id: 'TEST01',
    name: 'Machine Test 01',
    model: 'Test Model'
  },
  shift: 'Matin',
  date: '2025-01-16',
  period: {
    start: '2025-01-16T06:00:00Z',
    end: '2025-01-16T14:00:00Z'
  },
  performance: {
    oee: 85.5,
    availability: 92.3,
    performanceRate: 88.7,
    qualityRate: 95.2,
    runTime: 7.2,
    downTime: 0.8,
    theoreticalRate: 120,
    actualRate: 106,
    cycleTime: 34
  },
  production: {
    totalProduction: 850,
    goodParts: 810,
    rejects: 40
  },
  sessions: [
    {
      session_start: '2025-01-16T06:00:00Z',
      session_end: '2025-01-16T07:00:00Z',
      Quantite_Bon: 105,
      Quantite_Rejet: 5,
      cycle: 32.5,
      TRS: 87.2,
      Article: 'TEST-PART-001'
    },
    {
      session_start: '2025-01-16T07:00:00Z',
      session_end: '2025-01-16T08:00:00Z',
      Quantite_Bon: 110,
      Quantite_Rejet: 3,
      cycle: 31.8,
      TRS: 89.5,
      Article: 'TEST-PART-001'
    },
    {
      session_start: '2025-01-16T08:00:00Z',
      session_end: '2025-01-16T09:00:00Z',
      Quantite_Bon: 108,
      Quantite_Rejet: 4,
      cycle: 33.1,
      TRS: 86.8,
      Article: 'TEST-PART-002'
    }
  ]
};

async function testPDFGeneration() {
  console.log('🧪 Starting PDF Generation Test...');

  try {
    // Test frontend accessibility first
    console.log('🌐 Testing frontend accessibility...');
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

    try {
      const response = await fetch(`${frontendUrl}/reports/pdf-test`);
      if (response.ok) {
        console.log('✅ Frontend is accessible');
      } else {
        console.warn('⚠️ Frontend test route returned:', response.status);
      }
    } catch (fetchError) {
      console.error('❌ Frontend not accessible:', fetchError.message);
      console.log('💡 Make sure the frontend is running on', frontendUrl);
      throw new Error('Frontend not accessible - please start the frontend server');
    }

    // Test health check
    console.log('🔍 Testing PDF service health check...');
    const health = await pdfGenerationService.healthCheck();
    console.log('Health status:', health);

    if (health.status !== 'healthy') {
      throw new Error('PDF service is not healthy');
    }
    
    // Generate test PDF
    console.log('📄 Generating test PDF...');
    const startTime = Date.now();
    
    const pdfBuffer = await pdfGenerationService.generateShiftReportPDF(sampleReportData, {
      format: 'A4',
      margin: {
        top: '20mm',
        right: '15mm',
        bottom: '20mm',
        left: '15mm'
      }
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ PDF generated successfully in ${duration}ms`);
    console.log(`📊 PDF size: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);
    
    // Save test PDF
    const testDir = path.join(process.cwd(), 'test-output');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    const filename = `test-pdf-${Date.now()}.pdf`;
    const filepath = path.join(testDir, filename);
    
    fs.writeFileSync(filepath, pdfBuffer);
    console.log(`💾 Test PDF saved to: ${filepath}`);
    
    // Cleanup
    await pdfGenerationService.cleanup();
    console.log('🧹 Cleanup completed');
    
    console.log('🎉 PDF Generation Test completed successfully!');
    
  } catch (error) {
    console.error('❌ PDF Generation Test failed:', error);
    
    // Cleanup on error
    try {
      await pdfGenerationService.cleanup();
    } catch (cleanupError) {
      console.error('❌ Cleanup failed:', cleanupError);
    }
    
    process.exit(1);
  }
}

// Run the test
testPDFGeneration();
