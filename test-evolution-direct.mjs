// Direct test of dashboard data flow
import { createClient } from '@apollo/client';

const client = createClient({
  uri: 'http://localhost:4000/graphql',
});

const GET_STOPS_DATA = `
  query GetStopsData {
    getStopsData {
      stops {
        id
        Date_Insert
        Machine_Name
        duration_minutes
        Regleur_Prenom
      }
    }
  }
`;

async function testEvolutionDataGeneration() {
  try {
    console.log('🔄 Testing evolution data generation...');
    
    const response = await client.query({
      query: GET_STOPS_DATA
    });
    
    const stopsData = response.data?.getStopsData?.stops || [];
    console.log('📊 Raw stops data length:', stopsData.length);
    console.log('📊 Sample stops:', stopsData.slice(0, 3));
    
    // Mirror the evolution data generation logic
    const dailyStats = {};
    stopsData.forEach(stop => {
      if (stop.Date_Insert) {
        let date;
        if (stop.Date_Insert.includes('/')) {
          const [day, month, year] = stop.Date_Insert.split('/');
          date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        } else {
          date = stop.Date_Insert.split('T')[0];
        }
        
        if (!dailyStats[date]) {
          dailyStats[date] = { date, stops: 0, duration: 0 };
        }
        dailyStats[date].stops++;
        
        if (stop.duration_minutes && stop.duration_minutes > 0) {
          dailyStats[date].duration += parseFloat(stop.duration_minutes);
        }
      }
    });
    
    const evolutionData = Object.values(dailyStats)
      .sort((a, b) => new Date(a.date) - new Date(b.date))
      .slice(-7) // Last 7 days
      .map(item => ({
        ...item,
        displayDate: new Date(item.date).toLocaleDateString('fr-FR', { 
          day: '2-digit', 
          month: '2-digit' 
        })
      }));
    
    console.log('📈 Generated evolution data:', evolutionData);
    console.log('✅ Evolution data valid:', evolutionData.length > 0 && evolutionData[0].date && evolutionData[0].stops);
    
  } catch (error) {
    console.error('❌ Error testing evolution data:', error);
  }
}

testEvolutionDataGeneration();
