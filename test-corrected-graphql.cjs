const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testCorrectedGraphQL() {
  console.log('Testing corrected GraphQL queries...');
  
  try {
    // Test 1: StopSideCard with correct fields
    console.log('\n📊 Testing StopSideCard...');
    const sidecardsQuery = `
      query($filters: StopFilterInput) {
        getStopSidecards(filters: $filters) {
          Arret_Totale
          Arret_Totale_nondeclare
        }
      }
    `;
    
    const sidecardsResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: sidecardsQuery,
        variables: {
          filters: {
            model: 'IPS',
            date: '2024-01-01',
            dateRangeType: 'day'
          }
        }
      })
    });
    
    const sidecardsData = await sidecardsResponse.json();
    console.log('✅ StopSideCard Response:', JSON.stringify(sidecardsData, null, 2));
    
    // Test 2: MachineNames with correct fields
    console.log('\n🔧 Testing MachineNames...');
    const machineNamesQuery = `
      query {
        getStopMachineNames {
          Machine_Name
        }
      }
    `;
    
    const machineNamesResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: machineNamesQuery
      })
    });
    
    const machineNamesData = await machineNamesResponse.json();
    console.log('✅ MachineNames Response:', JSON.stringify(machineNamesData, null, 2));
    
    // Test 3: Machine Models (should still work)
    console.log('\n🏭 Testing MachineModels...');
    const machineModelsQuery = `
      query {
        getStopMachineModels {
          model
        }
      }
    `;
    
    const machineModelsResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: machineModelsQuery
      })
    });
    
    const machineModelsData = await machineModelsResponse.json();
    console.log('✅ MachineModels Response:', JSON.stringify(machineModelsData, null, 2));
    
    // Test 4: getAllMachineStops with correct query name
    console.log('\n📋 Testing getAllMachineStops...');
    const tableQuery = `
      query($filters: StopFilterInput) {
        getAllMachineStops(filters: $filters) {
          Date_Insert
          Machine_Name
          Part_NO
          Code_Stop
          Debut_Stop
          Fin_Stop_Time
          Regleur_Prenom
          duration_minutes
        }
      }
    `;
    
    const tableResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: tableQuery,
        variables: {
          filters: {
            model: 'IPS',
            date: '2024-01-01',
            dateRangeType: 'day'
          }
        }
      })
    });
    
    const tableData = await tableResponse.json();
    console.log('✅ getAllMachineStops Response:', tableData);
    
    // Test 5: getTop5Stops with correct query name
    console.log('\n📊 Testing getTop5Stops...');
    const topStopsQuery = `
      query($filters: StopFilterInput) {
        getTop5Stops(filters: $filters) {
          stopName
          count
        }
      }
    `;
    
    const topStopsResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: topStopsQuery,
        variables: {
          filters: {
            model: 'IPS',
            date: '2024-01-01',
            dateRangeType: 'day'
          }
        }
      })
    });
    
    const topStopsData = await topStopsResponse.json();
    console.log('✅ getTop5Stops Response:', topStopsData);
    
  } catch (error) {
    console.error('❌ Error testing GraphQL:', error);
  }
}

testCorrectedGraphQL();
