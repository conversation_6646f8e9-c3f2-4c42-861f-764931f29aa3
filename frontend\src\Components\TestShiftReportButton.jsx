import React, { useState } from "react";
import { <PERSON><PERSON>, Mo<PERSON>, message, Spin, Space } from "antd";
import { FilePdfOutlined, DownloadOutlined } from "@ant-design/icons";
// import superagent from "superagent";

/**
 * Component for testing PDF generation without database queries
 */
const TestShiftReportButton = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState(null);

  // Show modal to confirm report generation
  const showModal = () => {
    setIsModalVisible(true);
  };

  // Handle modal cancel
  const handleCancel = () => {
    setIsModalVisible(false);
  };

  // Generate test report
  const generateReport = async () => {
    try {
      setLoading(true);
      console.log("Starting test report generation");

      // Set a safety timeout to prevent UI from being stuck in loading state
      const safetyTimeout = setTimeout(() => {
        if (loading) {
          console.log("Safety timeout triggered after 30 seconds");
          setLoading(false);
          message.error("La génération du rapport a pris trop de temps. Veuillez réessayer.");
        }
      }, 30000); // 30 seconds safety timeout

      console.log("Sending API request to /api/shift-reports/test-generate");
      const response = await axios.post("/api/shift-reports/test-generate", {}, {
        timeout: 20000 // 20 seconds timeout
      });

      console.log("Received response:", response.status, response.statusText);

      // Clear the safety timeout since request completed
      clearTimeout(safetyTimeout);

      if (response.data && response.data.success) {
        setReportData(response.data);
        message.success("Rapport de test généré avec succès");

        // Automatically open the PDF in a new tab
        if (response.data.filePath) {
          console.log("Auto-opening PDF in new tab:", response.data.filePath);
          window.open(response.data.filePath, '_blank');
        }
      } else {
        message.error("Erreur lors de la génération du rapport de test");
      }
    } catch (error) {
      console.error("Error generating test report:", error);
      console.log("Error details:", {
        code: error.code,
        message: error.message,
        response: error.response ? {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        } : 'No response',
        request: error.request ? 'Request exists' : 'No request'
      });

      // Provide more specific error messages
      if (error.code === 'ECONNABORTED') {
        message.error("La génération du rapport a pris trop de temps. Veuillez réessayer. Le serveur n'a pas répondu dans le délai imparti.");
      } else if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        message.error(
          `Erreur ${error.response.status}: ${error.response.data?.error || error.response.statusText || "Erreur inconnue"}`
        );
      } else if (error.request) {
        // The request was made but no response was received
        message.error("Aucune réponse du serveur. Vérifiez votre connexion réseau.");
      } else {
        // Something happened in setting up the request that triggered an Error
        message.error(`Erreur: ${error.message}`);
      }
    } finally {
      console.log("Request completed (success or error), resetting loading state");
      setLoading(false);
    }
  };

  // Download the generated report
  const downloadReport = () => {
    if (!reportData || !reportData.filePath) {
      message.error("Aucun rapport disponible pour téléchargement");
      return;
    }

    // Use downloadPath if available, otherwise fall back to filePath
    const downloadUrl = reportData.downloadPath || reportData.filePath;
    console.log("Downloading report from:", downloadUrl);

    // Use window.open to open the file in a new tab
    window.open(downloadUrl, '_blank');
  };

  return (
    <>
      <Button
        type="primary"
        icon={<FilePdfOutlined />}
        onClick={showModal}
        style={{ marginLeft: 8 }}
      >
        Test PDF
      </Button>

      <Modal
        title="Générer un Rapport de Test"
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
      >
        <p>
          Générer un rapport PDF de test sans requêtes de base de données.
          Ce test permet de vérifier si la génération de PDF fonctionne correctement.
        </p>

        {loading ? (
          <div style={{ textAlign: "center", padding: "20px 0" }}>
            <Spin size="large" />
            <p style={{ marginTop: 16 }}>Génération du rapport de test en cours...</p>
          </div>
        ) : reportData ? (
          <div style={{ textAlign: "center", padding: "20px 0" }}>
            <p style={{ color: "green", fontSize: 16 }}>
              Rapport de test généré avec succès! Le PDF a été ouvert dans un nouvel onglet.
            </p>
            <p style={{ fontSize: 14, marginBottom: 16 }}>
              Si le PDF ne s'est pas ouvert automatiquement, ou pour le télécharger, cliquez ci-dessous:
            </p>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={downloadReport}
            >
              Ouvrir/Télécharger le PDF
            </Button>
          </div>
        ) : (
          <div style={{ textAlign: "right" }}>
            <Space>
              <Button onClick={handleCancel}>Annuler</Button>
              <Button type="primary" onClick={generateReport}>
                Générer le Rapport de Test
              </Button>
            </Space>
          </div>
        )}
      </Modal>
    </>
  );
};

export default TestShiftReportButton;
