/**
 * Test: Verify Backend Duration Calculation
 * 
 * This script tests the backend duration calculation specifically
 * and shows the detailed calculation process.
 */

import { GraphQLClient } from 'graphql-request';

async function testBackendDurationCalculation() {
  console.log('⏱️ Testing Backend Duration Calculation...\n');

  const client = new GraphQLClient('http://localhost:5000/api/graphql');

  const query = `
    query GetStopsWithDuration($filters: StopFilterInput) {
      getAllMachineStops(filters: $filters) {
        Date_Insert
        Machine_Name
        Debut_Stop
        Fin_Stop_Time
        duration_minutes
      }
    }
  `;

  // Test with a small dataset to see detailed calculation
  const filters = {
    model: "IPS",
    machine: "IPS01",
    startDate: "2025-04-29",
    endDate: "2025-04-30"
  };

  console.log('🔍 Testing with filters:', JSON.stringify(filters, null, 2));

  try {
    const result = await client.request(query, { filters });
    const stops = result.getAllMachineStops || [];

    console.log(`\n📊 Backend returned ${stops.length} stops`);

    if (stops.length > 0) {
      console.log('\n⏱️ Duration Calculation Details:');
      console.log('=================================');
      
      stops.slice(0, 5).forEach((stop, index) => {
        console.log(`\n${index + 1}. Stop ID: ${stop.ID_Duree}`);
        console.log(`   Machine: ${stop.Machine_Name}`);
        console.log(`   Start: ${stop.Debut_Stop}`);
        console.log(`   End: ${stop.Fin_Stop_Time}`);
        console.log(`   Calculated Duration: ${stop.duration_minutes} minutes`);
        
        // Verify calculation manually
        if (stop.Debut_Stop && stop.Fin_Stop_Time) {
          try {
            const parseDateTime = (dateStr) => {
              if (!dateStr) return null;
              const str = dateStr.toString().trim();
              const match = str.match(/^\s*(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?/);
              if (match) {
                const [_, day, month, year, hours, minutes, seconds = 0] = match;
                return new Date(year, month - 1, day, hours, minutes, seconds);
              }
              return null;
            };
            
            const startTime = parseDateTime(stop.Debut_Stop);
            const endTime = parseDateTime(stop.Fin_Stop_Time);
            
            if (startTime && endTime) {
              const manualDuration = Math.floor((endTime - startTime) / (1000 * 60));
              const matches = manualDuration === stop.duration_minutes;
              console.log(`   Manual verification: ${manualDuration} minutes ${matches ? '✅' : '❌'}`);
            }
          } catch (error) {
            console.log(`   Manual verification: Error - ${error.message}`);
          }
        }
      });

      // Summary statistics
      const totalDuration = stops.reduce((sum, stop) => sum + (stop.duration_minutes || 0), 0);
      const stopsWithDuration = stops.filter(stop => stop.duration_minutes > 0);
      
      console.log('\n📈 Summary Statistics:');
      console.log('======================');
      console.log(`Total stops: ${stops.length}`);
      console.log(`Stops with duration > 0: ${stopsWithDuration.length}`);
      console.log(`Total duration: ${totalDuration} minutes`);
      console.log(`Average duration: ${(totalDuration / stops.length).toFixed(2)} minutes per stop`);
      
      if (stopsWithDuration.length > 0) {
        const avgNonZero = totalDuration / stopsWithDuration.length;
        console.log(`Average non-zero duration: ${avgNonZero.toFixed(2)} minutes`);
      }

      // Group by date for chart verification
      const dailyStats = {};
      stops.forEach(stop => {
        if (!stop.Date_Insert) return;
        
        const dateMatch = stop.Date_Insert.match(/^\s*(\d{1,2})\/(\d{1,2})\/(\d{4})/);
        if (!dateMatch) return;
        
        const [_, day, month, year] = dateMatch;
        const date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        
        if (!dailyStats[date]) {
          dailyStats[date] = { stops: 0, duration: 0 };
        }
        
        dailyStats[date].stops++;
        dailyStats[date].duration += stop.duration_minutes || 0;
      });

      console.log('\n📅 Daily Aggregation (Chart Data Preview):');
      console.log('==========================================');
      Object.entries(dailyStats)
        .sort(([a], [b]) => a.localeCompare(b))
        .forEach(([date, stats]) => {
          console.log(`${date}: ${stats.stops} stops, ${stats.duration} minutes total`);
        });

    } else {
      console.log('❌ No stops returned from backend');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run the test
testBackendDurationCalculation().catch(console.error);
