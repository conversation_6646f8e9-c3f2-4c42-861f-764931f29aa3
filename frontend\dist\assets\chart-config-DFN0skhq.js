import{j as e,b as t}from"./index-Nnj1g72A.js";import{a9 as i,w as s,T as n,B as o,af as r,a6 as l,a7 as a,O as c,Y as d,a8 as h,a1 as u,a0 as p,m as g}from"./antd-vendor-4OvKHZ_k.js";import{C as m,a as x,b as y,P as f,c as b,d as S,p as C,e as R,f as k,i as E,A as T,g as A,h as v}from"./chart-vendor-CazprKWL.js";import{r as j}from"./utils-vendor-BlNwBmLj.js";const N=new class{constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectTimeout=null,this.pingInterval=null,this.connectionTimeout=null,this.defaultWsUrl="wss://charming-hermit-intense.ngrok-free.app",this.listeners={initialData:[],update:[],sessionUpdate:[],connect:[],disconnect:[],error:[]},this._setupNetworkListeners()}_setupNetworkListeners(){window.addEventListener("offline",(()=>{this._notifyListeners("error",{type:"network",message:"Network connection lost"})})),window.addEventListener("online",(()=>{!this.socket||this.socket.readyState!==WebSocket.CLOSED&&this.socket.readyState!==WebSocket.CLOSING||this.connect()}))}connect(){if(this.socket){if(this.socket.readyState===WebSocket.OPEN)return void this._notifyListeners("connect");if(this.socket.readyState===WebSocket.CONNECTING)return;this.socket.readyState!==WebSocket.CLOSING&&this.socket.readyState!==WebSocket.CLOSED||(this.socket=null)}this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null);const e=`${this.defaultWsUrl}/api/machine-data-ws`;try{this.socket=new WebSocket(e),this.connectionTimeout&&clearTimeout(this.connectionTimeout),this.connectionTimeout=setTimeout((()=>{if(this.socket&&this.socket.readyState!==WebSocket.OPEN){try{this.socket.close(),this.socket=null,this.isConnected=!1}catch(e){}this._handleConnectionFailure("Connection timeout"),this._notifyListeners("error",{type:"timeout",message:"WebSocket connection timed out after 15 seconds"})}this.connectionTimeout=null}),15e3),this.socket.onopen=()=>{this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),this.isConnected=!0,this.reconnectAttempts=0,this._notifyListeners("connect"),this.pingInterval=setInterval((()=>{this.socket&&this.socket.readyState===WebSocket.OPEN&&this.send({type:"ping"})}),3e4)},this.socket.onmessage=e=>{try{const t=JSON.parse(e.data);if("ping"!==t.type&&t.type,"pong"===t.type)return;t.type&&this.listeners[t.type]&&this._notifyListeners(t.type,t)}catch(t){}},this.socket.onclose=e=>{this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),this._clearPingInterval(),this.isConnected=!1;e.reason&&e.reason;this._notifyListeners("disconnect",e),!e.wasClean&&"visible"===document.visibilityState&&1e3!==e.code&&e.code},this.socket.onerror=e=>{this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=null),this.isConnected=!1,this._notifyListeners("error",e)}}catch(t){this._notifyListeners("error",t),this._handleConnectionFailure("Failed to create WebSocket")}}_handleConnectionFailure(e){this.isConnected=!1}_clearPingInterval(){this.pingInterval&&(clearInterval(this.pingInterval),this.pingInterval=null)}disconnect(){if(this._clearPingInterval(),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.socket)try{this.socket.readyState!==WebSocket.CLOSED&&this.socket.readyState!==WebSocket.CLOSING&&this.socket.close(1e3,"Disconnected by user")}catch(e){}finally{this.socket=null}this.isConnected=!1}send(e){if(!this.socket)return!1;switch(this.socket.readyState){case WebSocket.CONNECTING:return!1;case WebSocket.OPEN:try{const t="string"==typeof e?e:JSON.stringify(e);return this.socket.send(t),!0}catch(t){return!1}case WebSocket.CLOSING:return!1;case WebSocket.CLOSED:return this.reconnectAttempts<this.maxReconnectAttempts&&this.connect(),!1;default:return!1}}requestUpdate(){return this.ensureConnection(),this.send({type:"requestUpdate"})}ensureConnection(){return!1!==navigator.onLine&&(!(!this.socket||this.socket.readyState!==WebSocket.OPEN)||(!this.socket||this.socket.readyState!==WebSocket.CONNECTING)&&(!(!this.socket||this.socket.readyState===WebSocket.CLOSED||this.socket.readyState===WebSocket.CLOSING)||(this.connect(),!1)))}getState(){if(!this.socket)return"DISCONNECTED";switch(this.socket.readyState){case WebSocket.CONNECTING:return"CONNECTING";case WebSocket.OPEN:return"CONNECTED";case WebSocket.CLOSING:return"CLOSING";case WebSocket.CLOSED:return"DISCONNECTED";default:return"UNKNOWN"}}addEventListener(e,t){return this.listeners[e]&&this.listeners[e].push(t),()=>this.removeEventListener(e,t)}removeEventListener(e,t){this.listeners[e]&&(this.listeners[e]=this.listeners[e].filter((e=>e!==t)))}_notifyListeners(e,t){this.listeners[e]&&this.listeners[e].forEach((e=>{try{e(t)}catch(i){}}))}_scheduleReconnect(){this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null);const e=1e3*Math.pow(2,this.reconnectAttempts),t=1e3*Math.random(),i=Math.min(e+t,3e4);this.reconnectTimeout=setTimeout((()=>{if(this.reconnectAttempts++,!1===navigator.onLine){const e=()=>{window.removeEventListener("online",e),this.connect()};window.addEventListener("online",e),this._scheduleReconnect()}else this.connect()}),i)}isOnline(){return!1!==navigator.onLine}setDefaultUrl(e){if(e)return e.startsWith("ws:")||e.startsWith("wss:")||(e=`wss://${e}`),this.defaultWsUrl=e,this.defaultWsUrl}},{Title:L,Text:_}=n,I={primary:t.PRIMARY_BLUE,primaryLight:t.SELECTED_BG,success:t.SUCCESS,warning:t.WARNING,error:t.ERROR,gray:t.LIGHT_GRAY,textSecondary:t.LIGHT_GRAY,bgLight:t.LIGHT_BLUE_BG},W=e=>{if(null==e||""===e)return 0;const t=String(e).replace(/,/g,"."),i=Number.parseFloat(t);return isNaN(i)?0:i},O=({title:t,value:s,suffix:n="",color:o="inherit",style:r={},useDecimalComma:l=!1,useThousandComma:a=!1})=>{let c=s;return l?c=((e,t=1)=>null==e||""===e?"0":W(e).toFixed(t).replace(/\./g,","))(s):a&&(c=(e=>null==e||""===e?"0":W(e).toLocaleString("en-US").replace(/,/g,"."))(s)),e.jsxs(i,{size:"small",style:{background:I.bgLight,borderRadius:"8px",height:"100%",padding:"12px",...r},children:[e.jsx("div",{style:{fontSize:"12px",marginBottom:"4px",color:I.textSecondary},children:t}),e.jsxs("div",{style:{fontSize:"14px",fontWeight:"bold",color:o},children:[c," ",n]})]})},D=({machine:t,handleMachineClick:n,getStatusColor:m})=>{const x=e=>e>90?I.success:e>75?I.warning:I.error,y=W(t.TRS||"0").toFixed(1).replace(".",","),f=(W(t.Quantite_Bon)||0)/(W(t.Quantite_Planifier)||1)*100;return e.jsxs(i,{hoverable:!0,onClick:()=>n(t),className:"machine-card",style:{borderRadius:"12px",overflow:"hidden",height:"100%",transition:"all 0.3s",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.08)",position:"relative"},children:[e.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,height:"6px",background:t.id?m(t.status,t):I.gray}}),e.jsxs("div",{style:{padding:"16px 16px 0",marginTop:"6px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"flex-start",marginBottom:"16px"},children:[e.jsx("div",{style:{width:"48px",height:"48px",borderRadius:"12px",background:I.primaryLight,display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:e.jsx(s,{style:{fontSize:"24px",color:I.primary}})}),e.jsxs("div",{style:{flex:1},children:[e.jsx(L,{level:4,style:{margin:0,fontSize:"18px"},children:t.Machine_Name||"IPS01"}),e.jsxs(_,{type:"secondary",style:{fontSize:"14px"},children:["Chef de poste: ",t.Regleur_Prenom||"Non assigné"]}),"off"===t.Etat&&t.Code_arret&&e.jsx(_,{type:"danger",style:{fontSize:"16px",display:"block",marginTop:"4px"},children:t.Code_arret})]}),e.jsxs("div",{style:{textAlign:"right",display:"flex",flexDirection:"column",alignItems:"flex-end"},children:["on"===t.Etat&&e.jsx(o,{color:"#1890ff",text:e.jsx("span",{style:{color:"#1890ff",fontWeight:500},children:"Session active"}),style:{fontSize:"12px",marginBottom:"8px"}}),e.jsx("div",{style:{marginTop:"on"===t.Etat?"0":"12px"},children:e.jsx("div",{style:{width:"80px",height:"80px",position:"relative"},children:e.jsx(r,{type:"circle",percent:Number.parseFloat(y),width:80,strokeColor:x(Number.parseFloat(y)),strokeWidth:8,format:()=>e.jsxs("div",{children:[e.jsxs("div",{style:{fontSize:"18px",fontWeight:"bold"},children:[y,"%"]}),e.jsx("div",{style:{fontSize:"12px",marginTop:"-2px"},children:"TRS"})]})})})})]})]}),e.jsxs(l,{gutter:[16,16],style:{marginBottom:"16px"},children:[e.jsx(a,{span:8,children:e.jsx(O,{title:"Ordre de fabrication",value:t.Ordre_Fabrication||"N/A",style:{textAlign:"center"}})}),e.jsx(a,{span:8,children:e.jsx(O,{title:"Article",value:t.Article||"N/A",style:{textAlign:"center"}})}),e.jsx(a,{span:8,children:e.jsx(O,{title:"Empreintes",value:t.empreint+"/"+t.empreint||"N/A",style:{textAlign:"center"}})})]}),e.jsxs(l,{gutter:[16,16],style:{marginBottom:"16px"},children:[e.jsx(a,{span:8,children:e.jsx(O,{title:"Rejet",value:t.Quantite_Rejet||"0",suffix:"kg",style:{textAlign:"center"},useDecimalComma:!0})}),e.jsx(a,{span:8,children:e.jsx(O,{title:"Purge",value:t.Poids_Purge||"0",suffix:"Kg",style:{textAlign:"center"},useDecimalComma:!0})}),e.jsx(a,{span:8,children:e.jsx(O,{title:"Temps de cycle",value:W(t.cycle||"0").toFixed(2).replace(".",",")+"/"+W(t.cycle_theorique||"0").toFixed(2).replace(".",","),suffix:"sec",style:{textAlign:"center"}})})]}),e.jsxs("div",{style:{marginBottom:"16px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"4px"},children:[e.jsx(_,{strong:!0,children:"Progression"}),e.jsxs(_,{type:"secondary",children:[f.toFixed(1).replace(".",","),"% d'objectif"]})]}),e.jsx(r,{percent:f,strokeColor:x(f),showInfo:!1,strokeWidth:8,trailColor:"rgba(0,0,0,0.04)"})]}),e.jsxs("div",{style:{marginTop:"16px"},children:[e.jsx(c,{style:{margin:"16px 0"}}),e.jsxs(l,{gutter:16,children:[e.jsx(a,{span:8,children:e.jsx(d,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(h,{style:{marginRight:"4px",color:I.textSecondary}}),e.jsx("span",{style:{fontSize:"13px",color:I.textSecondary},children:"Planifié"})]}),value:W(t.Quantite_Planifier)||0,formatter:e=>e.toLocaleString().replace(/,/g,"."),suffix:"pcs",valueStyle:{fontSize:"16px",fontWeight:"bold"}})}),e.jsx(a,{span:8,children:e.jsx(d,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(u,{style:{marginRight:"4px",color:t.id?I.success:I.gray}}),e.jsx("span",{style:{fontSize:"13px",color:I.textSecondary},children:"Bon"})]}),value:W(t.Quantite_Bon)||0,formatter:e=>e.toLocaleString().replace(/,/g,"."),suffix:"pcs",valueStyle:{fontSize:"16px",fontWeight:"bold",color:I.success}})}),e.jsx(a,{span:8,children:e.jsx(d,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(p,{style:{marginRight:"4px",color:t.id?I.error:I.gray}}),e.jsx("span",{style:{fontSize:"13px",color:I.textSecondary},children:"Rejet"})]}),value:Math.trunc(1e3*t.Quantite_Rejet/t.Poid_unitaire||"0")||0,formatter:e=>e.toLocaleString().replace(/,/g,"."),suffix:"pcs",valueStyle:{fontSize:"16px",fontWeight:"bold",color:I.error}})})]}),e.jsx("div",{style:{marginBottom:"24px"}})]})]}),e.jsx("div",{style:{position:"absolute",bottom:"12px",right:"12px",background:I.primaryLight,borderRadius:"50%",width:"32px",height:"32px",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"},onClick:e=>{e.stopPropagation(),n(t)},children:e.jsx(g,{style:{color:I.primary}})})]})};var w=j();function B(){m.register(x,y,f,b,S,C,R,k,E,T,A,v),m.defaults.font.family="'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",m.defaults.responsive=!0,m.defaults.maintainAspectRatio=!1,m.defaults.animation=!1,m.defaults.devicePixelRatio=1}function P(e){const i=e?t.DARK.BORDER:t.ACCENT_BORDER,s=e?t.DARK.TEXT_SECONDARY:t.LIGHT_GRAY,n=e?t.DARK.BACKGROUND:t.WHITE,o=e?t.DARK.BORDER:t.ACCENT_BORDER,r=e?t.DARK.TEXT:t.DARK_GRAY,l=e?[t.DARK.PRIMARY_BLUE,t.DARK.SECONDARY_BLUE,t.SUCCESS,t.WARNING,t.ERROR,t.CHART_TERTIARY,t.INFO]:[t.PRIMARY_BLUE,t.SECONDARY_BLUE,t.SUCCESS,t.WARNING,t.ERROR,t.CHART_TERTIARY,t.INFO];m.defaults.color=s,m.defaults.borderColor=i,m.defaults.scale.grid.color=i,m.defaults.scale.ticks.color=s,m.defaults.plugins.tooltip.backgroundColor=e?"rgba(33, 33, 33, 0.9)":"rgba(255, 255, 255, 0.9)",m.defaults.plugins.tooltip.titleColor=e?"rgba(255, 255, 255, 0.95)":"rgba(0, 0, 0, 0.95)",m.defaults.plugins.tooltip.bodyColor=s,m.defaults.plugins.tooltip.borderColor=o,m.defaults.plugins.tooltip.boxPadding=6,m.defaults.plugins.legend.labels.color=s,m.defaults.plugins.legend.labels.boxWidth=12,m.defaults.plugins.legend.labels.padding=15,m.defaults.elements.point.backgroundColor=l[0],m.defaults.elements.point.borderColor=e?"#141414":"white",m.defaults.elements.line.borderWidth=2,m.defaults.elements.line.tension=.2,m.defaults.elements.bar.backgroundColor=l[0],m.defaults.elements.bar.borderWidth=0,"undefined"!=typeof document&&(document.documentElement.style.setProperty("--chart-bg",n),document.documentElement.style.setProperty("--chart-text",s),document.documentElement.style.setProperty("--chart-title",r),document.documentElement.style.setProperty("--chart-grid",i),document.documentElement.style.setProperty("--chart-axis-label",r),document.documentElement.style.setProperty("--chart-tooltip-bg",e?"rgba(33, 33, 33, 0.9)":"rgba(255, 255, 255, 0.9)"))}w.throttle(((e,t)=>{e&&e.data&&(e.data=t,e.update("none"))}),500);const G=window.innerWidth<768,U=e=>{const i=e?t.DARK.BORDER:t.ACCENT_BORDER,s=e?t.DARK.TEXT_SECONDARY:t.LIGHT_GRAY,n=e?t.DARK.TEXT:t.DARK_GRAY,o=e?t.DARK.BACKGROUND:t.WHITE,r=e?t.DARK.BORDER:t.ACCENT_BORDER,l=e?[t.DARK.PRIMARY_BLUE,t.DARK.SECONDARY_BLUE,"#60A5FA","#3730A3","#1E40AF","#2563EB","#6366F1"]:[t.PRIMARY_BLUE,t.SECONDARY_BLUE,t.CHART_TERTIARY,t.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3"],a=e?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.1)";return{responsive:!0,maintainAspectRatio:!1,animation:!1,devicePixelRatio:1,plugins:{legend:{position:"top",align:"center",labels:{color:s,padding:15,usePointStyle:!0,pointStyle:"circle",boxWidth:10,font:{weight:500}},display:!G},tooltip:{enabled:!G||window.innerWidth>480,backgroundColor:o,titleColor:e?"rgba(255, 255, 255, 0.95)":"rgba(0, 0, 0, 0.95)",bodyColor:s,borderColor:r,borderWidth:1,padding:10,cornerRadius:6,boxPadding:5,displayColors:!0,boxShadow:a,callbacks:{label:function(e){let t=e.dataset.label||"";return t&&(t+=": "),t+=Math.round(100*e.parsed.y)/100,t},title:function(e){return e[0].label}}},title:{display:!1,color:n,font:{weight:600,size:16}}},scales:{x:{grid:{display:!1,color:i,z:-1},border:{color:e?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:s,maxRotation:0,autoSkipPadding:10,maxTicksLimit:G?5:10,padding:8,font:{size:G?10:12}},title:{display:!1,color:n,font:{weight:500}}},y:{beginAtZero:!0,grid:{color:i,z:-1,lineWidth:1,drawBorder:!0},border:{color:e?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:s,precision:0,maxTicksLimit:G?5:8,padding:8,font:{size:G?10:12}},title:{display:!1,color:n,font:{weight:500}}}},elements:{point:{radius:G?0:3,hoverRadius:G?3:6,backgroundColor:function(e){const t=e.datasetIndex%l.length;return l[t]},borderColor:e?"#141414":"white",borderWidth:2,hoverBorderWidth:2,hoverBorderColor:e?"rgba(255, 255, 255, 0.5)":"rgba(0, 0, 0, 0.5)"},line:{borderWidth:G?2:3,tension:.2,fill:!1,borderColor:function(e){const t=e.datasetIndex%l.length;return l[t]},borderCapStyle:"round"},bar:{backgroundColor:function(e){const t=e.datasetIndex%l.length;return l[t]},borderWidth:0,borderRadius:4,hoverBackgroundColor:function(e){const t=e.datasetIndex%l.length;return l[t]}}}}};export{D as E,U as g,w as l,B as r,P as u,N as w};
