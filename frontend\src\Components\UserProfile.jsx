"use client"

import { useState, useEffect } from "react"
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Button,
  Divider,
  Typography,
  Avatar,
  Tabs,
  message,
  Descriptions,
  Badge,
  Select,
  Space,
  Tooltip,
  Tag,
  Switch,
} from "antd"
import {
  UserOutlined,
  LockOutlined,
  MailOutlined,
  SaveOutlined,
  EditOutlined,
  TeamOutlined,
  KeyOutlined,
  PhoneOutlined,
} from "@ant-design/icons"
import { useAuth } from "../hooks/useAuth"  // Fixed import path
import UserManagement from "./user-management"
import "./user-profile.css"
import { extractResponseData, isResponseSuccessful, getErrorMessage } from "../utils/apiUtils"

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs
const { Option } = Select

const UserProfile = () => {
  const { user, updateProfile, changePassword } = useAuth()
  const [profileForm] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const [editMode, setEditMode] = useState(false)
  const [loading, setLoading] = useState(false)

  // Détecter le mode sombre
  const [darkMode, setDarkMode] = useState(false)

  useEffect(() => {

    // Vérifier si le mode sombre est activé
    const isDarkMode =
      document.documentElement.classList.contains("dark") ||
      document.body.classList.contains("dark") ||
      localStorage.getItem("theme") === "dark"
    setDarkMode(isDarkMode)

    // Observer les changements de thème
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === "class") {
          const isDark = document.documentElement.classList.contains("dark") || document.body.classList.contains("dark")
          setDarkMode(isDark)
        }
      })
    })

    observer.observe(document.documentElement, { attributes: true })
    observer.observe(document.body, { attributes: true })

    return () => observer.disconnect()
  }, [])

  // Styles adaptés au mode sombre
  const cardStyle = {
    backgroundColor: darkMode ? "#1f1f1f" : "#ffffff",
    boxShadow: darkMode ? "0 4px 12px rgba(0, 0, 0, 0.5)" : "0 4px 12px rgba(0, 0, 0, 0.05)",
    borderRadius: "12px",
    border: "none",
    transition: "all 0.3s ease",
  }

  const avatarStyle = {
    backgroundColor: "#1890ff",
    boxShadow: darkMode ? "0 4px 8px rgba(24, 144, 255, 0.5)" : "0 4px 8px rgba(24, 144, 255, 0.2)",
    padding: "4px",
    border: "4px solid",
    borderColor: darkMode ? "#141414" : "#ffffff",
    transition: "all 0.3s ease",
  }

  const handleProfileUpdate = async (values) => {
    setLoading(true)
    try {
      const result = await updateProfile(values)
      if (result.success) {
        setEditMode(false)
        message.success("Profil mis à jour avec succès!")
      }
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordChange = async (values) => {
    setLoading(true)
    try {
      const result = await changePassword(values)
      if (result.success) {
        passwordForm.resetFields()
        message.success("Mot de passe changé avec succès!")
      }
    } finally {
      setLoading(false)
    }
  }

  const toggleEditMode = () => {
    if (!editMode) {
      profileForm.setFieldsValue({
        username: user?.username,
        email: user?.email,
        fullName: user?.fullName || "",
        phone: user?.phone || "",
      })
    }
    setEditMode(!editMode)
  }

  // Fonction pour obtenir le statut de l'utilisateur avec badge
  const getUserStatusBadge = () => {
    if (!user) return null

    if (user.role === "admin") {
      return (
        <Badge
          status="success"
          text={
            <Text strong style={{ color: "#52c41a" }}>
              Administrateur
            </Text>
          }
        />
      )
    } else if (user.active) {
      return <Badge status="processing" text={<Text>Utilisateur actif</Text>} />
    } else {
      return <Badge status="default" text={<Text type="secondary">Utilisateur inactif</Text>} />
    }
  }

  return (
    <div style={{ padding: 24 }}>
      <Row gutter={[24, 24]}>
        <Col xs={24} md={8}>
          <Card bordered={false} style={cardStyle} className="profile-card">
            <div style={{ textAlign: "center", marginBottom: 24 }}>
              <Avatar size={120} icon={<UserOutlined />} style={avatarStyle} className="profile-avatar" />
              <Title level={3} style={{ marginTop: 16, marginBottom: 4 }}>
                {user?.fullName || user?.username}
              </Title>
              <div style={{ marginBottom: 8 }}>{getUserStatusBadge()}</div>
              <Paragraph type="secondary" style={{ fontSize: "14px" }}>
                Membre depuis {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : "N/A"}
              </Paragraph>
            </div>

            <Divider style={{ margin: "12px 0 24px" }} />

            <Descriptions
              title={<Text strong>Informations</Text>}
              column={1}
              bordered={false}
              size="small"
              labelStyle={{
                fontWeight: "500",
                color: darkMode ? "rgba(255,255,255,0.85)" : "rgba(0,0,0,0.85)",
              }}
              contentStyle={{
                color: darkMode ? "rgba(255,255,255,0.65)" : "rgba(0,0,0,0.65)",
              }}
            >
              <Descriptions.Item label="Nom d'utilisateur">{user?.username}</Descriptions.Item>
              <Descriptions.Item label="Email">{user?.email}</Descriptions.Item>
              <Descriptions.Item label="Téléphone">{user?.phone || "Non renseigné"}</Descriptions.Item>
              <Descriptions.Item label="Rôle">
                {user?.role === "admin" ? "Administrateur" : "Utilisateur"}
              </Descriptions.Item>
              <Descriptions.Item label="Statut">{user?.active ? "Actif" : "Inactif"}</Descriptions.Item>
            </Descriptions>

            <div style={{ marginTop: 24, textAlign: "center" }}>
              <Space>
                <Tooltip title="Modifier le profil">
                  <Button type="primary" icon={<EditOutlined />} onClick={toggleEditMode} shape="round">
                    Modifier
                  </Button>
                </Tooltip>
                <Tooltip title="Changer le mot de passe">
                  <Button
                    icon={<KeyOutlined />}
                    onClick={() => document.getElementById("security-tab").click()}
                    shape="round"
                  >
                    Mot de passe
                  </Button>
                </Tooltip>
              </Space>
            </div>
          </Card>
        </Col>

        <Col xs={24} md={16}>
          <Card bordered={false} style={cardStyle} className="profile-tabs-card">
            <Tabs defaultActiveKey="profile">
              <TabPane
                tab={
                  <span>
                    <UserOutlined />
                    Profil
                  </span>
                }
                key="profile"
              >
                <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 16 }}>
                  <Title level={4}>Informations du profil</Title>
                  <Button
                    type={editMode ? "primary" : "default"}
                    icon={editMode ? <SaveOutlined /> : <EditOutlined />}
                    onClick={toggleEditMode}
                  >
                    {editMode ? "Enregistrer" : "Modifier"}
                  </Button>
                </div>

                {editMode ? (
                  <Form
                    form={profileForm}
                    layout="vertical"
                    onFinish={handleProfileUpdate}
                    initialValues={{
                      username: user?.username,
                      email: user?.email,
                      fullName: user?.fullName || "",
                      phone: user?.phone || "",
                    }}
                  >
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="fullName"
                          label="Nom complet"
                          rules={[{ required: true, message: "Veuillez entrer votre nom complet" }]}
                        >
                          <Input prefix={<UserOutlined />} placeholder="Nom complet" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="username"
                          label="Nom d'utilisateur"
                          rules={[{ required: true, message: "Veuillez entrer votre nom d'utilisateur" }]}
                        >
                          <Input prefix={<UserOutlined />} placeholder="Nom d'utilisateur" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="email"
                          label="Email"
                          rules={[
                            { required: true, message: "Veuillez entrer votre email" },
                            { type: "email", message: "Veuillez entrer un email valide" },
                          ]}
                        >
                          <Input prefix={<MailOutlined />} placeholder="Email" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="phone"
                          label="Téléphone"
                          rules={[{ pattern: /^[0-9+\s-]{8,15}$/, message: "Format de téléphone invalide" }]}
                        >
                          <Input prefix={<PhoneOutlined />} placeholder="Téléphone" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item>
                      <Button type="primary" htmlType="submit" loading={loading}>
                        Mettre à jour le profil
                      </Button>
                    </Form.Item>
                  </Form>
                ) : (
                  <Descriptions
                    bordered
                    column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}
                    labelStyle={{
                      fontWeight: "500",
                      color: darkMode ? "rgba(255,255,255,0.85)" : "rgba(0,0,0,0.85)",
                    }}
                  >
                    <Descriptions.Item label="Nom complet">{user?.fullName || "Non renseigné"}</Descriptions.Item>
                    <Descriptions.Item label="Nom d'utilisateur">{user?.username}</Descriptions.Item>
                    <Descriptions.Item label="Email">{user?.email}</Descriptions.Item>
                    <Descriptions.Item label="Téléphone">{user?.phone || "Non renseigné"}</Descriptions.Item>
                    <Descriptions.Item label="Rôle" span={2}>
                      <Tag color={user?.role === "admin" ? "green" : "blue"}>
                        {user?.role === "admin" ? "Administrateur" : "Utilisateur"}
                      </Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="Statut" span={2}>
                      <Badge status={user?.active ? "success" : "default"} text={user?.active ? "Actif" : "Inactif"} />
                    </Descriptions.Item>
                    <Descriptions.Item label="Compte créé" span={2}>
                      {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : "N/A"}
                    </Descriptions.Item>
                    <Descriptions.Item label="Dernière connexion" span={2}>
                      {user?.lastLogin ? new Date(user.lastLogin).toLocaleString() : "N/A"}
                    </Descriptions.Item>
                  </Descriptions>
                )}
              </TabPane>

              <TabPane
                tab={
                  <span id="security-tab">
                    <LockOutlined />
                    Sécurité
                  </span>
                }
                key="security"
              >
                <Title level={4}>Changer le mot de passe</Title>

                <Form form={passwordForm} layout="vertical" onFinish={handlePasswordChange}>
                  <Form.Item
                    name="currentPassword"
                    label="Mot de passe actuel"
                    rules={[{ required: true, message: "Veuillez entrer votre mot de passe actuel" }]}
                  >
                    <Input.Password prefix={<LockOutlined />} placeholder="Mot de passe actuel" />
                  </Form.Item>

                  <Form.Item
                    name="newPassword"
                    label="Nouveau mot de passe"
                    rules={[
                      { required: true, message: "Veuillez entrer votre nouveau mot de passe" },
                      { min: 8, message: "Le mot de passe doit contenir au moins 8 caractères" },
                      {
                        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                        message:
                          "Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial",
                      },
                    ]}
                  >
                    <Input.Password
                      prefix={<LockOutlined />}
                      placeholder="Nouveau mot de passe"
                      autoComplete="new-password"
                    />
                  </Form.Item>

                  <Form.Item
                    name="confirmPassword"
                    label="Confirmer le mot de passe"
                    dependencies={["newPassword"]}
                    rules={[
                      { required: true, message: "Veuillez confirmer votre mot de passe" },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue("newPassword") === value) {
                            return Promise.resolve()
                          }
                          return Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))
                        },
                      }),
                    ]}
                  >
                    <Input.Password
                      prefix={<LockOutlined />}
                      placeholder="Confirmer le mot de passe"
                      autoComplete="new-password"
                    />
                  </Form.Item>

                  <Form.Item>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      Changer le mot de passe
                    </Button>
                  </Form.Item>
                </Form>

                <Divider />

                <Title level={4}>Paramètres de sécurité</Title>
                <Descriptions bordered column={1}>
                  <Descriptions.Item label="Authentification à deux facteurs">
                    <Switch
                      checkedChildren="Activée"
                      unCheckedChildren="Désactivée"
                      defaultChecked={user?.twoFactorEnabled}
                      disabled
                    />
                    <Button type="link" disabled>
                      Configurer
                    </Button>
                  </Descriptions.Item>
                  <Descriptions.Item label="Notifications de connexion">
                    <Switch
                      checkedChildren="Activées"
                      unCheckedChildren="Désactivées"
                      defaultChecked={user?.loginNotifications}
                      disabled
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="Sessions actives">
                    <Button type="link" disabled>
                      Voir les sessions (1 active)
                    </Button>
                  </Descriptions.Item>
                </Descriptions>
              </TabPane>

              {user && user?.role === "admin" && (
                <TabPane
                  tab={
                    <span>
                      <TeamOutlined />
                      Gestion des utilisateurs
                    </span>
                  }
                  key="users"
                >
                  <UserManagement darkMode={darkMode} />
                </TabPane>
              )}
            </Tabs>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default UserProfile

