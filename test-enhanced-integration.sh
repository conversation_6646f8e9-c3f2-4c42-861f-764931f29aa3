#!/bin/bash

echo "🧪 Enhanced PDF Integration Test"
echo "==============================="
echo ""

# Test 1: Verify servers are running
echo "🔍 Step 1: Checking server status..."

# Check backend
BACKEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/api/health 2>/dev/null || echo "000")
if [ "$BACKEND_STATUS" = "200" ] || [ "$BACKEND_STATUS" = "404" ]; then
    echo "✅ Backend server is running on port 5000"
else
    echo "❌ Backend server not accessible"
fi

# Check frontend  
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5173 2>/dev/null || echo "000")
if [ "$FRONTEND_STATUS" = "200" ]; then
    echo "✅ Frontend server is running on port 5173"
else
    echo "❌ Frontend server not accessible"
fi

echo ""

# Test 2: Check component files exist
echo "🔍 Step 2: Checking enhanced components..."

BACKEND_DIR="c:/Users/<USER>/Desktop/TEST/locql project/backend"

if [ -f "$BACKEND_DIR/utils/pdfTemplate.js" ]; then
    echo "✅ PDFTemplate component exists"
else
    echo "❌ PDFTemplate component missing"
fi

if [ -f "$BACKEND_DIR/services/reportDataService.js" ]; then
    echo "✅ ReportDataService component exists"
else
    echo "❌ ReportDataService component missing"
fi

if [ -f "$BACKEND_DIR/utils/pdfGenerator.js" ]; then
    echo "✅ PDFGenerator component exists"
else
    echo "❌ PDFGenerator component missing"
fi

echo ""

# Test 3: Check frontend integration
echo "🔍 Step 3: Checking frontend integration..."

FRONTEND_FILE="c:/Users/<USER>/Desktop/TEST/locql project/frontend/src/Pages/reports.jsx"

if grep -q "useEnhancedReports" "$FRONTEND_FILE" 2>/dev/null; then
    echo "✅ Enhanced reports state implemented"
else
    echo "❌ Enhanced reports state missing"
fi

if grep -q "generate-enhanced" "$FRONTEND_FILE" 2>/dev/null; then
    echo "✅ Enhanced API endpoint integrated"
else
    echo "❌ Enhanced API endpoint missing"
fi

echo ""

# Test 4: Manual testing instructions
echo "📋 Step 4: Manual testing required..."
echo ""
echo "🌐 To complete testing:"
echo "1. Open: http://localhost:5173"
echo "2. Log in to the application"
echo "3. Navigate to Reports page"
echo "4. Look for Standard/Enhanced toggle for shift reports"
echo "5. Test both report generation modes"
echo ""
echo "📊 Expected results:"
echo "• Enhanced reports should be larger files"
echo "• Enhanced reports should have SOMIPEM branding"  
echo "• Enhanced reports should include French formatting"
echo "• Enhanced reports should show performance recommendations"
echo ""
echo "🚀 Test complete! Proceed with manual UI testing."
