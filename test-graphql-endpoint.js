// Test the actual GraphQL endpoint
const testQuery = `
  query {
    getFinalComprehensiveStopData {
      allStops {
        Machine_Name
        Code_Stop
        duration_minutes
      }
      sidecards {
        Arret_Totale
        Arret_Totale_nondeclare
      }
      totalRecords
      queryExecutionTime
    }
  }
`;

async function testGraphQLEndpoint() {
  try {
    console.log('🔍 Testing GraphQL endpoint...');
    
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: testQuery
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL errors:', result.errors);
      return;
    }

    const data = result.data.getFinalComprehensiveStopData;
    
    console.log('✅ GraphQL endpoint test results:');
    console.log(`📊 Total records: ${data.totalRecords}`);
    console.log(`⏱️  Execution time: ${data.queryExecutionTime}ms`);
    console.log('📈 Sidecards data:');
    console.log(`  - Arret_Totale: ${data.sidecards.Arret_Totale}`);
    console.log(`  - Arret_Totale_nondeclare: ${data.sidecards.Arret_Totale_nondeclare}`);
    
    // Count non-declared stops in the returned data
    const nonDeclaredCount = data.allStops.filter(stop => stop.Code_Stop === 'Arrêt non déclaré').length;
    console.log(`🔍 Non-declared stops in data: ${nonDeclaredCount}`);
    
    // Sample data
    console.log('\n📋 Sample stops:');
    data.allStops.slice(0, 5).forEach((stop, index) => {
      console.log(`  ${index + 1}. ${stop.Machine_Name} - ${stop.Code_Stop} (${stop.duration_minutes} min)`);
    });
    
    // Verify the fix worked
    if (data.sidecards.Arret_Totale_nondeclare > 0) {
      console.log('\n✅ SUCCESS: Non-declared stops count is now correctly returned!');
    } else {
      console.log('\n❌ ISSUE: Non-declared stops count is still 0');
    }
    
  } catch (error) {
    console.error('❌ Error testing GraphQL endpoint:', error.message);
  }
}

testGraphQLEndpoint();
