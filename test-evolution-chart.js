/**
 * Test Evolution Chart Data Generation
 * This script tests if the evolution chart data is being generated correctly
 */

// Test data format for evolution chart
const testEvolutionData = [
  {
    date: "2024-01-15",
    displayDate: "15/01",
    stops: 5,
    duration: 120
  },
  {
    date: "2024-01-16", 
    displayDate: "16/01",
    stops: 3,
    duration: 80
  },
  {
    date: "2024-01-17",
    displayDate: "17/01", 
    stops: 7,
    duration: 200
  }
];

console.log('📈 Test Evolution Chart Data:');
console.log('Data format correct:', testEvolutionData);
console.log('');

// Test data validation
function validateEvolutionData(data) {
  if (!Array.isArray(data)) {
    return { valid: false, error: 'Data is not an array' };
  }
  
  if (data.length === 0) {
    return { valid: false, error: 'Data array is empty' };
  }
  
  const firstItem = data[0];
  const requiredFields = ['date', 'displayDate', 'stops'];
  
  for (const field of requiredFields) {
    if (!(field in firstItem)) {
      return { valid: false, error: `Missing required field: ${field}` };
    }
  }
  
  return { valid: true, error: null };
}

const validation = validateEvolutionData(testEvolutionData);
console.log('✅ Validation result:', validation);

// Test date formatting
function testDateFormatting() {
  const testDates = [
    "2024-01-15T10:30:00.000Z",
    "2024-01-15",
    "15/01/2024"
  ];
  
  console.log('\n📅 Date formatting tests:');
  
  testDates.forEach(dateStr => {
    let formattedDate;
    let displayDate;
    
    if (dateStr.includes('/')) {
      const [day, month, year] = dateStr.split('/');
      formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    } else {
      formattedDate = dateStr.split('T')[0];
    }
    
    displayDate = new Date(formattedDate).toLocaleDateString('fr-FR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
    
    console.log(`Input: ${dateStr} -> Date: ${formattedDate} -> Display: ${displayDate}`);
  });
}

testDateFormatting();

console.log('\n🎯 Expected ArretLineChart props:');
console.log('- data: Array of objects with {date, displayDate, stops}');
console.log('- X-axis dataKey: "displayDate"');
console.log('- Line dataKey: "stops"');
console.log('- Loading: boolean');
