"use client"

import { createContext, useContext, useState, useEffect, useCallback } from "react"
import { message } from "antd"
import { useAuth } from "../hooks/useAuth"

// Création du contexte
const SettingsContext = createContext()

// Valeurs par défaut des paramètres
const defaultSettings = {
  // Interface settings
  darkMode: false,
  dashboardRefreshRate: 60,
  dataDisplayMode: "chart",
  compactMode: false,
  animationsEnabled: true,
  chartAnimations: true,
  defaultView: "dashboard",
  tableRowsPerPage: 20,

  // Notification settings
  notificationsEnabled: true,
  notifyMachineAlerts: true,
  notifyMaintenance: true,
  notifyUpdates: true,

  // Email settings
  emailNotifications: true,
  emailFormat: "html",
  emailDigest: false,

  // Shift report settings
  defaultShift: "Matin",
  shiftReportNotifications: true,
  shiftReportEmails: true,
  shift1Notifications: true,
  shift2Notifications: true,
  shift3Notifications: true,
  shift1Emails: true,
  shift2Emails: true,
  shift3Emails: true,

  // Report settings
  defaultReportFormat: "pdf",
  reportAutoDownload: false,

  // Security settings
  sessionTimeout: 60,
  loginNotifications: true,
  twoFactorAuth: false,
}

const SettingsProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth()
  const [settings, setSettings] = useState(defaultSettings)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fonction pour charger tous les paramètres
  const loadSettings = useCallback(async () => {
    if (!isAuthenticated) {
      setSettings(defaultSettings)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Load UI settings from localStorage
      let uiSettings = {};
      try {
        const storedSettings = localStorage.getItem('uiSettings');
        if (storedSettings) {
          uiSettings = JSON.parse(storedSettings);
        }
      } catch (e) {
        console.warn('Failed to load UI settings from localStorage:', e);
      }

      const response = await fetch("/api/settings")

      if (!response.ok) {
        throw new Error("Erreur lors du chargement des paramètres")
      }

      const data = await response.json()
      // Merge default settings, server settings, and UI settings
      setSettings({ ...defaultSettings, ...data, ...uiSettings })
    } catch (err) {
      console.error("Erreur lors du chargement des paramètres:", err)
      setError(err.message)
      message.error("Impossible de charger vos paramètres. Veuillez réessayer.")
    } finally {
      setLoading(false)
    }
  }, [isAuthenticated])

  // Charger les paramètres au chargement du composant et quand l'utilisateur change
  useEffect(() => {
    loadSettings()
  }, [loadSettings, user?.id])

  // Fonction pour mettre à jour un paramètre spécifique
  const updateSetting = useCallback(
    async (key, value) => {
      if (!isAuthenticated) return false

      try {
        const response = await fetch(`/api/settings/${key}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ value }),
        })

        if (!response.ok) {
          throw new Error("Erreur lors de la mise à jour du paramètre")
        }

        // Mettre à jour l'état local
        setSettings((prev) => ({ ...prev, [key]: value }))
        return true
      } catch (err) {
        console.error(`Erreur lors de la mise à jour du paramètre ${key}:`, err)
        message.error("Impossible de mettre à jour ce paramètre. Veuillez réessayer.")
        return false
      }
    },
    [isAuthenticated],
  )

  // Fonction pour mettre à jour plusieurs paramètres à la fois
  const updateSettings = async (newSettings) => {
    setLoading(true);
    try {
      // First, check if the settings table exists and has the right columns
      const checkResponse = await fetch('/api/settings/check-schema', {
        method: 'GET',
      });

      if (!checkResponse.ok) {
        console.warn('Settings schema check failed, proceeding with caution');
      }

      // Filter out settings that might not be in the database schema
      // This is a temporary solution until the database schema is updated
      const safeSettings = {};
      const knownDatabaseFields = [
        'user_id', 'notificationsEnabled', 'notifyMachineAlerts',
        'notifyMaintenance', 'notifyUpdates', 'emailNotifications',
        'emailFormat', 'emailDigest', 'defaultShift',
        'shiftReportNotifications', 'shiftReportEmails',
        'defaultReportFormat', 'reportAutoDownload',
        'sessionTimeout', 'loginNotifications', 'twoFactorAuth'
      ];

      // Only include settings that are likely to be in the database
      Object.keys(newSettings).forEach(key => {
        if (knownDatabaseFields.includes(key)) {
          safeSettings[key] = newSettings[key];
        }
      });

      // Store UI-specific settings in localStorage instead
      const uiSettings = {
        darkMode: newSettings.darkMode,
        compactMode: newSettings.compactMode,
        animationsEnabled: newSettings.animationsEnabled,
        chartAnimations: newSettings.chartAnimations,
        dataDisplayMode: newSettings.dataDisplayMode,
        dashboardRefreshRate: newSettings.dashboardRefreshRate,
        defaultView: newSettings.defaultView,
        tableRowsPerPage: newSettings.tableRowsPerPage
      };

      localStorage.setItem('uiSettings', JSON.stringify(uiSettings));

      // Send only the safe settings to the server
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(safeSettings),
      });

      // Check if the response is OK before trying to parse JSON
      if (!response.ok) {
        const errorData = await response.text();
        try {
          // Try to parse as JSON if possible
          const jsonError = JSON.parse(errorData);
          throw new Error(jsonError.error || `Server responded with status: ${response.status}`);
        } catch (parseError) {
          // If not JSON, use the text or status
          throw new Error(errorData || `Server responded with status: ${response.status}`);
        }
      }

      // Merge the server response with UI settings
      const serverData = await response.json();
      const mergedSettings = { ...serverData, ...uiSettings };
      setSettings(mergedSettings);
      return mergedSettings;
    } catch (error) {
      console.error('Erreur lors de la mise à jour des paramètres:', error);
      // Provide more specific error message
      throw new Error(`Erreur lors de la mise à jour des paramètres: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour tester les paramètres d'email
  const testEmailSettings = useCallback(async () => {
    if (!isAuthenticated) return false

    try {
      message.loading({ content: "Envoi de l'email de test...", key: "emailTest" })

      const response = await fetch("/api/settings/email/test", {
        method: "POST",
      })

      if (!response.ok) {
        throw new Error("Erreur lors de l'envoi de l'email de test")
      }

      const data = await response.json()

      if (data.success) {
        message.success({ content: "Email de test envoyé avec succès", key: "emailTest" })
        return true
      } else {
        throw new Error(data.error || "Erreur lors de l'envoi de l'email de test")
      }
    } catch (err) {
      console.error("Erreur lors du test des paramètres d'email:", err)
      message.error({ content: err.message, key: "emailTest" })
      return false
    }
  }, [isAuthenticated])

  // Fonction pour charger les paramètres de rapports
  const loadReportSettings = useCallback(async () => {
    if (!isAuthenticated) return null

    try {
      const response = await fetch("/api/settings/reports/preferences")

      if (!response.ok) {
        throw new Error("Erreur lors du chargement des paramètres de rapports")
      }

      const data = await response.json()

      // Mettre à jour l'état local avec les paramètres de rapports
      setSettings((prev) => ({ ...prev, ...data }))

      return data
    } catch (err) {
      console.error("Erreur lors du chargement des paramètres de rapports:", err)
      return null
    }
  }, [isAuthenticated])

  // Fonction pour charger les paramètres de quart
  const loadShiftSettings = useCallback(async () => {
    if (!isAuthenticated) return null

    try {
      const response = await fetch("/api/settings/shift/reports")

      if (!response.ok) {
        throw new Error("Erreur lors du chargement des paramètres de quart")
      }

      const data = await response.json()

      // Mettre à jour l'état local avec les paramètres de quart
      setSettings((prev) => ({ ...prev, ...data }))

      return data
    } catch (err) {
      console.error("Erreur lors du chargement des paramètres de quart:", err)
      return null
    }
  }, [isAuthenticated])

  // Fonction pour charger les paramètres d'email
  const loadEmailSettings = useCallback(async () => {
    if (!isAuthenticated) return null

    try {
      const response = await fetch("/api/settings/email/notifications")

      if (!response.ok) {
        throw new Error("Erreur lors du chargement des paramètres d'email")
      }

      const data = await response.json()

      // Mettre à jour l'état local avec les paramètres d'email
      setSettings((prev) => ({ ...prev, ...data }))

      return data
    } catch (err) {
      console.error("Erreur lors du chargement des paramètres d'email:", err)
      return null
    }
  }, [isAuthenticated])

  // Valeur du contexte
  const value = {
    settings,
    loading,
    error,
    loadSettings,
    updateSetting,
    updateSettings,
    testEmailSettings,
    loadReportSettings,
    loadShiftSettings,
    loadEmailSettings,
  }

  return <SettingsContext.Provider value={value}>{children}</SettingsContext.Provider>
}

// Hook personnalisé pour utiliser le contexte
function useSettings() {
  const context = useContext(SettingsContext)

  if (context === undefined) {
    throw new Error("useSettings doit être utilisé à l'intérieur d'un SettingsProvider")
  }

  return context
}

// Export the context, provider, and hook
export { SettingsContext, SettingsProvider, useSettings }

