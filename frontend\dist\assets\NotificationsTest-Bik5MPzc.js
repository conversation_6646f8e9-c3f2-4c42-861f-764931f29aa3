import{f as e,j as i,r as s}from"./index-Nnj1g72A.js";import{r as t}from"./react-vendor-tYPmozCJ.js";import{T as n,S as r,a9 as c,B as a,e as d,aJ as l,ar as o,ab as h,s as x}from"./antd-vendor-4OvKHZ_k.js";const{Title:j,Text:g}=n,{Option:u}=o,y=()=>{const[n,y]=t.useState(!1),[p,f]=t.useState({title:"Test Notification",message:"This is a test notification",priority:"medium",category:"info"}),{notifications:v,unreadCount:m,connectionStatus:C,connectionStats:w,connect:N,markAsRead:S,acknowledgeNotification:T,isConnected:k,isConnecting:A,hasError:b}=e();return i.jsxs("div",{style:{padding:"20px",maxWidth:"1200px",margin:"0 auto"},children:[i.jsx(j,{level:2,children:"Notifications System Test"}),i.jsxs(r,{direction:"vertical",size:"large",style:{width:"100%"},children:[i.jsx(c,{title:"Connection Status",children:i.jsxs(r,{direction:"vertical",style:{width:"100%"},children:[i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:"Status: "}),i.jsx(a,{status:(()=>{switch(C){case"connected":return"success";case"connecting":return"processing";case"error":return"warning";case"failed":return"error";default:return"default"}})(),text:C.toUpperCase()})]}),i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:"Is Connected: "}),i.jsx(g,{type:k?"success":"danger",children:k?"YES":"NO"})]}),i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:"Is Connecting: "}),i.jsx(g,{type:A?"warning":"secondary",children:A?"YES":"NO"})]}),i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:"Has Error: "}),i.jsx(g,{type:b?"danger":"success",children:b?"YES":"NO"})]}),w.connectedAt&&i.jsxs(i.Fragment,{children:[i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:"Connected At: "}),i.jsx(g,{children:new Date(w.connectedAt).toLocaleString()})]}),i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:"Messages Received: "}),i.jsx(g,{children:w.messagesReceived})]}),i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:"Reconnect Attempts: "}),i.jsx(g,{children:w.reconnectAttempts})]})]}),i.jsx(d,{type:"primary",onClick:N,disabled:A,loading:A,children:A?"Connecting...":"Reconnect"})]})}),i.jsx(c,{title:"Notifications Summary",children:i.jsxs(r,{direction:"vertical",children:[i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:"Total Notifications: "}),i.jsx(a,{count:v.length})]}),i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:"Unread Count: "}),i.jsx(a,{count:m})]})]})}),i.jsx(c,{title:"Create Test Notification",children:i.jsxs(r,{direction:"vertical",style:{width:"100%"},children:[i.jsxs("div",{children:[i.jsx(g,{children:"Title: "}),i.jsx(l,{value:p.title,onChange:e=>f({...p,title:e.target.value}),placeholder:"Notification title"})]}),i.jsxs("div",{children:[i.jsx(g,{children:"Message: "}),i.jsx(l.TextArea,{value:p.message,onChange:e=>f({...p,message:e.target.value}),placeholder:"Notification message",rows:3})]}),i.jsxs("div",{children:[i.jsx(g,{children:"Priority: "}),i.jsxs(o,{value:p.priority,onChange:e=>f({...p,priority:e}),style:{width:120},children:[i.jsx(u,{value:"low",children:"Low"}),i.jsx(u,{value:"medium",children:"Medium"}),i.jsx(u,{value:"high",children:"High"}),i.jsx(u,{value:"critical",children:"Critical"})]})]}),i.jsxs("div",{children:[i.jsx(g,{children:"Category: "}),i.jsxs(o,{value:p.category,onChange:e=>f({...p,category:e}),style:{width:150},children:[i.jsx(u,{value:"info",children:"Info"}),i.jsx(u,{value:"alert",children:"Alert"}),i.jsx(u,{value:"maintenance",children:"Maintenance"}),i.jsx(u,{value:"production",children:"Production"}),i.jsx(u,{value:"quality",children:"Quality"})]})]}),i.jsx(d,{type:"primary",onClick:async()=>{var e,i;y(!0);try{await s.withCredentials().post("/api/notifications").send(p).withCredentials();x.success("Test notification created successfully!")}catch(t){x.error("Failed to create test notification: "+((null==(i=null==(e=t.response)?void 0:e.data)?void 0:i.message)||t.message))}finally{y(!1)}},loading:n,children:"Create Test Notification"})]})}),i.jsx(c,{title:"Test Actions",children:i.jsxs(r,{children:[i.jsx(d,{type:"primary",onClick:()=>{v.length>0&&S(v[0].id)},disabled:0===v.length,children:"Mark First as Read"}),i.jsx(d,{type:"default",onClick:()=>{v.length>0&&T(v[0].id)},disabled:0===v.length,children:"Acknowledge First"})]})}),i.jsx(c,{title:"Notifications List",children:0===v.length?i.jsx(h,{message:"No notifications",description:"No notifications found. Check the console for API connection issues.",type:"info"}):i.jsxs(r,{direction:"vertical",style:{width:"100%"},children:[v.slice(0,5).map(((e,s)=>i.jsx(c,{size:"small",style:{border:e.isUnread?"2px solid #1890ff":"1px solid #f0f0f0",backgroundColor:e.isUnread?"#f0f7ff":"white"},children:i.jsxs("div",{children:[i.jsx(g,{strong:!0,children:e.title}),i.jsx("br",{}),i.jsx(g,{type:"secondary",children:e.message}),i.jsx("br",{}),i.jsxs(r,{children:[i.jsxs(g,{type:"secondary",children:["Priority: ",e.priority]}),i.jsxs(g,{type:"secondary",children:["Category: ",e.category]}),i.jsxs(g,{type:"secondary",children:["Created: ",new Date(e.created_at).toLocaleString()]}),e.read_at&&i.jsx(g,{type:"success",children:"Read"}),e.acknowledged_at&&i.jsx(g,{type:"success",children:"Acknowledged"})]})]})},e.id))),v.length>5&&i.jsxs(g,{type:"secondary",children:["... and ",v.length-5," more notifications"]})]})})]})]})};export{y as default};
