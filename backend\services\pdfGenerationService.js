import puppeteer from 'puppeteer';
import path from 'path';
import fs from 'fs/promises';

/**
 * Modern PDF Generation Service using React + Tailwind + Puppeteer
 * 
 * Advantages over PDFDocument:
 * - HTML/CSS layout is more maintainable than programmatic PDF construction
 * - Perfect chart rendering using existing Chart.js components
 * - Consistent styling with web interface using Tailwind CSS
 * - Better typography and responsive design capabilities
 * - Easier debugging and iteration on PDF layout
 */
class PDFGenerationService {
  constructor() {
    this.browser = null;
    this.isInitialized = false;
  }

  /**
   * Initialize Puppeteer browser instance
   * Reuse browser instance for better performance
   */
  async initialize() {
    if (this.isInitialized && this.browser) {
      return;
    }

    try {
      this.browser = await puppeteer.launch({
        headless: 'new', // Use new headless mode for better performance
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ],
        // Optimize for server environments
        defaultViewport: {
          width: 1200,
          height: 800,
          deviceScaleFactor: 2 // High DPI for crisp charts
        }
      });
      
      this.isInitialized = true;
      console.log('✅ PDF Generation Service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize PDF service:', error);
      throw new Error('PDF service initialization failed');
    }
  }

  /**
   * Generate PDF from report data using React template
   * @param {Object} reportData - Complete report data structure
   * @param {Object} options - PDF generation options
   * @returns {Buffer} PDF buffer
   */
  async generateShiftReportPDF(reportData, options = {}) {
    await this.initialize();

    const page = await this.browser.newPage();
    
    try {
      // Set viewport for consistent rendering
      await page.setViewport({
        width: 1200,
        height: 800,
        deviceScaleFactor: 2
      });

      // Navigate to PDF preview route with report data
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const pdfUrl = `${frontendUrl}/reports/pdf-preview`;

      // Pass report data as URL parameters (base64 encoded for complex data)
      const encodedData = Buffer.from(JSON.stringify(reportData)).toString('base64');
      const fullUrl = `${pdfUrl}?data=${encodedData}`;

      console.log('🔄 Navigating to PDF template...');
      console.log('📍 Frontend URL:', frontendUrl);
      console.log('🔗 Full PDF URL:', fullUrl.substring(0, 100) + '...');
      await page.goto(fullUrl, {
        waitUntil: 'networkidle0', // Wait for all network requests to complete
        timeout: 30000
      });

      console.log('🔍 Page loaded, checking for readiness...');

      // Check if page loaded successfully
      const title = await page.title();
      console.log('📄 Page title:', title);

      // Wait for charts to render completely
      try {
        await page.waitForSelector('[data-pdf-ready="true"]', {
          timeout: 20000 // Increased timeout
        });
        console.log('✅ PDF ready selector found');
      } catch (selectorError) {
        console.error('❌ PDF ready selector not found, checking page content...');

        // Debug: Check what's actually on the page
        const bodyContent = await page.evaluate(() => {
          return {
            hasDataAttribute: document.body.hasAttribute('data-pdf-ready'),
            dataAttributeValue: document.body.getAttribute('data-pdf-ready'),
            bodyText: document.body.innerText.substring(0, 200),
            errorElements: Array.from(document.querySelectorAll('[class*="error"]')).map(el => el.textContent)
          };
        });

        console.log('🔍 Page debug info:', bodyContent);
        throw selectorError;
      }

      // Additional wait for Chart.js animations to complete
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('📄 Generating PDF...');
      
      // Generate PDF with optimized settings for print
      const pdfBuffer = await page.pdf({
        format: 'A4',
        margin: {
          top: '20mm',
          right: '15mm',
          bottom: '20mm',
          left: '15mm'
        },
        printBackground: true, // Include background colors and images
        preferCSSPageSize: true, // Use CSS page size if specified
        displayHeaderFooter: true,
        headerTemplate: `
          <div style="font-size: 10px; color: #6B7280; width: 100%; text-align: center; margin: 0 15mm;">
            <span>SOMIPEM - Rapport de Quart</span>
          </div>
        `,
        footerTemplate: `
          <div style="font-size: 10px; color: #6B7280; width: 100%; text-align: center; margin: 0 15mm;">
            <span>Page <span class="pageNumber"></span> sur <span class="totalPages"></span> | Généré le ${new Date().toLocaleDateString('fr-FR')}</span>
          </div>
        `,
        ...options
      });

      console.log('✅ PDF generated successfully');
      return pdfBuffer;

    } catch (error) {
      console.error('❌ PDF generation failed:', error);
      throw new Error(`PDF generation failed: ${error.message}`);
    } finally {
      await page.close();
    }
  }

  /**
   * Cleanup browser instance
   */
  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.isInitialized = false;
      console.log('🧹 PDF service cleaned up');
    }
  }

  /**
   * Health check for PDF service
   */
  async healthCheck() {
    try {
      await this.initialize();
      const page = await this.browser.newPage();
      await page.goto('data:text/html,<h1>Health Check</h1>');
      await page.close();
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() };
    }
  }
}

// Export singleton instance
const pdfGenerationService = new PDFGenerationService();

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  await pdfGenerationService.cleanup();
});

process.on('SIGINT', async () => {
  await pdfGenerationService.cleanup();
});

export default pdfGenerationService;
