import{j as e,b as t}from"./index-Nnj1g72A.js";import{r as a}from"./react-vendor-tYPmozCJ.js";import{d as n,S as r,a6 as s,a7 as i,ar as o,aL as l,Z as c,f as d,a8 as u,c as h,e as p,aM as m,R as f,ad as g,j as D,as as M}from"./antd-vendor-4OvKHZ_k.js";import{i as x}from"./isoWeek-B92Rp6lO.js";import{c as y,a as S,b as w,I as j,u as v,d as C,e as Y,i as T}from"./eventHandlers-DY2JSJgz.js";const b=e=>{if(!e)return null;try{const t=String(e).trim();if(t.includes("/")){const e=t.split(" "),a=e[0],n=e[1]||"00:00:00",[r,s,i]=a.split("/");if(r&&s&&i&&r.length<=2&&s.length<=2&&4===i.length){r.padStart(2,"0"),s.padStart(2,"0");const e=n.split(":"),t=parseInt(e[0])||0,a=parseInt(e[1])||0,o=parseInt(e[2])||0,l=new Date(parseInt(i),parseInt(s)-1,parseInt(r),t,a,o);if(!isNaN(l.getTime()))return l}}if(t.includes("-")&&t.includes(" ")){const e=t.indexOf(" "),a=t.substring(0,e),n=t.substring(e+1);if(n.includes("-")){const e=n.lastIndexOf("-"),t=n.substring(0,e),r=n.substring(e+1);if(r.includes("-")){const[e,n]=r.split("-");if(a&&e&&n&&t){const r=t.split(":"),s=parseInt(r[0])||0,i=parseInt(r[1])||0,o=parseInt(r[2])||0,l=new Date(parseInt(a),parseInt(e)-1,parseInt(n),s,i,o);if(!isNaN(l.getTime()))return l}}}}const a=new Date(t);return isNaN(a.getTime())?null:a}catch(t){return null}};n.extend(x),n.extend(T);const _=a.createContext(),N=()=>{const e=a.useContext(_);return e||null},I=({children:t})=>{const[r,s]=a.useState({machineModels:[],machineNames:[],selectedMachineModel:"",selectedMachine:"",filteredMachineNames:[],dateRangeType:"month",selectedDate:null,dateFilterActive:!1,dateRangeDescription:"",arretStats:[],stopsData:[],topStopsData:[],durationTrend:[],machineComparison:[],operatorStats:[],stopReasons:[],chartData:[],filteredStopsData:[],disponibiliteTrendData:[],downtimeParetoData:[],mttrCalendarData:[],disponibiliteByMachineData:[],loading:!1,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,error:null,isChartModalVisible:!1,chartModalContent:null,chartOptions:{activeTab:"bar"},mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1,totalStops:0,undeclaredStops:0,avgDuration:0,totalDuration:0,sidebarStats:[],arretsByRange:[]}),[i,o]=a.useState(j),l=v(i,o),c=C({stopsData:r.stopsData,rawChartData:r.durationTrend,selectedMachine:r.selectedMachine,selectedMachineModel:r.selectedMachineModel,selectedDate:r.selectedDate,doper:r.doper}),d=a.useRef(!0),u=a.useRef(!1),h=(e=>({async getEssentialData(t){const a=await e("\n        query($filters: StopFilterInput) {\n          getStopSidecards(filters: $filters) {\n            Arret_Totale\n            Arret_Totale_nondeclare\n          }\n        }\n      ",{filters:t});return{sidecards:null==a?void 0:a.getStopSidecards,priority:1,loadingState:"essentialLoading"}},async getPerformanceData(t){const a=await e("\n        query($filters: StopFilterInput) {\n          getAllMachineStops(filters: $filters) {\n            Date_Insert\n            Machine_Name\n            Code_Stop\n            duration_minutes\n            Debut_Stop\n            Fin_Stop_Time\n          }\n        }\n      ",{filters:t}),n=(null==a?void 0:a.getAllMachineStops)||[];let r=0,s=0,i=0;if(n.length>0){const e=n.reduce(((e,t)=>{if(t.duration_minutes&&t.duration_minutes>0)return e+parseFloat(t.duration_minutes);if(t.Debut_Stop&&t.Fin_Stop_Time)try{const a=e=>{if(e.includes(" ")){const[t,a]=e.split(" "),[n,r,s]=t.split("/"),[i,o]=a.split(":");return new Date(s,r-1,n,i,o)}return new Date(e)},n=a(t.Debut_Stop),r=a(t.Fin_Stop_Time);if(!isNaN(n.getTime())&&!isNaN(r.getTime())){const t=r-n;return e+Math.max(0,Math.floor(t/6e4))}}catch(a){}return e}),0);r=n.length>0?e/n.length:0;const t=n.length>0?30:1,a=n.length/t;s=a>0?1440/a:0;const o=24*t*60;i=o>0?(o-e)/o*100:0,r=Math.max(0,Math.min(r,1440)),s=Math.max(0,Math.min(s,10080)),i=Math.max(0,Math.min(i,100))}return{performance:{mttr:Number(r.toFixed(1)),mtbf:Number(s.toFixed(1)),doper:Number(i.toFixed(1))},priority:2,loadingState:"essentialLoading"}},async getChartData(t){const[a,n]=await Promise.all([e("\n          query($filters: StopFilterInput) {\n            getTop5Stops(filters: $filters) {\n              stopName\n              count\n            }\n          }\n        ",{filters:t}),e("\n          query($filters: StopFilterInput) {\n            getMachineStopComparison(filters: $filters) {\n              Machine_Name\n              stops\n              totalDuration\n            }\n          }\n        ",{filters:t})]);return{topStops:(null==a?void 0:a.getTop5Stops)||[],machineComparison:(null==n?void 0:n.getMachineStopComparison)||[],priority:3,loadingState:"detailedLoading"}},async getTableData(t){const a=await e("\n        query($filters: StopFilterInput) {\n          getAllMachineStops(filters: $filters) {\n            Date_Insert\n            Machine_Name\n            Part_NO\n            Code_Stop\n            Debut_Stop\n            Fin_Stop_Time\n            Regleur_Prenom\n            duration_minutes\n          }\n        }\n      ",{filters:t});return{stopsData:(null==a?void 0:a.getAllMachineStops)||[],priority:4,loadingState:"detailedLoading"}},async getMachineModels(){const t=await e("\n        query {\n          getStopMachineModels {\n            model\n          }\n        }\n      ");return(null==t?void 0:t.getStopMachineModels)||[]},async getMachineNames(){const t=await e("\n        query {\n          getStopMachineNames {\n            Machine_Name\n          }\n        }\n      ");return(null==t?void 0:t.getStopMachineNames)||[]}}))(a.useCallback((async(e,t={})=>{const a=await fetch("/api/graphql",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:t})}),n=await a.json();if(n.errors)throw new Error(n.errors[0].message);return n.data}),[])),p=((e,t,n)=>{const r=a.useRef(!1),s=a.useRef(!0);return a.useRef(Date.now()),{fetchDataInQueue:a.useCallback((async(a=!1)=>{var i,o,l;if((!r.current||a)&&s.current){r.current=!0,t.selectedMachineModel&&t.selectedMachine&&t.selectedDate&&n((e=>({...e,complexFilterLoading:!0})));try{const a={model:t.selectedMachineModel||null,machine:t.selectedMachine||null,date:t.selectedDate?"string"==typeof t.selectedDate?t.selectedDate:t.selectedDate.format("YYYY-MM-DD"):null,startDate:t.selectedDate?"string"==typeof t.selectedDate?t.selectedDate:t.selectedDate.clone().startOf(t.dateRangeType).format("YYYY-MM-DD"):null,endDate:t.selectedDate?"string"==typeof t.selectedDate?t.selectedDate:t.selectedDate.clone().endOf(t.dateRangeType).format("YYYY-MM-DD"):null,dateRangeType:t.dateRangeType||"month"};n((e=>({...e,loading:!0,essentialLoading:!0})));const r=await e.getEssentialData(a);if(!s.current)return;if(r.sidecards){const e=[{title:"Total Arrêts",value:r.sidecards.Arret_Totale||0,icon:"🚨"},{title:"Arrêts Non Déclarés",value:r.sidecards.Arret_Totale_nondeclare||0,icon:"⚠️"},{title:"Durée Totale",value:0,suffix:"min",icon:"⏱️"},{title:"Durée Moyenne",value:0,suffix:"min",icon:"⏱️"},{title:"Interventions",value:0,icon:"🔧"}];n((t=>({...t,arretStats:e,totalStops:r.sidecards.Arret_Totale||0,undeclaredStops:r.sidecards.Arret_Totale_nondeclare||0,essentialLoading:!1})))}if(await new Promise((e=>setTimeout(e,100))),a.machine){const t=await e.getPerformanceData(a);if(!s.current)return;n((e=>({...e,mttr:t.performance.mttr,mtbf:t.performance.mtbf,doper:t.performance.doper,showPerformanceMetrics:!0})))}else n((e=>({...e,mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1})));await new Promise((e=>setTimeout(e,200))),n((e=>({...e,detailedLoading:!0})));const c=await e.getChartData(a);if(!s.current)return;if(c.topStops){const e=c.topStops.reduce(((e,t)=>e+t.count),0),t=c.topStops.map((t=>({...t,percentage:e>0?(t.count/e*100).toFixed(1):0}))),a=(c.machineComparison||[]).map((e=>{const t=e.totalStops||e.stops||e.incidents||0,a=e.totalDuration||e.duration||0;return{Machine_Name:e.Machine_Name,machine:e.Machine_Name,name:e.Machine_Name,stops:t,totalStops:t,totalDuration:a,avgDuration:t>0?(a/t).toFixed(1):0}})),r=t.map((e=>({reason:e.stopName||e.name||"Non défini",count:e.count||0,name:e.stopName||e.name||"Non défini",value:e.count||0,percentage:e.percentage,stopName:e.stopName||e.name||"Non défini"})));n((e=>({...e,topStopsData:t,machineComparison:a,stopReasons:r,chartData:[],durationTrend:[],disponibiliteTrendData:[],downtimeParetoData:t,mttrCalendarData:[],disponibiliteByMachineData:[]})))}await new Promise((e=>setTimeout(e,300)));const d=await e.getTableData(a);if(!s.current)return;n((e=>({...e,stopsData:d.stopsData})));const u=(null==(i=d.stopsData)?void 0:i.filter((e=>{if(t.selectedMachine&&e.Machine_Name!==t.selectedMachine)return!1;if(t.selectedDate&&e.Date_Insert){const a=b(e.Date_Insert),n=new Date(t.selectedDate);if(!a)return!1;if("day"===t.dateRangeType)return a.toDateString()===n.toDateString();if("week"===t.dateRangeType){const e=new Date(n);e.setDate(n.getDate()-n.getDay());const t=new Date(e);return t.setDate(e.getDate()+6),a>=e&&a<=t}if("month"===t.dateRangeType)return a.getMonth()===n.getMonth()&&a.getFullYear()===n.getFullYear()}return!0})))||[];n((e=>({...e,filteredStopsData:u})));const h={};u.forEach((e=>{if(e.Date_Insert&&"string"==typeof e.Date_Insert){let t;if(e.Date_Insert.toString(),t=b(e.Date_Insert),t){const a=t.toISOString().split("T")[0];h[a]||(h[a]={date:a,stops:0,duration:0}),h[a].stops++,e.duration_minutes&&e.duration_minutes>0&&(h[a].duration+=parseFloat(e.duration_minutes))}}}));let p=Object.values(h).sort(((e,t)=>new Date(e.date)-new Date(t.date)));if(t.selectedDate&&t.dateRangeType)if("month"===t.dateRangeType){const e=new Date(t.selectedDate),a=e.getMonth(),n=e.getFullYear();p=p.filter((e=>{const t=new Date(e.date);return t.getMonth()===a&&t.getFullYear()===n}))}else if("week"===t.dateRangeType){const e=new Date(t.selectedDate),a=new Date(e);a.setDate(e.getDate()-e.getDay());const n=new Date(a);n.setDate(a.getDate()+6),p=p.filter((e=>{const t=new Date(e.date);return t>=a&&t<=n}))}else{const e=new Date(t.selectedDate),a=new Date(e);a.setDate(e.getDate()-3);const n=new Date(e);n.setDate(e.getDate()+3),p=p.filter((e=>{const t=new Date(e.date);return t>=a&&t<=n}))}p=p.map((e=>{let t,a,n=!1;if(e.date&&"string"==typeof e.date)if(e.date.match(/^\d{4}-\d{2}-\d{2}$/))t=new Date(e.date),n=!isNaN(t.getTime());else{const a=e.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);if(a){const[e,r,s,i]=a,o=`${r}-${s.padStart(2,"0")}-${i.padStart(2,"0")}`;t=new Date(o),n=!isNaN(t.getTime())}}if(n&&t)a=t.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"});else{const t=e.date.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);if(t){const[e,n,r,s]=t;a=`${s.padStart(2,"0")}/${r.padStart(2,"0")}`}else a=e.date.slice(0,10)}return{...e,displayDate:a}}));let m=p;if(0===p.length&&(m=[]),n((e=>({...e,chartData:m}))),(null==(o=d.stopsData)?void 0:o.length)>0){const e={};d.stopsData.forEach((t=>{const a=t.Regleur_Prenom||"Non assigné";e[a]=(e[a]||0)+1}));const t=Object.entries(e).map((([e,t])=>({operator:e,interventions:t})));n((e=>({...e,operatorStats:t})))}let f=[],g=[],D=[];t.selectedMachine&&(null==(l=d.stopsData)?void 0:l.length)>0&&(f=y(u,t.dateRangeType),g=S(u),D=w(u)),n((e=>({...e,loading:!1,detailedLoading:!1,complexFilterLoading:!1,disponibiliteTrendData:f,mttrCalendarData:g,downtimeParetoData:D,arretsByRange:d.stopsData||[],sidebarStats:e.arretStats})))}catch(c){if(!s.current)return;n((e=>({...e,loading:!1,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,error:c.message||"Failed to fetch data",arretStats:[{title:"Total Arrêts",value:0,icon:"🚨"},{title:"Arrêts Non Déclarés",value:0,icon:"⚠️"},{title:"Durée Totale",value:0,suffix:"min",icon:"⏱️"},{title:"Durée Moyenne",value:0,suffix:"min",icon:"⏱️"},{title:"Interventions",value:0,icon:"🔧"}]})))}finally{r.current=!1}}}),[t.selectedMachineModel,t.selectedMachine,t.selectedDate,t.dateRangeType,e,n]),initializeMachineData:a.useCallback((async()=>{try{const t=await e.getMachineModels(),a=await e.getMachineNames();if(s.current){const e=a.map((e=>{var t;const a=e.Machine_Name;let n="";return n=a.startsWith("IPSO")?"IPSO":a.startsWith("IPS")?"IPS":a.startsWith("CCM")?"CCM":(null==(t=a.match(/^[A-Za-z]+/))?void 0:t[0])||"UNKNOWN",{name:a,model:n}}));n((a=>({...a,machineModels:t,machineNames:e,selectedMachineModel:"IPS"})))}}catch(t){s.current&&n((e=>({...e,error:t.message})))}}),[e,n]),setMounted:a.useCallback((e=>{s.current=e}),[]),isMounted:s.current,pendingFetch:r.current}})(h,r,s),m=Y(r,s,p,l),f=a.useCallback(((e,t)=>{if(!e)return{short:"",full:""};const a=n(e);if("day"===t)return{short:a.format("DD/MM"),full:a.format("DD/MM/YYYY")};if("week"===t){const e=a.startOf("isoWeek"),t=a.endOf("isoWeek");return{short:`${e.format("DD/MM")} - ${t.format("DD/MM")}`,full:`Semaine du ${e.format("DD/MM/YYYY")} au ${t.format("DD/MM/YYYY")}`}}return"month"===t?{short:a.format("MM/YYYY"),full:a.format("MMMM YYYY")}:{short:"",full:""}}),[]);a.useEffect((()=>{if(r.selectedMachineModel){const e=r.machineNames.filter((e=>e.model===r.selectedMachineModel||"string"==typeof e&&e.includes(r.selectedMachineModel)));s((t=>({...t,filteredMachineNames:e}))),r.selectedMachine&&!e.find((e=>("string"==typeof e?e:e.name)===r.selectedMachine))&&s((e=>({...e,selectedMachine:""})))}else s((e=>({...e,filteredMachineNames:[]})))}),[r.selectedMachineModel,r.machineNames,r.selectedMachine]),a.useEffect((()=>{(c.totalDuration||c.averageDuration||c.totalInterventions)&&s((e=>({...e,arretStats:e.arretStats.map((e=>"Durée Totale"===e.title?{...e,value:c.totalDuration}:"Durée Moyenne"===e.title?{...e,value:c.averageDuration}:"Interventions"===e.title?{...e,value:c.totalInterventions}:e))})))}),[c.totalDuration,c.averageDuration,c.totalInterventions]),a.useEffect((()=>{if(u.current)return;(async()=>{try{p.setMounted(!0),await p.initializeMachineData(),u.current=!0}catch(e){d.current&&s((t=>({...t,error:e.message})))}})()}),[]),a.useEffect((()=>{u.current&&r.selectedMachineModel&&p.fetchDataInQueue()}),[r.selectedMachineModel,r.selectedMachine,r.selectedDate,r.dateRangeType]),a.useEffect((()=>()=>{d.current=!1,p.setMounted(!1)}),[]);const g={...r,computedValues:c,formatDateRange:f,...m,refreshData:()=>p.fetchDataInQueue(!0),skeletonManager:l,showChartModal:e=>{s((t=>({...t,isChartModalVisible:!0,chartModalContent:e})))},hideChartModal:()=>{s((e=>({...e,isChartModalVisible:!1,chartModalContent:null})))},openChartModal:e=>{s((t=>({...t,isChartModalVisible:!0,chartModalContent:e})))},setChartOptions:e=>{s((t=>({...t,chartOptions:"function"==typeof e?e(t.chartOptions):e})))}};return e.jsx(_.Provider,{value:g,children:t})},{Option:R}=o,F=({onFilterChange:n})=>{const x=N();if(!x)return e.jsx("div",{children:"Context not available"});const{machineModels:y=[],filteredMachineNames:S=[],selectedMachineModel:w="",selectedMachine:j="",handleMachineModelChange:v,handleMachineChange:C,dateRangeType:Y="day",selectedDate:T=null,dateFilterActive:b=!1,handleDateRangeTypeChange:_,handleDateChange:I,loading:F=!1,stopsData:L=[],resetFilters:A,handleRefresh:$,complexFilterLoading:k=!1,dataManager:P}=x;a.useEffect((()=>{const e={model:w,machine:j,date:null==T?void 0:T.format("YYYY-MM-DD"),dateType:Y,dateFilterActive:b,hasAllFilters:w&&j&&b,dataCount:null==L?void 0:L.length};n&&"function"==typeof n&&n(e)}),[w,j,T,Y,b,null==L?void 0:L.length,n,y,S]);return e.jsxs(r,{direction:"vertical",size:"middle",style:{width:"100%"},children:[e.jsxs(s,{gutter:[16,16],align:"middle",children:[e.jsx(i,{xs:24,sm:12,lg:6,children:e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:4,fontSize:"12px",color:t.LIGHT_GRAY},children:"Modèle de Machine"}),e.jsx(o,{placeholder:"Sélectionner un modèle",value:w,onChange:v,style:{width:"100%"},allowClear:!0,showSearch:!0,filterOption:(e,t)=>t.children.toLowerCase().includes(e.toLowerCase()),children:y.map((t=>{const a="object"==typeof t?t.model:t;return e.jsx(R,{value:a,children:a},a)}))})]})}),e.jsx(i,{xs:24,sm:12,lg:6,children:e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:4,fontSize:"12px",color:t.LIGHT_GRAY},children:"Machine Spécifique"}),e.jsx(o,{placeholder:"Sélectionner une machine",value:j,onChange:e=>{"function"==typeof C&&C(e)},style:{width:"100%"},allowClear:!0,showSearch:!0,disabled:!w&&0===S.length,filterOption:(e,t)=>t.children.toLowerCase().includes(e.toLowerCase()),children:S.map((t=>{const a=t.name;return e.jsx(R,{value:a,children:a||"Unknown Machine"},a||`machine-${Math.random()}`)}))})]})}),e.jsx(i,{xs:24,sm:12,lg:6,children:e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:4,fontSize:"12px",color:t.LIGHT_GRAY},children:"Type de Période"}),e.jsx(l,{value:Y,onChange:_,options:[{label:"Jour",value:"day",icon:e.jsx(c,{})},{label:"Semaine",value:"week",icon:e.jsx(c,{})},{label:"Mois",value:"month",icon:e.jsx(c,{})}],style:{width:"100%"}})]})}),e.jsx(i,{xs:24,sm:12,lg:6,children:e.jsxs("div",{children:[e.jsx("div",{style:{marginBottom:4,fontSize:"12px",color:t.LIGHT_GRAY},children:"Sélection de Date"}),"day"===Y?e.jsx(M,{value:T,onChange:I,format:"DD/MM/YYYY",placeholder:"Sélectionner une date",allowClear:!0,style:{width:"100%"}}):"week"===Y?e.jsx(M,{value:T,onChange:I,picker:"week",format:"[Semaine] w YYYY",placeholder:"Sélectionner une semaine",allowClear:!0,style:{width:"100%"}}):"month"===Y?e.jsx(M,{value:T,onChange:I,picker:"month",format:"MMMM YYYY",placeholder:"Sélectionner un mois",allowClear:!0,style:{width:"100%"}}):null]})})]}),e.jsxs(s,{gutter:[16,16],align:"middle",justify:"space-between",children:[e.jsx(i,{children:e.jsx(r,{children:(w||j||b)&&e.jsxs(r,{wrap:!0,children:[w&&e.jsxs(d,{color:"blue",closable:!0,onClose:()=>v(""),children:["Modèle: ","object"==typeof w?w.model:w]}),j&&e.jsxs(d,{color:"green",closable:!0,onClose:()=>C(""),children:["Machine: ","object"==typeof j?j.Machine_Name:j]}),b&&T&&e.jsxs(d,{color:"orange",closable:!0,onClose:()=>I(null),children:[e.jsx(u,{style:{marginRight:4}}),T.format("day"===Y?"DD/MM/YYYY":"week"===Y?"[Semaine] w YYYY":"MMMM YYYY")]})]})})}),e.jsx(i,{children:e.jsxs(r,{children:[e.jsx(h,{title:"Effacer tous les filtres",children:e.jsx(p,{icon:e.jsx(m,{}),onClick:A,disabled:!w&&!j&&!b,children:"Effacer"})}),e.jsx(h,{title:"Forcer le rechargement manuel des données",children:e.jsx(p,{type:"primary",icon:e.jsx(f,{}),onClick:$,loading:F||k,children:F||k?"Chargement...":"Forcer Refresh"})})]})})]}),e.jsx(s,{children:e.jsx(i,{span:24,children:e.jsxs(r,{wrap:!0,children:[e.jsxs(d,{icon:e.jsx(g,{}),color:"processing",children:[L.length," arrêts trouvés",w&&!j&&` (modèle: ${w})`,j&&` (machine: ${j})`]}),(w||j||b)&&e.jsxs(d,{color:"blue",children:[[w&&"Modèle",j&&"Machine",b&&"Date"].filter(Boolean).length," filtre(s) actif(s)"]}),w&&!j&&F&&e.jsxs(d,{color:"processing",children:[e.jsx(u,{spin:!0})," Filtrage par modèle en cours..."]}),j&&F&&e.jsxs(d,{color:"processing",children:[e.jsx(u,{spin:!0})," Filtrage par machine spécifique en cours..."]}),b&&F&&e.jsxs(d,{color:"orange",children:[e.jsx(u,{spin:!0})," Filtrage par date en cours..."]}),F&&e.jsx(d,{color:"blue",children:"Chargement en cours..."}),k&&e.jsxs(d,{color:"gold",children:[e.jsx(u,{spin:!0})," Traitement complexe..."]}),e.jsx(d,{color:"success",style:{marginLeft:"auto"},children:"✓ Les changements de filtres actualisent automatiquement les données"})]})})}),x.error&&e.jsx(s,{style:{marginTop:"16px"},children:e.jsx(i,{span:24,children:e.jsx("div",{style:{padding:"12px",backgroundColor:"#FFFFFF",borderRadius:"8px",border:`1px solid ${t.PRIMARY_BLUE}`,boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(D,{style:{color:"#ff4d4f",fontSize:"16px",marginRight:"8px"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold",color:t.DARK_GRAY},children:"Erreur de chargement des données"}),e.jsx("div",{style:{fontSize:"12px",marginTop:"4px",color:t.LIGHT_GRAY},children:x.error.includes&&x.error.includes("AbortError")?"La requête a été interrompue. Ceci peut se produire lors d'un changement rapide de page.":"string"==typeof x.error?x.error:"Une erreur est survenue lors du chargement des données. Veuillez réessayer."}),e.jsxs(r,{style:{marginTop:"8px"},children:[e.jsx(p,{size:"small",type:"primary",onClick:()=>{x.graphQL&&x.graphQL.invalidateCache&&x.graphQL.invalidateCache(),$()},children:"Réessayer"}),e.jsx(p,{size:"small",onClick:A,children:"Réinitialiser les filtres"})]})]})]})})})})]})};export{I as A,F as a,N as u};
