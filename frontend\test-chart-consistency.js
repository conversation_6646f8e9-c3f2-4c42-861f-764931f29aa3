// Simple verification script to ensure both charts use consistent logic
// This script simulates the data processing to verify consistency

console.log('🔍 Testing chart data consistency...');

// Mock sample data similar to what the application receives
const mockStopsData = [
  {
    Debut_Stop: '2025-01-01T08:30:00',
    Fin_Stop_Time: '2025-01-01T08:45:00',
    duration_minutes: 15
  },
  {
    Debut_Stop: '2025-01-01T08:35:00',
    Fin_Stop_Time: '2025-01-01T09:05:00',
    duration_minutes: 30
  },
  {
    Debut_Stop: '2025-01-01T14:20:00',
    Fin_Stop_Time: '2025-01-01T14:50:00',
    duration_minutes: 30
  },
  {
    Debut_Stop: '2025-01-01T14:25:00',
    Fin_Stop_Time: '2025-01-01T15:25:00',
    duration_minutes: 60
  }
];

// Function from ArretHeatmapChart (updated)
const processDataHeatmap = (stopsData) => {
  const MAX_REASONABLE_DURATION = 480; // 8 hours maximum
  const MIN_REASONABLE_DURATION = 1;   // 1 minute minimum
  
  const hourlyStats = {};
  
  // Initialize all hours
  for (let hour = 0; hour < 24; hour++) {
    hourlyStats[hour] = {
      count: 0,
      totalDuration: 0,
      avgDuration: 0
    };
  }
  
  stopsData.forEach(stop => {
    if (stop.Debut_Stop) {
      try {
        const startTime = new Date(stop.Debut_Stop);
        const hour = startTime.getHours();
        
        if (hourlyStats[hour]) {
          let duration = 0;
          
          if (stop.duration_minutes !== undefined && stop.duration_minutes !== null) {
            duration = parseFloat(stop.duration_minutes);
          } else if (stop.Fin_Stop_Time) {
            const endTime = new Date(stop.Fin_Stop_Time);
            duration = (endTime - startTime) / (1000 * 60);
          }
          
          if (duration > 0 && duration >= MIN_REASONABLE_DURATION && duration <= MAX_REASONABLE_DURATION) {
            hourlyStats[hour].count += 1;
            hourlyStats[hour].totalDuration += duration;
          }
        }
      } catch (error) {
        console.warn('Error parsing time:', error);
      }
    }
  });

  // Calculate averages
  Object.keys(hourlyStats).forEach(hour => {
    const stats = hourlyStats[hour];
    stats.avgDuration = stats.count > 0 ? stats.totalDuration / stats.count : 0;
  });

  return hourlyStats;
};

// Function from ArretTimePatternChart (reference)
const processDataTimePattern = (stopsData) => {
  const MAX_REASONABLE_DURATION = 480; // 8 hours maximum
  const MIN_REASONABLE_DURATION = 1;   // 1 minute minimum
  
  const hourlyStats = {};
  
  // Initialize all hours
  for (let hour = 0; hour < 24; hour++) {
    hourlyStats[hour] = {
      count: 0,
      totalDuration: 0,
      avgDuration: 0
    };
  }
  
  stopsData.forEach(stop => {
    if (stop.Debut_Stop) {
      try {
        const startTime = new Date(stop.Debut_Stop);
        const hour = startTime.getHours();
        
        if (hourlyStats[hour]) {
          let duration = 0;
          
          if (stop.duration_minutes !== undefined && stop.duration_minutes !== null) {
            duration = parseFloat(stop.duration_minutes);
          } else if (stop.Fin_Stop_Time) {
            const endTime = new Date(stop.Fin_Stop_Time);
            duration = (endTime - startTime) / (1000 * 60);
          }
          
          if (duration > 0 && duration >= MIN_REASONABLE_DURATION && duration <= MAX_REASONABLE_DURATION) {
            hourlyStats[hour].count += 1;
            hourlyStats[hour].totalDuration += duration;
          }
        }
      } catch (error) {
        console.warn('Error parsing time:', error);
      }
    }
  });

  // Calculate averages
  Object.keys(hourlyStats).forEach(hour => {
    const stats = hourlyStats[hour];
    stats.avgDuration = stats.count > 0 ? stats.totalDuration / stats.count : 0;
  });

  return hourlyStats;
};

// Test both processing methods
const heatmapResult = processDataHeatmap(mockStopsData);
const timePatternResult = processDataTimePattern(mockStopsData);

console.log('\n📊 Results Comparison:');
console.log('Hour | Heatmap Avg | TimePattern Avg | Match?');
console.log('-----|-------------|-----------------|-------');

let allMatch = true;
for (let hour = 0; hour < 24; hour++) {
  const heatmapAvg = heatmapResult[hour].avgDuration.toFixed(2);
  const timePatternAvg = timePatternResult[hour].avgDuration.toFixed(2);
  const match = heatmapAvg === timePatternAvg ? '✅' : '❌';
  
  if (heatmapAvg !== timePatternAvg) {
    allMatch = false;
  }
  
  if (heatmapAvg !== '0.00' || timePatternAvg !== '0.00') {
    console.log(`${hour.toString().padStart(2, '0')}:00 | ${heatmapAvg.padStart(11, ' ')} | ${timePatternAvg.padStart(15, ' ')} | ${match}`);
  }
}

console.log('\n🎯 Overall Result:', allMatch ? '✅ Both charts use identical logic!' : '❌ Charts have different logic');

if (allMatch) {
  console.log('✨ Success! Both "Durée par Heure" and "Motifs Temporels" charts will now show consistent data.');
} else {
  console.log('⚠️  There are still differences in the calculation logic.');
}
