/**
 * Test script for backend SuperAgent implementation
 * Demonstrates health monitoring and external service integration
 */

import superAgentService from './services/SuperAgentService.js';
import healthMonitoringService from './services/HealthMonitoringService.js';

async function testSuperAgentBackend() {
  console.log('🧪 Testing Backend SuperAgent Implementation');
  console.log('=' .repeat(50));

  try {
    // Test 1: Basic SuperAgent Service functionality
    console.log('\n1️⃣ Testing SuperAgent Service...');
    
    const testEndpoint = 'http://httpbin.org/status/200';
    const result = await superAgentService.get(testEndpoint);
    
    console.log('✅ SuperAgent GET request successful:', {
      success: result.success,
      status: result.status
    });

    // Test 2: Health check functionality
    console.log('\n2️⃣ Testing Health Check...');
    
    const healthResult = await superAgentService.healthCheck('http://httpbin.org/status/200');
    console.log('✅ Health check result:', {
      healthy: healthResult.healthy,
      responseTime: healthResult.responseTime,
      url: healthResult.url
    });

    // Test 3: Batch health check
    console.log('\n3️⃣ Testing Batch Health Check...');
    
    const services = [
      { name: 'httpbin-200', url: 'http://httpbin.org/status/200' },
      { name: 'httpbin-404', url: 'http://httpbin.org/status/404' },
      { name: 'google', url: 'https://www.google.com' }
    ];

    const batchResult = await superAgentService.batchHealthCheck(services);
    console.log('✅ Batch health check:', {
      overall: batchResult.overall.healthy,
      totalServices: batchResult.overall.checkedServices,
      results: batchResult.services.map(s => ({
        name: s.name,
        healthy: s.healthy,
        responseTime: s.responseTime
      }))
    });

    // Test 4: Health Monitoring Service
    console.log('\n4️⃣ Testing Health Monitoring Service...');
    
    const healthStatus = await healthMonitoringService.performHealthCheck();
    console.log('✅ Health monitoring result:', {
      overall: healthStatus.overall,
      database: healthStatus.services.database?.status,
      elasticsearch: healthStatus.services.elasticsearch?.status,
      external: healthStatus.services.external?.status,
      timestamp: healthStatus.timestamp
    });

    // Test 5: Connectivity test
    console.log('\n5️⃣ Testing Connectivity...');
    
    const connectivityResult = await superAgentService.testConnectivity('https://www.google.com');
    console.log('✅ Connectivity test result:', {
      connected: connectivityResult.connected,
      responseTime: connectivityResult.responseTime,
      url: connectivityResult.url
    });

    // Test 6: Error handling
    console.log('\n6️⃣ Testing Error Handling...');
    
    const errorResult = await superAgentService.get('http://invalid-url-that-does-not-exist.com');
    console.log('✅ Error handling result:', {
      success: errorResult.success,
      error: errorResult.error ? 'Error caught properly' : 'No error',
      status: errorResult.status
    });

    // Test 7: POST request with data
    console.log('\n7️⃣ Testing POST Request...');
    
    const postData = { test: 'data', timestamp: new Date().toISOString() };
    const postResult = await superAgentService.post('http://httpbin.org/post', postData);
    console.log('✅ POST request result:', {
      success: postResult.success,
      status: postResult.status,
      dataEchoed: postResult.data?.json?.test === 'data'
    });

    console.log('\n🎉 All Backend SuperAgent Tests Completed Successfully!');
    console.log('=' .repeat(50));

    return {
      success: true,
      testsCompleted: 7,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// Test health endpoints simulation
async function testHealthEndpoints() {
  console.log('\n🏥 Testing Health Endpoints Simulation');
  console.log('=' .repeat(50));

  try {
    // Simulate what health endpoints would return
    const healthStats = healthMonitoringService.getHealthStatistics();
    console.log('📊 Health Statistics:', healthStats);

    const currentHealth = healthMonitoringService.getCurrentHealth();
    console.log('💓 Current Health:', currentHealth);

    // Test service connectivity
    const serviceTest = await healthMonitoringService.testServiceConnectivity('frontend-app');
    console.log('🔗 Service Connectivity Test:', serviceTest);

    console.log('\n✅ Health Endpoints Simulation Complete');

  } catch (error) {
    console.error('❌ Health endpoints test failed:', error);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🚀 Starting Backend SuperAgent Tests...\n');
  
  testSuperAgentBackend()
    .then(async (result) => {
      console.log('\n📋 Test Summary:', result);
      
      // Run health endpoints test
      await testHealthEndpoints();
      
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

export { testSuperAgentBackend, testHealthEndpoints };
