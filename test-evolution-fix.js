// Test the new evolution chart data generation
import fetch from 'node-fetch';

const testEvolutionChartGeneration = async () => {
  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getStopDashboardData(filters: { model: "IPS" }) {
              allStops {
                Machine_Name
                Date_Insert
              }
            }
          }
        `
      })
    });

    const data = await response.json();
    
    if (data.data && data.data.getStopDashboardData) {
      const allStops = data.data.getStopDashboardData.allStops;
      console.log('📊 Total stops available:', allStops.length);
      
      // Replicate the frontend logic exactly
      const stopsByDate = {};
      allStops.forEach(stop => {
        if (stop.Date_Insert) {
          // Parse DD/MM/YYYY format and extract date only
          const dateMatch = stop.Date_Insert.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})/);
          if (dateMatch) {
            const [, day, month, year] = dateMatch;
            const dateKey = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            
            if (!stopsByDate[dateKey]) {
              stopsByDate[dateKey] = 0;
            }
            stopsByDate[dateKey]++;
          }
        }
      });
      
      // Transform to chart data format
      const transformedChartData = Object.entries(stopsByDate)
        .map(([date, stops]) => {
          const displayDate = new Date(date).toLocaleDateString('fr-FR', { 
            day: '2-digit', 
            month: '2-digit' 
          });
          return {
            date,
            displayDate,
            stops
          };
        })
        .sort((a, b) => new Date(a.date) - new Date(b.date));
      
      console.log('📈 Evolution chart data:');
      console.log('- Total data points:', transformedChartData.length);
      console.log('- Date range:', 
        transformedChartData[0]?.displayDate, 
        'to', 
        transformedChartData[transformedChartData.length - 1]?.displayDate
      );
      console.log('- Sample data:', transformedChartData.slice(0, 5));
      console.log('- Last 5 data points:', transformedChartData.slice(-5));
      
      // Check for any data issues
      const invalidDates = transformedChartData.filter(item => 
        !item.displayDate || item.displayDate === 'Invalid Date'
      );
      console.log('❌ Invalid dates:', invalidDates.length);
      
      const zeroStops = transformedChartData.filter(item => item.stops === 0);
      console.log('⚠️ Days with zero stops:', zeroStops.length);
      
    } else {
      console.error('❌ GraphQL error:', data.errors);
    }
  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
};

testEvolutionChartGeneration();
