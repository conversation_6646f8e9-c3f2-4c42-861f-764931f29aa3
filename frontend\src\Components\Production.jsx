import React, { useState, useEffect, useCallback, memo } from 'react';
import superagent from 'superagent';
import { 
  Layout, 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Table, 
  DatePicker,
  Tag, 
  Spin, 
  Typography,
  Progress,
  Grid,
  Empty
} from 'antd';
import { 
  RiseOutlined, 
  FallOutlined,
  DashboardOutlined,
  Pie<PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  Bar<PERSON><PERSON>Outlined,
  Area<PERSON>hartOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { 
  ResponsiveContainer,
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { format } from 'd3-format'; // Add this import

// import superagent from 'superagent';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import './Production.css';
import { formatFrenchNumber, formatFrenchInteger, formatFrenchPercentage } from '../utils/numberFormatter';
const { Title, Text } = Typography;
const { useBreakpoint } = Grid;
const COLORS = ['#1890ff', '#13c2c2', '#52c41a', '#faad14', '#f5222d'];
  


// Enhanced chart tooltip formatters with French formatting
  const chartTooltipFormatter = (value, name) => {
    const formattedValue = Number.isInteger(value) 
      ? formatFrenchInteger(value)
      : formatFrenchNumber(value, 2);
    return [formattedValue, name];
  };
 // Enhanced MemoizedBarChart component
 const MemoizedBarChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={400}>
    <BarChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
      <XAxis 
        dataKey="date" 
        tick={{ fill: '#666' }}
        label={{ 
          value: 'Date', 
          position: 'bottom',
          offset: 0,
          style: { fill: '#666' }
        }}
      />
      <YAxis 
        yAxisId="left"
        tickFormatter={value => formatFrenchInteger(value)}
        label={{ 
          value: 'Quantité', 
          angle: -90, 
          position: 'insideLeft',
          style: { fill: '#666' }
        }}
      />
      <YAxis 
        yAxisId="right" 
        orientation="right"
        tickFormatter={value => `${formatFrenchPercentage(value, 1)}%`}
        label={{ 
          value: 'Taux de Rendement Synthétique', 
          angle: 90, 
          position: 'insideRight',
          style: { fill: '#666' }
        }}
      />
      <Tooltip 
        contentStyle={{
          backgroundColor: '#fff',
          border: '1px solid #f0f0f0',
          borderRadius: 8,
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}
        formatter={chartTooltipFormatter}
      />
      <Legend 
        wrapperStyle={{ paddingTop: 20 }}
        formatter={(value) => <span style={{ color: '#666' }}>{value}</span>}
      />
      <Bar 
        yAxisId="left" 
        dataKey="good" 
        name="Quantité bonnene" 
        fill="#82ca9d" 
        maxBarSize={40}
      />
      <Bar 
        yAxisId="left" 
        dataKey="reject" 
        name="Quantité Rejetéeée" 
        fill="#ff4d4f" 
        maxBarSize={40}
      />
      <Line 
        yAxisId="right" 
        type="monotone" 
        dataKey="trs" 
        name="TRS" 
        stroke="#1890ff" 
        strokeWidth={2}
        dot={{ r: 4 }}
        activeDot={{ r: 6 }}
      />
    </BarChart>
  </ResponsiveContainer>
));

const MemoizedMachinePerformance = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <BarChart data={data} margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="Machine_Name" />
      <YAxis />
      <Tooltip />
      <Legend />
      <Bar 
        dataKey="production" 
        name="Production" 
        fill={COLORS[0]} 
        stackId="a"
      />
      <Bar 
        dataKey="rejects" 
        name="Rejets" 
        fill={COLORS[1]} 
        stackId="a"
      />
    </BarChart>
  </ResponsiveContainer>
));
const MemoizedPieChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <PieChart margin={{ top: 16, right: 24, left: 24, bottom: 16 }}>
      <Pie
        data={data}
        dataKey="value"
        nameKey="name"
        cx="50%"
        cy="50%"
        innerRadius={60}
        outerRadius={80}
        paddingAngle={5}
        label
      >
        {data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
        ))}
      </Pie>
      <Tooltip />
      <Legend />
    </PieChart>
  </ResponsiveContainer>
));
const MemoizedLineChart = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <LineChart data={data}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="date" />
      <YAxis />
      <Tooltip />
      <Legend />
      <Line 
        type="monotone" 
        dataKey="oee" 
        name="Taux de Rendement Synthétique" 
        stroke={COLORS[0]} 
        strokeWidth={2}
      />
      <Line 
        type="monotone" 
        dataKey="speed" 
        name="Temps de Cycle" 
        stroke={COLORS[1]} 
        strokeWidth={2}
      />
    </LineChart>
  </ResponsiveContainer>
));
const MemoizedOeeTrend = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <LineChart data={data}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="date" />
      <YAxis />
      <Tooltip />
      <Legend />
      <Line 
        type="monotone" 
        dataKey="oee" 
        name="Taux de Rendement Synthétique" 
        stroke={COLORS[0]} 
        strokeWidth={2}
      />
    </LineChart>
  </ResponsiveContainer>
));
const MemoizedSpeedTrend = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <LineChart data={data}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="date" />
      <YAxis />
      <Tooltip />
      <Line 
        type="monotone" 
        dataKey="speed" 
        name="Temps de Cycle" 
        stroke={COLORS[1]} 
        strokeWidth={2}
      />
    </LineChart>
  </ResponsiveContainer>
));
const MemoizedShiftComparison = memo(({ data }) => (
  <ResponsiveContainer width="100%" height={300}>
    <BarChart data={data}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="Shift" />
      <YAxis />
      <Tooltip />
      <Legend />
      <Bar 
        dataKey="production" 
        name="Production" 
        fill={COLORS[2]} 
      />
      <Bar 
        dataKey="downtime" 
        name="Rejets" 
        fill={COLORS[3]} 
      />
    </BarChart>
  </ResponsiveContainer>
));
const Production = () => {
  const [chartData, setChartData] = useState([]);
  const [selectedDateData, setSelectedDateData] = useState([]);
  const [uniqueDates, setUniqueDates] = useState([]);
  const [goodQty, setGoodQty] = useState(0);
  const [rejetQty, setRejetQty] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dateFilter, setDateFilter] = useState(dayjs());
  const [performanceData, setPerformanceData] = useState([]);
  const [machinePerformance, setMachinePerformance] = useState([]);
  const [hourlyTrends, setHourlyTrends] = useState([]);
  const [operatorStats, setOperatorStats] = useState([]);
  const [oeeTrends, setOeeTrends] = useState([]);
  const [speedTrends, setSpeedTrends] = useState([]);
  const [shiftComparison, setShiftComparison] = useState([]);
  const [mergedData, setMergedData] = useState([]);
  const screens = useBreakpoint();

  const baseURL = process.env.NODE_ENV === 'production'
    ? 'https://charming-hermit-intense.ngrok-free.app'
    : 'http://localhost:5000';

  // 🔒 SECURITY: HTTP-only cookies configuration - no axios configuration needed

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const [
        chartRes, 
        datesRes, 
        prodRes, 
        rejetRes,
        machinesRes,
        hourlyRes,
        operatorsRes,
        oeeTrendsRes,
        speedTrendsRes,
        shiftRes
      ] = await Promise.all([
        superagent.get(baseURL + '/api/testing-chart-production').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/unique-dates-production').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/sidecards-prod').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/sidecards-prod-rejet').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/machine-performance').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/hourly-trends').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/operator-stats').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/machine-oee-trends').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/speed-trends').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/shift-comparison').withCredentials().timeout(30000).retry(2),

      ]);
      const oeeMap = oeeTrendsRes.data.reduce((acc, item) => {
        acc[item.date] = item.oee;
        return acc;
      }, {});
  
      const speedMap = speedTrendsRes.data.reduce((acc, item) => {
        acc[item.date] = item.speed;
        return acc;
      }, {});
  
      const allDates = [
        ...new Set([...Object.keys(oeeMap), ...Object.keys(speedMap)])
      ];
    
      const mergedRes = allDates.map(date => ({
        date,
        oee: oeeMap[date] || 0,
        speed: speedMap[date] || 0
      }));
  
      setMergedData(mergedRes);
      setChartData(chartRes.data.map(transformData));
      console.log(chartRes.data);
      setUniqueDates(datesRes.data);
      setGoodQty(prodRes.data[0]?.goodqty || 0);
      setRejetQty(rejetRes.data[0]?.rejetqty || 0);
      setMachinePerformance(machinesRes.data);
      setHourlyTrends(hourlyRes.data);
      console.log("Hourly trends ",hourlyRes.data);
      setOperatorStats(operatorsRes.data);
      setOeeTrends(oeeTrendsRes.data);
      setSpeedTrends(speedTrendsRes.data);
      setShiftComparison(shiftRes.data);
      console.log("Shift comparison ",shiftRes.data);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const transformData = (item) => ({
    date: item.Date_Insert_Day,
    good: parseInt(item.Total_Good_Qty_Day, 10) || 0,
    reject: parseInt(item.Total_Rejects_Qty_Day, 10) || 0,
    oee: parseFloat(item.OEE_Day) || 0,
    speed: parseFloat(item.Speed_Day) || 0
  });

  const handleDateChange = useCallback(debounce(async (date) => {
    if (!date) return;
    
    try {
      const formattedDate = date.format('YYYY-MM-DD');
      const response = await superagent.get(baseURL + `/api/arrets-production/${formattedDate}`).withCredentials().timeout(30000).retry(2);
      const data = response.body;
      setSelectedDateData(data);
    } catch (error) {
      console.error('Error fetching date data:', error);
    }
  }, 300), []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const stats = [
    { 
      title: 'Production Totale', 
      value: goodQty, 
      suffix: 'Pièces', 
      icon: <RiseOutlined />, 
      color: '#52c41a'
    },
    { 
      title: 'Rejets Totaux', 
      value: rejetQty, 
      suffix: 'Kg', 
      icon: <FallOutlined />, 
      color: '#f5222d'
    },
    { 
      title: 'TRS Moyen', 
      value: chartData.reduce((sum, d) => sum + d.trs, 0) / chartData.length || 0, 
      suffix: '%', 
      icon: <DashboardOutlined />, 
      color: '#1890ff'
    },
    { 
      title: 'Machines en Service', 
      value: machinePerformance.length, 
      suffix: 'Machines', 
      icon: <BarChartOutlined />, 
      color: '#13c2c2'
    }
  ];
  
// For machinePerformance data
const machinePerformanceColumns = [
  { title: 'Machine', dataIndex: 'Machine_Name', key: 'machine', fixed: 'left' },
  { title: 'Équipe', dataIndex: 'Shift', key: 'shift' },
  { 
    title: 'Production', 
    dataIndex: 'production', 
    key: 'production',
    render: (text) => <Tag color="green">{formatFrenchInteger(text)}</Tag>,
    sorter: (a, b) => a.production - b.production
  },
  { 
    title: 'Rejets', 
    dataIndex: 'rejects', 
    key: 'rejects',
    render: (text) => <Tag color="red">{text.toLocaleString()}</Tag>,
    sorter: (a, b) => a.rejects - b.rejects
  },
  { 
    title: 'Disponibilité', 
    dataIndex: 'availability', 
    key: 'availability',
    render: (text) => `${text}%`,
    sorter: (a, b) => a.availability - b.availability
  },
  { 
    title: 'Performance', 
    dataIndex: 'performance', 
    key: 'performance',
    render: (text) => `${text}%`,
    sorter: (a, b) => a.performance - b.performance
  },
  { 
    title: 'Taux de Rendement Synthétique', 
    dataIndex: 'oee', 
    key: 'oee',
    render: (text) => `${text}%`,
    sorter: (a, b) => a.oee - b.oee
  }
];
// For hourlyTrends data
const hourlyTrendsColumns = [
  { title: 'Heure', dataIndex: 'hour', key: 'hour' },
  { title: 'Temps de Cycle Moyen', dataIndex: 'average_speed', key: 'speed' }
];

// For operatorStats data
const operatorStatsColumns = [
  { title: 'Opérateur', dataIndex: 'operator', key: 'operator', fixed: 'left' },
  { title: 'Équipe', dataIndex: 'Shift', key: 'shift' },
  { 
    title: 'Production', 
    dataIndex: 'production', 
    key: 'production',
    render: (text) => <Tag color="blue">{text.toLocaleString()}</Tag>,
    sorter: (a, b) => a.production - b.production
  },
  { 
    title: 'Rejets', 
    dataIndex: 'rejects', 
    key: 'rejects',
    render: (text) => <Tag color="blue">{text ? text.toLocaleString() : 0}</Tag>,
    sorter: (a, b) => a.rejects - b.rejects
  },
  { 
    title: 'Efficacité', 
    dataIndex: 'efficiency', 
    render: (val) => <Progress percent={val} status={val > 80 ? 'success' : 'normal'} />,
    sorter: (a, b) => a.efficiency - b.efficiency
  },
  { 
    title: 'Taux de Rendement Synthétique', 
    dataIndex: 'oee', 
    render: (val) => <Progress percent={val} status={val > 85 ? 'success' : 'normal'} />,
    sorter: (a, b) => a.oee - b.oee
  }
];
 // Enhanced empty state handling
const renderChart = (data, Component) => data?.length > 0 ? (
  <Component data={data} />
) : (
  <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
    <Empty description="Aucune donnée disponible" />
  </div>
);


  return (
    <div style={{ padding: screens.md ? 24 : 16 }}>
      <Spin spinning={loading} tip="Chargement des données..." size="large">
        <Row gutter={[24, 24]}>
          {/* Statistics Cards */}
          {stats.map((stat, index) => (
            <Col key={stat.title} xs={24} sm={12} md={6}>
              <Card bordered={false} hoverable>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  precision={stat.title === 'TRS Moyen' ? 1 : 0}
                  suffix={stat.suffix}
                  prefix={React.cloneElement(stat.icon, { 
                    style: { color: stat.color, fontSize: 24 }
                  })}
                  valueStyle={{ 
                    fontSize: 20,
                    color: stat.color
                  }}
                />
                {stat.title === 'TRS Moyen' && (
                  <Progress 
                    percent={stat.value} 
                    strokeColor={stat.color}
                    showInfo={false}
                    style={{ marginTop: 12 }}
                  />
                )}
              </Card>
            </Col>
          ))}

            {/* Enhanced Date Picker */}
            <Col span={24}>
            <Card bordered={false} bodyStyle={{ padding: 0 }}>
              <DatePicker
                value={dateFilter}
                onChange={handleDateChange}
                format="DD/MM/YYYY"
                style={{ width: '100%' }}
                allowClear={false}
                disabledDate={(current) => current > dayjs().endOf('day')}
              />
            </Card>
          </Col>

              {/* Main Performance Chart */}
              <Col span={24}>
            <Card 
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <LineChartOutlined style={{ fontSize: 20, color: COLORS[0] }} />
                  <Text strong style={{ fontSize: 16 }}>Performance Hebdomadaire</Text>
                </div>
              }
              bordered={false}
            >
              {renderChart(chartData, MemoizedBarChart)}
            </Card>
          </Col>
  {/* Machine Performance Chart */}
  <Col xs={24} md={12}>
  <Card title="Performance des Équipements">
    <MemoizedMachinePerformance data={machinePerformance} />
  </Card>
</Col>

  {/* Hourly Trends Chart */}
  <Col xs={24} md={12}>
    <Card title="Évolution Horaire">
    <MemoizedLineChart 
  data={mergedData} 
/>    </Card>
  </Col>


          {/* Additional Charts */}
          <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <PieChartOutlined style={{ fontSize: 20, color: COLORS[3] }} />
                  <Text strong style={{ fontSize: 16 }}>Répartition Production</Text>
                </div>
              }
              bordered={false}
            >
              {renderChart([
                { name: 'Bonnes Pièces', value: goodQty },
                { name: 'Rejets', value: rejetQty }
              ], MemoizedPieChart)}
            </Card>
          </Col>

            <Col xs={24} md={12}>
              <Card
                title={<><LineChartOutlined /> Évolution du TRS</>}
              >
                <MemoizedOeeTrend data={oeeTrends} />
              </Card>
            </Col>

            <Col xs={24} md={12}>
              <Card
                title={<><LineChartOutlined /> Évolution du Temps de Cycle</>}
              >
                <MemoizedSpeedTrend data={speedTrends} />
              </Card>
            </Col>

            <Col xs={24} md={12}>
  <Card title="Comparaison des Équipes">
    {shiftComparison.some(item => item.production > 0) ? (
      <MemoizedShiftComparison data={shiftComparison} />
    ) : (
      <div>Aucune donnée disponible</div>
    )}
  </Card>
</Col>

            <Col xs={24} md={12}>
              <Card
                title={<><PieChartOutlined /> Disponibilité des Équipements</>}
              >
                <MemoizedPieChart data={machinePerformance.map(item => ({
                  name: item.machine,
                  value: item.availability
                }))} />
              </Card>
            </Col>
            <Col xs={24} md={12}>
  <Card
    title={<><AreaChartOutlined /> Évolution Horaire</>}
  >
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={hourlyTrends}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="hour" 
          tickFormatter={(tick) => {
            const date = new Date(tick);
            return `${dayjs(date).format('DD/MM')} ${date.getHours()}:00`;
          }}
        />
        <YAxis 
          tickFormatter={(value) => `${value} u/h`}
        />
        <Tooltip 
          formatter={(value, name) => [
            `${name}: ${value.toFixed(1)} unités/heure`
          ]}
        />
        <Area 
          type="monotone" 
          dataKey="average_speed" 
          name="Temps de Cycle Moyen"
          stroke={COLORS[2]} 
          fill={COLORS[2]} 
          fillOpacity={0.3}
        />
      </AreaChart>
    </ResponsiveContainer>
  </Card>
</Col>
          </Row>
 {/* Enhanced Machine Performance */}
 <Col span={24}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <BarChartOutlined style={{ fontSize: 20, color: COLORS[1] }} />
                  <Text strong style={{ fontSize: 16 }}>Performance par Équipement</Text>
                </div>
              }
              bordered={false}
            >
              <Table
                dataSource={machinePerformance}
                columns={machinePerformanceColumns}
                pagination={{ pageSize: 5 }}
                scroll={{ x: 1300 }}
                rowKey="Machine_Name"
              />
            </Card>
          </Col>
          
  {/* Enhanced Operator Stats */}
  <Col span={24}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <UserOutlined style={{ fontSize: 20, color: COLORS[2] }} />
                  <Text strong style={{ fontSize: 16 }}>Statistiques Opérateurs</Text>
                </div>
              }
              bordered={false}
            >
              <Table
                dataSource={operatorStats}
                columns={operatorStatsColumns}
                pagination={{ pageSize: 5 }}
                scroll={{ x: 1200 }}
                rowKey="operator"
              />
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default Production;