// Enhanced machine data handler for debugging and fixing the machine names issue
import { useState, useEffect } from 'react';
import useStopTableGraphQL from '../hooks/useStopTableGraphQL';

export function useMachineDataFixer() {
  const [machineModels, setMachineModels] = useState([]);
  const [allMachineNames, setAllMachineNames] = useState([]);
  const [filteredMachineNames, setFilteredMachineNames] = useState([]);
  const [selectedModel, setSelectedModel] = useState('');
  const [selectedMachine, setSelectedMachine] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const graphQL = useStopTableGraphQL();
  
  // Load machine models on init
  useEffect(() => {
    async function loadMachineModels() {
      setLoading(true);
      try {
        const models = await graphQL.getMachineModels();
        setMachineModels(Array.isArray(models) ? models : []);
        
        // Load all machine names as well
        const allMachines = await graphQL.getMachineNames();
        setAllMachineNames(Array.isArray(allMachines) ? allMachines : []);
        setFilteredMachineNames(Array.isArray(allMachines) ? allMachines : []);
        
        setError(null);
      } catch (err) {
        console.error('Failed to load machine models:', err);
        setError(err.message);
        // Fallback data
        setMachineModels(['IPS', 'AKROS', 'ML', 'FCS']);
      } finally {
        setLoading(false);
      }
    }
    
    loadMachineModels();
  }, [graphQL]);
  
  // Handle model selection change
  const handleModelChange = async (model) => {
    setSelectedModel(model);
    setSelectedMachine('');
    
    if (!model) {
      // Reset to all machines
      setFilteredMachineNames(allMachineNames);
      return;
    }
    
    setLoading(true);
    try {
      // Fetch machines specifically for this model
      const filteredMachines = await graphQL.getMachineNames({ model });
      setFilteredMachineNames(Array.isArray(filteredMachines) ? filteredMachines : []);
      setError(null);
    } catch (err) {
      console.error(`Failed to get machines for model ${model}:`, err);
      setError(err.message);
      // Reset to avoid showing wrong data
      setFilteredMachineNames([]);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle machine selection change
  const handleMachineChange = (machine) => {
    setSelectedMachine(machine);
  };
  
  // Reset all selections
  const resetSelections = () => {
    setSelectedModel('');
    setSelectedMachine('');
    setFilteredMachineNames(allMachineNames);
  };
  
  return {
    // Data
    machineModels,
    allMachineNames,
    filteredMachineNames,
    selectedModel,
    selectedMachine,
    loading,
    error,
    
    // Actions
    handleModelChange,
    handleMachineChange,
    resetSelections
  };
}
