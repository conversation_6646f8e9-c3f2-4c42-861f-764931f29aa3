/**
 * Real backend test - check what the actual GraphQL query returns
 * This will help us verify if the issue is in backend data or frontend processing
 */

const testBackendQuery = async () => {
  try {
    console.log('🚀 Testing real backend GraphQL query...');
    
    // Test the exact query that queuedDataManager makes for table data
    const query = `
      query GetAllMachineStops($filters: StopFilterInput) {
        getAllMachineStops(filters: $filters) {
          Code_Stop
          Date_Insert
          Machine_Name
          duration_minutes
          Debut_Stop
          Fin_Stop_Time
        }
      }
    `;
    
    // Test with IPS01 + April 2025 filter (matching screenshot)
    const variables = {
      filters: {
        machine: "IPS01",
        startDate: "2025-04-01",
        endDate: "2025-04-30"
      }
    };
    
    console.log('📤 Sending GraphQL query:', {
      query: query.trim(),
      variables
    });
    
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query,
        variables
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    console.log('📥 Backend response:', result);
    
    if (result.errors) {
      console.error('❌ GraphQL errors:', result.errors);
      return;
    }
    
    const stops = result.data?.getAllMachineStops || [];
    console.log('📊 Received stops data:', {
      totalStops: stops.length,
      sampleStops: stops.slice(0, 3),
      dateFormats: stops.slice(0, 5).map(s => ({
        id: s.Code_Stop,
        dateInsert: s.Date_Insert,
        machine: s.Machine_Name,
        duration: s.duration_minutes
      }))
    });
    
    if (stops.length > 0) {
      console.log('✅ Backend returning data correctly');
      
      // Test the date parsing on actual backend data
      console.log('🧪 Testing date parsing on real backend data:');
      stops.slice(0, 3).forEach(stop => {
        const dateStr = stop.Date_Insert;
        console.log(`Testing date: "${dateStr}"`);
        
        if (dateStr && dateStr.includes('/')) {
          const parts = dateStr.split(' ');
          const datePart = parts[0];
          const [day, month, year] = datePart.split('/');
          const isoDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
          console.log(`  Parsed: ${dateStr} -> ${isoDate}`);
        }
      });
      
    } else {
      console.log('❌ Backend returning no data for IPS01 + April 2025');
      
      // Test with no filters to see if backend has any data at all
      console.log('🔍 Testing with no filters to check if backend has any data...');
      
      const noFilterResponse = await fetch('http://localhost:5000/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query,
          variables: { filters: {} }
        })
      });
      
      const noFilterResult = await noFilterResponse.json();
      const allStops = noFilterResult.data?.getAllMachineStops || [];
      
      console.log('📊 Total stops in database (no filter):', allStops.length);
      if (allStops.length > 0) {
        console.log('Sample data:', allStops.slice(0, 3));
        console.log('Available machines:', [...new Set(allStops.map(s => s.Machine_Name))]);
        console.log('Date range:', {
          earliest: allStops.reduce((min, s) => s.Date_Insert < min ? s.Date_Insert : min, allStops[0]?.Date_Insert),
          latest: allStops.reduce((max, s) => s.Date_Insert > max ? s.Date_Insert : max, allStops[0]?.Date_Insert)
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Backend test error:', error);
  }
};

// Run the test
testBackendQuery();
