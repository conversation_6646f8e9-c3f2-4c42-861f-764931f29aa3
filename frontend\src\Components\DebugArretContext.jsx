import React, { useEffect, useState } from 'react';
import { ArretProvider, useArretContext } from '../context/arret/ArretContext';
import useStopTableGraphQL from '../hooks/useStopTableGraphQL';

/**
 * Debug component to test the data flow step by step
 */
const DebugArretContextContent = () => {
  const context = useArretContext();
  const directGraphQL = useStopTableGraphQL();
  const [directTest, setDirectTest] = useState(null);
  const [contextData, setContextData] = useState({});

  useEffect(() => {
    // Track context changes
    if (context) {
      setContextData({
        loading: context.loading,
        arretStats: context.arretStats,
        stopsData: context.stopsData,
        machineModels: context.machineModels,
        error: context.error
      });
    }
  }, [context]);

  const testDirectGraphQL = async () => {
    console.log('🧪 Testing direct GraphQL hook...');
    try {
      const result = await directGraphQL.getComprehensiveStopData({});
      console.log('🧪 Direct GraphQL result:', result);
      setDirectTest(result);
    } catch (error) {
      console.error('🧪 Direct GraphQL error:', error);
      setDirectTest({ error: error.message });
    }
  };

  const forceFetchData = () => {
    console.log('🧪 Forcing context data fetch...');
    if (context?.dataManager?.fetchData) {
      context.dataManager.fetchData(true);
    }
  };

  const clearCacheAndFetch = async () => {
    console.log('🧪 Clearing cache and fetching fresh data...');
    try {
      // Clear the GraphQL cache first
      await directGraphQL.invalidateCache();
      
      // Then force a fresh fetch
      if (context?.dataManager?.fetchData) {
        await context.dataManager.fetchData(true);
      }
    } catch (error) {
      console.error('🧪 Clear cache error:', error);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>🧪 Arret Context Debug Panel</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <button onClick={testDirectGraphQL} style={{ marginRight: '10px' }}>
          Test Direct GraphQL
        </button>
        <button onClick={forceFetchData} style={{ marginRight: '10px' }}>
          Force Context Fetch
        </button>
        <button onClick={clearCacheAndFetch}>
          Clear Cache & Fetch
        </button>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
        
        {/* Direct GraphQL Test */}
        <div style={{ border: '1px solid #ccc', padding: '10px' }}>
          <h3>Direct GraphQL Hook Test</h3>
          <pre style={{ fontSize: '12px', maxHeight: '300px', overflow: 'auto' }}>
            {directTest ? JSON.stringify(directTest, null, 2) : 'Not tested yet'}
          </pre>
        </div>

        {/* Context Data */}
        <div style={{ border: '1px solid #ccc', padding: '10px' }}>
          <h3>Context Data</h3>
          <div style={{ fontSize: '14px' }}>
            <div><strong>Loading:</strong> {String(contextData.loading)}</div>
            <div><strong>Error:</strong> {contextData.error || 'None'}</div>
            <div><strong>Arret Stats:</strong> {contextData.arretStats?.length || 0} items</div>
            <div><strong>Stops Data:</strong> {contextData.stopsData?.length || 0} stops</div>
            <div><strong>Machine Models:</strong> {contextData.machineModels?.length || 0} models</div>
          </div>
          
          {contextData.arretStats?.length > 0 && (
            <div style={{ marginTop: '10px' }}>
              <strong>Arret Stats Detail:</strong>
              <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
                {JSON.stringify(contextData.arretStats, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>

      {/* Raw Context Object */}
      <div style={{ marginTop: '20px', border: '1px solid #ccc', padding: '10px' }}>
        <h3>Raw Context Object Keys</h3>
        <div style={{ fontSize: '12px' }}>
          {context ? Object.keys(context).join(', ') : 'Context not available'}
        </div>
      </div>
    </div>
  );
};

const DebugArretContext = () => {
  return (
    <ArretProvider>
      <DebugArretContextContent />
    </ArretProvider>
  );
};

export default DebugArretContext;
