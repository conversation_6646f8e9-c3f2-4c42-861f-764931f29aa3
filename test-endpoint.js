// Quick test to verify GraphQL endpoint with proper JSON
import fetch from 'node-fetch';

const testGraphQLEndpoint = async () => {
  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getStopDashboardData {
              allStops {
                Machine_Name
                Code_Stop
                Debut_Stop
                duration_minutes
              }
              sidecards {
                Arret_Totale
                Arret_Totale_nondeclare
              }
              stopComparison {
                Machine_Name
                stops
                totalDuration
              }
            }
          }
        `
      })
    });

    const data = await response.json();
    
    if (data.data && data.data.getStopDashboardData) {
      console.log('✅ GraphQL endpoint working');
      console.log('📊 Total stops:', data.data.getStopDashboardData.allStops.length);
      console.log('🏭 Sample data:', data.data.getStopDashboardData.allStops.slice(0, 3));
      console.log('📈 Sidecards:', data.data.getStopDashboardData.sidecards);
      console.log('🔧 Stop comparison:', data.data.getStopDashboardData.stopComparison);
      
      // Check for "Unknown" machine names
      const unknownMachines = data.data.getStopDashboardData.allStops.filter(stop => 
        !stop.Machine_Name || stop.Machine_Name.toLowerCase().includes('unknown')
      );
      console.log('🔍 Unknown machines:', unknownMachines.length);
      
      return true;
    } else {
      console.error('❌ GraphQL error:', data.errors);
      return false;
    }
  } catch (error) {
    console.error('❌ Connection error:', error.message);
    return false;
  }
};

testGraphQLEndpoint();
