// Debug script to check what data the ArretLineChart is receiving
import fetch from 'node-fetch';

const testChartData = async () => {
  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getStopDashboardData(filters: { model: "IPS" }) {
              allStops {
                Machine_Name
                Date_Insert
                Debut_Stop
                duration_minutes
              }
              sidecards {
                Arret_Totale
                Arret_Totale_nondeclare
              }
              stopComparison {
                Machine_Name
                stops
                totalDuration
              }
            }
          }
        `
      })
    });

    const data = await response.json();
    
    if (data.data && data.data.getStopDashboardData) {
      console.log('📊 Available Data Fields:');
      console.log('- allStops count:', data.data.getStopDashboardData.allStops?.length || 0);
      console.log('- sidecards:', data.data.getStopDashboardData.sidecards);
      console.log('- stopComparison count:', data.data.getStopDashboardData.stopComparison?.length || 0);
      
      // Sample some data to understand the structure
      if (data.data.getStopDashboardData.allStops?.length > 0) {
        console.log('📅 Sample allStops data:');
        data.data.getStopDashboardData.allStops.slice(0, 5).forEach(stop => {
          console.log(`  - ${stop.Date_Insert} | ${stop.Machine_Name} | Duration: ${stop.duration_minutes}min`);
        });
        
        // Try to create evolution data from allStops
        const stopsByDate = {};
        data.data.getStopDashboardData.allStops.forEach(stop => {
          if (stop.Date_Insert) {
            // Parse DD/MM/YYYY format and extract date only
            const dateMatch = stop.Date_Insert.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})/);
            if (dateMatch) {
              const [, day, month, year] = dateMatch;
              const dateKey = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
              
              if (!stopsByDate[dateKey]) {
                stopsByDate[dateKey] = 0;
              }
              stopsByDate[dateKey]++;
            }
          }
        });
        
        console.log('📈 Evolution data created from allStops:');
        const evolutionData = Object.entries(stopsByDate)
          .map(([date, stops]) => ({
            date,
            displayDate: new Date(date).toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' }),
            stops
          }))
          .sort((a, b) => new Date(a.date) - new Date(b.date));
          
        console.log('- Count:', evolutionData.length);
        console.log('- Sample:', evolutionData.slice(0, 5));
      }
      
    } else {
      console.error('❌ GraphQL error:', data.errors);
    }
  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
};

testChartData();
