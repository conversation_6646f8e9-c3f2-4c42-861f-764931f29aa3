import{r as e,e as a,j as t,b as r}from"./index-Nnj1g72A.js";import{r as n,R as i}from"./react-vendor-tYPmozCJ.js";import{t as s,n as l,S as o}from"./dataUtils-CeV1Whgh.js";import{d,ao as c,aq as u,a as h,ai as p,aB as m,w as x,a8 as y,aC as f,a0 as g,a1 as j,S as _,q as D,T as b,f as M,c as R,af as S,D as P,e as v,aD as Y,x as T,A as N,am as C,Z as w,a7 as I,a6 as A,m as F,a5 as E,aE as k,ab as Q,a3 as $,aF as z,a9 as q,aG as L,ak as B,at as O,B as U,y as H,Y as G,aH as K,u as V,a4 as W,an as J}from"./antd-vendor-4OvKHZ_k.js";import{i as Z}from"./isoWeek-B92Rp6lO.js";import{u as X}from"./useDailyTableGraphQL-kyfCYKRH.js";import{F as ee,S as ae}from"./SearchResultsDisplay-_nzCIRa1.js";import{G as te}from"./GlobalSearchModal-CzoVtWJy.js";import{c as re,d as ne,a as ie,f as se}from"./numberFormatter-5BSX8Tmh.js";import{E as le,a as oe,b as de,c as ce,d as ue,e as he,f as pe,g as me,h as xe,i as ye,j as fe}from"./EnhancedChartComponents-C30-oOvy.js";import{y as ge}from"./chart-vendor-CazprKWL.js";d.extend(Z),d.extend(c),d.extend(u),d.locale("fr");const je=()=>{const[e,a]=n.useState(null),[t,r]=n.useState("day"),[i,s]=n.useState(""),[l,o]=n.useState(!1),c=n.useCallback(((e,a)=>{if(!e)return{short:"",full:""};try{const t=d(e);if(!t.isValid())return{short:"Date invalide",full:"Date invalide"};if("day"===a)return{short:t.format("DD/MM/YYYY"),full:`le ${t.format("DD MMMM YYYY")}`};if("week"===a){const e=t.startOf("isoWeek"),a=t.endOf("isoWeek"),r=t.isoWeek();return{short:`S${r} ${t.format("YYYY")}`,full:`Semaine ${r} (du ${e.format("DD MMMM")} au ${a.format("DD MMMM YYYY")})`}}if("month"===a){const e=t.format("MMMM"),a=e.charAt(0).toUpperCase()+e.slice(1);return{short:`${a} ${t.format("YYYY")}`,full:`${a} ${t.format("YYYY")}`}}return{short:"",full:""}}catch(t){return{short:"Erreur de date",full:"Erreur de date"}}}),[]),u=()=>{a(null),s(""),o(!1)},h=n.useCallback((()=>{const a=new URLSearchParams;if(e)try{const n=(r=e)?new Date(r).toISOString().split("T")[0]:null;n&&(a.append("date",n),a.append("dateRangeType",t))}catch(n){}var r;return a}),[e,t]);return{dateFilter:e,dateRangeType:t,dateRangeDescription:i,dateFilterActive:l,handleDateChange:e=>{if(e)try{const r=d(e).toDate();a(r);const{full:n}=c(r,t);s(n),o(!0)}catch(r){a(e);const{full:n}=c(e,t);s(n),o(!0)}else u()},handleDateRangeTypeChange:t=>{if(r(t),e){const r=d(e);let n=r;"week"===t?n=r.startOf("isoWeek"):"month"===t&&(n=r.startOf("month"));const i=n.toDate();a(i);const{full:l}=c(i,t);s(l)}},resetDateFilter:u,buildDateQueryParams:h,formatDateRange:c}},_e=n.createContext(),De=({children:r})=>{const i=(()=>{const[t,r]=n.useState([]),[i,s]=n.useState([]),[l,o]=n.useState([]),[d,c]=n.useState("IPS"),[u,h]=n.useState(""),[p,m]=n.useState(!1),x=n.useCallback((async()=>{try{m(!0);const t=await e.get("/api/machine-models").retry(2);if(t.body){const e=a(t),n=Array.isArray(e)?e.map((e=>e.model||e)):[];r(n.length>0?n:["IPS","CCM24"])}else r(["IPS","CCM24"])}catch(t){r(["IPS","CCM24"])}finally{m(!1)}}),[]),y=n.useCallback((async()=>{try{m(!0);const t=await e.get("/api/machine-names").retry(2);if(t.body){const e=a(t);Array.isArray(e)&&e.length>0?(s(e),e.find((e=>"IPS01"===e.Machine_Name))):s([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}else s([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(t){s([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}finally{m(!1)}}),[d]);return n.useEffect((()=>{if(d){const e=i.filter((e=>e.Machine_Name&&e.Machine_Name.startsWith(d)));o(e),u&&!e.some((e=>e.Machine_Name===u))&&h("")}else o([]),h("")}),[d,i,u]),n.useEffect((()=>{x()}),[x]),n.useEffect((()=>{y()}),[y]),{machineModels:t,machineNames:i,filteredMachineNames:l,selectedMachineModel:d,selectedMachine:u,loading:p,handleMachineModelChange:e=>{c(e)},handleMachineChange:e=>{h(e)},fetchMachineModels:x,fetchMachineNames:y,setSelectedMachineModel:c,setSelectedMachine:h}})(),o=je(),c=(({selectedMachineModel:t,selectedMachine:r,dateFilter:i,dateRangeType:o,buildDateQueryParams:c})=>{const u=(a,t)=>e[a](`https://charming-hermit-intense.ngrok-free.app${t}`).retry(2).set("withCredentials",!0),[p,m]=n.useState(!1),[x,y]=n.useState([]),[f,g]=n.useState([]),[j,_]=n.useState([]),[D,b]=n.useState([]),[M,R]=n.useState(0),[S,P]=n.useState(0),[v,Y]=n.useState([]),[T,N]=n.useState([]),[C,w]=n.useState([]),[I,A]=n.useState([]),F=n.useCallback((()=>{const e=new URLSearchParams;t&&!r?e.append("model",t):r&&e.append("machine",r),e.append("limit","100"),e.append("chartLimit","200"),e.append("page","1");const a=c();if(Object.entries(a).forEach((([a,t])=>{e.append(a,t)})),!a.date&&!a.dateRangeType){const a=d().subtract(7,"days").format("YYYY-MM-DD");e.append("date",a),e.append("dateRangeType","week"),e.append("defaultFilter","true")}const n=a.dateRangeType;return"month"===n?e.append("aggregateBy","day"):"year"===n&&e.append("aggregateBy","week"),e.toString()?`?${e.toString()}`:""}),[t,r,c]),E=n.useCallback((()=>{const e=d(),a=[];for(let n=9;n>=0;n--){const i=e.subtract(n,"day").format("YYYY-MM-DD");d(i).isValid()&&a.push({date:i,good:Math.floor(1e3*Math.random())+500,reject:Math.floor(100*Math.random())+10,oee:Math.floor(30*Math.random())+70,speed:Math.floor(5*Math.random())+5,Machine_Name:r||(t?`${t}01`:"IPS01"),Shift:["Matin","Après-midi","Nuit"][Math.floor(3*Math.random())]})}return a}),[r,t]),k=n.useCallback((async()=>{var e,n;if(t){m(!0);try{const c=F(),p=await Promise.allSettled([u("get",`/api/testing-chart-production${c}`),u("get","/api/unique-dates-production").catch((()=>({body:[]}))),u("get",`/api/sidecards-prod${c}`),u("get",`/api/sidecards-prod-rejet${c}`),u("get",`/api/machine-performance${c}`),u("get",`/api/hourly-trends${c}`),u("get",`/api/machine-oee-trends${c}`),u("get",`/api/speed-trends${c}`),u("get",`/api/shift-comparison${c}`),u("get",`/api/machine-daily-mould${c}`)]),[m,x,f,j,D,M,S,v,T,C]=p;if("fulfilled"===m.status&&m.value.body){const e=a(m.value),t=(Array.isArray(e)?e:[]).map(s);y(t)}else y([]);if("fulfilled"===x.status){const e=a(x.value);b(e||[])}if("fulfilled"===f.status){const t=a(f.value);R((null==(e=t[0])?void 0:e.goodqty)||0)}else R(0);if("fulfilled"===j.status){const e=a(j.value);P((null==(n=e[0])?void 0:n.rejetqty)||0)}else P(0);if("fulfilled"===D.status&&D.value.body){const e=a(D.value);g(e||[])}else g([]);if("fulfilled"===M.status){const e=a(M.value);w(e||[])}const I="fulfilled"===S.status&&S.value.body?a(S.value).reduce(((e,a)=>(e[a.date]=parseFloat(a.oee)||0,e)),{}):{},k="fulfilled"===v.status&&v.value.body?a(v.value).reduce(((e,a)=>{const t=parseFloat(a.speed);return!isNaN(t)&&t>0&&(e[a.date]=t),e}),{}):{},Q=[...new Set([...Object.keys(I),...Object.keys(k)])].sort(((e,a)=>d(e).diff(d(a))));let $=Q;if(Q.length>0){const e=d(Q[Q.length-1]);$=Q.filter((a=>e.diff(d(a),"day")<=60))}const z=$.map((e=>({date:e,oee:I[e]||0,speed:k[e]||null}))).sort(((e,a)=>d(e.date).diff(d(a.date))));if(C&&"fulfilled"===C.status&&C.value.body){const e=a(C.value);if(e.length>0)try{const a=e.map((e=>{const a=parseFloat(e.Good_QTY_Day||e.good||0),t=parseFloat(e.Rejects_QTY_Day||e.reject||0),r=parseFloat(e.OEE_Day||e.oee||0),n=parseFloat(e.Speed_Day||e.speed||0),i=parseFloat(e.Availability_Rate_Day||e.availability||0),s=parseFloat(e.Performance_Rate_Day||e.performance||0),o=parseFloat(e.Quality_Rate_Day||e.quality||0);let c=null;try{const a=e.Date_Insert_Day||e.date;if(a)if(d(a).isValid())c=d(a).format("YYYY-MM-DD");else{const e=["DD/MM/YYYY","MM/DD/YYYY","YYYY-MM-DD","YYYY/MM/DD","DD-MM-YYYY"];for(const t of e){const e=d(a,t);if(e.isValid()){c=e.format("YYYY-MM-DD");break}}}c||(c=d().format("YYYY-MM-DD"))}catch(x){c=d().format("YYYY-MM-DD")}const u=l(r),h=l(i),p=l(s),m=l(o);return{date:c,oee:u,speed:isNaN(n)?0:n,good:isNaN(a)?0:a,reject:isNaN(t)?0:t,Machine_Name:e.Machine_Name||"N/A",Shift:e.Shift||"N/A",availability:h,performance:p,quality:m}})).sort(((e,a)=>d(e.date).diff(d(a.date))));if(a.some((e=>e.good>0||e.reject>0||e.oee>0||e.speed>0)))_(a);else{const e=E();_(e),(t&&"IPS"!==t||r||i)&&h.info({message:"Données de démonstration",description:"Aucune donnée valide n'a été trouvée. Des données de démonstration sont affichées.",duration:5})}}catch(o){_(z)}else{if(z.length>0)_(z);else{const e=E();_(e),(t&&"IPS"!==t||r||i)&&h.info({message:"Données de démonstration",description:"Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",duration:5})}(t&&"IPS"!==t||r||i)&&h.info({message:"Aucune donnée disponible",description:"Aucune donnée n'a été trouvée pour les filtres sélectionnés. Essayez de modifier vos critères de recherche.",duration:5})}}else if(C&&C.status,z.length>0)_(z);else{const e=E();_(e),(t&&"IPS"!==t||r||i)&&h.info({message:"Données de démonstration",description:"Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",duration:5})}"fulfilled"===S.status&&Y(a(S.value)||[]),"fulfilled"===v.status&&N(a(v.value)||[]),"fulfilled"===T.status&&A(a(T.value)||[])}catch(o){R(0),P(0),y([]),g([])}finally{m(!1)}}}),[t,r,i,o,F,E]),Q=n.useCallback((async()=>{var e,t;try{m(!0);const r=await Promise.allSettled([u("get","/api/sidecards-prod"),u("get","/api/sidecards-prod-rejet")]),[n,i]=r;if("fulfilled"===n.status){const t=a(n.value);R((null==(e=t[0])?void 0:e.goodqty)||15e3)}else R(15e3);if("fulfilled"===i.status){const e=a(i.value);P((null==(t=e[0])?void 0:t.rejetqty)||750)}else P(750)}catch(r){R(15e3),P(750)}finally{m(!1)}}),[]),$=n.useCallback((()=>{let e=0;x.length>0&&(e=x.reduce(((e,a)=>{let t=parseFloat(a.oee||0);return t=l(t),e+t}),0)/x.length);const a=M+S>0?S/(M+S)*100:0,t=M+S>0?M/(M+S)*100:0;let r=0;x.length>0&&(r=x.reduce(((e,a)=>{let t=parseFloat(a.availability||0);return t=l(t),e+t}),0)/x.length);let n=0;x.length>0&&(n=x.reduce(((e,a)=>{let t=parseFloat(a.performance||0);return t=l(t),e+t}),0)/x.length);let i=0;return x.length>0&&(i=x.reduce(((e,a)=>{let t=parseFloat(a.quality||0);return t=l(t),e+t}),0)/x.length),{avgTRS:e,rejectRate:a,qualityRate:t,avgAvailability:r,avgPerformance:n,avgQuality:i}}),[x,M,S]);return n.useEffect((()=>{t?k():Q()}),[t,r,i,o,k,Q]),{loading:p,chartData:x,machinePerformance:f,mergedData:j,uniqueDates:D,goodQty:M,rejetQty:S,oeeTrends:v,speedTrends:T,hourlyTrends:C,shiftComparison:I,fetchData:k,fetchGeneralData:Q,calculateStatistics:$}})({selectedMachineModel:i.selectedMachineModel,selectedMachine:i.selectedMachine,dateFilter:o.dateFilter,dateRangeType:o.dateRangeType,buildDateQueryParams:o.buildDateQueryParams}),u=c.calculateStatistics(),p={...i,...o,...c,...u,resetFilters:()=>{o.resetDateFilter(),o.setDateRangeType("day"),i.setSelectedMachineModel(""),i.setSelectedMachine("")},handleRefresh:()=>{c.fetchData()}};return t.jsx(_e.Provider,{value:p,children:r})},{Text:be}=b,{Text:Me}=b,{Text:Re}=b,Se=({data:e,colors:a,dateRangeType:r,dateFilter:n,formatDateRange:i})=>t.jsxs(t.Fragment,{children:[t.jsx(I,{span:24,children:t.jsxs(A,{gutter:[24,24],children:[t.jsx(I,{xs:24,md:12,children:t.jsx(le,{title:"Quantité Bonne - "+("day"===r?"Journalière":"week"===r?"Hebdomadaire":"Mensuelle"),data:e,chartType:"bar",expandMode:"modal",onExpand:()=>{},onCollapse:()=>{},exportEnabled:!0,zoomEnabled:!0,extra:t.jsx(M,{color:n?"blue":"green",children:i(n,r)}),children:t.jsx(oe,{data:e,title:"Quantité Bonne",dataKey:"good",color:a[2],tooltipLabel:"Quantité bonne"})})}),t.jsx(I,{xs:24,md:12,children:t.jsx(le,{title:"Quantité Rejetée - "+("day"===r?"Journalière":"week"===r?"Hebdomadaire":"Mensuelle"),data:e,chartType:"bar",expandMode:"modal",onExpand:()=>{},onCollapse:()=>{},exportEnabled:!0,zoomEnabled:!0,extra:t.jsx(M,{color:n?"blue":"green",children:i(n,r)}),children:t.jsx(oe,{data:e,title:"Quantité Rejetée",dataKey:"reject",color:a[4],label:"Quantité",tooltipLabel:"Quantité rejetée",isKg:!0})})})]})}),t.jsx(I,{xs:24,md:24,children:t.jsxs(A,{gutter:[24,24],children:[t.jsx(I,{xs:24,md:12,children:t.jsx(le,{title:"Tendances TRS (Taux de Rendement Synthétique)",data:e,chartType:"line",expandMode:"modal",onExpand:()=>{},onCollapse:()=>{},exportEnabled:!0,zoomEnabled:!0,extra:t.jsx(M,{color:"cyan",children:"Évolution TRS"}),children:t.jsx(de,{data:e,color:a[0]})})}),t.jsx(I,{xs:24,md:12,children:t.jsx(le,{title:"Tendances Cycle De Temps",data:e,chartType:"line",expandMode:"modal",onExpand:()=>{},onCollapse:()=>{},exportEnabled:!0,zoomEnabled:!0,extra:t.jsx(M,{color:"orange",children:"Évolution Cycle"}),children:t.jsx(ce,{data:e,color:a[1]})})})]})})]}),Pe=({dataSource:e=[],columns:a=[],loading:r=!1,title:i,totalRecords:s=0,pageSize:l=100,currentPage:o=1,onPageChange:d,onPageSizeChange:c,exportEnabled:u=!1,onExport:h,maxRecordsWarning:p=1e3,performanceMode:m=!1,rowKey:x="id",scroll:y={x:1300},expandable:f,...g})=>{const[j,D]=n.useState(!1),b=n.useMemo((()=>{const a=e.length;return{recordCount:a,estimatedRenderTime:.1*a,isLargeDataset:a>p,performanceLevel:a>2e3?"poor":a>1e3?"warning":"good"}}),[e.length,p]),S=n.useCallback((async()=>{if(h){D(!0);try{await h({data:e,totalRecords:s,currentPage:o,pageSize:l})}catch(a){}finally{D(!1)}}}),[h,e,s,o,l]),P=n.useMemo((()=>({current:o,pageSize:l,total:s,showSizeChanger:!0,showQuickJumper:s>1e3,pageSizeOptions:["50","100","200","500"],showTotal:(e,a)=>`${a[0]}-${a[1]} sur ${e} enregistrements`,onChange:d,onShowSizeChange:c,size:"default"})),[o,l,s,d,c]),Y=()=>b.isLargeDataset?t.jsx(Q,{message:`Attention: ${b.recordCount} enregistrements`,description:`Le chargement peut prendre ${Math.ceil(b.estimatedRenderTime/1e3)}s. Considérez l'utilisation de filtres pour améliorer les performances.`,type:"warning",showIcon:!0,icon:t.jsx($,{}),style:{marginBottom:16},action:t.jsx(_,{children:t.jsx(v,{size:"small",type:"link",children:"Optimiser les filtres"})})}):null,T=n.useMemo((()=>i?t.jsxs(_,{children:[t.jsx("span",{children:i}),t.jsxs(M,{color:"good"===b.performanceLevel?"green":"warning"===b.performanceLevel?"orange":"red",children:[b.recordCount," enregistrements"]}),m&&t.jsx(R,{title:`Temps de rendu estimé: ${b.estimatedRenderTime.toFixed(1)}ms`,children:t.jsx(F,{style:{color:"#1890ff"}})})]}):null),[i,b,m]),N=n.useMemo((()=>t.jsxs(_,{children:[u&&t.jsx(v,{icon:t.jsx(C,{}),onClick:S,loading:j,disabled:0===e.length,children:"Exporter"}),m&&t.jsx(M,{color:"blue",children:"Mode Performance"})]})),[u,S,j,e.length,m]),w=n.useMemo((()=>({...g,dataSource:e,columns:a,loading:r,rowKey:x,scroll:b.isLargeDataset?{...y,y:400}:y,pagination:s>l&&P,size:b.isLargeDataset?"small":"middle",expandable:f,title:T?()=>T:void 0,extra:N,virtual:b.isLargeDataset,sticky:!0,showSorterTooltip:!1,rowSelection:g.rowSelection?{...g.rowSelection,preserveSelectedRowKeys:!0}:void 0})),[g,e,a,r,x,y,b.isLargeDataset,s,l,P,f,T,N]);return t.jsxs("div",{children:[t.jsx(Y,{}),t.jsx(E,{...w}),s>1e3&&t.jsx("div",{style:{marginTop:16,textAlign:"center"},children:t.jsx(k,{...P,simple:!1,showLessItems:!1})})]})};Pe.propTypes={dataSource:ge.array,columns:ge.array,loading:ge.bool,title:ge.string,totalRecords:ge.number,pageSize:ge.number,currentPage:ge.number,onPageChange:ge.func,onPageSizeChange:ge.func,exportEnabled:ge.bool,onExport:ge.func,maxRecordsWarning:ge.number,performanceMode:ge.bool,rowKey:ge.oneOfType([ge.string,ge.func]),scroll:ge.object,expandable:ge.object};const ve=({data:e=[],title:a,children:r,maxDataPoints:s=200,enableSampling:l=!0,enableExpansion:o=!0,enableExport:d=!0,performanceMode:c=!1,loading:u=!1,height:h=300,onExpand:p,onExport:m,extra:x,...y})=>{const[g,j]=n.useState(l),[D,b]=n.useState(!1),S=n.useMemo((()=>{if(!g||e.length<=s)return e;const a=Math.ceil(e.length/s),t=[];for(let r=0;r<e.length;r+=a)t.push(e[r]);return e.length>0&&t[t.length-1]!==e[e.length-1]&&t.push(e[e.length-1]),t}),[e,g,s]),P=n.useMemo((()=>{const a=e.length,t=S.length;return{originalCount:a,optimizedCount:t,reductionPercentage:a>0?((a-t)/a*100).toFixed(1):0,isLargeDataset:a>s,estimatedRenderTime:.5*t}}),[e.length,S.length,s]),Y=n.useCallback((async()=>{if(m){b(!0);try{await m({originalData:e,optimizedData:S,title:a,performanceMetrics:P})}catch(t){}finally{b(!1)}}}),[m,e,S,a,P]),N=n.useCallback((()=>{p&&p({data:S,title:a,performanceMetrics:P})}),[p,S,a,P]),w=()=>P.isLargeDataset?t.jsx(Q,{message:`Dataset volumineux: ${P.originalCount} points de données`,description:g?`Échantillonnage activé: ${P.optimizedCount} points affichés (réduction de ${P.reductionPercentage}%)`:"Tous les points sont affichés. Activez l'échantillonnage pour améliorer les performances.",type:g?"info":"warning",showIcon:!0,style:{marginBottom:16},action:t.jsx(L,{checked:g,onChange:j,checkedChildren:"Échantillonnage ON",unCheckedChildren:"Échantillonnage OFF",size:"small"})}):null,I=n.useMemo((()=>a?t.jsxs(_,{children:[t.jsx("span",{children:a}),P.isLargeDataset&&t.jsxs(M,{color:g?"green":"orange",icon:t.jsx(f,{}),children:[P.optimizedCount," points"]}),c&&t.jsx(R,{title:`Temps de rendu estimé: ${P.estimatedRenderTime.toFixed(1)}ms`,children:t.jsx(M,{color:"blue",icon:t.jsx(T,{}),children:"Performance"})})]}):null),[a,P,g,c]),A=n.useMemo((()=>t.jsxs(_,{children:[x,o&&t.jsx(R,{title:"Agrandir le graphique",children:t.jsx(v,{type:"text",icon:t.jsx(z,{}),onClick:N,size:"small"})}),d&&t.jsx(R,{title:"Exporter les données",children:t.jsx(v,{type:"text",icon:t.jsx(C,{}),onClick:Y,loading:D,size:"small",disabled:0===e.length})})]})),[x,o,d,N,Y,D,e.length]),F=n.useMemo((()=>0!==e.length&&(!P.isLargeDataset||(g||!c))),[e.length,P.isLargeDataset,g,c]),E=n.useMemo((()=>r&&F?i.cloneElement(r,{data:S,height:h}):null),[r,S,F,h]);return t.jsxs(q,{title:I,extra:A,loading:u,...y,children:[t.jsx(w,{}),F?E:t.jsx("div",{style:{height:h,display:"flex",alignItems:"center",justifyContent:"center",background:"#fafafa",border:"1px dashed #d9d9d9",borderRadius:6},children:t.jsxs(_,{direction:"vertical",align:"center",children:[t.jsx($,{style:{fontSize:24,color:"#faad14"}}),t.jsxs("div",{children:["Dataset trop volumineux (",P.originalCount," points)"]}),t.jsx(v,{type:"primary",onClick:()=>j(!0),icon:t.jsx(f,{}),children:"Activer l'échantillonnage"})]})})]})};ve.propTypes={data:ge.array,title:ge.string,children:ge.node,maxDataPoints:ge.number,enableSampling:ge.bool,enableExpansion:ge.bool,enableExport:ge.bool,performanceMode:ge.bool,loading:ge.bool,height:ge.number,onExpand:ge.func,onExport:ge.func,extra:ge.node};const Ye=(e,a)=>{if(!(null==e?void 0:e.start))return"Toutes les dates";const t=d(e.start),r=e.end?d(e.end):t;return"day"===a?t.format("DD/MM/YYYY"):"week"===a?`${t.format("DD/MM")} - ${r.format("DD/MM/YYYY")}`:"month"===a?t.format("MM/YYYY"):`${t.format("DD/MM/YYYY")} - ${r.format("DD/MM/YYYY")}`};d.locale("fr");const{Title:Te,Text:Ne,Paragraph:Ce}=b,{useBreakpoint:we}=B,Ie=[r.PRIMARY_BLUE,r.SECONDARY_BLUE,r.CHART_TERTIARY,r.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3","#1E40AF","#2563EB","#6366F1"],Ae=n.memo((()=>{const{dateFilter:e,dateRangeType:a,dateRangeDescription:s,selectedMachineModel:c,selectedMachine:u,machineModels:h,filteredMachineNames:b,handleMachineModelChange:k,handleMachineChange:Q,handleDateChange:$,handleDateRangeTypeChange:z,resetFilters:L,handleRefresh:B}=(()=>{const e=n.useContext(_e);if(void 0===e)throw new Error("useProduction must be used within a ProductionProvider");return e})();n.useCallback((()=>{const t={};return c&&(t.model=c),u&&(t.machine=u),(null==e?void 0:e.start)&&(null==e?void 0:e.end)&&(t.startDate=e.start.format("YYYY-MM-DD"),t.endDate=e.end.format("YYYY-MM-DD")),t.dateRangeType=a,t}),[c,u,e,a]);const{getDashboardData:Z,getAllDailyProduction:oe,loading:de}=X(),[ce,ge]=n.useState({allDailyProduction:[],productionChart:[],sidecards:{goodqty:0,rejetqty:0},machinePerformance:[],availabilityTrend:[]}),je=n.useCallback((async()=>{var t,r,n;try{const i={dateRangeType:a,model:c||void 0,machine:u||void 0,date:e?(n=e,n?new Date(n).toISOString().split("T")[0]:null):void 0};Object.keys(i).forEach((e=>{void 0===i[e]&&delete i[e]}));const[s,l]=await Promise.all([oe(),Z(i)]);ge({allDailyProduction:(null==s?void 0:s.getAllDailyProduction)||[],productionChart:((null==l?void 0:l.productionChart)||[]).map((e=>({date:e.Date_Insert_Day,good:parseInt(e.Total_Good_Qty_Day)||0,reject:parseInt(e.Total_Rejects_Qty_Day)||0,oee:100*(parseFloat(e.OEE_Day)||0),speed:parseFloat(e.Speed_Day)||0,availability:100*(parseFloat(e.Availability_Rate_Day)||0),performance:100*(parseFloat(e.Performance_Rate_Day)||0),quality:100*(parseFloat(e.Quality_Rate_Day)||0),OEE_Day:100*(parseFloat(e.OEE_Day)||0),Availability_Rate_Day:100*(parseFloat(e.Availability_Rate_Day)||0),Performance_Rate_Day:100*(parseFloat(e.Performance_Rate_Day)||0),Quality_Rate_Day:100*(parseFloat(e.Quality_Rate_Day)||0)}))),sidecards:{goodqty:parseInt(null==(t=null==l?void 0:l.sidecards)?void 0:t.goodqty)||0,rejetqty:parseInt(null==(r=null==l?void 0:l.sidecards)?void 0:r.rejetqty)||0},machinePerformance:((null==l?void 0:l.machinePerformance)||[]).map((e=>({...e,availability:100*(parseFloat(e.availability)||0),performance:100*(parseFloat(e.performance)||0),oee:100*(parseFloat(e.oee)||0),quality:100*(parseFloat(e.quality)||0),disponibilite:parseFloat(e.disponibilite)||0,downtime:parseFloat(e.downtime)||0}))),availabilityTrend:(null==l?void 0:l.availabilityTrend)||[]})}catch(i){}}),[a,c,u,e,oe,Z]);n.useEffect((()=>{je()}),[je]),n.useEffect((()=>{ce.productionChart.length}),[ce]);const[De,Re]=n.useState("1"),[Ae,Fe]=n.useState(0),[Ee,ke]=n.useState(null),[Qe,$e]=n.useState(""),[ze,qe]=n.useState(!1),[Le,Be]=n.useState(!1),Oe=we(),[Ue,He]=n.useState(!1),Ge=n.useCallback((e=>{$(e),He(!!e)}),[$]);n.useEffect((()=>{const e=ce.allDailyProduction.length+ce.productionChart.length+ce.machinePerformance.length;Fe(e)}),[ce]);const Ke=n.useCallback((async e=>{}),[]),Ve=n.useCallback(((e,a)=>{ke(e),$e(a),qe(!!e),e&&Re("3")}),[]),We=n.useCallback((e=>{Be(!1),"production-data"===e.type&&Re("3")}),[]);n.useCallback((()=>{ke(null),$e(""),qe(!1)}),[]);const Je=n.useMemo((()=>{let e=0,a=0,t=0,r=0;const n=ce.productionChart,i=ce.sidecards;if(n.length>0){const i=n.reduce(((e,a)=>{let t=parseFloat(a.oee||a.OEE_Day||0),r=parseFloat(a.availability||a.Availability_Rate_Day||0),n=parseFloat(a.performance||a.Performance_Rate_Day||0),i=parseFloat(a.quality||a.Quality_Rate_Day||0);return t=l(t),r=l(r),n=l(n),i=l(i),{oee:e.oee+t,availability:e.availability+r,performance:e.performance+n,quality:e.quality+i}}),{oee:0,availability:0,performance:0,quality:0});e=i.oee/n.length,a=i.availability/n.length,t=i.performance/n.length,r=i.quality/n.length}const s=parseInt(i.goodqty)||0,o=parseInt(i.rejetqty)||0;return{avgTRS:e,avgAvailability:a,avgPerformance:t,avgQuality:r,rejectRate:s+o>0?o/(s+o)*100:0,qualityRate:s+o>0?s/(s+o)*100:0,totalGood:s,totalRejects:o}}),[ce]),{avgTRS:Ze,avgAvailability:Xe,avgPerformance:ea,avgQuality:aa,rejectRate:ta,qualityRate:ra,totalGood:na,totalRejects:ia}=Je,sa=n.useCallback((()=>{const e=(new Date).getHours();return e>=6&&e<14?"Matin":e>=14&&e<22?"Après-midi":"Nuit"}),[]),la=n.useMemo((()=>((e,a,n,i,s,l,o)=>[{title:"Production Totale",value:re(e,"Pcs"),rawValue:e,suffix:"Pcs",icon:t.jsx(p,{}),color:r.PRIMARY_BLUE,description:"Nombre total de pièces bonnes produites"},{title:"Rejet Total",value:re(a,"Kg"),rawValue:a,suffix:"Kg",icon:t.jsx(m,{}),color:r.PRIMARY_BLUE,description:"Nombre total de pièces rejetées"},{title:"TRS Moyen",value:re(n,"%"),rawValue:n,suffix:"%",icon:t.jsx(x,{}),color:r.PRIMARY_BLUE,description:"Taux de Rendement Synthétique moyen (OEE_Day)"},{title:"Disponibilité",value:re(i,"%"),rawValue:i,suffix:"%",icon:t.jsx(y,{}),color:r.PRIMARY_BLUE,description:"Taux de disponibilité moyen (Availability_Rate_Day)"},{title:"Performance",value:re(s,"%"),rawValue:s,suffix:"%",icon:t.jsx(f,{}),color:r.PRIMARY_BLUE,description:"Taux de performance moyen (Performance_Rate_Day)"},{title:"Taux de Rejet",value:re(l,"%"),rawValue:l,suffix:"%",icon:t.jsx(g,{}),color:r.PRIMARY_BLUE,description:"Pourcentage de pièces rejetées sur la production totale"},{title:"Taux de Qualité",value:re(o,"%"),rawValue:o,suffix:"%",icon:t.jsx(j,{}),color:r.PRIMARY_BLUE,description:"Pourcentage de pièces bonnes sur la production totale"}])(na,ia,Ze,Xe,ea,ta,ra)),[na,ia,Ze,Xe,ea,ta,ra]),oa=n.useMemo((()=>{return e=Ie,a=l,[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:a=>t.jsxs(_,{children:[t.jsx(D,{style:{color:e[0]}}),t.jsx(be,{strong:!0,children:a||"N/A"})]}),sorter:(e,a)=>(e.Machine_Name||"").localeCompare(a.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:e=>{if(!e)return t.jsx(be,{children:"N/A"});const a=new Date(e);return t.jsx(be,{children:a.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})})},sorter:(e,a)=>new Date(e.Date_Insert_Day||0)-new Date(a.Date_Insert_Day||0)},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:e=>t.jsxs(M,{color:"green",children:[ne(parseFloat(e)||0)," h"]}),sorter:(e,a)=>(parseFloat(e.Run_Hours_Day)||0)-(parseFloat(a.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:e=>t.jsxs(M,{color:"orange",children:[ne(parseFloat(e)||0)," h"]}),sorter:(e,a)=>(parseFloat(e.Down_Hours_Day)||0)-(parseFloat(a.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:e=>t.jsxs(M,{color:"green",children:[ie(parseInt(e)||0)," pcs"]}),sorter:(e,a)=>(parseInt(e.Good_QTY_Day)||0)-(parseInt(a.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:e=>t.jsxs(M,{color:"red",children:[ie(parseInt(e)||0)," pcs"]}),sorter:(e,a)=>(parseInt(e.Rejects_QTY_Day)||0)-(parseInt(a.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:e=>t.jsx(M,{color:"blue",children:ne(parseFloat(e)||0)}),sorter:(e,a)=>(parseFloat(e.Speed_Day)||0)-(parseFloat(a.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:e=>{const r=a(e);return t.jsx(R,{title:`${se(r,1)}% de disponibilité`,children:t.jsx(S,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${se(e,1)}%`})})},sorter:(e,t)=>a(e.Availability_Rate_Day)-a(t.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:e=>{const r=a(e);return t.jsx(R,{title:`${r.toFixed(1)}% de performance`,children:t.jsx(S,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>a(e.Performance_Rate_Day)-a(t.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:e=>{const r=a(e);return t.jsx(R,{title:`${r.toFixed(1)}% de qualité`,children:t.jsx(S,{percent:r,size:"small",status:r>90?"success":r>80?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>a(e.Quality_Rate_Day)-a(t.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:e=>{const r=a(e);return t.jsx(R,{title:`${r.toFixed(1)}% de TRS`,children:t.jsx(S,{percent:r,size:"small",status:r>85?"success":r>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>a(e.OEE_Day)-a(t.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:e=>t.jsx(M,{color:"blue",children:e||"N/A"}),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(e,a)=>a.Shift===e},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:e=>t.jsx(M,{color:"purple",children:e||"N/A"})},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:e=>t.jsx(M,{color:"cyan",children:e||"N/A"})},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:e=>t.jsx(M,{color:"magenta",children:e||"N/A"})},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:e=>t.jsx(M,{color:"gold",children:e||"N/A"})},{title:"Actions",key:"actions",fixed:"right",width:80,render:()=>t.jsx(P,{menu:{items:[{key:"1",icon:t.jsx(T,{}),label:"Voir tendances"},{key:"2",icon:t.jsx(N,{}),label:"Paramètres"},{key:"3",icon:t.jsx(C,{}),label:"Exporter données"}]},trigger:["click"],children:t.jsx(v,{type:"text",icon:t.jsx(Y,{})})})}];var e,a}),[Ie]),da=n.useMemo((()=>((e,a=[])=>{const r=e=>{if(null==e||""===e)return 0;if("number"==typeof e&&!isNaN(e))return e<=1&&e>0?100*e:e;if("string"==typeof e){const a=parseFloat(e.replace(",","."));if(!isNaN(a))return a<=1&&a>0?100*a:a}return 0};return[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",fixed:"left",width:120,render:a=>t.jsxs(_,{children:[t.jsx(D,{style:{color:e[0]}}),t.jsx(Me,{strong:!0,children:a||"N/A"})]}),filters:Array.from(new Set(a.map((e=>e.Machine_Name||"N/A")))).map((e=>({text:e,value:e}))),onFilter:(e,a)=>a.Machine_Name===e||"N/A"===e&&!a.Machine_Name,sorter:(e,a)=>(e.Machine_Name||"").localeCompare(a.Machine_Name||"")},{title:"Date d'Insertion",dataIndex:"Date_Insert_Day",key:"Date_Insert_Day",width:160,render:a=>{if(!a)return t.jsx(Me,{children:"N/A"});const r=new Date(a);return t.jsxs(_,{children:[t.jsx(w,{style:{color:e[1]}}),t.jsx(Me,{children:r.toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})})]})},sorter:(e,a)=>new Date(e.Date_Insert_Day||0)-new Date(a.Date_Insert_Day||0),defaultSortOrder:"descend"},{title:"Heures de Fonctionnement",dataIndex:"Run_Hours_Day",key:"Run_Hours_Day",width:150,render:e=>t.jsxs(M,{color:"green",children:[ne(parseFloat(e)||0)," h"]}),sorter:(e,a)=>(parseFloat(e.Run_Hours_Day)||0)-(parseFloat(a.Run_Hours_Day)||0)},{title:"Heures d'Arrêt",dataIndex:"Down_Hours_Day",key:"Down_Hours_Day",width:130,render:e=>t.jsxs(M,{color:"orange",children:[ne(parseFloat(e)||0)," h"]}),sorter:(e,a)=>(parseFloat(e.Down_Hours_Day)||0)-(parseFloat(a.Down_Hours_Day)||0)},{title:"Quantité Bonne",dataIndex:"Good_QTY_Day",key:"Good_QTY_Day",width:140,render:e=>t.jsxs(M,{color:"green",children:[ie(parseInt(e)||0)," pcs"]}),sorter:(e,a)=>(parseInt(e.Good_QTY_Day)||0)-(parseInt(a.Good_QTY_Day)||0)},{title:"Quantité Rejetée",dataIndex:"Rejects_QTY_Day",key:"Rejects_QTY_Day",width:140,render:e=>t.jsxs(M,{color:"red",children:[ie(parseInt(e)||0)," pcs"]}),sorter:(e,a)=>(parseInt(e.Rejects_QTY_Day)||0)-(parseInt(a.Rejects_QTY_Day)||0)},{title:"Vitesse",dataIndex:"Speed_Day",key:"Speed_Day",width:100,render:e=>t.jsx(M,{color:"blue",children:ne(parseFloat(e)||0)}),sorter:(e,a)=>(parseFloat(e.Speed_Day)||0)-(parseFloat(a.Speed_Day)||0)},{title:"Taux de Disponibilité",dataIndex:"Availability_Rate_Day",key:"Availability_Rate_Day",width:160,render:e=>{const a=r(e);return t.jsx(R,{title:`${se(a,1)}% de disponibilité`,children:t.jsx(S,{percent:a,size:"small",status:a>85?"success":a>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${se(e,1)}%`})})},sorter:(e,a)=>r(e.Availability_Rate_Day)-r(a.Availability_Rate_Day)},{title:"Taux de Performance",dataIndex:"Performance_Rate_Day",key:"Performance_Rate_Day",width:160,render:e=>{const a=r(e);return t.jsx(R,{title:`${se(a,1)}% de performance`,children:t.jsx(S,{percent:a,size:"small",status:a>85?"success":a>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${se(e,1)}%`})})},sorter:(e,a)=>r(e.Performance_Rate_Day)-r(a.Performance_Rate_Day)},{title:"Taux de Qualité",dataIndex:"Quality_Rate_Day",key:"Quality_Rate_Day",width:140,render:e=>{const a=r(e);return t.jsx(R,{title:`${se(a,1)}% de qualité`,children:t.jsx(S,{percent:a,size:"small",status:a>90?"success":a>80?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${se(e,1)}%`})})},sorter:(e,a)=>r(e.Quality_Rate_Day)-r(a.Quality_Rate_Day)},{title:"TRS",dataIndex:"OEE_Day",key:"OEE_Day",width:120,render:e=>{const a=r(e);return t.jsx(R,{title:`${se(a,1)}% de TRS`,children:t.jsx(S,{percent:a,size:"small",status:a>85?"success":a>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0,0%":`${se(e,1)}%`})})},sorter:(e,a)=>r(e.OEE_Day)-r(a.OEE_Day),defaultSortOrder:"descend"},{title:"Équipe",dataIndex:"Shift",key:"Shift",width:100,render:e=>t.jsx(M,{color:"blue",children:e||"N/A"}),filters:[{text:"Shift 1",value:"Shift 1"},{text:"Shift 2",value:"Shift 2"},{text:"Shift 3",value:"Shift 3"}],onFilter:(e,a)=>a.Shift===e},{title:"Numéro de Pièce",dataIndex:"Part_Number",key:"Part_Number",width:140,render:e=>t.jsx(M,{color:"purple",children:e||"N/A"}),filters:Array.from(new Set(a.map((e=>e.Part_Number||"N/A")))).map((e=>({text:e,value:e}))),onFilter:(e,a)=>a.Part_Number===e||"N/A"===e&&!a.Part_Number},{title:"Poids Unitaire",dataIndex:"Poid_Unitaire",key:"Poid_Unitaire",width:120,render:e=>t.jsx(M,{color:"cyan",children:e||"N/A"})},{title:"Cycle Théorique",dataIndex:"Cycle_Theorique",key:"Cycle_Theorique",width:130,render:e=>t.jsx(M,{color:"magenta",children:e||"N/A"})},{title:"Poids Purge",dataIndex:"Poid_Purge",key:"Poid_Purge",width:110,render:e=>t.jsx(M,{color:"gold",children:e||"N/A"})}]})(Ie,ce.allDailyProduction)),[Ie,ce.allDailyProduction]),ca=n.useCallback(((e,a=0)=>{if(null==e||""===e)return a;if("number"==typeof e&&!isNaN(e))return e;const t=String(e).trim().replace(",","."),r=parseFloat(t);return isNaN(r)?a:r}),[]),ua=n.useCallback(((e,a=0)=>{if(null==e||""===e)return a;if("number"==typeof e&&!isNaN(e))return Math.round(e);const t=String(e).trim(),r=parseInt(t,10);return isNaN(r)?a:r}),[]),ha=n.useMemo((()=>ce.allDailyProduction.map((e=>({...e,date:(()=>{try{const a=e.Date_Insert_Day||e.date;if(a){if(a.includes("/")){let e=d(a,"DD/MM/YYYY HH:mm:ss");if(e.isValid()||(e=d(a,"DD/MM/YYYY")),e.isValid())return e.format("YYYY-MM-DD")}const e=d(a);if(e.isValid())return e.format("YYYY-MM-DD")}return d().format("YYYY-MM-DD")}catch(a){return d().format("YYYY-MM-DD")}})(),Machine_Name:e.Machine_Name||"N/A",Shift:e.Shift||"N/A",good:ua(e.Good_QTY_Day),reject:ua(e.Rejects_QTY_Day),oee:(()=>{const a=ca(e.OEE_Day);return a>0&&a<=1?100*a:a})(),speed:ca(e.Speed_Day,null),mould_number:e.Part_Number||"N/A",poid_unitaire:e.Poid_Unitaire||"N/A",cycle_theorique:e.Cycle_Theorique||"N/A",poid_purge:e.Poid_Purge||"N/A",availability:(()=>{const a=ca(e.Availability_Rate_Day);return a>0&&a<=1?100*a:a})(),performance:(()=>{const a=ca(e.Performance_Rate_Day);return a>0&&a<=1?100*a:a})(),quality:(()=>{const a=ca(e.Quality_Rate_Day);return a>0&&a<=1?100*a:a})(),run_hours:ca(e.Run_Hours_Day),down_hours:ca(e.Down_Hours_Day)})))),[ce.allDailyProduction,ca,ua]),pa=n.useMemo((()=>[{key:"1",label:t.jsxs("span",{children:[t.jsx(T,{}),"Tendances"]}),children:t.jsx(A,{gutter:[24,24],children:de?t.jsx(I,{span:24,children:t.jsx(q,{children:t.jsx(O,{active:!0,paragraph:{rows:8}})})}):t.jsx(Se,{data:ce.productionChart,colors:Ie,dateRangeType:a,dateFilter:e,formatDateRange:Ye})})},{key:"2",label:t.jsxs("span",{children:[t.jsx(H,{}),"Performance"]}),children:t.jsx(A,{gutter:[24,24],children:de?t.jsx(I,{span:24,children:t.jsx(q,{children:t.jsx(O,{active:!0,paragraph:{rows:8}})})}):t.jsxs(t.Fragment,{children:[t.jsx(I,{span:24,children:t.jsx(q,{title:t.jsxs(_,{children:[t.jsx(H,{style:{fontSize:20,color:Ie[1]}}),t.jsx(Ne,{strong:!0,children:"Performance des Machines"})]}),variant:"borderless",extra:t.jsx(U,{count:ce.machinePerformance.length,style:{backgroundColor:Ie[1]}}),children:t.jsxs(A,{gutter:[24,24],children:[t.jsx(I,{xs:24,md:12,children:t.jsx(le,{title:"Production par Machine",data:ce.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(ue,{data:ce.machinePerformance})})}),t.jsx(I,{xs:24,md:12,children:t.jsx(le,{title:"Rejets par Machine",data:ce.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(he,{data:ce.machinePerformance})})})]})})}),t.jsxs(I,{xs:24,md:12,children:["              ",t.jsx(le,{title:"TRS par Machine",data:ce.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(pe,{data:ce.machinePerformance})})]}),t.jsx(I,{xs:24,md:12,children:t.jsx(ve,{title:"Répartition Production - Qualité",data:[{name:"Bonnes Pièces",value:Number(na)||0},{name:"Rejets",value:Number(ia)||0}].filter((e=>e.value>0)),maxDataPoints:50,enableSampling:!1,enableExpansion:!0,loading:de,onExport:Ke,extra:t.jsx(M,{color:"red",children:"Qualité"}),children:t.jsx(me,{data:[{name:"Bonnes Pièces",value:Number(na)||0},{name:"Rejets",value:Number(ia)||0}].filter((e=>e.value>0)),colors:[Ie[2],Ie[4]]})})}),t.jsx(I,{xs:24,md:24,children:t.jsx(q,{title:t.jsxs(_,{children:[t.jsx(H,{style:{fontSize:20,color:Ie[3]}}),t.jsx(Ne,{strong:!0,children:"Comparaison des Équipes"})]}),variant:"borderless",extra:t.jsx(M,{color:"orange",children:"Par équipe"}),children:t.jsxs(A,{gutter:[24,24],children:[t.jsx(I,{xs:24,md:12,children:t.jsx(le,{title:"Production par Équipe",data:ce.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(xe,{data:ce.machinePerformance,title:"Production par Équipe",dataKey:"production",color:Ie[2],label:"Production",tooltipLabel:"Production",isKg:!1})})}),t.jsxs(I,{xs:24,md:12,children:[ce.machinePerformance.length>0&&(()=>null)(),t.jsx(le,{title:"Temps d'arrêt par Équipe",data:ce.machinePerformance,chartType:"bar",expandMode:"modal",children:t.jsx(xe,{data:ce.machinePerformance,title:"Temps d'arrêt par Équipe",dataKey:"downtime",color:Ie[4],label:"Temps d'arrêt (heures)",tooltipLabel:"Temps d'arrêt (heures)",isKg:!1})})]}),t.jsx(I,{xs:24,md:12,children:t.jsx(le,{title:"TRS par Équipe",data:ce.machinePerformance,chartType:"line",expandMode:"modal",children:t.jsx(ye,{data:ce.machinePerformance,color:Ie[0]})})}),t.jsx(I,{xs:24,md:12,children:t.jsx(le,{title:"Performance par Équipe",data:ce.machinePerformance,chartType:"line",expandMode:"modal",children:t.jsx(fe,{data:ce.machinePerformance,color:Ie[5]})})})]})})})]})})},{key:"3",label:t.jsxs("span",{children:[t.jsx(K,{}),"Détails"]}),children:t.jsxs(A,{gutter:[24,24],children:[t.jsx(I,{span:24,children:t.jsx(q,{title:t.jsxs(_,{children:[t.jsx(D,{style:{fontSize:20,color:Ie[1]}}),t.jsx(Ne,{strong:!0,children:"Données Journalières par Machine"})]}),variant:"borderless",extra:t.jsxs(_,{children:[t.jsx(U,{count:ce.allDailyProduction.length,style:{backgroundColor:Ie[1]}}),t.jsx(v,{type:"link",icon:t.jsx(C,{}),disabled:!0,children:"Exporter"})]}),children:t.jsx(E,{dataSource:ce.allDailyProduction,columns:oa,pagination:{pageSize:5,showSizeChanger:!0,pageSizeOptions:["5","10","20"],showTotal:e=>`Total ${e} enregistrements`},scroll:{x:1800},rowKey:(e,a)=>`${e.Machine_Name}-${e.Date_Insert_Day}-${a}`})})}),t.jsx(I,{span:24,children:t.jsx(Pe,{title:"Données Détaillées de Production",dataSource:ha,columns:da,totalRecords:ha.length,pageSize:50,currentPage:1,onExport:Ke,maxRecordsWarning:500,loading:de,scroll:{x:2200},rowKey:(e,a)=>`${e.Date_Insert_Day}-${e.Machine_Name||"unknown"}-${e.Part_Number||"unknown"}-${a}`,expandable:{expandedRowRender:e=>t.jsx(q,{size:"small",title:"Informations du moule",children:t.jsxs(A,{gutter:[16,16],children:[t.jsx(I,{span:6,children:t.jsx(G,{title:"Numéro de Pièce",value:e.Part_Number||"N/A",valueStyle:{fontSize:16}})}),t.jsx(I,{span:6,children:t.jsx(G,{title:"Poids Unitaire",value:e.Poid_Unitaire||"N/A",valueStyle:{fontSize:16},suffix:"g"})}),t.jsx(I,{span:6,children:t.jsx(G,{title:"Cycle Théorique",value:e.Cycle_Theorique||"N/A",valueStyle:{fontSize:16},suffix:"s"})}),t.jsx(I,{span:6,children:t.jsx(G,{title:"Poids Purge",value:e.Poid_Purge||"N/A",valueStyle:{fontSize:16},suffix:"g"})})]})}),expandRowByClick:!0,rowExpandable:e=>e.Part_Number&&"N/A"!==e.Part_Number}})})]})}]),[ce.allDailyProduction,ce.machinePerformance,ce.sidecards,Ie,a,e,Ye,ha,da,Ke,de,oa]),ma=de,xa=de,ya=ce.allDailyProduction.length>0||ce.productionChart.length>0,fa=ce.productionChart.length>0||ce.sidecards.goodqty>0;return t.jsxs("div",{style:{padding:Oe.md?24:16},children:[t.jsx(V,{spinning:ma,tip:"Chargement des données...",size:"large",children:t.jsxs(A,{gutter:[24,24],children:[t.jsx(I,{span:24,children:t.jsx(q,{variant:"borderless",styles:{body:{padding:Oe.md?24:16}},children:t.jsxs(A,{gutter:[24,24],align:"middle",children:[t.jsx(I,{xs:24,md:12,children:t.jsxs(Te,{level:3,style:{marginBottom:8},children:[t.jsx(x,{style:{marginRight:12,color:Ie[0]}}),"Tableau de Bord de Production"]})}),t.jsx(I,{xs:24,md:12,style:{textAlign:Oe.md?"right":"left"},children:t.jsxs(_,{direction:"vertical",style:{width:"100%"},children:[t.jsx(ee,{selectedMachineModel:c,selectedMachine:u,machineModels:h,filteredMachineNames:b,dateRangeType:a,dateFilter:e,dateFilterActive:Ue,handleMachineModelChange:k,handleMachineChange:Q,handleDateRangeTypeChange:z,handleDateChange:Ge,resetFilters:L,handleRefresh:B,loading:de,dataSize:Ae,pageType:"production",onSearchResults:Ve,enableElasticsearch:!0}),Ae>500&&t.jsxs(M,{color:"blue",icon:t.jsx(f,{}),children:[Ae," enregistrements"]}),(c||Ue)&&t.jsxs(_,{wrap:!0,style:{marginTop:8},children:[c&&t.jsxs(M,{color:"blue",closable:!0,onClose:()=>k(""),children:["Modèle: ",c]}),u&&t.jsxs(M,{color:"green",closable:!0,onClose:()=>Q(""),children:["Machine: ",u]}),Ue&&t.jsxs(M,{color:"purple",closable:!0,onClose:()=>$(null),children:["Période: ",s]})]}),!(c||Ue)&&fa&&t.jsx(_,{wrap:!0,style:{marginTop:8},children:t.jsx(M,{color:"green",icon:t.jsx(f,{}),children:"Powered by GraphQL"})})]})})]})})}),la.slice(0,4).map((e=>t.jsx(I,{xs:24,sm:12,md:6,children:t.jsx(q,{hoverable:!0,loading:xa,style:{backgroundColor:"#FFFFFF",border:`1px solid ${r.PRIMARY_BLUE}`,borderTop:`3px solid ${r.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:xa?t.jsx(O,{active:!0,paragraph:{rows:1}}):t.jsxs(t.Fragment,{children:[t.jsx(G,{title:t.jsx(R,{title:e.description,children:t.jsxs(_,{children:[i.cloneElement(e.icon,{style:{color:r.PRIMARY_BLUE,fontSize:20}}),t.jsx("span",{style:{color:r.DARK_GRAY,fontWeight:600},children:e.title}),t.jsx(F,{style:{color:r.LIGHT_GRAY,fontSize:14}})]})}),value:e.value,precision:e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité")?1:0,suffix:e.suffix,valueStyle:{fontSize:24,color:r.PRIMARY_BLUE,fontWeight:700}}),(e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité"))&&t.jsx(S,{percent:e.value,strokeColor:r.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6})]})})},e.title))),la.slice(4).map((e=>t.jsx(I,{xs:24,sm:12,md:6,children:t.jsx(q,{hoverable:!0,loading:xa,style:{backgroundColor:"#FFFFFF",border:`1px solid ${r.PRIMARY_BLUE}`,borderTop:`3px solid ${r.PRIMARY_BLUE}`,height:"100%",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:xa?t.jsx(O,{active:!0,paragraph:{rows:1}}):t.jsxs(t.Fragment,{children:[t.jsx(G,{title:t.jsx(R,{title:e.description,children:t.jsxs(_,{children:[i.cloneElement(e.icon,{style:{color:r.PRIMARY_BLUE,fontSize:20}}),t.jsx("span",{style:{color:r.DARK_GRAY,fontWeight:600},children:e.title}),t.jsx(F,{style:{color:r.LIGHT_GRAY,fontSize:14}})]})}),value:e.value,precision:e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité")?1:0,suffix:e.suffix,valueStyle:{fontSize:24,color:r.PRIMARY_BLUE,fontWeight:700}}),(e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité"))&&t.jsx(S,{percent:e.value,strokeColor:r.SECONDARY_BLUE,trailColor:"#F3F4F6",showInfo:!1,status:"normal",style:{marginTop:12},strokeWidth:6})]})})},e.title))),ma?t.jsx(I,{span:24,children:t.jsx(q,{children:t.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"},children:[t.jsx(V,{size:"large",style:{marginBottom:24}}),t.jsx(Te,{level:3,children:"Chargement des données..."}),t.jsx(Ce,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600},children:c?`Chargement des données pour ${c}...`:"Chargement des données de production pour tous les modèles de machines..."})]})})}):ya?t.jsx(t.Fragment,{children:t.jsx(I,{span:24,children:t.jsx(q,{variant:"borderless",children:t.jsx(W,{defaultActiveKey:"1",onChange:Re,items:pa,tabBarExtraContent:t.jsxs(_,{children:[t.jsx(v,{type:"link",icon:t.jsx(J,{}),onClick:()=>Be(!0),children:"Recherche globale"}),t.jsx(v,{type:"link",icon:t.jsx(C,{}),disabled:!0,children:"Exporter"}),u&&t.jsx(o,{machineId:u,machineName:u,shift:sa()})]})})})})}):t.jsx(I,{span:24,children:t.jsx(q,{children:t.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"},children:[t.jsx(x,{style:{fontSize:64,color:"#1890ff",marginBottom:24}}),t.jsx(Te,{level:3,children:"Aucune donnée disponible"}),t.jsx(Ce,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600},children:c?`Aucune donnée n'a été trouvée pour le modèle ${c} avec les filtres sélectionnés. Essayez de modifier vos critères de recherche ou sélectionnez un autre modèle.`:"Aucune donnée de production n'est disponible pour la période sélectionnée. Vérifiez votre connexion ou contactez l'administrateur système."})]})})})]})}),ze&&Ee&&t.jsx("div",{style:{marginTop:24},children:t.jsx(ae,{results:Ee,searchQuery:Qe,pageType:"production",loading:de,onResultSelect:e=>{},onPageChange:e=>{}})}),t.jsx(te,{visible:Le,onClose:()=>Be(!1),onResultSelect:We})]})})),Fe=n.memo((()=>t.jsx(De,{children:t.jsx(Ae,{})})));export{Fe as default};
