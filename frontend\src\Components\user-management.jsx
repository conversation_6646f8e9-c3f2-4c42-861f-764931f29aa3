"use client"

import { useState, useEffect } from "react"
import {
  Button,
  Table,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Popconfirm,
  message,
  Typography,
  Badge,
  Tooltip,
  Switch,
  Row,
  Col,
  Divider,
  Avatar,
  Empty,
} from "antd"
import {
  UserAddOutlined,
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  ReloadOutlined,
  UserOutlined,
  LockOutlined,
  MailOutlined,
  PhoneOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  KeyOutlined,
} from "@ant-design/icons"
import { useAuth } from "../hooks/useAuth"
import { extractResponseData, isResponseSuccessful, getErrorMessage } from "../utils/apiUtils"

const { Title, Text } = Typography
const { Option } = Select

const UserManagement = ({ darkMode }) => {
  const { user: currentUser, createUser, updateUser, deleteUser, getAllUsers, resetUserPassword } = useAuth()
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [modalTitle, setModalTitle] = useState("Ajouter un utilisateur")
  const [editingUser, setEditingUser] = useState(null)
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState("")
  const [passwordVisible, setPasswordVisible] = useState(false)
  const [resetPasswordModalVisible, setResetPasswordModalVisible] = useState(false)
  const [selectedUserForReset, setSelectedUserForReset] = useState(null)
  const [resetPasswordForm] = Form.useForm()

  // Charger les utilisateurs
  const fetchUsers = async () => {
    setLoading(true)
    try {
      const response = await getAllUsers()
      if (response.success) {
        setUsers(response.data || [])
      } else {
        message.error("Erreur lors du chargement des utilisateurs")
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Erreur lors du chargement des utilisateurs")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  // Filtrer les utilisateurs en fonction de la recherche
  const filteredUsers = users.filter(
    (user) =>
      user.username?.toLowerCase().includes(searchText.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchText.toLowerCase()) ||
      (user.fullName && user.fullName.toLowerCase().includes(searchText.toLowerCase())),
  )

  // Ouvrir le modal pour ajouter un utilisateur
  const showAddModal = () => {
    setModalTitle("Ajouter un utilisateur")
    setEditingUser(null)
    form.resetFields()
    setModalVisible(true)
    setPasswordVisible(false)
  }

  // Ouvrir le modal pour éditer un utilisateur
  const showEditModal = (user) => {
    setModalTitle("Modifier l'utilisateur")
    setEditingUser(user)
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      role: user.role,
      fullName: user.fullName || "",
      phone: user.phone || "",
      active: user.active,
    })
    setModalVisible(true)
  }

  // Gérer la soumission du formulaire
  const handleFormSubmit = async (values) => {
    try {
      if (editingUser) {
        // Mise à jour d'un utilisateur existant
        const response = await updateUser(editingUser.id, values)
        if (response.success) {
          message.success("Utilisateur mis à jour avec succès")
          fetchUsers()
          setModalVisible(false)
        } else {
          message.error(response.message || "Erreur lors de la mise à jour de l'utilisateur")
        }
      } else {
        // Création d'un nouvel utilisateur
        const response = await createUser(values)
        if (response.success) {
          message.success("Utilisateur créé avec succès")
          fetchUsers()
          setModalVisible(false)
        } else {
          message.error(response.message || "Erreur lors de la création de l'utilisateur")
        }
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Une erreur est survenue")
    }
  }

  // Gérer la suppression d'un utilisateur
  const handleDeleteUser = async (userId) => {
    try {
      const response = await deleteUser(userId)
      if (response.success) {
        message.success("Utilisateur supprimé avec succès")
        fetchUsers()
      } else {
        message.error(response.message || "Erreur lors de la suppression de l'utilisateur")
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Une erreur est survenue")
    }
  }

  // Ouvrir le modal de réinitialisation de mot de passe
  const showResetPasswordModal = (user) => {
    setSelectedUserForReset(user)
    resetPasswordForm.resetFields()
    setResetPasswordModalVisible(true)
  }

  // Gérer la réinitialisation du mot de passe
  const handleResetPassword = async (values) => {
    try {
      const response = await resetUserPassword(selectedUserForReset.id, values.newPassword)
      if (response.success) {
        message.success("Mot de passe réinitialisé avec succès")
        setResetPasswordModalVisible(false)
      } else {
        message.error(response.message || "Erreur lors de la réinitialisation du mot de passe")
      }
    } catch (error) {
      console.error("Erreur:", error)
      message.error("Une erreur est survenue")
    }
  }

  // Colonnes du tableau
  const columns = [
    {
      title: "Utilisateur",
      key: "user",
      render: (_, record) => (
        <Space>
          <Avatar
            icon={<UserOutlined />}
            style={{
              backgroundColor: record.role === "admin" ? "#52c41a" : "#1890ff",
              marginRight: 8,
            }}
          />
          <div>
            <Text strong>{record.fullName || record.username}</Text>
            <div>
              <Text type="secondary" style={{ fontSize: "12px" }}>
                {record.email}
              </Text>
            </div>
          </div>
        </Space>
      ),
      sorter: (a, b) => (a.fullName || a.username).localeCompare(b.fullName || b.username),
    },
    {
      title: "Rôle",
      dataIndex: "role",
      key: "role",
      render: (role) => (
        <Tag color={role === "admin" ? "green" : "blue"}>{role === "admin" ? "Administrateur" : "Utilisateur"}</Tag>
      ),
      filters: [
        { text: "Administrateur", value: "admin" },
        { text: "Utilisateur", value: "user" },
      ],
      onFilter: (value, record) => record.role === value,
    },
    {
      title: "Statut",
      dataIndex: "active",
      key: "active",
      render: (active) => <Badge status={active ? "success" : "default"} text={active ? "Actif" : "Inactif"} />,
      filters: [
        { text: "Actif", value: true },
        { text: "Inactif", value: false },
      ],
      onFilter: (value, record) => record.active === value,
    },
    {
      title: "Créé le",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date) => (date ? new Date(date).toLocaleDateString() : "N/A"),
      sorter: (a, b) => new Date(a.createdAt || 0) - new Date(b.createdAt || 0),
      responsive: ["md"],
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Modifier">
            <Button
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
              type="text"
              disabled={record.id === currentUser?.id}
            />
          </Tooltip>
          <Tooltip title="Réinitialiser le mot de passe">
            <Button
              icon={<KeyOutlined />}
              onClick={() => showResetPasswordModal(record)}
              type="text"
              disabled={record.id === currentUser?.id}
            />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm
              title="Êtes-vous sûr de vouloir supprimer cet utilisateur ?"
              onConfirm={() => handleDeleteUser(record.id)}
              okText="Oui"
              cancelText="Non"
              disabled={record.id === currentUser?.id}
            >
              <Button danger icon={<DeleteOutlined />} type="text" disabled={record.id === currentUser?.id} />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 16, flexWrap: "wrap", gap: "8px" }}>
        <Title level={4}>Gestion des utilisateurs</Title>
        <Space wrap>
          <Input
            placeholder="Rechercher un utilisateur"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 250 }}
            allowClear
          />
          <Button type="primary" icon={<UserAddOutlined />} onClick={showAddModal}>
            Ajouter un utilisateur
          </Button>
          <Button icon={<ReloadOutlined />} onClick={fetchUsers}>
            Actualiser
          </Button>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={filteredUsers}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total) => `Total: ${total} utilisateurs`,
        }}
        locale={{
          emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="Aucun utilisateur trouvé" />,
        }}
      />

      {/* Modal pour ajouter/modifier un utilisateur */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
        destroyOnClose
      >
        <Form form={form} layout="vertical" onFinish={handleFormSubmit} initialValues={{ role: "user", active: true }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="fullName"
                label="Nom complet"
                rules={[{ required: true, message: "Veuillez entrer le nom complet" }]}
              >
                <Input prefix={<UserOutlined />} placeholder="Nom complet" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="username"
                label="Nom d'utilisateur"
                rules={[{ required: true, message: "Veuillez entrer le nom d'utilisateur" }]}
              >
                <Input prefix={<UserOutlined />} placeholder="Nom d'utilisateur" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: "Veuillez entrer l'email" },
                  { type: "email", message: "Veuillez entrer un email valide" },
                ]}
              >
                <Input prefix={<MailOutlined />} placeholder="Email" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="Téléphone"
                rules={[{ pattern: /^[0-9+\s-]{8,15}$/, message: "Format de téléphone invalide" }]}
              >
                <Input prefix={<PhoneOutlined />} placeholder="Téléphone" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role"
                label="Rôle"
                rules={[{ required: true, message: "Veuillez sélectionner un rôle" }]}
              >
                <Select placeholder="Sélectionner un rôle">
                  <Option value="user">Utilisateur</Option>
                  <Option value="admin">Administrateur</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="active" label="Statut" valuePropName="checked">
                <Switch checkedChildren={<CheckCircleOutlined />} unCheckedChildren={<CloseCircleOutlined />} />
              </Form.Item>
            </Col>
          </Row>

          {!editingUser && (
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="password"
                  label="Mot de passe"
                  rules={[
                    { required: true, message: "Veuillez entrer un mot de passe" },
                    { min: 8, message: "Le mot de passe doit contenir au moins 8 caractères" },
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="Mot de passe"
                    iconRender={(visible) => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
                    visibilityToggle={{ visible: passwordVisible, onVisibleChange: setPasswordVisible }}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>Annuler</Button>
              <Button type="primary" htmlType="submit">
                {editingUser ? "Mettre à jour" : "Ajouter"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Modal pour réinitialiser le mot de passe */}
      <Modal
        title="Réinitialiser le mot de passe"
        open={resetPasswordModalVisible}
        onCancel={() => setResetPasswordModalVisible(false)}
        footer={null}
        destroyOnClose
      >
        <Form form={resetPasswordForm} layout="vertical" onFinish={handleResetPassword}>
          <Form.Item
            name="newPassword"
            label="Nouveau mot de passe"
            rules={[
              { required: true, message: "Veuillez entrer un nouveau mot de passe" },
              { min: 8, message: "Le mot de passe doit contenir au moins 8 caractères" },
              {
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                message:
                  "Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial",
              },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="Nouveau mot de passe"
              iconRender={(visible) => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="Confirmer le mot de passe"
            dependencies={["newPassword"]}
            rules={[
              { required: true, message: "Veuillez confirmer le mot de passe" },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("newPassword") === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error("Les deux mots de passe ne correspondent pas"))
                },
              }),
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="Confirmer le mot de passe" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: "right" }}>
            <Space>
              <Button onClick={() => setResetPasswordModalVisible(false)}>Annuler</Button>
              <Button type="primary" htmlType="submit">
                Réinitialiser
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default UserManagement

