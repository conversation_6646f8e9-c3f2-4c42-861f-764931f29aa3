"use client"

import React from "react"
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ReferenceLine,
  Cell,
} from "recharts"
import { Typography, Empty } from "antd"

const { Text } = Typography

// Chart colors
const CHART_COLORS = {
  primary: "#1890ff",
  secondary: "#13c2c2",
  success: "#52c41a",
  warning: "#faad14",
  danger: "#f5222d",
  purple: "#722ed1",
  pink: "#eb2f96",
  orange: "#fa8c16",
  cyan: "#13c2c2",
  lime: "#a0d911",
}

// Color mapping for machine models
const MODEL_COLORS = {
  IPS: CHART_COLORS.primary,
  CCM24: CHART_COLORS.purple,
  default: CHART_COLORS.secondary,
}

// Helper function to format percentage values
const formatPercentage = (value) => {
  if (value === undefined || value === null) return "N/A";

  // Convert to number
  let numValue = Number(value);

  // Check if value is between 0 and 1, and convert to percentage if needed
  if (!isNaN(numValue) && numValue > 0 && numValue < 1) {
    numValue = numValue * 100;
  }

  return `${numValue.toFixed(1)}%`;
};

const DisponibiliteByMachineChart = ({
  data = [],
  selectedMachine = "",
  selectedMachineModel = "",
  targetValue = 85, // Default target value for disponibilité (85%)
  loading = false,
}) => {
  // Process data to group by machine model and sort by disponibilité
  const processedData = React.useMemo(() => {
    // Check if data is an array and has items
    if (!data || !Array.isArray(data) || data.length === 0) return []

    try {
      // Convert disponibilite values from 0-1 to 0-100 if needed
      const normalizedData = [...data].map(item => {
        // Create a new object to avoid mutating the original data
        const newItem = { ...item };

        // Check if disponibilite is between 0 and 1, and convert to percentage if needed
        if (newItem.disponibilite !== undefined && newItem.disponibilite !== null) {
          const value = Number(newItem.disponibilite);
          if (!isNaN(value) && value > 0 && value < 1) {
            newItem.disponibilite = value * 100;
          }
        }

        return newItem;
      });

      // Sort data by disponibilité in descending order
      return normalizedData.sort((a, b) => b.disponibilite - a.disponibilite);
    } catch (error) {
      console.error("Error processing disponibilite data:", error)
      return []
    }
  }, [data])

  // Custom tooltip to show detailed information
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || !payload.length) return null

    const item = payload[0].payload

    return (
      <div
        style={{
          backgroundColor: "#fff",
          padding: "10px",
          border: "1px solid #ccc",
          borderRadius: "4px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
        }}
      >
        <Text strong>{item.machine}</Text>
        <div>
        <Text>Disponibilité: {formatPercentage(item.disponibilite)}</Text>
        </div>
        <div>
        <Text>MTTR: {Number(item.mttr).toFixed(1)} min</Text>
        </div>
        <div>
        <Text>MTBF: {Number(item.mtbf).toFixed(1)} min</Text>
        </div>
        <div>
          <Text>Nombre d'arrêts: {item.stops}</Text>
        </div>
      </div>
    )
  }

  // Get color for a machine based on model and selection
  const getMachineColor = (machine, model) => {
    // If this is the selected machine, highlight it
    if (machine === selectedMachine) {
      return CHART_COLORS.warning
    }

    // Otherwise, color by model
    if (model && MODEL_COLORS[model]) {
      return MODEL_COLORS[model]
    }

    return MODEL_COLORS.default
  }

  if (!processedData || processedData.length === 0) {
    return <Empty description="Aucune donnée disponible pour la comparaison des machines" />
  }

  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={processedData} layout="vertical" margin={{ top: 20, right: 30, left: 80, bottom: 10 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" horizontal={true} vertical={false} />
        <XAxis type="number" domain={[0, 100]} tickFormatter={(value) => `${value}%`} tick={{ fill: "#666" }} />
        <YAxis dataKey="machine" type="category" tick={{ fill: "#666" }} width={70} />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <ReferenceLine
          x={targetValue}
          stroke={CHART_COLORS.success}
          strokeDasharray="3 3"
          label={{
            value: `Objectif: ${targetValue}%`,
            position: "top",
            fill: CHART_COLORS.success,
            fontSize: 12,
          }}
        />
        <Bar dataKey="disponibilite" name="Disponibilité" radius={[0, 4, 4, 0]} isAnimationActive={!loading}>
          {processedData.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={getMachineColor(entry.machine, entry.model)}
              // Highlight the selected machine with a stroke
              stroke={entry.machine === selectedMachine ? "#000" : undefined}
              strokeWidth={entry.machine === selectedMachine ? 1 : 0}
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

export default React.memo(DisponibiliteByMachineChart)
