// Test GraphQL endpoint to see what's being returned
import fetch from 'node-fetch';

const graphqlQuery = `
  query getStopDashboardData {
    getAllMachineStops(limit: 100) {
      Date_Insert
      Duree_Arret
      Machine_ID
    }
  }
`;

const testGraphQL = async () => {
  try {
    console.log('Testing GraphQL endpoint...');
    
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: graphqlQuery
      })
    });

    const data = await response.json();
    
    if (data.errors) {
      console.error('GraphQL errors:', data.errors);
      return;
    }

    const allStops = data.data.getAllMachineStops;
    console.log('📊 Total stops from GraphQL:', allStops.length);
    
    if (allStops.length > 0) {
      console.log('First few stops:');
      allStops.slice(0, 5).forEach((stop, index) => {
        console.log(`${index + 1}. ${stop.Date_Insert} - Duration: ${stop.Duree_Arret} - Machine: ${stop.Machine_ID}`);
      });
      
      // Group by date like the frontend does
      const stopsByDate = {};
      allStops.forEach(stop => {
        if (stop.Date_Insert) {
          const dateMatch = stop.Date_Insert.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})/);
          if (dateMatch) {
            const [, day, month, year] = dateMatch;
            const dateKey = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            
            if (!stopsByDate[dateKey]) {
              stopsByDate[dateKey] = 0;
            }
            stopsByDate[dateKey]++;
          }
        }
      });
      
      console.log('\n📈 Grouped by date:');
      Object.entries(stopsByDate)
        .sort(([a], [b]) => new Date(a) - new Date(b))
        .slice(0, 10)
        .forEach(([date, count]) => {
          console.log(`${date}: ${count} stops`);
        });
    }
    
  } catch (error) {
    console.error('Error testing GraphQL:', error);
  }
};

testGraphQL();
