import React, { useEffect, useState } from 'react';
import { ArretProvider } from '../context/arret/ArretContext';
import ArretFilters from '../Components/arrets/ArretFilters';
import { Row, Col, Card, Typography, Divider } from 'antd';

const { Title, Text } = Typography;

/**
 * Manual test component for ArretFilters data flow
 * This component shows how data flows from ArretContext to ArretFilters
 * and back to parent components through the onFilterChange prop.
 */
const ArretFiltersTest = () => {
  const [filterState, setFilterState] = useState(null);
  const [filterHistory, setFilterHistory] = useState([]);
  
  // Handle filter changes coming from ArretFilters
  const handleFilterChange = (newFilters) => {
    setFilterState(newFilters);
    
    // Add to history with timestamp for debugging
    setFilterHistory(prev => [
      {
        timestamp: new Date().toISOString(),
        filters: { ...newFilters }
      },
      ...prev.slice(0, 4) // Keep only last 5 changes
    ]);
  };
  
  return (
    <ArretProvider>
      <div style={{ padding: 24 }}>
        <Title level={2}>Test de flux de données ArretFilters</Title>
        <Text>Ce composant teste le flux de données entre ArretContext et ArretFilters</Text>
        
        <Divider />
        
        <Row gutter={[24, 24]}>
          <Col span={24}>
            <Card title="Filtres">
              <ArretFilters onFilterChange={handleFilterChange} />
            </Card>
          </Col>
          
          <Col span={24}>
            <Card title="État actuel des filtres">
              <pre>{JSON.stringify(filterState, null, 2)}</pre>
            </Card>
          </Col>
          
          <Col span={24}>
            <Card title="Historique des changements">
              {filterHistory.map((item, index) => (
                <div key={index} style={{ marginBottom: 16 }}>
                  <Text strong>{item.timestamp}</Text>
                  <pre>{JSON.stringify(item.filters, null, 2)}</pre>
                  <Divider />
                </div>
              ))}
            </Card>
          </Col>
        </Row>
      </div>
    </ArretProvider>
  );
};

export default ArretFiltersTest;
