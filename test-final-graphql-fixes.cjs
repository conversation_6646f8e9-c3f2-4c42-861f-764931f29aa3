// Comprehensive test for all GraphQL field fixes
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testAllFixedQueries() {
  console.log('🧪 Testing ALL Fixed GraphQL Queries...\n');
  
  const baseURL = 'http://localhost:5000/api/graphql';
  const testFilters = {
    model: 'IPS',
    date: '2024-01-01',
    dateRangeType: 'day'
  };

  const tests = [
    {
      name: 'StopSideCard',
      query: `
        query($filters: StopFilterInput) {
          getStopSidecards(filters: $filters) {
            Arret_Totale
            Arret_Totale_nondeclare
          }
        }
      `,
      expectedFields: ['Arret_Totale', 'Arret_Totale_nondeclare'],
      dataPath: 'data.getStopSidecards'
    },
    {
      name: 'MachineNames',
      query: `
        query {
          getStopMachineNames {
            Machine_Name
          }
        }
      `,
      expectedFields: ['Machine_Name'],
      dataPath: 'data.getStopMachineNames',
      noFilters: true
    },
    {
      name: 'MachineModels',
      query: `
        query {
          getStopMachineModels {
            model
          }
        }
      `,
      expectedFields: ['model'],
      dataPath: 'data.getStopMachineModels',
      noFilters: true
    },
    {
      name: 'AllMachineStops',
      query: `
        query($filters: StopFilterInput) {
          getAllMachineStops(filters: $filters) {
            Date_Insert
            Machine_Name
            Part_NO
            Code_Stop
            Debut_Stop
            Fin_Stop_Time
            Regleur_Prenom
            duration_minutes
          }
        }
      `,
      expectedFields: ['Date_Insert', 'Machine_Name', 'Part_NO', 'Code_Stop', 'Debut_Stop', 'Fin_Stop_Time', 'Regleur_Prenom', 'duration_minutes'],
      dataPath: 'data.getAllMachineStops'
    },
    {
      name: 'Top5Stops',
      query: `
        query($filters: StopFilterInput) {
          getTop5Stops(filters: $filters) {
            stopName
            count
          }
        }
      `,
      expectedFields: ['stopName', 'count'],
      dataPath: 'data.getTop5Stops'
    }
  ];

  let passedTests = 0;
  let failedTests = 0;

  for (const test of tests) {
    try {
      console.log(`🧪 Testing ${test.name}...`);
      
      const response = await fetch(baseURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: test.query,
          variables: test.noFilters ? {} : { filters: testFilters }
        })
      });
      
      const result = await response.json();
      
      if (result.errors) {
        console.error(`❌ ${test.name} FAILED:`, result.errors[0].message);
        failedTests++;
        continue;
      }
      
      // Check if data exists
      const data = test.dataPath.split('.').reduce((obj, key) => obj?.[key], result);
      
      if (data === undefined || data === null) {
        console.log(`⚠️  ${test.name} returned no data (this might be expected)`);
        passedTests++;
        continue;
      }
      
      // For arrays, check the first item
      const sampleData = Array.isArray(data) ? data[0] : data;
      
      if (sampleData) {
        // Verify expected fields exist
        const missingFields = test.expectedFields.filter(field => !(field in sampleData));
        if (missingFields.length > 0) {
          console.error(`❌ ${test.name} FAILED: Missing fields: ${missingFields.join(', ')}`);
          failedTests++;
          continue;
        }
      }
      
      console.log(`✅ ${test.name} PASSED`);
      passedTests++;
      
    } catch (error) {
      console.error(`❌ ${test.name} ERROR:`, error.message);
      failedTests++;
    }
  }

  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Success Rate: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`);
  
  if (failedTests === 0) {
    console.log('\n🎉 All GraphQL queries are working correctly!');
  } else {
    console.log('\n⚠️  Some queries still need fixes.');
  }
}

testAllFixedQueries().catch(console.error);
