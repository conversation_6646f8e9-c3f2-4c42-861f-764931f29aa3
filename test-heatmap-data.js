// Test the durationTrend data structure
const testDurationTrend = [
  {"hour":0,"avgDuration":10.6471},
  {"hour":1,"avgDuration":30.3636},
  {"hour":2,"avgDuration":23.2},
  {"hour":7,"avgDuration":76.2917},
  {"hour":8,"avgDuration":19.0909},
  {"hour":9,"avgDuration":94.1875}
];

// Test the ArretHeatmapChart data validation logic
const safeData = Array.isArray(testDurationTrend) ? testDurationTrend : (testDurationTrend?.data || []);

console.log('Original data:', testDurationTrend);
console.log('Safe data:', safeData);
console.log('Is array?', Array.isArray(testDurationTrend));
console.log('Length:', safeData.length);
console.log('Has data?', safeData && safeData.length > 0);

if (safeData && safeData.length > 0) {
  console.log('✅ Chart should display data');
  console.log('First item:', safeData[0]);
  console.log('Has hour field?', safeData[0].hasOwnProperty('hour'));
  console.log('Has avgDuration field?', safeData[0].hasOwnProperty('avgDuration'));
} else {
  console.log('❌ Chart will show empty state');
}
