// Debug script to manually test the frontend's GraphQL queries
console.log('🔍 Testing Frontend GraphQL Queries...');

// Test the exact queries the frontend is using
const testFrontendQueries = async () => {
  try {
    // Test 1: Essential data query (what the frontend uses)
    console.log('🔍 Testing Frontend Essential Data Query...');
    const essentialResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query($filters: StopFilterInput) {
            getStopSidecards(filters: $filters) {
              Arret_Totale
              Arret_Totale_nondeclare
            }
          }
        `,
        variables: {
          filters: {
            model: 'IPS'
          }
        }
      })
    });
    
    const essentialResult = await essentialResponse.json();
    console.log('✅ Frontend Essential Data:', essentialResult);
    
    // Test 2: Table data query (what the frontend uses)
    console.log('🔍 Testing Frontend Table Data Query...');
    const tableResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query($filters: StopFilterInput) {
            getAllMachineStops(filters: $filters) {
              Date_Insert
              Machine_Name
              Part_NO
              Code_Stop
              Debut_Stop
              Fin_Stop_Time
              Regleur_Prenom
              duration_minutes
            }
          }
        `,
        variables: {
          filters: {
            model: 'IPS'
          }
        }
      })
    });
    
    const tableResult = await tableResponse.json();
    console.log('✅ Frontend Table Data:', {
      totalStops: tableResult.data?.getAllMachineStops?.length || 0,
      errors: tableResult.errors || null,
      sampleStops: tableResult.data?.getAllMachineStops?.slice(0, 3) || []
    });
    
    // Test 3: Machine models query
    console.log('🔍 Testing Machine Models Query...');
    const modelsResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getStopMachineModels {
              model
            }
          }
        `
      })
    });
    
    const modelsResult = await modelsResponse.json();
    console.log('✅ Machine Models:', modelsResult);
    
    // Test 4: Machine names query
    console.log('🔍 Testing Machine Names Query...');
    const namesResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query {
            getStopMachineNames {
              Machine_Name
            }
          }
        `
      })
    });
    
    const namesResult = await namesResponse.json();
    console.log('✅ Machine Names:', {
      totalMachines: namesResult.data?.getStopMachineNames?.length || 0,
      errors: namesResult.errors || null,
      sampleNames: namesResult.data?.getStopMachineNames?.slice(0, 5) || []
    });
    
    // Test 5: Manual calculation with correct logic
    if (tableResult.data?.getAllMachineStops) {
      const allStops = tableResult.data.getAllMachineStops;
      const declaredStops = allStops.filter(stop => {
        const codeStop = stop.Code_Stop;
        return codeStop && 
               codeStop !== 'Arrêt non déclaré' && 
               codeStop !== 'Non déclaré' &&
               codeStop !== 'Undeclared';
      });
      const undeclaredStops = allStops.length - declaredStops.length;
      
      console.log('🔍 Manual Calculation (Frontend Logic):', {
        totalStops: allStops.length,
        declaredStops: declaredStops.length,
        undeclaredStops: undeclaredStops,
        percentageUndeclared: ((undeclaredStops / allStops.length) * 100).toFixed(1),
        backendUndeclared: essentialResult.data?.getStopSidecards?.Arret_Totale_nondeclare || 0,
        matches: undeclaredStops === (essentialResult.data?.getStopSidecards?.Arret_Totale_nondeclare || 0)
      });
    }
    
  } catch (error) {
    console.error('❌ Error testing frontend queries:', error);
  }
};

// Run the test
testFrontendQueries();
