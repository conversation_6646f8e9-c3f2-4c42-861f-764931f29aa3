import React, { useState } from 'react';
import { Card, Button, Space, Typography, Alert, Badge, message, Select, Input } from 'antd';
import { useSSE } from '../context/SSEContext';
import superagent from 'superagent';

const { Title, Text } = Typography;
const { Option } = Select;

const NotificationsTest = () => {
  const [creating, setCreating] = useState(false);
  const [testNotification, setTestNotification] = useState({
    title: 'Test Notification',
    message: 'This is a test notification',
    priority: 'medium',
    category: 'info'
  });
  const {
    notifications,
    unreadCount,
    connectionStatus,
    connectionStats,
    connect,
    markAsRead,
    acknowledgeNotification,
    isConnected,
    isConnecting,
    hasError
  } = useSSE();

  const handleTestMarkAsRead = () => {
    if (notifications.length > 0) {
      markAsRead(notifications[0].id);
    }
  };

  const handleTestAcknowledge = () => {
    if (notifications.length > 0) {
      acknowledgeNotification(notifications[0].id);
    }
  };

  const handleCreateTestNotification = async () => {
    setCreating(true);
    try {
      const response = await superagent
        .withCredentials()
        .post('/api/notifications')
        .send(testNotification)
        .withCredentials();
      console.log('✅ Test notification created:', response.data);
      message.success('Test notification created successfully!');
    } catch (error) {
      console.error('❌ Failed to create test notification:', error);
      message.error('Failed to create test notification: ' + (error.response?.data?.message || error.message));
    } finally {
      setCreating(false);
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'success';
      case 'connecting': return 'processing';
      case 'error': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>Notifications System Test</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Connection Status */}
        <Card title="Connection Status">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>Status: </Text>
              <Badge status={getConnectionStatusColor()} text={connectionStatus.toUpperCase()} />
            </div>
            <div>
              <Text strong>Is Connected: </Text>
              <Text type={isConnected ? 'success' : 'danger'}>
                {isConnected ? 'YES' : 'NO'}
              </Text>
            </div>
            <div>
              <Text strong>Is Connecting: </Text>
              <Text type={isConnecting ? 'warning' : 'secondary'}>
                {isConnecting ? 'YES' : 'NO'}
              </Text>
            </div>
            <div>
              <Text strong>Has Error: </Text>
              <Text type={hasError ? 'danger' : 'success'}>
                {hasError ? 'YES' : 'NO'}
              </Text>
            </div>
            
            {connectionStats.connectedAt && (
              <>
                <div>
                  <Text strong>Connected At: </Text>
                  <Text>{new Date(connectionStats.connectedAt).toLocaleString()}</Text>
                </div>
                <div>
                  <Text strong>Messages Received: </Text>
                  <Text>{connectionStats.messagesReceived}</Text>
                </div>
                <div>
                  <Text strong>Reconnect Attempts: </Text>
                  <Text>{connectionStats.reconnectAttempts}</Text>
                </div>
              </>
            )}
            
            <Button 
              type="primary" 
              onClick={connect}
              disabled={isConnecting}
              loading={isConnecting}
            >
              {isConnecting ? 'Connecting...' : 'Reconnect'}
            </Button>
          </Space>
        </Card>

        {/* Notifications Summary */}
        <Card title="Notifications Summary">
          <Space direction="vertical">
            <div>
              <Text strong>Total Notifications: </Text>
              <Badge count={notifications.length} />
            </div>
            <div>
              <Text strong>Unread Count: </Text>
              <Badge count={unreadCount} />
            </div>
          </Space>
        </Card>

        {/* Create Test Notification */}
        <Card title="Create Test Notification">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text>Title: </Text>
              <Input 
                value={testNotification.title}
                onChange={(e) => setTestNotification({...testNotification, title: e.target.value})}
                placeholder="Notification title"
              />
            </div>
            <div>
              <Text>Message: </Text>
              <Input.TextArea 
                value={testNotification.message}
                onChange={(e) => setTestNotification({...testNotification, message: e.target.value})}
                placeholder="Notification message"
                rows={3}
              />
            </div>
            <div>
              <Text>Priority: </Text>
              <Select 
                value={testNotification.priority}
                onChange={(value) => setTestNotification({...testNotification, priority: value})}
                style={{ width: 120 }}
              >
                <Option value="low">Low</Option>
                <Option value="medium">Medium</Option>
                <Option value="high">High</Option>
                <Option value="critical">Critical</Option>
              </Select>
            </div>
            <div>
              <Text>Category: </Text>
              <Select 
                value={testNotification.category}
                onChange={(value) => setTestNotification({...testNotification, category: value})}
                style={{ width: 150 }}
              >
                <Option value="info">Info</Option>
                <Option value="alert">Alert</Option>
                <Option value="maintenance">Maintenance</Option>
                <Option value="production">Production</Option>
                <Option value="quality">Quality</Option>
              </Select>
            </div>
            <Button 
              type="primary" 
              onClick={handleCreateTestNotification}
              loading={creating}
            >
              Create Test Notification
            </Button>
          </Space>
        </Card>

        {/* Test Actions */}
        <Card title="Test Actions">
          <Space>
            <Button 
              type="primary" 
              onClick={handleTestMarkAsRead}
              disabled={notifications.length === 0}
            >
              Mark First as Read
            </Button>
            <Button 
              type="default" 
              onClick={handleTestAcknowledge}
              disabled={notifications.length === 0}
            >
              Acknowledge First
            </Button>
          </Space>
        </Card>

        {/* Notifications List */}
        <Card title="Notifications List">
          {notifications.length === 0 ? (
            <Alert 
              message="No notifications" 
              description="No notifications found. Check the console for API connection issues."
              type="info" 
            />
          ) : (
            <Space direction="vertical" style={{ width: '100%' }}>
              {notifications.slice(0, 5).map((notification, index) => (
                <Card 
                  key={notification.id} 
                  size="small"
                  style={{ 
                    border: notification.isUnread ? '2px solid #1890ff' : '1px solid #f0f0f0',
                    backgroundColor: notification.isUnread ? '#f0f7ff' : 'white'
                  }}
                >
                  <div>
                    <Text strong>{notification.title}</Text>
                    <br />
                    <Text type="secondary">{notification.message}</Text>
                    <br />
                    <Space>
                      <Text type="secondary">Priority: {notification.priority}</Text>
                      <Text type="secondary">Category: {notification.category}</Text>
                      <Text type="secondary">
                        Created: {new Date(notification.created_at).toLocaleString()}
                      </Text>
                      {notification.read_at && (
                        <Text type="success">Read</Text>
                      )}
                      {notification.acknowledged_at && (
                        <Text type="success">Acknowledged</Text>
                      )}
                    </Space>
                  </div>
                </Card>
              ))}
              {notifications.length > 5 && (
                <Text type="secondary">
                  ... and {notifications.length - 5} more notifications
                </Text>
              )}
            </Space>
          )}
        </Card>
      </Space>
    </div>
  );
};

export default NotificationsTest;