import http from 'http';

function testGraphQL() {
  const postData = JSON.stringify({
    query: `{
      getFinalComprehensiveStopData(filters: { limit: 5 }) {
        totalRecords
        queryExecutionTime
        allStops {
          Machine_Name
          Code_Stop
          duration_minutes
        }
        topStops {
          stopName
          count
        }
        sidecards {
          Arret_Totale
          Arret_Totale_nondeclare
        }
      }
    }`
  });

  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/graphql',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        if (result.errors) {
          console.error('❌ GraphQL errors:', result.errors);
        } else {
          console.log('✅ GraphQL test successful!');
          const stopData = result.data.getFinalComprehensiveStopData;
          console.log(`⏱️  Query time: ${stopData.queryExecutionTime}ms`);
          console.log(`📊 Total records: ${stopData.totalRecords}`);
          console.log(`🏭 Stops fetched: ${stopData.allStops.length}`);
          console.log(`🔝 Top stops: ${stopData.topStops.length}`);
          console.log(`📋 Sidecards:`, stopData.sidecards);
          
          if (stopData.allStops.length > 0) {
            console.log('Sample stop:', stopData.allStops[0]);
          }
        }
      } catch (err) {
        console.error('❌ Failed to parse response:', err);
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`❌ Request error: ${e.message}`);
  });

  req.write(postData);
  req.end();
}

console.log('🧪 Testing optimized GraphQL endpoint...');
testGraphQL();
