// Test direct API calls to bypass GraphQL hook issues
const axios = require('axios');

const baseURL = 'http://localhost:5000';

async function testDirectAPI() {
  try {
    console.log('Testing direct API calls...');
    
    // Test machine models
    console.log('1. Testing machine models...');
    const modelsResponse = await axios.get(`${baseURL}/api/unique-machine-models`);
    console.log('✅ Machine models:', modelsResponse.data);
    
    // Test machine names
    console.log('2. Testing machine names...');
    const namesResponse = await axios.get(`${baseURL}/api/unique-machine-names`);
    console.log('✅ Machine names:', namesResponse.data);
    
    // Test stats
    console.log('3. Testing stats...');
    const statsResponse = await axios.get(`${baseURL}/api/sidecards-arret`);
    console.log('✅ Stats:', statsResponse.data);
    
    console.log('🎉 All API endpoints working!');
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

testDirectAPI();
