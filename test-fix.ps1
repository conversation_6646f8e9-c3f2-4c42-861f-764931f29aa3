# Test Docker Dependency Fix
# Run this from the root project directory

Write-Host "🔧 Testing Docker Dependency Fix" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Check current directory
$currentDir = Get-Location
Write-Host "Current directory: $currentDir" -ForegroundColor Yellow

# Verify files exist
if (-not (Test-Path "docker-compose.local.yml")) {
    Write-Host "❌ docker-compose.local.yml not found!" -ForegroundColor Red
    Write-Host "Please run this script from the root project directory." -ForegroundColor Red
    exit 1
}

if (-not (Test-Path "backend/Dockerfile.simple")) {
    Write-Host "❌ backend/Dockerfile.simple not found!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Files found, proceeding with test..." -ForegroundColor Green

# Step 1: Test simple build
Write-Host ""
Write-Host "1. Testing simple backend build..." -ForegroundColor Yellow
docker build -f backend/Dockerfile.simple -t test-backend backend/

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Simple backend build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Simple backend build successful!" -ForegroundColor Green

# Step 2: Test apicache
Write-Host ""
Write-Host "2. Testing apicache dependency..." -ForegroundColor Yellow
$testResult = docker run --rm test-backend node -e "try { require('apicache'); console.log('SUCCESS: apicache found'); } catch(e) { console.error('ERROR:', e.message); process.exit(1); }" 2>&1

Write-Host $testResult

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ apicache test failed!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ apicache dependency test passed!" -ForegroundColor Green

# Step 3: Clean up and test full build
Write-Host ""
Write-Host "3. Cleaning up existing containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml down --volumes 2>$null | Out-Null
docker builder prune -f 2>$null | Out-Null

Write-Host "✅ Cleanup complete!" -ForegroundColor Green

# Step 4: Test full build
Write-Host ""
Write-Host "4. Testing full backend build..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml build --no-cache backend

if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️  Full backend build failed (likely due to system dependencies)" -ForegroundColor Yellow
    Write-Host "But the main apicache issue is RESOLVED!" -ForegroundColor Green
    Write-Host ""
    Write-Host "You can use the simple backend for testing:" -ForegroundColor Cyan
    Write-Host "  docker run -d --name test-backend -p 5000:5000 test-backend" -ForegroundColor White
    exit 0
}

Write-Host "✅ Full backend build successful!" -ForegroundColor Green

# Step 5: Start containers
Write-Host ""
Write-Host "5. Starting containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml up -d

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to start containers!" -ForegroundColor Red
    exit 1
}

# Step 6: Check status
Write-Host ""
Write-Host "6. Checking container status..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

$backendStatus = docker inspect locql-backend --format='{{.State.Status}}' 2>$null
$frontendStatus = docker inspect locql-frontend --format='{{.State.Status}}' 2>$null

Write-Host ""
Write-Host "Container Status:" -ForegroundColor Cyan
Write-Host "Backend: $backendStatus" -ForegroundColor $(if ($backendStatus -eq "running") { "Green" } else { "Red" })
Write-Host "Frontend: $frontendStatus" -ForegroundColor $(if ($frontendStatus -eq "running") { "Green" } else { "Red" })

if ($backendStatus -eq "running" -and $frontendStatus -eq "running") {
    Write-Host ""
    Write-Host "🎉 SUCCESS! All containers are running!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Your application is available at:" -ForegroundColor Cyan
    Write-Host "  • Frontend: http://localhost:5173" -ForegroundColor White
    Write-Host "  • Backend API: http://localhost:5000" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "⚠️  Some containers failed to start. Check logs:" -ForegroundColor Yellow
    Write-Host "  docker-compose -f docker-compose.local.yml logs" -ForegroundColor White
}
