import db from "../db.js";
import NotificationService from "../utils/notificationService.js";
import sseNotificationService from "./SSENotificationService.js";

/**
 * Service for monitoring machine data and generating automated alerts
 */
class MachineAlertService {
  constructor() {
    this.alertThresholds = {
      trs: {
        warning: 70,    // TRS below 70% triggers warning
        critical: 50    // TRS below 50% triggers critical alert
      },
      rejectRate: {
        warning: 5,     // Reject rate above 5% triggers warning
        critical: 10    // Reject rate above 10% triggers critical alert
      },
      downtimeMinutes: {
        warning: 5,     // Machine offline for 5+ minutes triggers warning
        critical: 15    // Machine offline for 15+ minutes triggers critical alert
      }
    };

    this.alertHistory = new Map(); // Track recent alerts to prevent spam
    this.alertCooldown = 15 * 60 * 1000; // 15 minutes cooldown between similar alerts
    this.monitoringInterval = null;
    this.isMonitoring = false;
  }

  /**
   * Start monitoring machine data for alerts
   * @param {number} intervalMs - Monitoring interval in milliseconds (default: 60000 = 1 minute)
   */
  startMonitoring(intervalMs = 60000) {
    if (this.isMonitoring) {
      console.log('Machine alert monitoring is already running');
      return;
    }

    console.log(`Starting machine alert monitoring with ${intervalMs}ms interval`);
    this.isMonitoring = true;

    this.monitoringInterval = setInterval(async () => {
      try {
        await this.checkAllMachines();
      } catch (error) {
        console.error('Error in machine alert monitoring:', error);
      }
    }, intervalMs);

    // Run initial check
    this.checkAllMachines();
  }

  /**
   * Stop monitoring machine data
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('Machine alert monitoring stopped');
  }

  /**
   * Check all machines for alert conditions
   */
  async checkAllMachines() {
    try {
      console.log(`🔍 Machine alert check started`);

      const [machines] = await db.execute(`
        SELECT
          id,
          Machine_Name,
          Etat,
          TRS,
          Quantite_Bon,
          Quantite_Rejet,
          Stop_Time,
          Code_arret,
          TIMESTAMPDIFF(MINUTE,
            CASE
              WHEN Etat = 'off' AND Stop_Time IS NOT NULL
              THEN STR_TO_DATE(Stop_Time, '%H:%i:%s')
              ELSE NOW()
            END,
            NOW()
          ) as downtime_minutes
        FROM real_time_table
      `);

      console.log(`📊 Checking ${machines.length} machines for alerts...`);

      let alertsProcessed = 0;
      let alertsCreated = 0;
      let alertsSkipped = 0;

      for (const machine of machines) {
        const result = await this.checkMachineAlerts(machine);
        if (result) {
          alertsProcessed++;
          if (result.created) alertsCreated++;
          if (result.skipped) alertsSkipped++;
        }
      }

      if (alertsProcessed > 0) {
        console.log(`📈 Machine alert check completed: ${alertsProcessed} alerts processed, ${alertsCreated} created, ${alertsSkipped} skipped`);
      }

      // Clean up old alert history
      this.cleanupAlertHistory();
    } catch (error) {
      console.error('❌ Error checking machine alerts:', error);
    }
  }

  /**
   * Check individual machine for alert conditions
   * @param {Object} machine - Machine data from database
   * @returns {Object|null} - Alert result if any alert was processed
   */
  async checkMachineAlerts(machine) {
    const machineId = machine.id;
    const machineName = machine.Machine_Name;
    let alertResult = null;

    // Check machine downtime
    if (machine.Etat === 'off') {
      const downtimeResult = await this.checkDowntimeAlert(machine);
      if (downtimeResult) alertResult = downtimeResult;
    }

    // Check TRS performance
    if (machine.TRS !== null && machine.TRS !== '') {
      const trsResult = await this.checkTRSAlert(machine);
      if (trsResult) alertResult = trsResult;
    }

    // Check reject rate
    const rejectResult = await this.checkRejectRateAlert(machine);
    if (rejectResult) alertResult = rejectResult;

    return alertResult;
  }

  /**
   * Check for machine downtime alerts
   * @param {Object} machine - Machine data
   * @returns {Object|null} - Alert result if alert was processed
   */
  async checkDowntimeAlert(machine) {
    const downtimeMinutes = machine.downtime_minutes || 0;
    const machineId = machine.id;
    const machineName = machine.Machine_Name;

    let alertLevel = null;
    let priority = 'medium';
    let severity = 'warning';

    if (downtimeMinutes >= this.alertThresholds.downtimeMinutes.critical) {
      alertLevel = 'critical';
      priority = 'critical';
      severity = 'critical';
    } else if (downtimeMinutes >= this.alertThresholds.downtimeMinutes.warning) {
      alertLevel = 'warning';
      priority = 'high';
      severity = 'warning';
    }

    if (alertLevel && this.shouldSendAlert(machineId, 'downtime', alertLevel)) {
      const title = `Machine ${machineName} - Downtime Alert`;
      const message = `Machine ${machineName} has been offline for ${downtimeMinutes} minutes. ` +
                     `Stop reason: ${machine.Code_arret || 'Unknown'}`;

      const result = await this.createMachineAlert({
        title,
        message,
        machineId,
        machineName,
        priority,
        severity,
        alertType: 'downtime',
        alertLevel,
        metadata: {
          downtimeMinutes,
          stopReason: machine.Code_arret
        }
      });

      return result;
    }

    return null;
  }

  /**
   * Check for TRS performance alerts
   * @param {Object} machine - Machine data
   * @returns {Object|null} - Alert result if alert was processed
   */
  async checkTRSAlert(machine) {
    const trs = parseFloat(machine.TRS);
    if (isNaN(trs)) return null;

    const machineId = machine.id;
    const machineName = machine.Machine_Name;

    let alertLevel = null;
    let priority = 'medium';
    let severity = 'warning';

    if (trs < this.alertThresholds.trs.critical) {
      alertLevel = 'critical';
      priority = 'critical';
      severity = 'critical';
    } else if (trs < this.alertThresholds.trs.warning) {
      alertLevel = 'warning';
      priority = 'high';
      severity = 'warning';
    }

    if (alertLevel && this.shouldSendAlert(machineId, 'trs', alertLevel)) {
      const title = `Machine ${machineName} - Low TRS Performance`;
      const message = `Machine ${machineName} TRS has dropped to ${trs}%, below the ${alertLevel} threshold of ${
        alertLevel === 'critical' ? this.alertThresholds.trs.critical : this.alertThresholds.trs.warning
      }%.`;

      const result = await this.createMachineAlert({
        title,
        message,
        machineId,
        machineName,
        priority,
        severity,
        alertType: 'trs',
        alertLevel,
        metadata: {
          currentTRS: trs,
          threshold: alertLevel === 'critical' ? this.alertThresholds.trs.critical : this.alertThresholds.trs.warning
        }
      });

      return result;
    }

    return null;
  }

  /**
   * Check for high reject rate alerts
   * @param {Object} machine - Machine data
   * @returns {Object|null} - Alert result if alert was processed
   */
  async checkRejectRateAlert(machine) {
    const quantiteBon = parseInt(machine.Quantite_Bon) || 0;
    const quantiteRejet = parseInt(machine.Quantite_Rejet) || 0;
    const totalProduction = quantiteBon + quantiteRejet;

    if (totalProduction === 0) return null; // No production to check

    const rejectRate = (quantiteRejet / totalProduction) * 100;
    const machineId = machine.id;
    const machineName = machine.Machine_Name;

    let alertLevel = null;
    let priority = 'medium';
    let severity = 'warning';

    if (rejectRate > this.alertThresholds.rejectRate.critical) {
      alertLevel = 'critical';
      priority = 'critical';
      severity = 'critical';
    } else if (rejectRate > this.alertThresholds.rejectRate.warning) {
      alertLevel = 'warning';
      priority = 'high';
      severity = 'warning';
    }

    if (alertLevel && this.shouldSendAlert(machineId, 'reject_rate', alertLevel)) {
      const title = `Machine ${machineName} - High Reject Rate`;
      const message = `Machine ${machineName} reject rate is ${rejectRate.toFixed(1)}%, above the ${alertLevel} threshold of ${
        alertLevel === 'critical' ? this.alertThresholds.rejectRate.critical : this.alertThresholds.rejectRate.warning
      }%. Total production: ${totalProduction}, Rejects: ${quantiteRejet}.`;

      const result = await this.createMachineAlert({
        title,
        message,
        machineId,
        machineName,
        priority,
        severity,
        alertType: 'reject_rate',
        alertLevel,
        metadata: {
          rejectRate: rejectRate.toFixed(1),
          totalProduction,
          rejects: quantiteRejet,
          threshold: alertLevel === 'critical' ? this.alertThresholds.rejectRate.critical : this.alertThresholds.rejectRate.warning
        }
      });

      return result;
    }

    return null;
  }

  /**
   * Create a machine alert notification
   * @param {Object} alertData - Alert data
   */
  async createMachineAlert(alertData) {
    try {
      console.log(`🔍 Attempting to create machine alert: ${alertData.title}`);

      const notification = {
        title: alertData.title,
        message: alertData.message,
        category: 'machine_alert',
        priority: alertData.priority,
        severity: alertData.severity,
        source: 'machine_monitoring',
        machine_id: alertData.machineId,
        userId: null // Broadcast to all users
      };

      console.log(`🔍 About to call NotificationService.createNotification with:`, notification);
      
      // Try to create notification in database
      try {
        const savedNotification = await NotificationService.createNotification(notification);
        console.log(`✅ Machine alert saved to database: ${savedNotification.id}`);
        
        // Broadcast notification via SSE to all connected users
        console.log(`📡 Broadcasting machine alert via SSE: ${alertData.title}`);
        await sseNotificationService.broadcastNotification(savedNotification);
        
        // Record alert in history
        this.recordAlert(alertData.machineId, alertData.alertType, alertData.alertLevel);
        
        console.log(`🔔 Machine alert created and broadcast successfully: ${alertData.title} (ID: ${savedNotification.id})`);
        return { created: true, id: savedNotification.id };
        
      } catch (dbError) {
        console.error(`❌ Database save failed for machine alert:`, dbError.message);
        
        // Fallback: broadcast mock notification via SSE only
        const mockNotification = {
          id: Date.now(),
          title: notification.title,
          message: notification.message,
          category: notification.category,
          priority: notification.priority,
          severity: notification.severity,
          source: notification.source,
          machine_id: notification.machine_id,
          user_id: notification.userId,
          timestamp: new Date().toISOString(),
          created_at: new Date().toISOString(),
          read_at: null,
          acknowledged_at: null,
          read: false,
          acknowledged: false
        };

        console.log(`� Broadcasting fallback mock notification via SSE: ${alertData.title}`);
        try {
          await sseNotificationService.broadcastNotification(mockNotification);
          console.log(`⚠️ Machine alert broadcast as mock only (database failed): ${alertData.title} (ID: ${mockNotification.id})`);
        } catch (broadcastError) {
          console.error(`❌ SSE broadcast also failed:`, broadcastError);
        }
        
        // Record alert in history
        this.recordAlert(alertData.machineId, alertData.alertType, alertData.alertLevel);
        
        return { created: false, id: mockNotification.id, error: dbError.message };
      }
    } catch (error) {
      console.error('❌ Error creating machine alert:', error);
      throw error;
    }
  }

  /**
   * Check if an alert should be sent (considering cooldown period)
   * @param {number} machineId - Machine ID
   * @param {string} alertType - Type of alert (downtime, trs, reject_rate)
   * @param {string} alertLevel - Alert level (warning, critical)
   * @returns {boolean} - Whether to send the alert
   */
  shouldSendAlert(machineId, alertType, alertLevel) {
    const alertKey = `${machineId}_${alertType}_${alertLevel}`;
    const lastAlert = this.alertHistory.get(alertKey);
    const now = Date.now();

    if (!lastAlert || (now - lastAlert) > this.alertCooldown) {
      return true;
    }

    return false;
  }

  /**
   * Record an alert in the history
   * @param {number} machineId - Machine ID
   * @param {string} alertType - Type of alert
   * @param {string} alertLevel - Alert level
   */
  recordAlert(machineId, alertType, alertLevel) {
    const alertKey = `${machineId}_${alertType}_${alertLevel}`;
    this.alertHistory.set(alertKey, Date.now());
  }

  /**
   * Clean up old alert history entries
   */
  cleanupAlertHistory() {
    const now = Date.now();
    const cutoffTime = now - (this.alertCooldown * 2); // Keep history for 2x cooldown period

    for (const [key, timestamp] of this.alertHistory.entries()) {
      if (timestamp < cutoffTime) {
        this.alertHistory.delete(key);
      }
    }
  }

  /**
   * Update alert thresholds
   * @param {Object} newThresholds - New threshold values
   */
  updateThresholds(newThresholds) {
    this.alertThresholds = { ...this.alertThresholds, ...newThresholds };
    console.log('Machine alert thresholds updated:', this.alertThresholds);
  }

  /**
   * Get current alert statistics
   * @returns {Object} - Alert statistics
   */
  getAlertStats() {
    return {
      isMonitoring: this.isMonitoring,
      alertHistorySize: this.alertHistory.size,
      thresholds: this.alertThresholds,
      cooldownPeriod: this.alertCooldown
    };
  }
}

// Create singleton instance
const machineAlertService = new MachineAlertService();

export default machineAlertService;
