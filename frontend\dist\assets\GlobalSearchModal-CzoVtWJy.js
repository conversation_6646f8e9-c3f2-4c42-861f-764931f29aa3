import{r as e,j as t}from"./index-Nnj1g72A.js";import{r}from"./react-vendor-tYPmozCJ.js";import{M as s,aJ as a,T as n,ab as o,u as i,L as c,E as l,an as d,O as h,S as u,a8 as p,d as y,c as m,f as g,e as x,aO as j,ae as f,A as w,aP as b}from"./antd-vendor-4OvKHZ_k.js";const S=new class{constructor(){this.baseURL="http://localhost:5000/search"}async globalSearch(t,r={}){try{const{page:s=1,size:a=20}=r;return(await e.get(`${this.baseURL}/global`).query({q:t,page:s,size:a}).set("withCredentials",!0).retry(2)).body}catch(s){throw this.handleError(s)}}async searchMachineSessions(t={}){try{return(await e.get(`${this.baseURL}/sessions`).query(t).set("withCredentials",!0).retry(2)).body}catch(r){throw this.handleError(r)}}async searchReports(t={}){try{return(await e.get(`${this.baseURL}/reports`).query(t).set("withCredentials",!0).retry(2)).body}catch(r){throw this.handleError(r)}}async searchProductionData(t={}){try{const r=(await e.get(`${this.baseURL}/production`).query(t).set("withCredentials",!0).timeout(1e4).retry(2)).body;return r.searchMethod,r}catch(r){if("ECONNABORTED"===r.code||r.message.includes("timeout"))throw new Error("La recherche a pris trop de temps. Veuillez réessayer avec des critères plus spécifiques.");throw this.handleError(r)}}async searchMachineStops(t={}){try{const r=(await e.get(`${this.baseURL}/stops`).query(t).set("withCredentials",!0).timeout(1e4).retry(2)).body;return r.searchMethod,r}catch(r){if("ECONNABORTED"===r.code||r.message.includes("timeout"))throw new Error("La recherche a pris trop de temps. Veuillez réessayer avec des critères plus spécifiques.");throw this.handleError(r)}}async getMachinePerformanceAnalytics(t,r){try{return(await e.get(`${this.baseURL}/analytics/machine-performance`).query({dateFrom:t,dateTo:r}).set("withCredentials",!0).retry(2)).body}catch(s){throw this.handleError(s)}}async getSuggestions(t,r="machineName",s=10){try{return(await e.get(`${this.baseURL}/suggest`).query({q:t,field:r,size:s}).set("withCredentials",!0).retry(2)).body.suggestions}catch(a){return[]}}async checkHealth(){try{return(await e.get(`${this.baseURL}/health`).set("withCredentials",!0).retry(2)).body}catch(t){return{elasticsearch:{status:"error",error:t.message}}}}async reindex(t="all",r={}){try{return(await e.post(`${this.baseURL}/reindex`).send({index:t,...r}).set("withCredentials",!0).retry(2)).body}catch(s){throw this.handleError(s)}}handleError(e){return e.response?{message:e.response.data.error||"Search request failed",details:e.response.data.details,status:e.response.status}:e.request?{message:"No response from search service",details:"Please check your connection and try again"}:{message:"Search request failed",details:e.message}}buildMachineSessionFilters(e){const t={};return e.query&&(t.q=e.query),e.machineId&&(t.machineId=e.machineId),e.machineModel&&(t.machineModel=e.machineModel),e.status&&(t.status=e.status),e.shift&&(t.shift=e.shift),e.dateFrom&&(t.dateFrom=e.dateFrom),e.dateTo&&(t.dateTo=e.dateTo),e.page&&(t.page=e.page),e.size&&(t.size=e.size),t}buildReportFilters(e){const t={};return e.query&&(t.q=e.query),e.type&&(t.type=e.type),e.machineId&&(t.machineId=e.machineId),e.generatedBy&&(t.generatedBy=e.generatedBy),e.dateFrom&&(t.dateFrom=e.dateFrom),e.dateTo&&(t.dateTo=e.dateTo),e.page&&(t.page=e.page),e.size&&(t.size=e.size),t}formatSearchResults(e,t){return e&&e.results?e.results.map((e=>({id:e.id,type:e.type||t,score:e.score,title:this.extractTitle(e.data,e.type),description:this.extractDescription(e.data,e.type),timestamp:e.data.timestamp||e.data.generatedAt||e.data.date,highlight:e.highlight,data:e.data}))):[]}extractTitle(e,t){switch(t){case"machine-session":return`${e.machineName} - Session ${e.sessionId}`;case"production-data":return`${e.machineName} - Production ${e.date}`;case"machine-stop":return`${e.machineName} - Arrêt ${e.stopCode}`;case"report":return e.title||`${e.type} Report`;default:return e.title||e.machineName||"Unknown"}}extractDescription(e,t){var r,s,a,n;switch(t){case"machine-session":return`Operator: ${e.operator||"Unknown"}, TRS: ${e.trs||0}%, Production: ${(null==(r=e.production)?void 0:r.total)||0}`;case"production-data":return`OEE: ${(null==(a=null==(s=e.performance)?void 0:s.oee)?void 0:a.toFixed(1))||0}%, Production: ${(null==(n=e.production)?void 0:n.good)||0} pièces, Opérateur: ${e.operator||"N/A"}`;case"machine-stop":return`${e.stopDescription||e.stopCode}, Durée: ${e.duration||0} min, Catégorie: ${e.stopCategory||"N/A"}`;case"report":return e.description||`Generated by ${e.generatedBy||"Unknown"}`;default:return JSON.stringify(e).substring(0,100)+"..."}}createDebouncedSearch(e,t=300){let r;return(...s)=>(clearTimeout(r),new Promise(((a,n)=>{r=setTimeout((async()=>{try{const t=await e(...s);a(t)}catch(t){n(t)}}),t)})))}},{Text:v,Title:T}=n,{Search:C}=a,$=({visible:e,onClose:a,onResultSelect:n})=>{const[T,$]=r.useState(""),[E,R]=r.useState([]),[z,q]=r.useState(!1),[M,L]=r.useState(null),[F,I]=r.useState(null),N=r.useCallback(S.createDebouncedSearch(S.globalSearch.bind(S),300),[]);r.useEffect((()=>{T.trim().length>=2?D(T.trim()):(R([]),I(null),L(null))}),[T]);const D=async e=>{q(!0),L(null);try{const t=await N(e,{size:20}),r=S.formatSearchResults(t,"global");R(r),I({total:t.total,query:e,timestamp:new Date})}catch(t){L(t.message||"Search failed"),R([]),I(null)}finally{q(!1)}},A=e=>{n&&n(e),a()},U=e=>{switch(e){case"production-data":return t.jsx(w,{style:{color:"#52c41a"}});case"machine-stop":return t.jsx(p,{style:{color:"#ff4d4f"}});case"machine-session":return t.jsx(w,{style:{color:"#1890ff"}});case"report":return t.jsx(f,{style:{color:"#722ed1"}});default:return t.jsx(d,{style:{color:"#666"}})}},k=e=>{const r={"production-data":{color:"green",text:"Production"},"machine-stop":{color:"red",text:"Arrêt"},"machine-session":{color:"blue",text:"Session"},report:{color:"purple",text:"Rapport"},"maintenance-log":{color:"orange",text:"Maintenance"}}[e]||{color:"default",text:"Inconnu"};return t.jsx(g,{color:r.color,children:r.text})},O=e=>{if(!e)return null;const r=Object.keys(e);return 0===r.length?null:t.jsx("div",{style:{marginTop:8},children:r.map((r=>t.jsxs("div",{style:{marginBottom:4},children:[t.jsxs(v,{type:"secondary",style:{fontSize:"12px"},children:[t.jsx(b,{})," ",r,":"]}),t.jsx("div",{style:{fontSize:"12px",marginLeft:16},dangerouslySetInnerHTML:{__html:e[r].join(" ... ")}})]},r)))})};return t.jsxs(s,{title:t.jsxs(u,{children:[t.jsx(d,{}),t.jsx("span",{children:"Global Search"})]}),open:e,onCancel:()=>{$(""),R([]),I(null),L(null),a()},footer:null,width:800,style:{top:50},destroyOnClose:!0,children:[t.jsx("div",{style:{marginBottom:16},children:t.jsx(C,{placeholder:"Rechercher dans les données de production, arrêts, sessions et rapports...",value:T,onChange:e=>$(e.target.value),size:"large",allowClear:!0,autoFocus:!0})}),F&&t.jsx("div",{style:{marginBottom:16},children:t.jsxs(v,{type:"secondary",style:{fontSize:"12px"},children:["Found ",F.total,' results for "',F.query,'" (',((Date.now()-F.timestamp.getTime())/1e3).toFixed(2),"s)"]})}),M&&t.jsx(o,{message:"Search Error",description:M,type:"error",showIcon:!0,style:{marginBottom:16}}),t.jsx("div",{style:{maxHeight:"60vh",overflowY:"auto"},children:z?t.jsxs("div",{style:{textAlign:"center",padding:"40px 0"},children:[t.jsx(i,{size:"large"}),t.jsx("div",{style:{marginTop:16},children:t.jsx(v,{type:"secondary",children:"Searching..."})})]}):E.length>0?t.jsx(c,{dataSource:E,renderItem:e=>t.jsxs(c.Item,{onClick:()=>A(e),style:{cursor:"pointer",padding:"12px 16px",borderRadius:"6px",margin:"4px 0",transition:"all 0.2s",border:"1px solid transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="#f5f5f5",e.currentTarget.style.borderColor="#d9d9d9"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.borderColor="transparent"},children:[t.jsx(c.Item.Meta,{avatar:U(e.type),title:t.jsxs(u,{children:[t.jsx(v,{strong:!0,children:e.title}),k(e.type),e.score&&t.jsx(m,{title:`Relevance score: ${e.score.toFixed(2)}`,children:t.jsxs(g,{color:"purple",style:{fontSize:"10px"},children:[Math.round(100*e.score),"%"]})})]}),description:t.jsxs("div",{children:[t.jsx(v,{type:"secondary",children:e.description}),e.timestamp&&t.jsxs("div",{style:{marginTop:4},children:[t.jsx(p,{style:{marginRight:4}}),t.jsx(v,{type:"secondary",style:{fontSize:"12px"},children:y(e.timestamp).format("DD/MM/YYYY HH:mm")})]}),O(e.highlight)]})}),t.jsx("div",{children:t.jsx(x,{type:"text",size:"small",icon:t.jsx(j,{}),onClick:t=>{t.stopPropagation(),A(e)}})})]},e.id),split:!1}):T.trim().length>=2?t.jsx(l,{description:"No results found",image:l.PRESENTED_IMAGE_SIMPLE}):t.jsxs("div",{style:{textAlign:"center",padding:"40px 0"},children:[t.jsx(d,{style:{fontSize:"48px",color:"#d9d9d9"}}),t.jsx("div",{style:{marginTop:16},children:t.jsx(v,{type:"secondary",children:"Type at least 2 characters to start searching"})}),t.jsx("div",{style:{marginTop:8},children:t.jsx(v,{type:"secondary",style:{fontSize:"12px"},children:"Search across machine sessions, reports, and maintenance logs"})})]})}),E.length>0&&t.jsxs(t.Fragment,{children:[t.jsx(h,{}),t.jsx("div",{style:{textAlign:"center"},children:t.jsx(v,{type:"secondary",style:{fontSize:"12px"},children:"Click on any result to view details"})})]})]})};export{$ as G,S as s};
