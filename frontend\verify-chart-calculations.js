// Verification script for time pattern calculations
console.log('🧮 VERIFICATION: Time Pattern Chart Calculations');

// Simulate the hourly average calculation
function verifyHourlyAverages(sampleData) {
  const hourlyStats = {};
  
  // Initialize hours
  for (let hour = 0; hour < 24; hour++) {
    hourlyStats[hour] = {
      count: 0,
      totalDuration: 0,
      avgDuration: 0,
      durations: []
    };
  }
  
  // Process sample data
  sampleData.forEach(stop => {
    const hour = stop.hour;
    const duration = stop.duration;
    
    hourlyStats[hour].count += 1;
    hourlyStats[hour].totalDuration += duration;
    hourlyStats[hour].durations.push(duration);
  });
  
  // Calculate averages
  Object.keys(hourlyStats).forEach(hour => {
    const stats = hourlyStats[hour];
    stats.avgDuration = stats.count > 0 ? stats.totalDuration / stats.count : 0;
    
    if (stats.count > 0) {
      console.log(`Hour ${hour}: ${stats.count} stops, Total: ${stats.totalDuration}min, Avg: ${stats.avgDuration.toFixed(1)}min`);
      console.log(`  Individual durations: [${stats.durations.join(', ')}]`);
    }
  });
  
  return hourlyStats;
}

// Test case 1: Normal operation
console.log('\n📋 Test Case 1: Normal Operation');
const normalData = [
  { hour: 9, duration: 15 },
  { hour: 9, duration: 25 },
  { hour: 9, duration: 20 },
  { hour: 11, duration: 30 },
  { hour: 11, duration: 40 }
];

verifyHourlyAverages(normalData);

// Test case 2: Extreme values (like what might cause the high peaks)
console.log('\n📋 Test Case 2: With Extreme Values');
const extremeData = [
  { hour: 11, duration: 15 },
  { hour: 11, duration: 5000 }, // Very long stop (maintenance?)
  { hour: 22, duration: 20 },
  { hour: 22, duration: 30 },
  { hour: 22, duration: 100 }
];

verifyHourlyAverages(extremeData);

console.log('\n✅ Verification complete. Check if results match your dashboard values.');
