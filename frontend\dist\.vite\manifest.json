{"_ArretContext-CHrB8ruD.js": {"file": "assets/ArretContext-CHrB8ruD.js", "name": "ArretContext", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js", "_useStopTableGraphQL-BIk1S3c3.js"]}, "_ArretErrorBoundary-Npc3DceX.js": {"file": "assets/ArretErrorBoundary-Npc3DceX.js", "name": "ArretErrorBoundary", "imports": ["index.html", "_react-vendor-tYPmozCJ.js"]}, "_ArretFilters-DTWquWOy.js": {"file": "assets/ArretFilters-DTWquWOy.js", "name": "ArretFilters", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js"]}, "_EnhancedChartComponents-BI9rDKsk.css": {"file": "assets/EnhancedChartComponents-BI9rDKsk.css", "src": "_EnhancedChartComponents-BI9rDKsk.css"}, "_EnhancedChartComponents-C30-oOvy.js": {"file": "assets/EnhancedChartComponents-C30-oOvy.js", "name": "EnhancedChartComponents", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_chart-vendor-CazprKWL.js", "_antd-vendor-4OvKHZ_k.js"], "css": ["assets/EnhancedChartComponents-BI9rDKsk.css"]}, "_GlobalSearchModal-CzoVtWJy.js": {"file": "assets/GlobalSearchModal-CzoVtWJy.js", "name": "GlobalSearchModal", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "_Login-BS9aZW5k.css": {"file": "assets/Login-BS9aZW5k.css", "src": "_Login-BS9aZW5k.css"}, "_SearchResultsDisplay-_nzCIRa1.js": {"file": "assets/SearchResultsDisplay-_nzCIRa1.js", "name": "SearchResultsDisplay", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_chart-vendor-CazprKWL.js", "_antd-vendor-4OvKHZ_k.js", "_GlobalSearchModal-CzoVtWJy.js"]}, "_antd-vendor-4OvKHZ_k.js": {"file": "assets/antd-vendor-4OvKHZ_k.js", "name": "antd-vendor", "imports": ["_react-vendor-tYPmozCJ.js"]}, "_chart-config-DFN0skhq.js": {"file": "assets/chart-config-DFN0skhq.js", "name": "chart-config", "imports": ["index.html", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js", "_utils-vendor-BlNwBmLj.js"], "css": ["assets/chart-config-KsRtBkUc.css"]}, "_chart-config-KsRtBkUc.css": {"file": "assets/chart-config-KsRtBkUc.css", "src": "_chart-config-KsRtBkUc.css"}, "_chart-vendor-CazprKWL.js": {"file": "assets/chart-vendor-CazprKWL.js", "name": "chart-vendor", "imports": ["_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "_dataUtils-CeV1Whgh.js": {"file": "assets/dataUtils-CeV1Whgh.js", "name": "dataUtils", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "_eventHandlers-DY2JSJgz.js": {"file": "assets/eventHandlers-DY2JSJgz.js", "name": "eventHandlers", "imports": ["_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "_isoWeek-B92Rp6lO.js": {"file": "assets/isoWeek-B92Rp6lO.js", "name": "isoWeek", "imports": ["_react-vendor-tYPmozCJ.js"]}, "_logo_for_DarkMode-95VNBnHa.js": {"file": "assets/logo_for_DarkMode-95VNBnHa.js", "name": "logo_for_DarkMode", "assets": ["assets/logo-CQMHWFEM.jpg", "assets/logo_for_DarkMode-BUuyL7WI.jpg"]}, "_numberFormatter-5BSX8Tmh.js": {"file": "assets/numberFormatter-5BSX8Tmh.js", "name": "numberF<PERSON>atter"}, "_performance-metrics-gauge-By5N0v5D.js": {"file": "assets/performance-metrics-gauge-By5N0v5D.js", "name": "performance-metrics-gauge", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js"]}, "_react-vendor-tYPmozCJ.js": {"file": "assets/react-vendor-tYPmozCJ.js", "name": "react-vendor"}, "_useDailyTableGraphQL-kyfCYKRH.js": {"file": "assets/useDailyTableGraphQL-kyfCYKRH.js", "name": "useDailyTableGraphQL", "imports": ["_react-vendor-tYPmozCJ.js"]}, "_useMobile-BeW-phh2.js": {"file": "assets/useMobile-BeW-phh2.js", "name": "useMobile", "imports": ["_react-vendor-tYPmozCJ.js"]}, "_usePermission-B71YYXey.js": {"file": "assets/usePermission-B71YYXey.js", "name": "usePermission", "imports": ["index.html"]}, "_useStopTableGraphQL-BIk1S3c3.js": {"file": "assets/useStopTableGraphQL-BIk1S3c3.js", "name": "useStopTableGraphQL", "imports": ["_react-vendor-tYPmozCJ.js", "index.html"]}, "_utils-vendor-BlNwBmLj.js": {"file": "assets/utils-vendor-BlNwBmLj.js", "name": "utils-vendor", "imports": ["_react-vendor-tYPmozCJ.js"]}, "index.html": {"file": "assets/index-Nnj1g72A.js", "name": "index", "src": "index.html", "isEntry": true, "imports": ["_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"], "dynamicImports": ["src/Components/MainLayout.jsx", "src/Pages/OptimizedDailyPerformanceDashboard.jsx", "src/Components/DailyPerformanceDashboard.jsx", "src/Components/Arrets2.jsx", "src/Pages/ArretsDashboard.jsx", "src/Pages/ProductionDashboard.jsx", "src/Components/production-page.jsx", "src/Components/UserProfile.jsx", "src/Components/ErrorPage.jsx", "src/Components/UnauthorizedPage.jsx", "src/Components/AdminPanel.jsx", "src/Components/Login.jsx", "src/Components/ResetPassword.jsx", "src/Components/user-management.jsx", "src/Components/PermissionTest.jsx", "src/Components/charts/ChartExpansion/ChartPerformanceTest.jsx", "src/Components/charts/ChartExpansion/ModalTestPage.jsx", "src/Components/ProtectedRoute.jsx", "src/Components/PermissionRoute.jsx", "src/Pages/notifications.jsx", "src/Pages/settings.jsx", "src/Pages/reports.jsx", "src/Pages/AnalyticsDashboard.jsx", "src/Components/NotificationsTest.jsx", "src/Components/SSEConnectionTest.jsx", "src/Components/IntegrationTestComponent.jsx", "src/Components/DebugArretContext.jsx", "src/tests/ArretFiltersTest.jsx", "src/Pages/DiagnosticPage.jsx", "src/Pages/MachineDataFixerTest.jsx"], "css": ["assets/index-BFda_FW7.css"]}, "src/Components/AdminPanel.jsx": {"file": "assets/AdminPanel-CL2Ce1JA.js", "name": "AdminPanel", "src": "src/Components/AdminPanel.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/Arrets2.jsx": {"file": "assets/Arrets2-Dan5RqFE.js", "name": "Arrets2", "src": "src/Components/Arrets2.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_chart-vendor-CazprKWL.js", "_performance-metrics-gauge-By5N0v5D.js", "_SearchResultsDisplay-_nzCIRa1.js", "_GlobalSearchModal-CzoVtWJy.js"]}, "src/Components/DailyPerformanceDashboard.jsx": {"file": "assets/DailyPerformanceDashboard-Ddzr-ihr.js", "name": "DailyPerformanceDashboard", "src": "src/Components/DailyPerformanceDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_chart-vendor-CazprKWL.js", "_chart-config-DFN0skhq.js", "_antd-vendor-4OvKHZ_k.js", "_utils-vendor-BlNwBmLj.js"]}, "src/Components/DebugArretContext.jsx": {"file": "assets/DebugArretContext-rPcuNFll.js", "name": "DebugArretContext", "src": "src/Components/DebugArretContext.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_ArretContext-CHrB8ruD.js", "_useStopTableGraphQL-BIk1S3c3.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js"]}, "src/Components/ErrorPage.jsx": {"file": "assets/ErrorPage-CUHLFBBJ.js", "name": "ErrorPage", "src": "src/Components/ErrorPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/IntegrationTestComponent.jsx": {"file": "assets/IntegrationTestComponent-CfJxrn1P.js", "name": "IntegrationTestComponent", "src": "src/Components/IntegrationTestComponent.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_ArretContext-CHrB8ruD.js", "_ArretErrorBoundary-Npc3DceX.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js", "_useStopTableGraphQL-BIk1S3c3.js"]}, "src/Components/Login.jsx": {"file": "assets/Login-ICFgDl9J.js", "name": "<PERSON><PERSON>", "src": "src/Components/Login.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_logo_for_DarkMode-95VNBnHa.js", "_antd-vendor-4OvKHZ_k.js"], "css": ["assets/Login-BS9aZW5k.css"]}, "src/Components/MainLayout.jsx": {"file": "assets/MainLayout-PuDutJVl.js", "name": "MainLayout", "src": "src/Components/MainLayout.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_logo_for_DarkMode-95VNBnHa.js", "_usePermission-B71YYXey.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/NotificationsTest.jsx": {"file": "assets/NotificationsTest-Bik5MPzc.js", "name": "NotificationsTest", "src": "src/Components/NotificationsTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/PermissionRoute.jsx": {"file": "assets/PermissionRoute-DniQ07V4.js", "name": "PermissionRoute", "src": "src/Components/PermissionRoute.jsx", "isDynamicEntry": true, "imports": ["index.html", "_usePermission-B71YYXey.js", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/PermissionTest.jsx": {"file": "assets/PermissionTest-BQS3Cayk.js", "name": "PermissionTest", "src": "src/Components/PermissionTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_usePermission-B71YYXey.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/ProtectedRoute.jsx": {"file": "assets/ProtectedRoute-DzdgNZgj.js", "name": "ProtectedRoute", "src": "src/Components/ProtectedRoute.jsx", "isDynamicEntry": true, "imports": ["index.html", "_antd-vendor-4OvKHZ_k.js", "_react-vendor-tYPmozCJ.js"]}, "src/Components/ResetPassword.jsx": {"file": "assets/ResetPassword-BJFERVFL.js", "name": "ResetPassword", "src": "src/Components/ResetPassword.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"], "css": ["assets/Login-BS9aZW5k.css"]}, "src/Components/SSEConnectionTest.jsx": {"file": "assets/SSEConnectionTest-DtZPEkCn.js", "name": "SSEConnectionTest", "src": "src/Components/SSEConnectionTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/UnauthorizedPage.jsx": {"file": "assets/UnauthorizedPage-Dpgojqtk.js", "name": "UnauthorizedPage", "src": "src/Components/UnauthorizedPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/UserProfile.jsx": {"file": "assets/UserProfile-C3GfGBZg.js", "name": "UserProfile", "src": "src/Components/UserProfile.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "src/Components/user-management.jsx", "_antd-vendor-4OvKHZ_k.js"], "css": ["assets/UserProfile-BQyCACqm.css"]}, "src/Components/charts/ChartExpansion/ChartPerformanceTest.jsx": {"file": "assets/ChartPerformanceTest-CXTIRL3h.js", "name": "ChartPerformanceTest", "src": "src/Components/charts/ChartExpansion/ChartPerformanceTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_EnhancedChartComponents-C30-oOvy.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js"]}, "src/Components/charts/ChartExpansion/ModalTestPage.jsx": {"file": "assets/ModalTestPage-CNxtDx0W.js", "name": "ModalTestPage", "src": "src/Components/charts/ChartExpansion/ModalTestPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_EnhancedChartComponents-C30-oOvy.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js"]}, "src/Components/production-page.jsx": {"file": "assets/production-page-DS59idhX.js", "name": "production-page", "src": "src/Components/production-page.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_dataUtils-CeV1Whgh.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js"]}, "src/Components/user-management.jsx": {"file": "assets/user-management-CjwWAgRm.js", "name": "user-management", "src": "src/Components/user-management.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Pages/AnalyticsDashboard.jsx": {"file": "assets/AnalyticsDashboard-BXPF8ybB.js", "name": "AnalyticsDashboard", "src": "src/Pages/AnalyticsDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Pages/ArretsDashboard.jsx": {"file": "assets/ArretsDashboard-Bsr4GZPh.js", "name": "ArretsDashboard", "src": "src/Pages/ArretsDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_ArretFilters-DTWquWOy.js", "_antd-vendor-4OvKHZ_k.js", "_numberFormatter-5BSX8Tmh.js", "_chart-vendor-CazprKWL.js", "_performance-metrics-gauge-By5N0v5D.js", "_GlobalSearchModal-CzoVtWJy.js", "_ArretErrorBoundary-Npc3DceX.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js"]}, "src/Pages/DiagnosticPage.jsx": {"file": "assets/DiagnosticPage-D2YbXHWF.js", "name": "DiagnosticPage", "src": "src/Pages/DiagnosticPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_useStopTableGraphQL-BIk1S3c3.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Pages/MachineDataFixerTest.jsx": {"file": "assets/MachineDataFixerTest-DvKDDMZz.js", "name": "MachineDataFixerTest", "src": "src/Pages/MachineDataFixerTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_useStopTableGraphQL-BIk1S3c3.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Pages/OptimizedDailyPerformanceDashboard.jsx": {"file": "assets/OptimizedDailyPerformanceDashboard-CGG8Hs6B.js", "name": "OptimizedDailyPerformanceDashboard", "src": "src/Pages/OptimizedDailyPerformanceDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_chart-config-DFN0skhq.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js", "_utils-vendor-BlNwBmLj.js"], "css": ["assets/OptimizedDailyPerformanceDashboard-BaYlTMQF.css"]}, "src/Pages/ProductionDashboard.jsx": {"file": "assets/ProductionDashboard-B_9wC_vy.js", "name": "ProductionDashboard", "src": "src/Pages/ProductionDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_dataUtils-CeV1Whgh.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_useDailyTableGraphQL-kyfCYKRH.js", "_SearchResultsDisplay-_nzCIRa1.js", "_GlobalSearchModal-CzoVtWJy.js", "_numberFormatter-5BSX8Tmh.js", "_EnhancedChartComponents-C30-oOvy.js", "_chart-vendor-CazprKWL.js"]}, "src/Pages/notifications.jsx": {"file": "assets/notifications-D3JAF9Lv.js", "name": "notifications", "src": "src/Pages/notifications.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_useMobile-BeW-phh2.js"]}, "src/Pages/reports.jsx": {"file": "assets/reports-BssKTAKY.js", "name": "reports", "src": "src/Pages/reports.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_useMobile-BeW-phh2.js", "_numberFormatter-5BSX8Tmh.js", "_useDailyTableGraphQL-kyfCYKRH.js"]}, "src/Pages/settings.jsx": {"file": "assets/settings-DNbJpze-.js", "name": "settings", "src": "src/Pages/settings.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/assets/logo.jpg": {"file": "assets/logo-CQMHWFEM.jpg", "src": "src/assets/logo.jpg"}, "src/assets/logo_for_DarkMode.jpg": {"file": "assets/logo_for_DarkMode-BUuyL7WI.jpg", "src": "src/assets/logo_for_DarkMode.jpg"}, "src/tests/ArretFiltersTest.jsx": {"file": "assets/ArretFiltersTest-DA6kwE0V.js", "name": "ArretFiltersTest", "src": "src/tests/ArretFiltersTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_ArretContext-CHrB8ruD.js", "_ArretFilters-DTWquWOy.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js", "_useStopTableGraphQL-BIk1S3c3.js"]}}