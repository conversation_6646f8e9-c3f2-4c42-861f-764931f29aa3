import React from 'react';
import { useArretQueuedContext } from '../../context/arret/ArretQueuedContext.jsx';
import superagent from 'superagent';

const ArretContextDebug = () => {
  const context = useArretQueuedContext();
  const testApiCall = async () => {
    try {
      console.log('🧪 Testing direct API calls...');
      
      // Test total stops using SuperAgent with HTTP-only cookies
      const totalResponse = await superagent.get('http://localhost:5000/api/sidecards-arret').withCredentials().timeout(30000).retry(2);
      console.log('✅ Total stops API call successful:', totalResponse.body);

      // Test non-declared stops using SuperAgent with HTTP-only cookies
      const nonDeclaredResponse = await superagent.get('http://localhost:5000/api/sidecards-arretnonDeclare').withCredentials().timeout(30000).retry(2);
      console.log('✅ Non-declared stops API call successful:', nonDeclaredResponse.body);
      
      alert(`API calls successful!\nTotal: ${totalResponse.data[0]?.Arret_Totale}\nNon-declared: ${nonDeclaredResponse.data[0]?.Arret_Totale_nondeclare}\nCheck console for details.`);
    } catch (error) {
      console.error('❌ Direct API calls failed:', error);
      alert('API calls failed! Check console for details.');
    }
  };

  if (!context) {
    return (
      <div style={{ padding: '20px', background: '#ffebee', border: '1px solid #f44336' }}>
        <h3>🚨 Context Debug - NO CONTEXT AVAILABLE</h3>
        <button onClick={testApiCall} style={{ margin: '10px', padding: '10px', background: '#2196f3', color: 'white', border: 'none', borderRadius: '4px' }}>
          Test Direct API Call
        </button>
      </div>
    );
  }  const {
    loading,
    error,
    arretStats,
    arretNonDeclareStats,
    // Note: Production data removed - focusing only on stops/downtime
    performanceData,
    topStopsData,
    stopReasons,
    stopsData,
    sidebarStats,
    machineModels,
    machineNames,
    filteredMachineNames,
    selectedMachineModel,
    selectedMachine,
    percentageNonDeclared
  } = context;

  return (
    <div style={{ padding: '20px', background: '#e8f5e8', border: '1px solid #4caf50', marginBottom: '20px' }}>
      <h3>🔍 Context Debug Information</h3>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '10px' }}>
        <div>
          <h4>Loading & Error State</h4>
          <p><strong>Loading:</strong> {loading ? '✅ Yes' : '❌ No'}</p>
          <p><strong>Error:</strong> {error || 'None'}</p>
        </div>        <div>
          <h4>Core Data Arrays</h4>
          <p><strong>arretStats:</strong> {arretStats?.length || 0} items</p>
          <p><strong>arretNonDeclareStats:</strong> {arretNonDeclareStats?.length || 0} items</p>
          {/* Production data removed - focusing only on stops/downtime */}
          <p><strong>performanceData:</strong> {performanceData?.length || 0} items</p>
          <p><strong>stopReasons:</strong> {stopReasons?.length || 0} items</p>
          <p><strong>stopsData:</strong> {stopsData?.length || 0} items</p>
          <p><strong>topStopsData:</strong> {topStopsData?.length || 0} items</p>
        </div>

        <div>
          <h4>Computed Data</h4>
          <p><strong>sidebarStats:</strong> {sidebarStats?.length || 0} items</p>
          <p><strong>percentageNonDeclared:</strong> {percentageNonDeclared}%</p>
          {sidebarStats?.length > 0 && (
            <ul>
              {sidebarStats.map((stat, index) => (
                <li key={index}>{stat.title}: {stat.value} {stat.suffix}</li>
              ))}
            </ul>
          )}
        </div>

        <div>
          <h4>Machine Data</h4>
          <p><strong>machineModels:</strong> {machineModels?.length || 0} items</p>
          <p><strong>machineNames:</strong> {machineNames?.length || 0} items</p>
          <p><strong>filteredMachineNames:</strong> {filteredMachineNames?.length || 0} items</p>
          <p><strong>selectedMachineModel:</strong> {selectedMachineModel || 'None'}</p>
          <p><strong>selectedMachine:</strong> {selectedMachine || 'None'}</p>
        </div>
      </div>      <div style={{ marginTop: '10px' }}>
        <button onClick={testApiCall} style={{ margin: '10px', padding: '10px', background: '#2196f3', color: 'white', border: 'none', borderRadius: '4px' }}>
          Test Direct API Call
        </button>
          <h4>Raw Data Samples</h4>
        {arretStats?.length > 0 && (
          <div>
            <strong>arretStats[0]:</strong> 
            <pre style={{ fontSize: '10px', maxHeight: '100px', overflow: 'auto' }}>
              {JSON.stringify(arretStats[0], null, 2)}
            </pre>
          </div>
        )}
        
        {arretNonDeclareStats?.length > 0 && (
          <div>
            <strong>arretNonDeclareStats[0]:</strong> 
            <pre style={{ fontSize: '10px', maxHeight: '100px', overflow: 'auto' }}>
              {JSON.stringify(arretNonDeclareStats[0], null, 2)}
            </pre>
          </div>
        )}
        
        {stopReasons?.length > 0 && (
          <div>
            <strong>stopReasons[0]:</strong> 
            <pre style={{ fontSize: '10px', maxHeight: '100px', overflow: 'auto' }}>
              {JSON.stringify(stopReasons[0], null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default ArretContextDebug;
