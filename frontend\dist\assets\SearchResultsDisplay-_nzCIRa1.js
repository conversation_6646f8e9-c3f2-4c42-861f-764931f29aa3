import{j as e}from"./index-Nnj1g72A.js";import{r as s}from"./react-vendor-tYPmozCJ.js";import{y as t}from"./chart-vendor-CazprKWL.js";import{a6 as a,a7 as r,S as n,B as i,aI as l,aJ as o,e as c,an as d,f as u,aK as h,aG as p,O as x,c as m,ar as j,ad as y,aL as g,Z as f,as as v,d as S,aM as C,R as b,aC as k,a8 as M,ab as R,a9 as w,E as T,L as $,aE as z,T as E,aN as D,aO as Y,ae as F,ah as A,av as N,y as I,Y as q,aP as P}from"./antd-vendor-4OvKHZ_k.js";import{s as L}from"./GlobalSearchModal-CzoVtWJy.js";const O=e=>{switch(e){case"day":case"week":default:return"DD/MM/YYYY";case"month":return"MM/YYYY"}},{Option:B}=j,{Search:_}=o,G=({machineModels:t,filteredMachineNames:o,selectedMachineModel:w="",selectedMachine:T="",dateFilter:$=null,dateRangeType:z,dateFilterActive:E,handleMachineModelChange:D,handleMachineChange:Y,handleDateChange:F,handleDateRangeTypeChange:A,resetFilters:N,handleRefresh:I,loading:q=!1,dataSize:P=0,estimatedLoadTime:G=0,pageType:Q="production",onSearchResults:H,enableElasticsearch:J=!0})=>{const[K,V]=s.useState(""),[Z,U]=s.useState([]),[W,X]=s.useState(!1),[ee,se]=s.useState(null),[te,ae]=s.useState(!1),[re,ne]=s.useState(!1);s.useEffect((()=>{J&&ie()}),[J]);const ie=async()=>{try{const e=await L.checkHealth();ae("healthy"===e.elasticsearch.status)}catch(e){ae(!1)}},le=s.useCallback(L.createDebouncedSearch((async e=>{if(!e||e.length<2)U([]);else try{const s="production"===Q?"machineName":"stopDescription",t=await L.getSuggestions(e,s,8);U(t.map((e=>({value:e}))))}catch(s){U([])}}),300),[Q]),oe=async e=>{if(e.trim()&&te){X(!0);try{const s={query:e.trim(),dateFrom:null==$?void 0:$.startDate,dateTo:null==$?void 0:$.endDate,machineId:T,machineModel:w,page:1,size:50};let t;"production"===Q?t=await L.searchProductionData(s):"arrets"===Q&&(t=await L.searchMachineStops(s)),se(t),ne(!0),H&&H(t,e)}catch(s){se(null)}finally{X(!1)}}},ce=()=>{V(""),se(null),ne(!1),U([]),H&&H(null,"")},de=e=>{const s=S();let t,a;switch(e){case"today":t=s,a="day";break;case"week":t=s,a="week";break;case"month":t=s,a="month";break;case"last7days":t=s.subtract(7,"days"),a="week";break;case"last30days":t=s.subtract(30,"days"),a="month";break;default:return}A(a),F(t)},ue=P>1e3?{type:"warning",message:`Attention: ${P} enregistrements à charger (temps estimé: ${G}s)`}:P>500?{type:"info",message:`${P} enregistrements à charger`}:null;return e.jsx("div",{children:e.jsxs(a,{gutter:[16,16],children:[te&&e.jsxs(r,{span:24,children:[e.jsxs(n,{wrap:!0,style:{width:"100%"},children:[e.jsx(i,{dot:re,color:"green",children:e.jsx(l,{style:{width:300},options:Z,onSearch:e=>{V(e),te&&le(e)},onSelect:e=>{V(e),oe(e)},value:K,placeholder:`Rechercher ${"production"===Q?"dans les données de production":"dans les arrêts"}...`,children:e.jsx(_,{loading:W,onSearch:oe,enterButton:e.jsx(c,{type:"primary",icon:e.jsx(d,{}),children:"Rechercher"})})})}),re&&e.jsxs(n,{children:[e.jsx(u,{color:"green",icon:e.jsx(h,{}),children:"Mode recherche actif"}),e.jsx(c,{size:"small",onClick:ce,children:"Retour aux filtres"}),ee&&e.jsxs(u,{color:"blue",children:[ee.total," résultat(s) trouvé(s)"]})]}),e.jsx(p,{checkedChildren:"ES",unCheckedChildren:"SQL",checked:re,onChange:e=>{e||ce()},title:"Basculer entre recherche Elasticsearch et filtres SQL"})]}),e.jsx(x,{style:{margin:"12px 0"}})]}),e.jsx(r,{span:24,children:e.jsxs(n,{wrap:!0,children:[e.jsx(m,{title:"Filtrer par modèle de machine (optionnel - toutes les données sont affichées par défaut)",children:e.jsx(j,{placeholder:"Tous les modèles",style:{width:150},value:w||void 0,onChange:D,allowClear:!0,suffixIcon:e.jsx(y,{style:{color:"#1890ff"}}),children:t.map((s=>e.jsx(B,{value:s,children:s},s)))})}),e.jsx(j,{placeholder:"Sélectionner une machine",style:{width:150},value:T||void 0,onChange:Y,disabled:!w||0===o.length,allowClear:!0,children:o.map((s=>e.jsx(B,{value:s.Machine_Name,children:s.Machine_Name},s.Machine_Name)))}),e.jsx(g,{options:[{label:"Jour",value:"day",icon:e.jsx(f,{})},{label:"Semaine",value:"week",icon:e.jsx(f,{})},{label:"Mois",value:"month",icon:e.jsx(f,{})}],value:z,onChange:A}),e.jsx(v,{placeholder:"Sélectionner un "+("day"===z?"jour":"week"===z?"semaine":"mois"),format:O(z),value:$?S($):null,onChange:F,picker:"day"===z?void 0:z,allowClear:!0,style:{width:180}}),e.jsx(m,{title:"Réinitialiser les filtres",children:e.jsx(c,{icon:e.jsx(C,{}),onClick:N,disabled:!w&&!T&&!E})}),e.jsx(m,{title:"Rafraîchir les données",children:e.jsx(c,{type:"primary",icon:e.jsx(b,{}),onClick:I,loading:q})})]})}),e.jsx(r,{span:24,children:e.jsxs(n,{split:e.jsx(x,{type:"vertical"}),wrap:!0,children:[e.jsxs(n,{children:[e.jsx(u,{icon:e.jsx(k,{}),color:"blue",children:"Filtres rapides:"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("today"),children:"Aujourd'hui"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("week"),children:"Cette semaine"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("month"),children:"Ce mois"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("last7days"),children:"7 derniers jours"}),e.jsx(c,{size:"small",type:"link",onClick:()=>de("last30days"),children:"30 derniers jours"})]}),!w&&e.jsx(u,{icon:e.jsx(y,{}),color:"blue",children:"Affichage de tous les modèles de machines"}),!E&&e.jsx(u,{icon:e.jsx(M,{}),color:"green",children:"Filtre par défaut: 7 derniers jours"})]})}),ue&&e.jsx(r,{span:24,children:e.jsx(R,{message:ue.message,type:ue.type,showIcon:!0,closable:!0,style:{marginBottom:0}})})]})})};G.propTypes={machineModels:t.array.isRequired,filteredMachineNames:t.array.isRequired,selectedMachineModel:t.string,selectedMachine:t.string,dateFilter:t.object,dateRangeType:t.string.isRequired,dateFilterActive:t.bool.isRequired,handleMachineModelChange:t.func.isRequired,handleMachineChange:t.func.isRequired,handleDateChange:t.func.isRequired,handleDateRangeTypeChange:t.func.isRequired,resetFilters:t.func.isRequired,handleRefresh:t.func.isRequired,loading:t.bool,dataSize:t.number,estimatedLoadTime:t.number,pageType:t.oneOf(["production","arrets"]),onSearchResults:t.func,enableElasticsearch:t.bool};const{Text:Q,Title:H,Paragraph:J}=E,K=({results:t,searchQuery:l,pageType:o,loading:h=!1,onResultSelect:p,onPageChange:j,currentPage:y=1,pageSize:g=20})=>{const[f,v]=s.useState([]);if(!t)return null;const C=s=>{switch(s){case"production-data":return e.jsx(I,{style:{color:"#52c41a"}});case"machine-stop":return e.jsx(N,{style:{color:"#ff4d4f"}});case"machine-session":return e.jsx(A,{style:{color:"#1890ff"}});case"report":return e.jsx(F,{style:{color:"#722ed1"}});default:return e.jsx(d,{style:{color:"#666"}})}},b=s=>{const t={"production-data":{color:"green",text:"Production"},"machine-stop":{color:"red",text:"Arrêt"},"machine-session":{color:"blue",text:"Session"},report:{color:"purple",text:"Rapport"}}[s]||{color:"default",text:"Inconnu"};return e.jsx(u,{color:t.color,children:t.text})},k=e=>{const{data:s,type:t}=e;switch(t){case"production-data":return`${s.machineName} - ${S(s.date).format("DD/MM/YYYY")}`;case"machine-stop":return`Arrêt ${s.machineName} - ${s.stopCode}`;case"machine-session":return`Session ${s.machineName} - ${s.sessionId}`;case"report":return s.title||`Rapport ${s.type}`;default:return"Résultat de recherche"}},E=e=>{var s,t,a,r,n,i;const{data:l,type:o}=e;switch(o){case"production-data":return`OEE: ${(null==(t=null==(s=l.performance)?void 0:s.oee)?void 0:t.toFixed(1))||0}% | Production: ${(null==(a=l.production)?void 0:a.good)||0} pièces | Opérateur: ${l.operator||"N/A"}`;case"machine-stop":return`${l.stopDescription} | Durée: ${l.duration||0} min | Catégorie: ${l.stopCategory||"N/A"}`;case"machine-session":return`TRS: ${(null==(n=null==(r=l.performance)?void 0:r.trs)?void 0:n.toFixed(1))||0}% | Production: ${(null==(i=l.production)?void 0:i.total)||0} | Opérateur: ${l.operator||"N/A"}`;case"report":return l.description||`Généré par ${l.generatedBy||"N/A"}`;default:return"Aucune description disponible"}},L=s=>{if(!s)return null;const t=Object.keys(s);return 0===t.length?null:e.jsxs("div",{style:{marginTop:8,padding:"8px",backgroundColor:"#f6ffed",borderRadius:"4px"},children:[e.jsxs(Q,{type:"secondary",style:{fontSize:"12px"},children:[e.jsx(P,{})," Correspondances trouvées:"]}),t.map((t=>e.jsxs("div",{style:{marginTop:4},children:[e.jsxs(Q,{strong:!0,style:{fontSize:"12px",color:"#52c41a"},children:[t,":"]}),e.jsx("div",{style:{fontSize:"12px",marginLeft:8},dangerouslySetInnerHTML:{__html:s[t].join(" ... ")}})]},t)))]})},O=s=>{var t,n,l,o,c,d;const{data:h,type:p}=s;return"production-data"===p?e.jsxs(a,{gutter:16,style:{marginTop:8},children:[e.jsx(r,{span:6,children:e.jsx(q,{title:"OEE",value:(null==(t=h.performance)?void 0:t.oee)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})}),e.jsx(r,{span:6,children:e.jsx(q,{title:"Production",value:(null==(n=h.production)?void 0:n.good)||0,suffix:"pcs",valueStyle:{fontSize:"14px"}})}),e.jsx(r,{span:6,children:e.jsx(q,{title:"Qualité",value:(null==(l=h.performance)?void 0:l.quality)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})}),e.jsx(r,{span:6,children:e.jsx(q,{title:"TRS",value:(null==(o=h.performance)?void 0:o.trs)||0,suffix:"%",precision:1,valueStyle:{fontSize:"14px"}})})]}):"machine-stop"===p?e.jsxs(a,{gutter:16,style:{marginTop:8},children:[e.jsx(r,{span:8,children:e.jsx(q,{title:"Durée",value:h.duration||0,suffix:"min",valueStyle:{fontSize:"14px"}})}),e.jsx(r,{span:8,children:e.jsx(u,{color:"high"===h.severity?"red":"medium"===h.severity?"orange":"green",children:h.severity||"low"})}),e.jsx(r,{span:8,children:e.jsx(i,{status:(null==(c=h.resolution)?void 0:c.resolved)?"success":"error",text:(null==(d=h.resolution)?void 0:d.resolved)?"Résolu":"En cours"})})]}):null},B=e=>{p&&p(e)};return e.jsx(w,{title:e.jsxs(n,{children:[e.jsx(d,{}),e.jsxs("span",{children:['Résultats de recherche pour "',l,'"']}),e.jsxs(u,{color:"blue",children:[t.total," résultat(s)"]})]}),extra:e.jsx(n,{children:e.jsx(c,{size:"small",type:"link",children:"Exporter tous"})}),children:0===t.total?e.jsx(T,{description:`Aucun résultat trouvé pour "${l}"`,image:T.PRESENTED_IMAGE_SIMPLE}):e.jsxs(e.Fragment,{children:[e.jsx(R,{message:`${t.total} résultat(s) trouvé(s) dans les ${"production"===o?"données de production":"arrêts de machine"}`,type:"info",showIcon:!0,style:{marginBottom:16}}),e.jsx($,{dataSource:t["production"===o?"production":"stops"]||t.results||[],renderItem:s=>e.jsx($.Item,{style:{padding:"16px",borderRadius:"8px",margin:"8px 0",border:"1px solid #f0f0f0",backgroundColor:"#fafafa",transition:"all 0.2s"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="#f5f5f5",e.currentTarget.style.borderColor="#d9d9d9"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="#fafafa",e.currentTarget.style.borderColor="#f0f0f0"},actions:[e.jsx(m,{title:"Voir les détails",children:e.jsx(c,{type:"text",icon:e.jsx(D,{}),onClick:()=>B(s)})}),e.jsx(m,{title:"Exporter",children:e.jsx(c,{type:"text",icon:e.jsx(Y,{}),onClick:()=>B(s)})})],children:e.jsx($.Item.Meta,{avatar:C(s.type),title:e.jsxs(n,{children:[e.jsx(Q,{strong:!0,style:{cursor:"pointer"},onClick:()=>B(s),children:k(s)}),b(s.type),s.score&&e.jsx(m,{title:`Score de pertinence: ${s.score.toFixed(3)}`,children:e.jsxs(u,{color:"purple",style:{fontSize:"10px"},children:[Math.round(100*s.score),"%"]})})]}),description:e.jsxs("div",{children:[e.jsx(J,{style:{marginBottom:8},children:E(s)}),s.data.timestamp&&e.jsxs("div",{style:{marginBottom:8},children:[e.jsx(M,{style:{marginRight:4}}),e.jsx(Q,{type:"secondary",style:{fontSize:"12px"},children:S(s.data.timestamp||s.data.date).format("DD/MM/YYYY HH:mm")})]}),O(s),L(s.highlight)]})})},s.id),loading:h,split:!1}),t.totalPages>1&&e.jsxs(e.Fragment,{children:[e.jsx(x,{}),e.jsx("div",{style:{textAlign:"center"},children:e.jsx(z,{current:y,total:t.total,pageSize:g,onChange:j,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>`${s[0]}-${s[1]} sur ${e} résultats`})})]})]})})};export{G as F,K as S};
