import PDFReportTemplate from './utils/pdfTemplate.js';
import ReportDataService from './services/reportDataService.js';
import generateEnhancedPdfContent from './utils/pdfGenerator.js';
import PDFDocument from 'pdfkit';

console.log('🧪 Testing Enhanced PDF Components...\n');

try {
  // Test PDFTemplate
  const doc = new PDFDocument();
  const template = new PDFReportTemplate(doc);
  console.log('✅ PDFReportTemplate: Constructor works');
  console.log('✅ PDFReportTemplate: formatFrenchNumber =', template.formatFrenchNumber(1234.56));
  
  // Test ReportDataService
  const mockDb = { execute: () => {} };
  const dataService = new ReportDataService(mockDb);
  console.log('✅ ReportDataService: Constructor works');
  console.log('✅ ReportDataService: parseNumeric =', dataService.parseNumeric('123,45'));
  
  // Test generateEnhancedPdfContent
  console.log('✅ generateEnhancedPdfContent: Function imported successfully');
  
  console.log('\n🎉 All enhanced PDF components loaded successfully!');
  
} catch (error) {
  console.error('❌ Error testing components:', error);
  process.exit(1);
}
