import React, { useState, useEffect } from 'react';
import { <PERSON>, Typography, Button, message, Alert } from 'antd';
import { useAuth } from '../hooks/useAuth';
import { secureHttp } from '../utils/superagentConfig';

const { Title, Text } = Typography;

/**
 * 🔍 Minimal Admin Panel Test Component
 * This component tests basic admin functionality without complex dependencies
 */
const AdminPanelTest = () => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [error, setError] = useState(null);
  const { user, isAuthenticated } = useAuth();

  console.log('🔍 [AdminPanelTest] Component rendered, user:', user);

  const testUsersAPI = async () => {
    console.log('🔍 [AdminPanelTest] Testing /api/users...');
    setLoading(true);
    setError(null);
    
    try {
      const response = await secureHttp.get('/api/users');
      console.log('🔍 [AdminPanelTest] Users response:', response);
      
      if (response.body && response.body.success) {
        setUsers(response.body.data || []);
        message.success(`Loaded ${response.body.data?.length || 0} users`);
      } else {
        setUsers(response.body || []);
        message.success(`Loaded ${response.body?.length || 0} users`);
      }
    } catch (error) {
      console.error('🔍 [AdminPanelTest] Users API error:', error);
      setError(`Users API Error: ${error.message} (Status: ${error.status})`);
      message.error(`Failed to load users: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testRolesAPI = async () => {
    console.log('🔍 [AdminPanelTest] Testing /api/roles...');
    setLoading(true);
    setError(null);
    
    try {
      const response = await secureHttp.get('/api/roles');
      console.log('🔍 [AdminPanelTest] Roles response:', response);
      
      if (response.body && response.body.success) {
        setRoles(response.body.data || []);
        message.success(`Loaded ${response.body.data?.length || 0} roles`);
      } else {
        setRoles(response.body || []);
        message.success(`Loaded ${response.body?.length || 0} roles`);
      }
    } catch (error) {
      console.error('🔍 [AdminPanelTest] Roles API error:', error);
      setError(`Roles API Error: ${error.message} (Status: ${error.status})`);
      message.error(`Failed to load roles: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testAuthAPI = async () => {
    console.log('🔍 [AdminPanelTest] Testing /api/me...');
    setLoading(true);
    setError(null);
    
    try {
      const response = await secureHttp.get('/api/me');
      console.log('🔍 [AdminPanelTest] Auth response:', response);
      message.success('Authentication test successful');
    } catch (error) {
      console.error('🔍 [AdminPanelTest] Auth API error:', error);
      setError(`Auth API Error: ${error.message} (Status: ${error.status})`);
      message.error(`Authentication test failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('🔍 [AdminPanelTest] Component mounted');
    console.log('🔍 [AdminPanelTest] Authentication state:', { isAuthenticated, user });
  }, [isAuthenticated, user]);

  return (
    <div style={{ padding: 24 }}>
      <Card>
        <Title level={2}>🔍 Admin Panel Test</Title>
        
        <div style={{ marginBottom: 16 }}>
          <Title level={4}>Authentication Status</Title>
          <Text>Authenticated: <strong>{isAuthenticated ? 'YES' : 'NO'}</strong></Text>
          <br />
          <Text>User Role: <strong>{user?.role || 'Unknown'}</strong></Text>
          <br />
          <Text>User ID: <strong>{user?.id || 'Unknown'}</strong></Text>
        </div>

        {error && (
          <Alert
            message="API Error"
            description={error}
            type="error"
            style={{ marginBottom: 16 }}
            showIcon
          />
        )}

        <div style={{ marginBottom: 16 }}>
          <Title level={4}>API Tests</Title>
          <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
            <Button 
              type="primary" 
              onClick={testAuthAPI}
              loading={loading}
            >
              Test Auth (/api/me)
            </Button>
            <Button 
              onClick={testUsersAPI}
              loading={loading}
            >
              Test Users (/api/users)
            </Button>
            <Button 
              onClick={testRolesAPI}
              loading={loading}
            >
              Test Roles (/api/roles)
            </Button>
          </div>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Title level={4}>Data Summary</Title>
          <Text>Users loaded: <strong>{users.length}</strong></Text>
          <br />
          <Text>Roles loaded: <strong>{roles.length}</strong></Text>
        </div>

        {users.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Title level={5}>Users Preview</Title>
            <pre style={{ 
              background: '#f6f8fa', 
              padding: 8, 
              borderRadius: 4, 
              fontSize: '12px',
              maxHeight: '200px',
              overflow: 'auto'
            }}>
              {JSON.stringify(users.slice(0, 3), null, 2)}
            </pre>
          </div>
        )}

        {roles.length > 0 && (
          <div>
            <Title level={5}>Roles Preview</Title>
            <pre style={{ 
              background: '#f6f8fa', 
              padding: 8, 
              borderRadius: 4, 
              fontSize: '12px',
              maxHeight: '200px',
              overflow: 'auto'
            }}>
              {JSON.stringify(roles.slice(0, 3), null, 2)}
            </pre>
          </div>
        )}
      </Card>
    </div>
  );
};

export default AdminPanelTest;
