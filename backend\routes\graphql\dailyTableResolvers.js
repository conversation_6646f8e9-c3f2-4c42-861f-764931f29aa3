/**
 * GraphQL resolvers for daily production table data
 * Recreates functionality from dailyTable.js routes as GraphQL endpoints
 */

import { GraphQLObjectType, GraphQLString, GraphQLInt, GraphQLFloat, GraphQLList, GraphQLSchema, GraphQLInputObjectType, GraphQLBoolean } from 'graphql';
import { executeQuery } from '../../utils/dbUtils.js';
import { buildDateFilter } from '../../utils/dateUtils.js';

// Comprehensive data normalization utilities
const normalizeNumericValue = (value, defaultValue = null) => {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  
  if (typeof value === 'number' && !isNaN(value)) {
    return value;
  }
  
  if (typeof value === 'string') {
    // Remove whitespace
    let cleanValue = value.trim();
    
    // Handle empty strings after trimming
    if (cleanValue === '') return defaultValue;
    
    // Handle percentage values (e.g., "99%", "95,5%")
    if (cleanValue.includes('%')) {
      const percentValue = cleanValue.replace('%', '').replace(',', '.');
      const parsed = parseFloat(percentValue);
      return isNaN(parsed) ? defaultValue : parsed / 100; // Convert to decimal
    }
    
    // Handle comma decimal separator (European format: "1,59" -> "1.59")
    cleanValue = cleanValue.replace(',', '.');
    
    // Parse the numeric value
    const parsed = parseFloat(cleanValue);
    return isNaN(parsed) ? defaultValue : parsed;
  }
  
  return defaultValue;
};

const normalizeIntegerValue = (value, defaultValue = 0) => {
  const numericValue = normalizeNumericValue(value, defaultValue);
  return numericValue !== null ? Math.round(numericValue) : defaultValue;
};

const normalizeDateValue = (dateValue) => {
  if (!dateValue) return null;
  
  // Handle various date formats from database
  const dateStr = String(dateValue).trim();
  
  try {
    // Handle DD/MM/YYYY HH:mm:ss format
    if (dateStr.includes('/') && dateStr.includes(':')) {
      // Extract date part (DD/MM/YYYY)
      const datePart = dateStr.split(' ')[0];
      const [day, month, year] = datePart.split('/');
      
      // Convert to ISO format YYYY-MM-DD
      if (day && month && year) {
        const paddedDay = day.padStart(2, '0');
        const paddedMonth = month.padStart(2, '0');
        return `${year}-${paddedMonth}-${paddedDay}`;
      }
    }
    
    // Handle DD/MM/YYYY format (without time)
    if (dateStr.includes('/') && !dateStr.includes(':')) {
      const [day, month, year] = dateStr.split('/');
      if (day && month && year) {
        const paddedDay = day.padStart(2, '0');
        const paddedMonth = month.padStart(2, '0');
        return `${year}-${paddedMonth}-${paddedDay}`;
      }
    }
    
    // Handle already formatted dates (YYYY-MM-DD)
    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return dateStr;
    }
    
    // If all else fails, try to parse as Date and format
    const parsedDate = new Date(dateStr);
    if (!isNaN(parsedDate.getTime())) {
      return parsedDate.toISOString().split('T')[0];
    }
    
    return null;
  } catch (error) {
    console.warn('Date parsing error:', error.message, 'for value:', dateValue);
    return null;
  }
};

// Utility function to normalize all data inconsistencies
const normalizeDataRow = (row) => {
  if (!row || typeof row !== 'object') return row;
  
  return {
    ...row,
    // Normalize date
    Date_Insert_Day: normalizeDateValue(row.Date_Insert_Day),
    
    // Normalize numeric values with comma/dot handling
    Run_Hours_Day: normalizeNumericValue(row.Run_Hours_Day, 0),
    Down_Hours_Day: normalizeNumericValue(row.Down_Hours_Day, 0),
    Good_QTY_Day: normalizeIntegerValue(row.Good_QTY_Day, 0),
    Rejects_QTY_Day: normalizeIntegerValue(row.Rejects_QTY_Day, 0),
    Speed_Day: normalizeNumericValue(row.Speed_Day, 0),
    
    // Normalize percentage/decimal values
    Availability_Rate_Day: normalizeNumericValue(row.Availability_Rate_Day, 0),
    Performance_Rate_Day: normalizeNumericValue(row.Performance_Rate_Day, 0),
    Quality_Rate_Day: normalizeNumericValue(row.Quality_Rate_Day, 0),
    OEE_Day: normalizeNumericValue(row.OEE_Day, 0),
    
    // Normalize aggregate fields if they exist
    Total_Good_Qty_Day: normalizeIntegerValue(row.Total_Good_Qty_Day, 0),
    Total_Rejects_Qty_Day: normalizeIntegerValue(row.Total_Rejects_Qty_Day, 0),
    
    // Normalize performance fields for machine performance queries
    production: normalizeIntegerValue(row.production, 0),
    rejects: normalizeIntegerValue(row.rejects, 0),
    availability: normalizeNumericValue(row.availability, 0),
    performance: normalizeNumericValue(row.performance, 0),
    quality: normalizeNumericValue(row.quality, 0),
    oee: normalizeNumericValue(row.oee, 0),
    disponibilite: normalizeNumericValue(row.disponibilite, 0),
    
    // Keep string fields as-is but ensure they're not null
    Machine_Name: row.Machine_Name || 'Unknown',
    Shift: row.Shift || 'Unknown',
    Part_Number: row.Part_Number || null,
    Poid_Unitaire: row.Poid_Unitaire || null,
    Cycle_Theorique: row.Cycle_Theorique || null,
    Poid_Purge: row.Poid_Purge || null
  };
};

// Main normalization function for arrays of data
const normalizeAvailabilityMetrics = (data) => {
  if (!data || !Array.isArray(data)) return data;
  
  return data.map(normalizeDataRow);
};

// GraphQL Types with proper data types
const DailyProductionType = new GraphQLObjectType({
  name: 'DailyProduction',
  fields: {
    Machine_Name: { type: GraphQLString },
    Date_Insert_Day: { type: GraphQLString },
    Run_Hours_Day: { type: GraphQLFloat },
    Down_Hours_Day: { type: GraphQLFloat },
    Good_QTY_Day: { type: GraphQLInt },
    Rejects_QTY_Day: { type: GraphQLInt },
    Speed_Day: { type: GraphQLFloat },
    Availability_Rate_Day: { type: GraphQLFloat },
    Performance_Rate_Day: { type: GraphQLFloat },
    Quality_Rate_Day: { type: GraphQLFloat },
    OEE_Day: { type: GraphQLFloat },
    Shift: { type: GraphQLString },
    Part_Number: { type: GraphQLString },
    Poid_Unitaire: { type: GraphQLString },
    Cycle_Theorique: { type: GraphQLString },
    Poid_Purge: { type: GraphQLString }
  }
});

const ProductionChartType = new GraphQLObjectType({
  name: 'ProductionChart',
  fields: {
    Date_Insert_Day: { type: GraphQLString },
    Total_Good_Qty_Day: { type: GraphQLInt },
    Total_Rejects_Qty_Day: { type: GraphQLInt },
    OEE_Day: { type: GraphQLFloat },
    Speed_Day: { type: GraphQLFloat },
    Availability_Rate_Day: { type: GraphQLFloat },
    Performance_Rate_Day: { type: GraphQLFloat },
    Quality_Rate_Day: { type: GraphQLFloat }
  }
});

const MachineModelType = new GraphQLObjectType({
  name: 'MachineModel',
  fields: {
    model: { type: GraphQLString }
  }
});

const MachineNameType = new GraphQLObjectType({
  name: 'MachineName',
  fields: {
    Machine_Name: { type: GraphQLString }
  }
});

const SideCardType = new GraphQLObjectType({
  name: 'SideCard',
  fields: {
    goodqty: { type: GraphQLInt },
    rejetqty: { type: GraphQLInt }
  }
});

const MachinePerformanceType = new GraphQLObjectType({
  name: 'MachinePerformance',
  fields: {
    Machine_Name: { type: GraphQLString },
    Shift: { type: GraphQLString },
    production: { type: GraphQLInt },
    rejects: { type: GraphQLInt },
    availability: { type: GraphQLFloat },
    performance: { type: GraphQLFloat },
    oee: { type: GraphQLFloat },
    quality: { type: GraphQLFloat },
    disponibilite: { type: GraphQLFloat },
    downtime: { type: GraphQLFloat }
  }
});

const AvailabilityTrendType = new GraphQLObjectType({
  name: 'AvailabilityTrend',
  fields: {
    date: { type: GraphQLString },
    machine: { type: GraphQLString },
    disponibilite: { type: GraphQLFloat }
  }
});

const PerformanceMetricsType = new GraphQLObjectType({
  name: 'PerformanceMetrics',
  fields: {
    machine: { type: GraphQLString },
    model: { type: GraphQLString },
    disponibilite: { type: GraphQLFloat },
    stops: { type: GraphQLInt },
    mttr: { type: GraphQLFloat },
    mtbf: { type: GraphQLFloat }
  }
});

// Input Types
const FilterInputType = new GraphQLInputObjectType({
  name: 'FilterInput',
  fields: {
    date: { type: GraphQLString },
    dateRangeType: { type: GraphQLString },
    model: { type: GraphQLString },
    machine: { type: GraphQLString },
    startDate: { type: GraphQLString },
    endDate: { type: GraphQLString },
    page: { type: GraphQLInt },
    limit: { type: GraphQLInt }
  }
});

// Resolvers
const dailyProductionResolvers = {
  // Get all daily production data - FIXED: Now accepts filters
  getAllDailyProduction: {
    type: new GraphQLList(DailyProductionType),
    args: {
      filters: { type: FilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      // 🔍 DEBUG: Log received filters
      console.log('🔍 [BACKEND DEBUG] getAllDailyProduction received filters:', filters);

      const { dateRangeType = "day", model, machine, page = 1, limit = 1000 } = filters;
      const offset = (page - 1) * limit;

      let query = "SELECT * FROM machine_daily_table_mould WHERE 1=1";
      let queryParams = [];

      // Apply date filter if provided
      if (filters.date) {
        const dateFilter = buildDateFilter(filters.date, dateRangeType);
        query += dateFilter.condition;
        queryParams = queryParams.concat(dateFilter.params);

        // 🔍 DEBUG: Log date filter details
        console.log('🔍 [BACKEND DEBUG] Date filter applied:', {
          originalDate: filters.date,
          dateRangeType,
          condition: dateFilter.condition,
          params: dateFilter.params
        });
      }

      // Apply machine filters
      if (model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      } else if (machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(machine);
      }

      query += ` ORDER BY Date_Insert_Day DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

      // 🔍 DEBUG: Log final query
      console.log('🔍 [BACKEND DEBUG] getAllDailyProduction final query:', query);
      console.log('🔍 [BACKEND DEBUG] Query params:', queryParams);

      const { success, data, error } = await executeQuery(query, queryParams);

      // 🔍 DEBUG: Log query results
      console.log('🔍 [BACKEND DEBUG] getAllDailyProduction results:', {
        success,
        dataLength: data?.length,
        firstItem: data?.[0],
        error
      });

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return normalizeAvailabilityMetrics(data);
    }
  },

  // Get production chart data
  getProductionChart: {
    type: new GraphQLList(ProductionChartType),
    args: {
      filters: { type: FilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      // 🔍 DEBUG: Log received filters
      console.log('🔍 [BACKEND DEBUG] getProductionChart received filters:', filters);

      const { dateRangeType = "day", model, machine = "IPS01", page = 1, limit = 100 } = filters;
      const offset = (page - 1) * limit;
        let query = `
        SELECT
          DATE_FORMAT(
            CASE 
              WHEN Date_Insert_Day LIKE '%/%' THEN
                STR_TO_DATE(
                  SUBSTRING_INDEX(Date_Insert_Day, ' ', 1), 
                  '%d/%m/%Y'
                )
              ELSE
                STR_TO_DATE(Date_Insert_Day, '%Y-%m-%d')
            END,
            '%Y-%m-%d'
          ) AS Date_Insert_Day,
          SUM(
            CASE 
              WHEN Good_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Good_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS Total_Good_Qty_Day,
          SUM(
            CASE 
              WHEN Rejects_QTY_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Rejects_QTY_Day, ',', '.') AS DECIMAL(15,2))
              ELSE 0 
            END
          ) AS Total_Rejects_Qty_Day,
          AVG(
            CASE 
              WHEN OEE_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,4)) / 100
              WHEN OEE_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,4))
              ELSE 0 
            END
          ) AS OEE_Day,
          AVG(
            CASE 
              WHEN Speed_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Speed_Day, ',', '.') AS DECIMAL(10,2))
              ELSE 0 
            END
          ) AS Speed_Day,
          AVG(
            CASE 
              WHEN Availability_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4)) / 100
              WHEN Availability_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,4))
              ELSE 0 
            END
          ) AS Availability_Rate_Day,
          AVG(
            CASE 
              WHEN Performance_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Performance_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4)) / 100
              WHEN Performance_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,4))
              ELSE 0 
            END
          ) AS Performance_Rate_Day,
          AVG(
            CASE 
              WHEN Quality_Rate_Day LIKE '%\\%%' THEN
                CAST(REPLACE(REPLACE(Quality_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,4)) / 100
              WHEN Quality_Rate_Day REGEXP '^[0-9]+([,.][0-9]+)?$' THEN
                CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,4))
              ELSE 0 
            END
          ) AS Quality_Rate_Day
        FROM machine_daily_table_mould
        WHERE 1=1`;

      let queryParams = [];

      if (filters.date) {
        const dateFilter = buildDateFilter(filters.date, dateRangeType);
        query += dateFilter.condition;
        queryParams = queryParams.concat(dateFilter.params);

        // 🔍 DEBUG: Log date filter details
        console.log('🔍 [BACKEND DEBUG] Date filter applied:', {
          originalDate: filters.date,
          dateRangeType,
          condition: dateFilter.condition,
          params: dateFilter.params
        });
      }

      if (model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      } else if (machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(machine);
      }

      query += ` GROUP BY Date_Insert_Day ORDER BY Date_Insert_Day DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

      // 🔍 DEBUG: Log final query
      console.log('🔍 [BACKEND DEBUG] Final query:', query);
      console.log('🔍 [BACKEND DEBUG] Query params:', queryParams);

      const { success, data, error } = await executeQuery(query, queryParams);

      // 🔍 DEBUG: Log query results
      console.log('🔍 [BACKEND DEBUG] Query results:', {
        success,
        dataLength: data?.length,
        firstItem: data?.[0],
        error
      });

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return normalizeAvailabilityMetrics(data);
    }
  },

  // Get unique production dates
  getUniqueDates: {
    type: new GraphQLList(GraphQLString),
    resolve: async () => {
      const query = `
        SELECT DISTINCT
          DATE_FORMAT(
            STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'),
            '%Y-%m-%d'
          ) AS date
        FROM machine_daily_table_mould
        WHERE
          STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >=
          DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ORDER BY date DESC
      `;

      const { success, data, error } = await executeQuery(query);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data.map(item => item.date);
    }
  },

  // Get production sidecards
  getProductionSidecards: {
    type: SideCardType,
    args: {
      filters: { type: FilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const { date, dateRangeType = "day", model, machine } = filters;

      // Good quantity query
      let goodQuery = `SELECT SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS goodqty FROM machine_daily_table_mould WHERE 1=1`;
      let rejectQuery = `SELECT SUM(CAST(Rejects_QTY_Day AS UNSIGNED)) AS rejetqty FROM machine_daily_table_mould WHERE 1=1`;
      
      let queryParams = [];

      if (date) {
        if (dateRangeType === "day") {
          goodQuery += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(?, '%Y-%m-%d')`;
          rejectQuery += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(?, '%Y-%m-%d')`;
          queryParams.push(date);
        } else if (dateRangeType === "week") {
          const weekCondition = ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >=
                    DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY)
                    AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') <=
                    DATE_ADD(DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL (WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d'))) DAY), INTERVAL 6 DAY)`;
          goodQuery += weekCondition;
          rejectQuery += weekCondition;
          queryParams.push(date, date, date, date);
        } else if (dateRangeType === "month") {
          const monthCondition = ` AND YEAR(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = YEAR(STR_TO_DATE(?, '%Y-%m-%d'))
                    AND MONTH(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = MONTH(STR_TO_DATE(?, '%Y-%m-%d'))`;
          goodQuery += monthCondition;
          rejectQuery += monthCondition;
          queryParams.push(date, date);
        }
      }

      if (model) {
        goodQuery += ` AND Machine_Name LIKE ?`;
        rejectQuery += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      } else if (machine) {
        goodQuery += ` AND Machine_Name = ?`;
        rejectQuery += ` AND Machine_Name = ?`;
        queryParams.push(machine);
      }

      const [goodResult, rejectResult] = await Promise.all([
        executeQuery(goodQuery, queryParams),
        executeQuery(rejectQuery, queryParams)
      ]);

      if (!goodResult.success || !rejectResult.success) {
        throw new Error(`Database query failed`);
      }

      const goodqty = goodResult.data[0]?.goodqty || 0;
      const rejetqty = rejectResult.data[0]?.rejetqty || 0;

      return {
        goodqty: Number(goodqty),
        rejetqty: Number(rejetqty)
      };
    }
  },

  // Get machine models
  getMachineModels: {
    type: new GraphQLList(MachineModelType),
    resolve: async () => {
      const query = `
        SELECT DISTINCT
          SUBSTRING(Machine_Name, 1,
            CASE
              WHEN Machine_Name REGEXP '^[A-Za-z]+[0-9]'
              THEN LENGTH(SUBSTRING_INDEX(Machine_Name, REGEXP_SUBSTR(Machine_Name, '[0-9].*$'), 1))
              ELSE LENGTH(Machine_Name)
            END
          ) AS model
        FROM machine_daily_table_mould
        WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
      `;

      const { success, data, error } = await executeQuery(query);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return data;
    }
  },

  // Get machine names
  getMachineNames: {
    type: new GraphQLList(MachineNameType),
    resolve: async () => {
      const query = `
        SELECT DISTINCT Machine_Name
        FROM machine_daily_table_mould
        WHERE Machine_Name IS NOT NULL AND Machine_Name != ''
        ORDER BY Machine_Name
      `;

      const defaultMachines = [
        { Machine_Name: "IPS01" },
        { Machine_Name: "IPS02" },
        { Machine_Name: "IPS03" },
        { Machine_Name: "IPS04" },
      ];

      const { success, data } = await executeQuery(query);

      if (!success || !data || data.length === 0) {
        return defaultMachines;
      }

      return data;
    }
  },

  // Get machine performance
  getMachinePerformance: {
    type: new GraphQLList(MachinePerformanceType),
    args: {
      filters: { type: FilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const { date, dateRangeType = "day", model, machine = "IPS01", page = 1, limit = 50 } = filters;
      const offset = (page - 1) * limit;      let query = `
        SELECT
          Machine_Name,
          Shift,
          SUM(CAST(Good_QTY_Day AS UNSIGNED)) AS production,
          SUM(CAST(Rejects_QTY_Day AS UNSIGNED)) AS rejects,
          ROUND(AVG(CASE 
            WHEN Availability_Rate_Day LIKE '%\\%%' THEN CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,2)) / 100
            ELSE CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,2))
          END), 2) AS availability,
          ROUND(AVG(CASE 
            WHEN Performance_Rate_Day LIKE '%\\%%' THEN CAST(REPLACE(REPLACE(Performance_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,2)) / 100
            ELSE CAST(REPLACE(Performance_Rate_Day, ',', '.') AS DECIMAL(10,2))
          END), 2) AS performance,
          ROUND(AVG(CASE 
            WHEN OEE_Day LIKE '%\\%%' THEN CAST(REPLACE(REPLACE(OEE_Day, '%', ''), ',', '.') AS DECIMAL(10,2)) / 100
            ELSE CAST(REPLACE(OEE_Day, ',', '.') AS DECIMAL(10,2))
          END), 2) AS oee,
          ROUND(AVG(CASE 
            WHEN Quality_Rate_Day LIKE '%\\%%' THEN CAST(REPLACE(REPLACE(Quality_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,2)) / 100
            ELSE CAST(REPLACE(Quality_Rate_Day, ',', '.') AS DECIMAL(10,2))
          END), 2) AS quality,
          ROUND(AVG(CASE 
            WHEN Availability_Rate_Day LIKE '%\\%%' THEN CAST(REPLACE(REPLACE(Availability_Rate_Day, '%', ''), ',', '.') AS DECIMAL(10,2))
            ELSE CAST(REPLACE(Availability_Rate_Day, ',', '.') AS DECIMAL(10,2)) * 100
          END), 2) AS disponibilite,
          ROUND(SUM(CAST(REPLACE(Down_Hours_Day, ',', '.') AS DECIMAL(10,2))), 2) AS downtime
        FROM machine_daily_table_mould
        WHERE 1=1`;

      const queryParams = [];

      if (date) {
        if (dateRangeType === "day") {
          query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(?, '%Y-%m-%d')`;
          queryParams.push(date);
        } else if (dateRangeType === "week") {
          query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') >= DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d')) DAY)
                    AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') <= DATE_ADD(DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d')) DAY), INTERVAL 6 DAY)`;
          queryParams.push(date, date, date, date);
        } else if (dateRangeType === "month") {
          query += ` AND YEAR(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = YEAR(STR_TO_DATE(?, '%Y-%m-%d'))
                    AND MONTH(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y')) = MONTH(STR_TO_DATE(?, '%Y-%m-%d'))`;
          queryParams.push(date, date);
        }
      }

      if (model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      } else if (machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(machine);
      }

      query += ` GROUP BY Machine_Name, Shift ORDER BY production DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      return normalizeAvailabilityMetrics(data);
    }
  },

  // Get availability trends
  getAvailabilityTrend: {
    type: new GraphQLList(AvailabilityTrendType),
    args: {
      filters: { type: FilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const { startDate, endDate, model, machine } = filters;
      
      let query = `
        SELECT
          DATE_FORMAT(STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y'), '%Y-%m-%d') AS date,
          Machine_Name,
          ROUND(AVG(CAST(Availability_Rate_Day AS DECIMAL(10,2))), 2) AS disponibilite
        FROM machine_daily_table_mould
        WHERE 1=1`;

      const queryParams = [];

      if (startDate && endDate) {
        query += ` AND STR_TO_DATE(Date_Insert_Day, '%d/%m/%Y') BETWEEN STR_TO_DATE(?, '%Y-%m-%d') AND STR_TO_DATE(?, '%Y-%m-%d')`;
        queryParams.push(startDate, endDate);
      }

      if (model) {
        query += ` AND Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      } else if (machine) {
        query += ` AND Machine_Name = ?`;
        queryParams.push(machine);
      }

      query += ` GROUP BY date, Machine_Name ORDER BY date ASC, Machine_Name`;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }

      const transformedData = data.map(row => ({
        date: row.date,
        machine: row.Machine_Name,
        disponibilite: row.disponibilite
      }));

      return normalizeAvailabilityMetrics(transformedData);
    }
  },

  // Get performance metrics by machine
  getPerformanceMetrics: {
    type: new GraphQLList(PerformanceMetricsType),
    args: {
      filters: { type: FilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      const { date, dateRangeType = "day", model, machine } = filters;
      
      let query = `
        SELECT
          mdt.Machine_Name AS machine,
          SUBSTRING(mdt.Machine_Name, 1,
            CASE
              WHEN mdt.Machine_Name REGEXP '^[A-Za-z]+[0-9]'
              THEN LENGTH(SUBSTRING_INDEX(mdt.Machine_Name, REGEXP_SUBSTR(mdt.Machine_Name, '[0-9].*$'), 1))
              ELSE LENGTH(mdt.Machine_Name)
            END
          ) AS model,
          ROUND(AVG(CAST(mdt.Availability_Rate_Day AS DECIMAL(10,2))), 2) AS disponibilite,
          COUNT(mst.Machine_Name) AS stops,
          ROUND(
            AVG(
              CASE
                WHEN mst.Machine_Name IS NOT NULL
                THEN TIMESTAMPDIFF(
                  MINUTE,
                  STR_TO_DATE(mst.Debut_Stop, '%d/%m/%Y %H:%i'),
                  STR_TO_DATE(mst.Fin_Stop_Time, '%d/%m/%Y %H:%i')
                )
                ELSE 0
              END
            ), 2
          ) AS mttr,
          ROUND(
            (SUM(CAST(mdt.Run_Hours_Day AS DECIMAL(10,2))) * 60)
            / NULLIF(COUNT(mst.Machine_Name), 0),
            2
          ) AS mtbf
        FROM machine_daily_table_mould mdt
        LEFT JOIN machine_stop_table_mould mst
          ON mdt.Machine_Name = mst.Machine_Name
          AND STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(mst.Date_Insert, '%d/%m/%Y')
        WHERE 1=1`;

      const queryParams = [];

      if (date) {
        if (dateRangeType === "day") {
          query += ` AND STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y') = STR_TO_DATE(?, '%Y-%m-%d')`;
          queryParams.push(date);
        } else if (dateRangeType === "week") {
          query += ` AND STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y') >= DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d')) DAY)
                    AND STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y') <= DATE_ADD(DATE_SUB(STR_TO_DATE(?, '%Y-%m-%d'), INTERVAL WEEKDAY(STR_TO_DATE(?, '%Y-%m-%d')) DAY), INTERVAL 6 DAY)`;
          queryParams.push(date, date, date, date);
        } else if (dateRangeType === "month") {
          query += ` AND YEAR(STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y')) = YEAR(STR_TO_DATE(?, '%Y-%m-%d'))
                    AND MONTH(STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y')) = MONTH(STR_TO_DATE(?, '%Y-%m-%d'))`;
          queryParams.push(date, date);
        }
      } else {
        query += ` AND STR_TO_DATE(mdt.Date_Insert_Day, '%d/%m/%Y') >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)`;
      }

      if (model) {
        query += ` AND mdt.Machine_Name LIKE ?`;
        queryParams.push(`${model}%`);
      } else if (machine) {
        query += ` AND mdt.Machine_Name = ?`;
        queryParams.push(machine);
      }

      query += ` GROUP BY mdt.Machine_Name`;

      const { success, data, error } = await executeQuery(query, queryParams);

      if (!success) {
        throw new Error(`Database query failed: ${error}`);
      }      return normalizeAvailabilityMetrics(data);
    }
  },

  // Comprehensive dashboard data resolver
  getDashboardData: {
    type: new GraphQLObjectType({
      name: 'DashboardData',
      fields: {
        productionChart: { type: new GraphQLList(ProductionChartType) },
        sidecards: { type: SideCardType },
        machinePerformance: { type: new GraphQLList(MachinePerformanceType) },
        availabilityTrend: { type: new GraphQLList(AvailabilityTrendType) }
      }
    }),
    args: {
      filters: { type: FilterInputType }
    },
    resolve: async (_, { filters = {} }) => {
      try {
        // Get all the data in parallel
        const [
          productionChartResult,
          sidecardsResult,
          machinePerformanceResult,
          availabilityTrendResult
        ] = await Promise.all([
          // Get production chart data
          dailyProductionResolvers.getProductionChart.resolve(_, { filters }),
          // Get sidecards data
          dailyProductionResolvers.getProductionSidecards.resolve(_, { filters }),
          // Get machine performance data
          dailyProductionResolvers.getMachinePerformance.resolve(_, { filters }),
          // Get availability trend data
          dailyProductionResolvers.getAvailabilityTrend.resolve(_, { filters })
        ]);

        return {
          productionChart: productionChartResult || [],
          sidecards: sidecardsResult || { goodqty: 0, rejetqty: 0 },
          machinePerformance: machinePerformanceResult || [],
          availabilityTrend: availabilityTrendResult || []
        };
      } catch (error) {
        console.error('Error in getDashboardData:', error);
        throw new Error(`Failed to fetch dashboard data: ${error.message}`);
      }
    }
  }
};

// Export types for schema
export const dailyTableTypes = {
  DailyProductionType,
  ProductionChartType,
  SideCardType,
  MachineModelType,
  MachineNameType,
  MachinePerformanceType,
  AvailabilityTrendType,
  PerformanceMetricsType,
  FilterInputType
};

// Export queries for schema
export const dailyTableQueries = dailyProductionResolvers;

export default dailyProductionResolvers;
