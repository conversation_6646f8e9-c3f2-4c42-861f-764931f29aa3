import{j as e}from"./index-Nnj1g72A.js";import{r as a}from"./react-vendor-tYPmozCJ.js";import{u as n}from"./useStopTableGraphQL-BIk1S3c3.js";import{T as l,a9 as i,S as s,ar as t,O as r,a5 as c}from"./antd-vendor-4OvKHZ_k.js";const{Title:d,Text:o}=l,{Option:h}=t,g=()=>{const{machineModels:l,filteredMachineNames:g,selectedModel:m,selectedMachine:M,loading:u,error:y,handleModelChange:x,handleMachineChange:j}=function(){const[e,l]=a.useState([]),[i,s]=a.useState([]),[t,r]=a.useState([]),[c,d]=a.useState(""),[o,h]=a.useState(""),[g,m]=a.useState(!1),[M,u]=a.useState(null),y=n();return a.useEffect((()=>{!async function(){m(!0);try{const e=await y.getMachineModels();l(Array.isArray(e)?e:[]);const a=await y.getMachineNames();s(Array.isArray(a)?a:[]),r(Array.isArray(a)?a:[]),u(null)}catch(e){u(e.message),l(["IPS","AKROS","ML","FCS"])}finally{m(!1)}}()}),[y]),{machineModels:e,allMachineNames:i,filteredMachineNames:t,selectedModel:c,selectedMachine:o,loading:g,error:M,handleModelChange:async e=>{if(d(e),h(""),e){m(!0);try{const a=await y.getMachineNames({model:e});r(Array.isArray(a)?a:[]),u(null)}catch(a){u(a.message),r([])}finally{m(!1)}}else r(i)},handleMachineChange:e=>{h(e)},resetSelections:()=>{d(""),h(""),r(i)}}}(),S=[{title:"Machine Name",dataIndex:"Machine_Name",key:"name",render:e=>e||"Unknown"},{title:"Raw Data",key:"raw",render:(a,n)=>e.jsx("pre",{style:{maxHeight:"100px",overflow:"auto"},children:JSON.stringify(n,null,2)})}];return e.jsxs("div",{style:{padding:24},children:[e.jsx(d,{level:2,children:"Machine Data Fixer Test"}),y&&e.jsx(o,{type:"danger",children:y}),e.jsx(i,{loading:u,children:e.jsxs(s,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsx(o,{strong:!0,children:"Machine Model:"}),e.jsx(t,{style:{width:200,marginLeft:16},value:m,onChange:x,placeholder:"Select a model",allowClear:!0,loading:u,children:l.map((a=>e.jsx(h,{value:a,children:a},a)))})]}),e.jsxs("div",{children:[e.jsx(o,{strong:!0,children:"Machine:"}),e.jsx(t,{style:{width:300,marginLeft:16},value:M,onChange:j,placeholder:"Select a machine",allowClear:!0,loading:u,disabled:0===g.length,children:g.map((a=>e.jsx(h,{value:a.Machine_Name||String(a),children:a.Machine_Name||String(a)},a.Machine_Name||String(a))))})]})]})}),e.jsx(r,{}),e.jsxs(d,{level:3,children:["Machines for ",m||"All Models"]}),e.jsx(c,{dataSource:g,columns:S,rowKey:e=>e.Machine_Name||JSON.stringify(e),pagination:{pageSize:5},loading:u})]})};export{g as default};
