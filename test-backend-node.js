/**
 * NODE.JS COMPATIBLE TEST SCRIPT - THREE FILTERS FREEZE FIX
 * 
 * This script tests the backend components and validates the fix implementation
 */

import fetch from 'node-fetch';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test configuration for Node.js environment
const testConfig = {
  baseURL: 'http://localhost:3000', // Adjust port as needed
  graphqlEndpoint: '/api/graphql',
  testScenarios: {
    scenario1: {
      name: "Backend GraphQL - Triple Filter Query",
      filters: {
        model: "IPS",
        machine: "IPS01", 
        startDate: "2025-04-01",
        endDate: "2025-04-30",
        dateRangeType: "month"
      }
    },
    scenario2: {
      name: "Backend GraphQL - Sequential Filter Application",
      steps: [
        { filters: { model: "IPS" } },
        { filters: { model: "IPS", machine: "IPS01" } },
        { filters: { model: "IPS", machine: "IPS01", startDate: "2025-04-01", endDate: "2025-04-30" } }
      ]
    },
    scenario3: {
      name: "Backend GraphQL - Performance Limits",
      filters: {
        model: "IPS",
        machine: "IPS01",
        startDate: "2025-01-01", 
        endDate: "2025-12-31", // Large date range
        limit: 300 // Test limit enforcement
      }
    }
  }
};

// Performance monitoring for Node.js
const performanceMonitor = {
  metrics: {},
  
  start(label) {
    this.metrics[label] = { start: process.hrtime.bigint() };
    console.log(`⏱️ Starting ${label}`);
  },
  
  end(label) {
    if (this.metrics[label]) {
      this.metrics[label].end = process.hrtime.bigint();
      this.metrics[label].duration = Number(this.metrics[label].end - this.metrics[label].start) / 1000000; // Convert to ms
      console.log(`✅ ${label} completed in ${this.metrics[label].duration.toFixed(2)}ms`);
    }
  },
  
  report() {
    console.log('\n📊 Performance Report:');
    Object.entries(this.metrics).forEach(([label, metric]) => {
      if (metric.duration) {
        console.log(`  ${label}: ${metric.duration.toFixed(2)}ms`);
      }
    });
    return this.metrics;
  }
};

// GraphQL query utilities
const graphqlQueries = {
  getAllMachineStops: `
    query GetAllMachineStops($filters: StopFilterInput) {
      getAllMachineStops(filters: $filters) {
        Machine_Name
        Date_Insert
        Part_NO
        Code_Stop
        duration_minutes
      }
    }
  `,
  
  getStopDashboardData: `
    query GetStopDashboardData($filters: StopFilterInput) {
      getStopDashboardData(filters: $filters) {
        allStops {
          Machine_Name
          Date_Insert
          Code_Stop
          duration_minutes
        }
        sidecards {
          Arret_Totale
          Arret_Totale_nondeclare
        }
        topStops {
          stopName
          count
        }
      }
    }
  `
};

// Test execution engine for Node.js
const testEngine = {
  async executeGraphQLQuery(query, variables = {}) {
    const url = `${testConfig.baseURL}${testConfig.graphqlEndpoint}`;
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          variables
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(`GraphQL errors: ${result.errors.map(e => e.message).join(', ')}`);
      }

      return result.data;
    } catch (error) {
      console.error('❌ GraphQL query failed:', error.message);
      throw error;
    }
  },

  async testTripleFilterQuery(filters) {
    console.log('🔍 Testing triple filter query with filters:', filters);
    
    performanceMonitor.start('Triple Filter Query');
    
    try {
      const result = await this.executeGraphQLQuery(
        graphqlQueries.getAllMachineStops,
        { filters }
      );
      
      performanceMonitor.end('Triple Filter Query');
      
      const recordCount = result.getAllMachineStops?.length || 0;
      console.log(`📊 Query returned ${recordCount} records`);
      
      // Validate performance expectations
      const queryTime = performanceMonitor.metrics['Triple Filter Query'].duration;
      
      if (queryTime > 10000) {
        console.warn(`⚠️ Slow query detected: ${queryTime.toFixed(2)}ms`);
        return false;
      }
      
      if (recordCount > 500) {
        console.warn(`⚠️ Large dataset returned: ${recordCount} records (expected max 500 for triple filters)`);
        return false;
      }
      
      console.log('✅ Triple filter query performed within expected parameters');
      return true;
      
    } catch (error) {
      performanceMonitor.end('Triple Filter Query');
      console.error('❌ Triple filter query failed:', error.message);
      return false;
    }
  },

  async testSequentialFilters(steps) {
    console.log('🔄 Testing sequential filter application');
    
    let allPassed = true;
    
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      const stepName = `Sequential Step ${i + 1}`;
      
      console.log(`📋 ${stepName}: ${Object.keys(step.filters).join(', ')}`);
      
      performanceMonitor.start(stepName);
      
      try {
        const result = await this.executeGraphQLQuery(
          graphqlQueries.getAllMachineStops,
          step
        );
        
        performanceMonitor.end(stepName);
        
        const recordCount = result.getAllMachineStops?.length || 0;
        console.log(`  📊 Returned ${recordCount} records`);
        
      } catch (error) {
        performanceMonitor.end(stepName);
        console.error(`  ❌ Step failed: ${error.message}`);
        allPassed = false;
      }
    }
    
    return allPassed;
  },

  async testPerformanceLimits(filters) {
    console.log('🎯 Testing performance limits enforcement');
    
    performanceMonitor.start('Performance Limits Test');
    
    try {
      const result = await this.executeGraphQLQuery(
        graphqlQueries.getAllMachineStops,
        { filters }
      );
      
      performanceMonitor.end('Performance Limits Test');
      
      const recordCount = result.getAllMachineStops?.length || 0;
      console.log(`📊 Query with large date range returned ${recordCount} records`);
      
      // Should respect the limit parameter
      if (recordCount > filters.limit) {
        console.error(`❌ Limit not enforced: got ${recordCount}, expected max ${filters.limit}`);
        return false;
      }
      
      console.log('✅ Performance limits properly enforced');
      return true;
      
    } catch (error) {
      performanceMonitor.end('Performance Limits Test');
      console.error('❌ Performance limits test failed:', error.message);
      return false;
    }
  },

  async checkServerHealth() {
    console.log('🏥 Checking server health...');
    
    try {
      const response = await fetch(`${testConfig.baseURL}/api/health`);
      if (response.ok) {
        console.log('✅ Server is healthy');
        return true;
      } else {
        console.warn('⚠️ Server health check failed, but continuing tests...');
        return true; // Continue tests anyway
      }
    } catch (error) {
      console.warn('⚠️ Could not check server health, assuming server is running...');
      return true; // Continue tests anyway
    }
  }
};

// Main test runner
const runBackendTests = async () => {
  console.log('🧪 STARTING BACKEND TRIPLE FILTER TESTS');
  console.log('=======================================');
  
  // Check server health first
  const serverHealthy = await testEngine.checkServerHealth();
  if (!serverHealthy) {
    console.error('❌ Server is not accessible. Please ensure the backend is running.');
    process.exit(1);
  }
  
  const results = {};
  
  // Test scenario 1: Direct triple filter query
  console.log('\n🎯 Testing Scenario 1: Triple Filter Query');
  results.scenario1 = await testEngine.testTripleFilterQuery(
    testConfig.testScenarios.scenario1.filters
  );
  
  // Test scenario 2: Sequential filter application
  console.log('\n🎯 Testing Scenario 2: Sequential Filter Application');
  results.scenario2 = await testEngine.testSequentialFilters(
    testConfig.testScenarios.scenario2.steps
  );
  
  // Test scenario 3: Performance limits
  console.log('\n🎯 Testing Scenario 3: Performance Limits');
  results.scenario3 = await testEngine.testPerformanceLimits(
    testConfig.testScenarios.scenario3.filters
  );
  
  // Generate report
  console.log('\n📊 TEST RESULTS');
  console.log('================');
  Object.entries(results).forEach(([scenario, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${scenario}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🏆 Overall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  performanceMonitor.report();
  
  return { results, performance: performanceMonitor.metrics, success: allPassed };
};

// Frontend test instructions
const printFrontendInstructions = () => {
  console.log('\n🌐 FRONTEND TESTING INSTRUCTIONS');
  console.log('================================');
  console.log('To test the frontend fixes, follow these steps:');
  console.log('');
  console.log('1. Open your browser and navigate to: http://localhost:3000/arrets-dashboard');
  console.log('2. Open browser Developer Tools (F12)');
  console.log('3. Go to Console tab');
  console.log('4. Copy and paste the browser test script:');
  console.log('');
  console.log('// BROWSER TEST SCRIPT');
  console.log('window.testTripleFilters = async () => {');
  console.log('  console.log("🧪 Testing Triple Filter Scenario");');
  console.log('  ');
  console.log('  // Test the exact scenario that was causing freezes');
  console.log('  console.log("1️⃣ Default IPS model should be selected");');
  console.log('  ');
  console.log('  console.log("2️⃣ Manually select machine IPS01");');
  console.log('  console.log("3️⃣ Wait for data to load (should be fast)");');
  console.log('  ');
  console.log('  console.log("4️⃣ Manually select date filter: April 2025");');
  console.log('  console.log("5️⃣ Watch for freeze - should NOT freeze!");');
  console.log('  ');
  console.log('  // Monitor performance');
  console.log('  const startTime = performance.now();');
  console.log('  setTimeout(() => {');
  console.log('    const elapsed = performance.now() - startTime;');
  console.log('    console.log(`⏱️ Time since filter change: ${elapsed.toFixed(2)}ms`);');
  console.log('    if (elapsed < 10000) {');
  console.log('      console.log("✅ No freeze detected - fix working!");');
  console.log('    } else {');
  console.log('      console.log("❌ Potential freeze - needs investigation");');
  console.log('    }');
  console.log('  }, 8000);');
  console.log('};');
  console.log('');
  console.log('window.testTripleFilters();');
  console.log('');
  console.log('5. Execute the script and follow the manual steps');
  console.log('6. Observe console output for performance monitoring');
};

// Error handling for missing dependencies
const checkDependencies = () => {
  try {
    // This will be available if running as ES module
    return true;
  } catch (error) {
    console.error('❌ Missing dependencies. Please ensure you have node-fetch installed:');
    console.error('npm install node-fetch');
    return false;
  }
};

// Main execution
const main = async () => {
  if (!checkDependencies()) {
    process.exit(1);
  }
  
  console.log('🎯 Three Filter Freeze Fix - Backend Testing');
  console.log(`📍 Server URL: ${testConfig.baseURL}`);
  console.log('');
  
  try {
    const results = await runBackendTests();
    
    printFrontendInstructions();
    
    if (results.success) {
      console.log('\n🎉 All backend tests passed! The triple filter freeze fix is working.');
      process.exit(0);
    } else {
      console.log('\n⚠️ Some backend tests failed. Please check the server logs.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 Test execution failed:', error.message);
    console.error('\nPossible causes:');
    console.error('- Backend server is not running');
    console.error('- GraphQL endpoint is not accessible');
    console.error('- Database connection issues');
    process.exit(1);
  }
};

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  testConfig,
  testEngine,
  performanceMonitor,
  runBackendTests,
  main
};
