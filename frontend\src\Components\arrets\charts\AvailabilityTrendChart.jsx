import React, { memo } from 'react';
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Area, AreaChart } from 'recharts';
import { Empty, Spin } from 'antd';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const AvailabilityTrendChart = memo(({ data = [], loading = false }) => {
  if (loading) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }
  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);

  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="Aucune donnée de disponibilité disponible" />      </div>
    );
  }  // Process data to focus only on availability
  const processedData = safeData.map(item => {
    let disponibilite = parseFloat(item.disponibilite || item.availability || 0);
    
    // If the value is between 0 and 1, it's likely a decimal (e.g., 0.85 for 85%)
    // Convert it to percentage for proper display
    if (disponibilite > 0 && disponibilite <= 1) {
      disponibilite = disponibilite * 100;
    }
    
    return {
      date: item.date || item.Stop_Date,
      disponibilite: Math.round(disponibilite * 100) / 100, // Round to 2 decimals
      downtime: item.downtime || 0,
      stopCount: item.stopCount || 0
    };
  }).filter(item => item.date); // Only include items with valid dates

  // Calculate average availability for reference line
  const avgAvailability = processedData.reduce((sum, item) => sum + item.disponibilite, 0) / processedData.length;return (
    <ResponsiveContainer width="100%" height="100%">      
      <AreaChart data={processedData} margin={{ top: 20, right: 20, left: 10, bottom: 30 }}>
        <defs>
          <linearGradient id="availabilityGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={SOMIPEM_COLORS.PRIMARY_BLUE} stopOpacity={0.4}/>
            <stop offset="95%" stopColor={SOMIPEM_COLORS.PRIMARY_BLUE} stopOpacity={0.1}/>
          </linearGradient>
        </defs>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" strokeWidth={1} />
        <XAxis
          dataKey="date"
          tick={{ fill: SOMIPEM_COLORS.LIGHT_GRAY, fontSize: 11 }}
          height={30}
          angle={-30}
          textAnchor="end"
          tickFormatter={(date) => {
            try {
              return new Date(date).toLocaleDateString('fr-FR', { 
                day: '2-digit', 
                month: '2-digit' 
              });
            } catch {
              return date;
            }
          }}
        />        <YAxis
          tick={{ fill: SOMIPEM_COLORS.LIGHT_GRAY, fontSize: 11 }}
          width={40}
          domain={[0, 100]}
          tickCount={5}
          tickFormatter={(value) => `${value}%`}
          label={{
            value: "Disponibilité (%)",
            angle: -90,
            position: "insideLeft",
            style: { fill: SOMIPEM_COLORS.DARK_GRAY, fontSize: 12 },
            offset: 0
          }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`,
            borderRadius: 4,
            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
            fontSize: "12px",
            color: SOMIPEM_COLORS.DARK_GRAY
          }}          formatter={(value, name) => {
            if (name === 'Disponibilité') {
              return [`${value.toFixed(1)}%`, 'Disponibilité'];
            }
            return [value, name];
          }}
          labelFormatter={(date) => {
            try {
              return new Date(date).toLocaleDateString('fr-FR', { 
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              });
            } catch {
              return date;
            }
          }}
        />        <Area
          type="monotone"
          dataKey="disponibilite"
          stroke={SOMIPEM_COLORS.PRIMARY_BLUE}
          strokeWidth={2}
          fill="url(#availabilityGradient)"
          dot={{ fill: SOMIPEM_COLORS.PRIMARY_BLUE, strokeWidth: 1, r: 3 }}
          activeDot={{ r: 5, fill: "#fff", stroke: SOMIPEM_COLORS.PRIMARY_BLUE, strokeWidth: 2 }}
          animationDuration={1000}
        />
        {/* Reference line for average */}
        <Line
          type="monotone"
          dataKey={() => avgAvailability}
          stroke={SOMIPEM_COLORS.SECONDARY_BLUE}
          strokeWidth={2}
          strokeDasharray="5 5"
          dot={false}
          name={`Moyenne: ${avgAvailability.toFixed(1)}%`}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
});

AvailabilityTrendChart.displayName = 'AvailabilityTrendChart';

export default AvailabilityTrendChart;
