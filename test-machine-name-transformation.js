// Test machine name transformation logic
const machineNames = [
  { Machine_Name: "CCM24SB" },
  { Machine_Name: "CCM24SC" },
  { Machine_Name: "IPS01" },
  { Machine_Name: "IPS02" },
  { Machine_Name: "IPS03" },
  { Machine_Name: "IPSO1" }
];

console.log('🧪 Testing machine name transformation...\n');

const transformedNames = machineNames.map(item => {
  const machineName = item.Machine_Name;
  // Extract model from machine name (e.g., "IPS01" -> "IPS", "CCM24SB" -> "CCM")
  // Check longer strings first to avoid "IPSO1" being matched as "IPS"
  let model = '';
  if (machineName.startsWith('IPSO')) {
    model = 'IPSO';
  } else if (machineName.startsWith('IPS')) {
    model = 'IPS';
  } else if (machineName.startsWith('CCM')) {
    model = 'CCM';
  } else {
    // Fallback: extract letters from the beginning
    model = machineName.match(/^[A-Za-z]+/)?.[0] || 'UNKNOWN';
  }
  
  return {
    name: machineName,
    model: model
  };
});

console.log('✅ Transformed machine names:', transformedNames);

// Test filtering by model
console.log('\n🔍 Testing filtering by model "IPS":');
const ipsFiltered = transformedNames.filter(name => name.model === 'IPS');
console.log('IPS machines:', ipsFiltered);

console.log('\n🔍 Testing filtering by model "CCM":');
const ccmFiltered = transformedNames.filter(name => name.model === 'CCM');
console.log('CCM machines:', ccmFiltered);

console.log('\n🔍 Testing filtering by model "IPSO":');
const ipsoFiltered = transformedNames.filter(name => name.model === 'IPSO');
console.log('IPSO machines:', ipsoFiltered);
