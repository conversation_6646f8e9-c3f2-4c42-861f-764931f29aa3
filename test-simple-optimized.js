/**
 * Simple test for optimized GraphQL resolver
 */

import fetch from 'node-fetch';

async function testSimpleQuery() {
  console.log('🧪 Testing optimized GraphQL resolver...');
  
  const query = `
    query {
      getStopMachineModels {
        model
      }
    }
  `;

  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query })
    });

    if (!response.ok) {
      console.error(`❌ HTTP error! status: ${response.status}`);
      return;
    }

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return;
    }

    console.log('✅ Success! Machine models:', result.data);
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSimpleQuery();
