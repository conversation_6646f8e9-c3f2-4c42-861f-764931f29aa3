/**
 * Test GraphQL Query for getAllMachineStops
 * Debug the actual data being returned from the database
 */

async function testGraphQLQuery() {
  console.log('🧪 Testing GraphQL getAllMachineStops query...');
  
  // Test filters that should match April 2025 data
  const testFilters = {
    model: "IPS",
    machine: "IPS01",
    date: "2025-04-15",
    startDate: "2025-04-01", 
    endDate: "2025-04-30",
    dateRangeType: "month"
  };
  
  console.log('🎯 Test filters:', testFilters);
  
  try {
    const response = await fetch('/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query($filters: StopFilterInput) {
            getAllMachineStops(filters: $filters) {
              Date_Insert
              Machine_Name
              Part_NO
              Code_Stop
              Debut_Stop
              Fin_Stop_Time
              Regleur_Prenom
              duration_minutes
            }
          }
        `,
        variables: { filters: testFilters }
      })
    });
    
    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return;
    }
    
    const stops = result.data?.getAllMachineStops || [];
    
    console.log('📊 Query Results:');
    console.log('- Total stops returned:', stops.length);
    console.log('- Sample stops:', stops.slice(0, 3));
    console.log('- Date formats:', stops.slice(0, 5).map(s => s.Date_Insert));
    console.log('- Machines:', [...new Set(stops.map(s => s.Machine_Name))]);
    
    // Test date parsing
    console.log('\n🔍 Date parsing test:');
    const sampleDates = stops.slice(0, 5).map(s => s.Date_Insert);
    sampleDates.forEach(dateStr => {
      const match = dateStr.match(/^(\d{4}) \d{2}:\d{2}:\d{2}-(\d{1,2})-\s*(\d{1,2})$/);
      if (match) {
        const [_, year, month, day] = match;
        const isoDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        console.log(`${dateStr} -> ${isoDate}`);
      } else {
        console.log(`${dateStr} -> PARSE FAILED`);
      }
    });
    
    // Check if backend filtering is working
    console.log('\n🎯 Backend Filtering Analysis:');
    if (stops.length === 0) {
      console.log('❌ No data returned - backend filtering might be too strict or no data exists');
    } else {
      const machines = [...new Set(stops.map(s => s.Machine_Name))];
      const hasCorrectMachine = machines.includes('IPS01');
      console.log('- Machines in results:', machines);
      console.log('- Contains IPS01:', hasCorrectMachine);
      
      if (!hasCorrectMachine) {
        console.log('❌ Backend machine filtering failed - should only return IPS01 data');
      } else {
        console.log('✅ Backend machine filtering working');
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing GraphQL query:', error);
  }
}

// Run the test if we're on the frontend
if (typeof window !== 'undefined') {
  testGraphQLQuery();
}
