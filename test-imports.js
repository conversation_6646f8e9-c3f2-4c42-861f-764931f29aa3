/**
 * Test if the PDF components can be imported without errors
 */

console.log('🧪 Testing PDF Component Imports...\n');

// Test 1: Check if the files exist
import fs from 'fs';
import path from 'path';

const componentsToCheck = [
  'frontend/src/pages/reports/pdf-test-simple.jsx',
  'frontend/src/pages/reports/pdf-test.jsx', 
  'frontend/src/pages/reports/pdf-preview.jsx',
  'frontend/src/components/reports/PDFReportTemplate.jsx'
];

console.log('📁 Checking file existence...');
for (const component of componentsToCheck) {
  const exists = fs.existsSync(component);
  console.log(`  ${exists ? '✅' : '❌'} ${component}`);
}

// Test 2: Check for common import issues
console.log('\n🔍 Checking for common import issues...');

const checkFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check for common issues
    const issues = [];
    
    if (content.includes('import React') && !content.includes("import React from 'react'")) {
      issues.push('React import might be incorrect');
    }
    
    if (content.includes('useEffect') && !content.includes('import React, { useEffect }')) {
      issues.push('useEffect import might be missing');
    }
    
    if (content.includes('useState') && !content.includes('useState')) {
      issues.push('useState import might be missing');
    }
    
    if (content.includes('Chart') && !content.includes('chart.js')) {
      issues.push('Chart.js imports might be missing');
    }
    
    if (content.includes('dayjs') && !content.includes("import dayjs")) {
      issues.push('dayjs import might be missing');
    }
    
    return issues;
  } catch (error) {
    return [`File read error: ${error.message}`];
  }
};

for (const component of componentsToCheck) {
  if (fs.existsSync(component)) {
    const issues = checkFile(component);
    console.log(`  📄 ${path.basename(component)}: ${issues.length === 0 ? '✅ No issues' : '⚠️ ' + issues.join(', ')}`);
  }
}

// Test 3: Check if routes are properly added to App.jsx
console.log('\n🛣️ Checking App.jsx routes...');
try {
  const appContent = fs.readFileSync('frontend/src/App.jsx', 'utf8');
  
  const routeChecks = [
    { route: '/reports/pdf-test-simple', component: 'PDFTestSimplePage' },
    { route: '/reports/pdf-test', component: 'PDFTestPage' },
    { route: '/reports/pdf-preview', component: 'PDFPreviewPage' }
  ];
  
  for (const check of routeChecks) {
    const hasRoute = appContent.includes(check.route);
    const hasComponent = appContent.includes(check.component);
    const hasImport = appContent.includes(`import("./pages/reports/`);
    
    console.log(`  📍 ${check.route}:`);
    console.log(`    Route defined: ${hasRoute ? '✅' : '❌'}`);
    console.log(`    Component referenced: ${hasComponent ? '✅' : '❌'}`);
    console.log(`    Import statement: ${hasImport ? '✅' : '❌'}`);
  }
  
} catch (error) {
  console.log(`  ❌ Error reading App.jsx: ${error.message}`);
}

console.log('\n💡 If all checks pass but components still don\'t load:');
console.log('   1. Check browser console for runtime errors');
console.log('   2. Verify all dependencies are installed (npm install)');
console.log('   3. Try restarting the development server');
console.log('   4. Check for TypeScript/JSX compilation errors');

console.log('\n🔗 Test URLs:');
console.log('   http://localhost:5173/reports/pdf-test-simple');
console.log('   http://localhost:5173/reports/pdf-test');
console.log('   http://localhost:5173/reports/pdf-preview?data=eyJ0ZXN0IjoidmFsdWUifQ==');
