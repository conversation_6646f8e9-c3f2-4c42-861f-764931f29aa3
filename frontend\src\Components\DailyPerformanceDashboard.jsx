"use client"

import { useEffect, useState, useRef } from "react"
import superagent from "superagent"
import {
  Card,
  Row,
  Col,
  Progress,
  Statistic,
  Spin,
  Alert,
  Tabs,
  Modal,
  Table,
  Tag,
  Empty,
  Button,
  Badge,
  Divider,
  Typography,
  Space,
  message,
  notification,
} from "antd"
//import ShiftReportButton from "./ShiftReportButton"
//import TestShiftReportButton from "./TestShiftReportButton"
import {
  DashboardOutlined,
  LineChartOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  AlertOutlined,
  ToolOutlined,
  InfoCircleOutlined,
  FilterOutlined,
  FileTextOutlined,
  AreaC<PERSON>Outlined,
  Clock<PERSON><PERSON><PERSON>Outlined,
  HistoryOutlined,
  PlayCircleOutlined,
  WarningOutlined,
  RiseOutlined,
} from "@ant-design/icons"
import { Tooltip, Popover, Radio } from "antd"

import { Bar, Line } from "react-chartjs-2"
import { useTheme } from "../theme-context"
import { registerChartComponents, updateChartColors, getBaseChartOptions } from "./chart-config"
import { useAuth } from "../hooks/useAuth"
import EnhancedMachineCard from "./dashboard/enhanced-machine-card"
import "./dashboard/machine-card.css"
import websocketService from "../utils/websocketService"

// Add CSS for machine card placeholder and WebSocket status indicators
const machineCardPlaceholderStyle = `
  .machine-card-placeholder {
    position: relative;
    transition: all 0.3s;
  }
  .machine-card-placeholder:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .info-card {
    background: var(--info-card-bg, #f9f9f9);
    color: var(--info-card-text, inherit);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    transition: all 0.3s;
  }

  [data-theme="dark"] .info-card {
    --info-card-bg: #141414;
    --info-card-text: rgba(255, 255, 255, 0.85);
  }

  .info-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .info-card-title {
    font-weight: 500;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
  }

  .info-card-title .anticon {
    margin-right: 8px;
    color: #1890ff;
  }

  .info-card-content {
    display: flex;
    flex-wrap: wrap;
  }

  .info-item {
    flex: 1 0 50%;
    margin-bottom: 8px;
  }

  .info-label {
    color: var(--info-label-color, #8c8c8c);
    font-size: 12px;
  }

  .info-value {
    font-weight: 500;
    color: var(--info-value-color, inherit);
  }

  [data-theme="dark"] {
    --info-label-color: rgba(255, 255, 255, 0.45);
    --info-value-color: rgba(255, 255, 255, 0.85);
  }

  .chart-container {
    background: var(--chart-bg, white);
    color: var(--chart-text, #000);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid var(--chart-border, rgba(0, 0, 0, 0.06));
  }

  .chart-container:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  }

  .chart-title {
    font-weight: 500;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    color: var(--chart-title, inherit);
    padding-bottom: 8px;
    border-bottom: 1px solid var(--chart-border, rgba(0, 0, 0, 0.06));
  }

  .chart-title .anticon {
    margin-right: 8px;
    color: var(--chart-icon, #1890ff);
  }

  .chart-container canvas {
    margin: 0 auto;
  }

  /* Tooltip custom styling */
  .chart-tooltip {
    background-color: var(--chart-tooltip-bg, rgba(255, 255, 255, 0.95)) !important;
    border-color: var(--chart-border, rgba(0, 0, 0, 0.1)) !important;
    color: var(--chart-text, rgba(0, 0, 0, 0.7)) !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  }

  /* Dark mode styles */
  [data-theme="dark"] .chart-container {
    --chart-bg: #141414;
    --chart-text: rgba(255, 255, 255, 0.85);
    --chart-title: rgba(255, 255, 255, 0.85);
    --chart-icon: #1890ff;
    --chart-border: rgba(255, 255, 255, 0.1);
    --chart-tooltip-bg: rgba(33, 33, 33, 0.95);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  [data-theme="dark"] .chart-container:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  }

  /* Ensure chart legends are properly styled in dark mode */
  [data-theme="dark"] .chart-container .recharts-legend-item-text,
  [data-theme="dark"] .chart-container .recharts-cartesian-axis-tick-value {
    fill: var(--chart-text, rgba(255, 255, 255, 0.85)) !important;
    color: var(--chart-text, rgba(255, 255, 255, 0.85)) !important;
  }

  /* WebSocket status indicators */
  .ws-status-tag {
    display: inline-flex;
    align-items: center;
    transition: all 0.3s;
  }

  .ws-status-tag .anticon {
    margin-right: 6px;
  }

  .ws-status-updating {
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.7;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.7;
    }
  }

  /* Smooth transitions for data updates */
  .ant-statistic-content-value-int,
  .ant-statistic-content-value-decimal,
  .ant-progress-inner,
  .ant-progress-bg,
  .ant-tag,
  .ant-badge-status-text,
  .ant-progress-text {
    transition: all 0.5s ease-in-out;
  }

  /* Highlight effect for updated values */
  .value-updated {
    animation: highlight-update 1.5s ease-out;
  }

  @keyframes highlight-update {
    0% {
      background-color: rgba(24, 144, 255, 0.2);
    }
    100% {
      background-color: transparent;
    }
  }

  /* Make machine cards transition smoothly */
  .machine-card-container {
    transition: all 0.3s ease-in-out;
  }

  /* Ensure no flicker during updates */
  .ant-card,
  .ant-table-wrapper,
  .ant-progress,
  .ant-statistic {
    will-change: contents;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
`

// Add the style to the document
if (typeof document !== "undefined") {
  const styleElement = document.createElement("style")
  styleElement.textContent = machineCardPlaceholderStyle
  document.head.appendChild(styleElement)
}

const { Title, Text } = Typography
const { TabPane } = Tabs

// Enregistrer les composants Chart.js au chargement du module
registerChartComponents()

const DailyPerformanceDashboard = () => {
  const { darkMode } = useTheme()
  const { isAuthenticated, user } = useAuth() // Get user info as well
  const chartRefs = useRef({})

  // Import and use the WebSocket service

  // Add state to track machine session status
  const [sessionStatus, setSessionStatus] = useState({})

  // Add state for WebSocket connection and status
  const [socket, setSocket] = useState(null)
  const [wsStatus, setWsStatus] = useState({
    connected: false,
    connecting: false,
    updating: false
  })

  const [currentDate] = useState(
    new Date().toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  )

  // Add these state variables inside the DailyPerformanceDashboard component's useState
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [selectedShift, setSelectedShift] = useState("all")
  const [selectedView, setSelectedView] = useState("machines")
  const [operatorStats, setOperatorStats] = useState([])
  const [productionStats, setProductionStats] = useState(null)

  const [state, setState] = useState({
    machineData: [],
    previousMachineData: [], // Pour suivre les changements d'état des machines
    sideCardData: {},
    dailyStats: [],
    selectedMachine: null,
    machineHistory: [],
    historyLoading: false,
    historyError: null,
    loading: true,
    error: null,
    visible: false,
    lastUpdate: new Date(),
  })

  const baseURL =
    process.env.NODE_ENV === "production" ? "https://charming-hermit-intense.ngrok-free.app" : "http://localhost:5000"
  // 🔒 SECURITY: HTTP-only cookies configuration - no axios configuration needed
  // Authentication is handled automatically via HTTP-only cookies

  // Mettre à jour les couleurs des graphiques lorsque le mode sombre change
  useEffect(() => {
    // Update chart colors based on dark mode
    updateChartColors(darkMode)

    // Apply theme attribute to document for CSS variables
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', darkMode ? 'dark' : 'light')
    }

    // Force update any existing charts
    Object.values(chartRefs.current).forEach(chart => {
      if (chart && chart.current) {
        chart.current.update()
      }
    })
  }, [darkMode])

  // Helper function to safely parse float values
  const safeParseFloat = (value) => {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  };



  // Fallback function to fetch data via REST API if WebSocket fails
  const fetchDataFallback = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true }))

      // 🔒 SECURITY: Use HTTP-only cookies for authentication
      const baseURL = process.env.NODE_ENV === "production" ? "https://charming-hermit-intense.ngrok-free.app" : "http://localhost:5000"
      const responses = await Promise.all([
        superagent.get(baseURL + "/api/RealTimeTable").withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + "/api/MachineCard").withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + "/api/sidecards").withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + "/api/dailyStats").withCredentials().timeout(30000).retry(2),
      ])

      const [rawData, machineCards, sideCards, daily] = responses.map(r => r.body)

      // Save the previous state before updating
      const previousMachineData = [...state.machineData]

      const formattedMachineData = machineCards.map((m) => {
        // Calculate status based on TRS
        const trs = safeParseFloat(m.TRS || "0")
        const status = trs > 80 ? "success" : trs > 60 ? "warning" : "error"
        // Calculate progress
        const quantiteBon = safeParseFloat(m.Quantite_Bon || "0")
        const quantitePlanifier = safeParseFloat(m.Quantite_Planifier || "0")
        const progress = (quantiteBon / (quantitePlanifier || 1)) * 100

        return {
          ...m,
          status: status,
          progress: progress,
        }
      })

      setState((prev) => ({
        ...prev,
        previousMachineData: previousMachineData,
        machineData: formattedMachineData,
        sideCardData: sideCards[0] || {},
        dailyStats: daily,
        error: null,
        loading: false,
        lastUpdate: new Date(),
      }))

      // Pass the previous state to handleMachineSessions
      await handleMachineSessions(formattedMachineData, previousMachineData)
    } catch (error) {
      console.error("Error fetching data:", error)
      setState((prev) => ({
        ...prev,
        error: error.message || "Failed to fetch data",
        loading: false,
        lastUpdate: new Date(),
      }))
    }
  }
  // Setup WebSocket connection and event listeners
  useEffect(() => {
    // Update WebSocket status to connecting
    setWsStatus(prev => ({ ...prev, connecting: true }));

    // Connect to WebSocket server
    websocketService.connect();

    // Set up event listeners for different types of messages
    const initialDataListener = (data) => {
      console.log("Received initial data from WebSocket");

      // Update WebSocket status - no longer loading
      setWsStatus(prev => ({ ...prev, connecting: false, updating: false }));

      // Format machine data with calculated fields
      const formattedMachineData = data.machineData.map((m) => {
        // Calculate status based on TRS
        const trs = safeParseFloat(m.TRS || "0");
        const status = trs > 80 ? "success" : trs > 60 ? "warning" : "error";

        // Calculate progress
        const quantiteBon = safeParseFloat(m.Quantite_Bon || "0");
        const quantitePlanifier = safeParseFloat(m.Quantite_Planifier || "0");
        const progress = (quantiteBon / (quantitePlanifier || 1)) * 100;

        return {
          ...m,
          status: status,
          progress: progress,
        };
      });

      // Create a map of active sessions by machine ID for quick lookup
      const activeSessionMap = {};
      data.activeSessions.forEach((session) => {
        activeSessionMap[session.machine_id] = session;
      });

      // Update session status state
      const newSessionStatus = {};
      data.activeSessions.forEach((session) => {
        newSessionStatus[session.machine_id] = {
          active: true,
          startTime: new Date(session.session_start),
          lastUpdate: new Date(session.last_updated),
          sessionId: session.id
        };
      });
      setSessionStatus(newSessionStatus);

      // Update state with all fetched data
      setState(prev => ({
        ...prev,
        machineData: formattedMachineData,
        previousMachineData: [...formattedMachineData], // Save a copy for comparison
        sideCardData: data.sideCardData || {},
        dailyStats: data.dailyStats || [],
        error: null,
        loading: false,
        lastUpdate: new Date()
      }));

      // Show success notification
      notification.success({
        message: "Données chargées",
        description: "Connexion en temps réel établie avec succès",
        icon: <CheckCircleOutlined style={{ color: "#52c41a" }} />,
        placement: "bottomRight",
        duration: 3
      });
    };

    const updateListener = (data) => {
      console.log("Received update from WebSocket", data);

      // Use a subtle indicator instead of a loading spinner
      // Just update the status without showing a spinner
      setWsStatus(prev => ({ ...prev, updating: true }));

      // Set a timeout to reset the updating status after a short delay
      setTimeout(() => {
        setWsStatus(prev => ({ ...prev, updating: false }));
      }, 500);

      // Save the previous state before updating
      const previousMachineData = [...state.machineData];

      // Process the changed machines
      const changedMachines = data.data.changedMachines || [];
      const fullData = data.data.fullData || [];

      // Format the full data with calculated fields
      const formattedMachineData = fullData.map((m) => {
        // Calculate status based on TRS
        const trs = safeParseFloat(m.TRS || "0");
        const status = trs > 80 ? "success" : trs > 60 ? "warning" : "error";

        // Calculate progress
        const quantiteBon = safeParseFloat(m.Quantite_Bon || "0");
        const quantitePlanifier = safeParseFloat(m.Quantite_Planifier || "0");
        const progress = (quantiteBon / (quantitePlanifier || 1)) * 100;

        return {
          ...m,
          status: status,
          progress: progress,
        };
      });
      // Update the machine data in state WITHOUT setting loading: true
      setState(prev => {
        return {
          ...prev,
          previousMachineData: prev.machineData,
          machineData: formattedMachineData,
          lastUpdate: new Date()
          // No loading: true here, so the UI won't show a spinner
        };
      });
      console.log("Formatted machine data:", formattedMachineData);

      // Process machine sessions after data is updated
      handleMachineSessions(formattedMachineData, previousMachineData);

      // Only show notification for significant changes to reduce notification fatigue
      if (changedMachines.length > 2) {
        notification.info({
          message: "Données mises à jour",
          description: `${changedMachines.length} machine(s) mise(s) à jour`,
          icon: <ReloadOutlined style={{ color: "#1890ff" }} />,
          placement: "bottomRight",
          duration: 2
        });
      }
    };

    const sessionUpdateListener = (data) => {
      console.log("Received session update from WebSocket", data);

      const { sessionData, updateType } = data;
      const machineId = sessionData.machine_id;

      // Update the session status based on the update type
      if (updateType === 'created' || updateType === 'updated') {
        setSessionStatus(prev => ({
          ...prev,
          [machineId]: {
            active: true,
            startTime: new Date(sessionData.session_start),
            lastUpdate: new Date(sessionData.last_updated),
            sessionId: sessionData.id
          }
        }));

        // Show notification for created/updated session
        const notificationConfig = {
          created: {
            message: "Session démarrée",
            description: `Nouvelle session pour ${sessionData.Machine_Name || 'la machine ' + machineId}`,
            icon: <PlayCircleOutlined style={{ color: "#52c41a" }} />
          },
          updated: {
            message: "Session mise à jour",
            description: `Session mise à jour pour ${sessionData.Machine_Name || 'la machine ' + machineId}`,
            icon: <ReloadOutlined style={{ color: "#1890ff" }} />
          }
        };

        notification.info({
          ...notificationConfig[updateType],
          placement: "bottomRight",
          duration: 3
        });
      } else if (updateType === 'stopped') {
        // For stopped sessions, remove from active sessions
        setSessionStatus(prev => {
          const newStatus = { ...prev };
          delete newStatus[machineId];
          return newStatus;
        });

        // Show notification for stopped session
        notification.info({
          message: "Session terminée",
          description: `Session terminée pour ${sessionData.Machine_Name || 'la machine ' + machineId}`,
          icon: <ClockCircleOutlined style={{ color: "#faad14" }} />,
          placement: "bottomRight",
          duration: 3
        });
      }
    };

    const connectListener = () => {
      console.log("WebSocket connected");
      // Update WebSocket status
      setWsStatus(prev => ({ ...prev, connected: true, connecting: false }));

      // Request initial data when connection is established
      websocketService.requestUpdate();

      notification.success({
        message: "Connexion établie",
        description: "Connexion en temps réel établie avec succès",
        icon: <CheckCircleOutlined style={{ color: "#52c41a" }} />,
        placement: "bottomRight",
        duration: 3,
        key: "websocket-connecting" // Use same key to replace the connecting notification
      });
    };

    const disconnectListener = () => {
      console.log("WebSocket disconnected");
      // Update WebSocket status
      setWsStatus(prev => ({ ...prev, connected: false, connecting: true }));

      notification.warning({
        message: "Connexion perdue",
        description: "Tentative de reconnexion en cours...",
        icon: <ReloadOutlined spin style={{ color: "#faad14" }} />,
        placement: "bottomRight",
        duration: 4,
        key: "websocket-reconnecting"
      });

      // Do not fall back to HTTP polling for testing
      console.log("WebSocket disconnected - NOT falling back to HTTP polling (disabled for testing)");
    };

    const errorListener = (error) => {
      console.error("WebSocket error:", error);

      // Update WebSocket status
      setWsStatus(prev => ({ ...prev, connected: false, connecting: false }));

      notification.error({
        message: "Erreur de connexion",
        description: "Impossible de se connecter au service de données en temps réel. Utilisation du mode de secours.",
        icon: <CloseCircleOutlined style={{ color: "#ff4d4f" }} />,
        placement: "bottomRight",
        duration: 4,
        key: "websocket-error"
      });

      // Update state with error
      setState(prev => ({
        ...prev,
        error: "Erreur de connexion WebSocket"
      }));

      // Do not fall back to HTTP polling for testing
      console.log("WebSocket error - NOT falling back to HTTP polling (disabled for testing)");
    };

    // Register all event listeners
    const unsubscribeInitialData = websocketService.addEventListener('initialData', initialDataListener);
    const unsubscribeUpdate = websocketService.addEventListener('update', updateListener);
    const unsubscribeSessionUpdate = websocketService.addEventListener('sessionUpdate', sessionUpdateListener);
    const unsubscribeConnect = websocketService.addEventListener('connect', connectListener);
    const unsubscribeDisconnect = websocketService.addEventListener('disconnect', disconnectListener);
    const unsubscribeError = websocketService.addEventListener('error', errorListener);

    // Show connecting notification and update WebSocket status
    setWsStatus(prev => ({ ...prev, connecting: true }));
    notification.info({
      message: "Connexion en cours",
      description: "Établissement de la connexion en temps réel...",
      icon: <ReloadOutlined spin style={{ color: "#1890ff" }} />,
      placement: "bottomRight",
      duration: 2,
      key: "websocket-connecting"
    });

    // Do not fall back to HTTP polling for testing
    const fallbackTimeout = setTimeout(() => {
      if (!websocketService.isConnected) {
        console.log("WebSocket connection timeout - NOT falling back to HTTP polling (disabled for testing)");

        // Show notification that we're still waiting for WebSocket
        notification.info({
          message: "WebSocket Connection",
          description: "Still attempting to establish WebSocket connection. No fallback to HTTP polling.",
          icon: <ReloadOutlined spin style={{ color: "#1890ff" }} />,
          placement: "bottomRight",
          duration: 5,
          key: "websocket-waiting"
        });
      }
    }, 5000);

    // Clean up on unmount
    return () => {
      unsubscribeInitialData();
      unsubscribeUpdate();
      unsubscribeSessionUpdate();
      unsubscribeConnect();
      unsubscribeDisconnect();
      unsubscribeError();
      websocketService.disconnect();
      clearTimeout(fallbackTimeout);
    };
  }, []); // Empty dependency array means this runs once on mount

  // Function to fetch data using HTTP as a fallback
  const fetchData = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }))

      // Fetch all required data in parallel using SuperAgent
      const [machineCardsRes, sideCardsRes, dailyStatsRes, activeSessionsRes] = await Promise.all([
        superagent.get(baseURL + '/api/MachineCard').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/sidecards').withCredentials().timeout(30000).retry(2),
        superagent.get(baseURL + '/api/dailyStats').withCredentials().timeout(30000).retry(2),
        fetchActiveSessionsData()
      ])

      // Format machine data with calculated fields
      const formattedMachineData = machineCardsRes.data.map((m) => {
        // Calculate status based on TRS
        const trs = safeParseFloat(m.TRS || "0")
        const status = trs > 80 ? "success" : trs > 60 ? "warning" : "error"

        // Calculate progress
        const quantiteBon = safeParseFloat(m.Quantite_Bon || "0")
        const quantitePlanifier = safeParseFloat(m.Quantite_Planifier || "0")
        const progress = (quantiteBon / (quantitePlanifier || 1)) * 100

        return {
          ...m,
          status: status,
          progress: progress,
        }
      })

      // Create a map of active sessions by machine ID for quick lookup
      const activeSessionMap = {}
      activeSessionsRes.forEach((session) => {
        activeSessionMap[session.machine_id] = session
      })

      // Update session status state
      const newSessionStatus = {}
      activeSessionsRes.forEach((session) => {
        newSessionStatus[session.machine_id] = {
          active: true,
          startTime: new Date(session.session_start),
          lastUpdate: new Date(session.last_updated),
          sessionId: session.id
        }
      })
      setSessionStatus(newSessionStatus)

      // Update state with all fetched data
      setState(prev => ({
        ...prev,
        machineData: formattedMachineData,
        previousMachineData: [...formattedMachineData], // Save a copy for comparison
        sideCardData: sideCardsRes.data[0] || {},
        dailyStats: dailyStatsRes.data || [],
        activeSessions: activeSessionMap,
        error: null,
        loading: false,
        lastUpdate: new Date()
      }))

      // Process machine sessions after data is loaded
      handleMachineSessions(formattedMachineData, [])
      return Promise.resolve() // Add this line

    } catch (error) {
      console.error("Error fetching data:", error)
      setState(prev => ({
        ...prev,
        error: error.message || "Failed to fetch data",
        loading: false,
        lastUpdate: new Date()
      }))

      // Show error notification
      notification.error({
        message: "Erreur de chargement",
        description: `Impossible de charger les données: ${error.message}`,
        icon: <CloseCircleOutlined style={{ color: "#ff4d4f" }} />,
        placement: "bottomRight",
        duration: 4
      })
      return Promise.reject(error) // Add this line

    }
  }

  // Function to fetch active sessions
  // This function is now deprecated, use fetchActiveSessionsData instead
  const fetchActiveSessions = async () => {
    console.warn("fetchActiveSessions is deprecated, use fetchActiveSessionsData instead")
    return fetchActiveSessionsData()
  }






  // Improved function to handle machine sessions
  const handleMachineSessions = async (currentMachines, previousMachines) => {
    try {
      // Get current active sessions
      const activeSessions = await fetchActiveSessionsData()
      const activeSessionMap = {}

      // Create a map of active sessions by machine ID for quick lookup
      activeSessions.forEach((session) => {
        activeSessionMap[session.machine_id] = session
      })

      // Create a map of previous machine states for comparison
      const prevMachineMap = {}
      previousMachines.forEach((machine) => {
        if (machine.id) {
          prevMachineMap[machine.id] = machine
        }
      })

      // Process each machine to determine if we need to create, update, or stop a session
      for (const machine of currentMachines) {
        if (!machine.id) continue // Skip machines without ID

        // Ensure the critical fields are present
        const machineData = {
          ...machine,
          // Ensure these fields are always present with at least a "0" value
          Regleur_Prenom: machine.Regleur_Prenom || "0",
          Quantite_Planifier: machine.Quantite_Planifier || "0",
          Quantite_Bon: machine.Quantite_Bon || "0",
          Quantite_Rejet: machine.Quantite_Rejet || "0",
          TRS: machine.TRS || "0",
          // Add new fields from real_time_table
          Poid_unitaire: machine.Poid_unitaire || "0",
          cycle_theorique: machine.cycle_theorique || "0",
          empreint: machine.empreint || "0",
          Etat: machine.Etat || "off",
          Code_arret: machine.Code_arret || "",
        }
        const prevMachine = prevMachineMap[machine.id]
        const hasActiveSession = !!activeSessionMap[machine.id]

        // Case 1: Machine is ON and has no active session (new session needed)
        if (machine.Etat === "on" && !hasActiveSession) {


          await axios.post("/api/createSession", {
            machineId: machine.id,
            machineData: machineData,
          })

          // Update session status in state
          setSessionStatus((prev) => ({
            ...prev,
            [machine.id]: {
              active: true,
              startTime: new Date(),
              lastUpdate: new Date(),
            },
          }))

          notification.success({
            message: "Nouvelle session démarrée",
            description: `Session started for ${machine.Machine_Name}`,
            icon: <InfoCircleOutlined style={{ color: "#52c41a" }} />,
            placement: "bottomRight",
            duration: 3,
          })
        }
        // Case 2: Machine is ON and has an active session (update needed)
        else if (machine.Etat === "on" && hasActiveSession) {

          await axios.post("/api/updateSession", {
            machineId: machine.id,
            machineData: machineData,
          })

          // Update session status in state
          setSessionStatus((prev) => ({
            ...prev,
            [machine.id]: {
              ...prev[machine.id],
              lastUpdate: new Date(),
            },
          }))
        }
        // Case 3: Machine was ON but is now OFF (stop session)
        else if (machine.Etat === "off" && hasActiveSession) {
          await axios.post("/api/stopSession", {
            machineId: machine.id,
          })

          // Update session status in state
          setSessionStatus((prev) => ({
            ...prev,
            [machine.id]: {
              active: false,
              endTime: new Date(),
            },
          }))

          notification.info({
            message: "Session terminée",
            description: `Session ended for ${machine.Machine_Name}`,
            icon: <InfoCircleOutlined style={{ color: "#1890ff" }} />,
            placement: "bottomRight",
            duration: 3,
          })
        }
        // Case 4: Machine is OFF and has no active session (no action needed)
      }
    } catch (error) {
      console.error("Erreur lors de la gestion des sessions:", error)
      notification.error({
        message: "Erreur de session",
        description: `Session management error: ${error.message}`,
        icon: <AlertOutlined style={{ color: "#ff4d4f" }} />,
        placement: "bottomRight",
        duration: 4,
      })
    }
  }


  const totalProduction = state.machineData.reduce((sum, m) => sum + safeParseFloat(m.Quantite_Bon || 0), 0)

  const totalRejets = state.machineData.reduce((sum, m) => sum + safeParseFloat(m.Quantite_Rejet || 0), 0)

  const tauxRejet =
    totalProduction + totalRejets > 0 ? ((totalRejets / (totalProduction + totalRejets)) * 100).toFixed(1) : "0.0"

  // Function to manually refresh data via WebSocket
  const refreshData = () => {
    if (websocketService.isConnected) {
      // Use a subtle indicator instead of a loading spinner
      setWsStatus(prev => ({ ...prev, updating: true }));

      // Request fresh data from the server
      websocketService.requestUpdate();

      // Show a small notification in the status bar instead of a popup
      // This is less intrusive than a full notification

      // Set a timeout to reset the updating status after a short delay
      setTimeout(() => {
        setWsStatus(prev => ({ ...prev, updating: false }));
      }, 1000);
    } else {
      // Only for fallback mode (HTTP), we show loading because it's a full refresh
      setState(prev => ({ ...prev, loading: true }));

      // Do not fall back to HTTP polling for testing
      console.log("WebSocket not connected - NOT falling back to HTTP polling (disabled for testing)");

      notification.info({
        message: "WebSocket Connection",
        description: "Attempting to establish WebSocket connection. No fallback to HTTP polling.",
        icon: <ReloadOutlined spin style={{ color: "#1890ff" }} />,
        placement: "bottomRight",
        duration: 3,
      });
    }
  }

  // Mise à jour pour utiliser les sessions de machines au lieu de l'historique en temps réel
  const fetchMachineHistory = async (machineName) => {
    try {
      setState((prev) => ({ ...prev, historyLoading: true, historyError: null }))

      // Trouver l'ID de la machine à partir de son nom
      const machine = state.machineData.find((m) => m.Machine_Name === machineName)

      if (!machine || !machine.id) {
        throw new Error("Machine non trouvée ou ID manquant")
      }

      // Récupérer l'historique des sessions pour cette machine
      const token = localStorage.getItem("token")
      const baseURL = process.env.NODE_ENV === "production" ? "https://charming-hermit-intense.ngrok-free.app" : "http://localhost:5000"
      const response = await superagent.get(baseURL + `/api/machineSessions/${machine.id}`).set('x-auth-token', token).withCredentials()

      // Vérifier si la réponse contient des données valides
      if (!response.body) {
        throw new Error("Données de session invalides")
      }

      const sessions = Array.isArray(response.body) ? response.body : []

      // Traiter les données pour mettre en évidence les sessions actives
      const processedData = sessions.map((session) => ({
        ...session,
        isActive: !session.session_end,
        highlight: !session.session_end,
      }))

      setState((prev) => ({
        ...prev,
        machineHistory: processedData,
        historyLoading: false,
      }))
    } catch (error) {
      console.error("Erreur lors de la récupération de l'historique:", error)
      setState((prev) => ({
        ...prev,
        historyError: error.message || "Impossible de récupérer l'historique de la machine",
        historyLoading: false,
        machineHistory: [], // Réinitialiser l'historique en cas d'erreur
      }))
    }
  }

  // Fonction pour récupérer les données des sessions actives
  const fetchActiveSessionsData = async () => {
    try {
      // 🔒 SECURITY: Use HTTP-only cookies for authentication
      const baseURL = process.env.NODE_ENV === "production" ? "https://charming-hermit-intense.ngrok-free.app" : "http://localhost:5000"
      const response = await superagent.get(baseURL + "/api/activeSessions").withCredentials().timeout(30000).retry(2)
      return response.body
    } catch (error) {
      console.error("Erreur lors de la récupération des sessions actives:", error)
      return []
    }
  }

  // Fonction pour récupérer les données des sessions terminées
  const fetchCompletedSessions = async () => {
    try {
      const token = localStorage.getItem("token")
      const baseURL = process.env.NODE_ENV === "production" ? "https://charming-hermit-intense.ngrok-free.app" : "http://localhost:5000"
      const response = await superagent.get(baseURL + "/api/allSessions").set('x-auth-token', token).withCredentials()
      return response.body
    } catch (error) {
      console.error("Erreur lors de la récupération des sessions terminées:", error)
      return []
    }
  }

  // Update the prepareChartData function to use the safe parsing functions
  const prepareChartData = async () => {
    try {
      // Récupérer les sessions actives
      const activeSessions = await fetchActiveSessionsData()

      // Create a map of active sessions by machine ID for quick lookup
      const activeSessionMap = {}
      activeSessions.forEach((session) => {
        activeSessionMap[session.machine_id] = session
      })

      // Vérifier si toutes les machines sont inactives (Etat off)
      const allMachinesInactive = state.machineData.every((m) => m.Etat === "off")

      if (allMachinesInactive) {
        // Si toutes les machines sont inactives, récupérer les données des sessions terminées
        const completedSessions = await fetchCompletedSessions()

        if (completedSessions.length > 0) {
          // Faire une copie profonde des données actuelles
          const currentMachineData = JSON.parse(JSON.stringify(state.machineData))
          // Regrouper les sessions par machine
          const sessionsByMachine = {}
          completedSessions.forEach((session) => {
            if (!sessionsByMachine[session.machine_id]) {
              sessionsByMachine[session.machine_id] = []
            }
            sessionsByMachine[session.machine_id].push(session)
          })

          // Mettre à jour les données pour les machines avec les dernières sessions terminées
          const updatedMachineData = currentMachineData.map((machine) => {
            const machineSessions = sessionsByMachine[machine.id] || []

            if (machineSessions.length > 0) {
              // Trier les sessions par date de fin (la plus récente d'abord)
              machineSessions.sort((a, b) => new Date(b.session_end) - new Date(a.session_end))
              const latestSession = machineSessions[0]

              return {
                ...machine,
                TRS:  machine.TRS || latestSession.TRS,
                Quantite_Bon: latestSession.Quantite_Bon || machine.Quantite_Bon,
                Quantite_Rejet: latestSession.Quantite_Rejet || machine.Quantite_Rejet,
                progress:
                  ((safeParseFloat(latestSession.Quantite_Bon) || 0) / (safeParseFloat(machine.Quantite_Planifier) || 1)) *
                  100,
                status:
                  (safeParseFloat(latestSession.TRS) || 0) > 80
                    ? "success"
                    : (safeParseFloat(latestSession.TRS) || 0) > 60
                      ? "warning"
                      : "error",
                sessionData: latestSession,
                isHistoricalData: true,
              }
            }
            return machine
          })
          // Mettre à jour l'état avec les données historiques
          setState((prev) => ({
            ...prev,
            machineData: updatedMachineData,
            lastUpdate: new Date(),
            isHistoricalView: true,
          }))

          return
        }
      }
      // Si des machines sont actives ou s'il n'y a pas de sessions terminées, utiliser les données en temps réel
      if (activeSessions.length > 0) {
        // Faire une copie profonde des données actuelles pour éviter les références circulaires
        const currentMachineData = JSON.parse(JSON.stringify(state.machineData))
        // Mettre à jour les données pour les machines avec des sessions actives
        const updatedMachineData = currentMachineData.map((machine) => {
          // Chercher si la machine a une session active
          const activeSession = activeSessionMap[machine.id]

          if (activeSession && machine.Etat === "on") {
            // Utiliser les données de la session active
            return {
              ...machine,
              TRS: activeSession.TRS || machine.TRS,
              Quantite_Bon: activeSession.Quantite_Bon || machine.Quantite_Bon,
              Quantite_Rejet: activeSession.Quantite_Rejet || machine.Quantite_Rejet,
              progress:
                ((safeParseFloat(activeSession.Quantite_Bon) || 0) / (safeParseFloat(machine.Quantite_Planifier) || 1)) *
                100,
              status:
                (safeParseFloat(activeSession.TRS) || 0) > 80
                  ? "success"
                  : (safeParseFloat(activeSession.TRS) || 0) > 60
                    ? "warning"
                    : "error",
              sessionData: activeSession,
              isHistoricalData: false,
            }
          }
          return machine
        })

        // Vérifier si les données ont réellement changé avant de mettre à jour l'état
        const hasChanges = JSON.stringify(updatedMachineData) !== JSON.stringify(state.machineData)

        if (hasChanges) {
          setState((prev) => ({
            ...prev,
            machineData: updatedMachineData,
            lastUpdate: new Date(), // Mettre à jour l'horodatage
            isHistoricalView: false,
          }))
        }
      }
    } catch (error) {
      console.error("Erreur lors de la préparation des données des graphiques:", error)
    }
  }

  // Function to fetch operator stats
  const fetchOperatorStats = async () => {
    try {
      const token = localStorage.getItem("token")
      const baseURL = process.env.NODE_ENV === "production" ? "https://charming-hermit-intense.ngrok-free.app" : "http://localhost:5000"
      const response = await superagent.get(baseURL + "/api/operator-stats").set('x-auth-token', token).withCredentials()
      setOperatorStats(response.body)
    } catch (error) {
      console.error("Error fetching operator stats:", error)
      message.error("Failed to load operator statistics")
    }
  }

  // Function to fetch production stats
  const fetchProductionStats = async () => {
    try {
      const token = localStorage.getItem("token")
      const baseURL = process.env.NODE_ENV === "production" ? "https://charming-hermit-intense.ngrok-free.app" : "http://localhost:5000"
      const response = await superagent.get(baseURL + "/api/production-stats").set('x-auth-token', token).withCredentials()
      setProductionStats(response.body)
    } catch (error) {
      console.error("Error fetching production stats:", error)
      message.error("Failed to load production statistics")
    }
  }

  // Function to handle date change
  const handleDateChange = (date) => {
    if (date) {
      setSelectedDate(date.toDate())
      // Refresh data with the new date
      handleRefresh()
    }
  }

  // Function to handle shift change
  const handleShiftChange = (value) => {
    setSelectedShift(value)
    // Refresh data with the new shift
    handleRefresh()
  }

  // Function to handle view change
  const handleViewChange = (e) => {
    setSelectedView(e.target.value)
  }



  useEffect(() => {
    // Référence pour suivre si le composant est monté
    let isMounted = true

    const fetchDataAndUpdateChart = async () => {
      if (!isMounted) return

      try {
        await fetchData()
        // Attendre un court instant pour s'assurer que l'état est mis à jour
        if (isMounted) {
          setTimeout(() => {
            if (isMounted) {
              prepareChartData()
            }
          }, 500)
        }
      } catch (error) {
        console.error("Erreur lors de la mise à jour des données:", error)
      }
    }

    // Initial data load
    fetchDataAndUpdateChart()
    fetchOperatorStats()
    fetchProductionStats()

    // Set up polling interval - reduced frequency to prevent excessive session creation
    const interval = setInterval(fetchDataAndUpdateChart, 15000)

    // Cleanup on component unmount
    return () => {
      isMounted = false
      clearInterval(interval)
    }
  }, []) // Empty dependency array to run only on mount

  const handleMachineClick = (machine) => {
    if (!machine.id) {
      message.info("Cette machine n'est pas encore configurée")
      return
    }
console.log( "the trs is"+machine.trs)
    setState((prev) => ({
      ...prev,
      selectedMachine: machine.Machine_Name,
      visible: true,
    }))
    fetchMachineHistory(machine.Machine_Name)
  }

  const handleRefresh = async () => {
    try {
      if (wsStatus.connected) {
        // If WebSocket is connected, use a subtle indicator instead of a loading message
        setWsStatus(prev => ({ ...prev, updating: true }));

        // Request fresh data from the server
        websocketService.requestUpdate();

        // Prepare chart data and fetch additional stats without showing loading indicators
        await prepareChartData();
        await fetchOperatorStats();
        await fetchProductionStats();

        // Reset updating status after a short delay
        setTimeout(() => {
          setWsStatus(prev => ({ ...prev, updating: false }));

          // Use a small badge in the status bar instead of a popup message
          // This is handled by the UI automatically via the wsStatus.updating state
        }, 1000);
      } else {
        // Only for fallback mode (HTTP), we show loading because it's a full refresh
        message.loading({
          content: "Actualisation des données en cours...",
          key: "refreshMessage",
          duration: 0,
        });

        // Do not fall back to HTTP polling for testing
        console.log("WebSocket not connected - NOT falling back to HTTP polling (disabled for testing)");

        // Show notification that we're still waiting for WebSocket
        notification.info({
          message: "WebSocket Connection",
          description: "Attempting to establish WebSocket connection. No fallback to HTTP polling.",
          icon: <ReloadOutlined spin style={{ color: "#1890ff" }} />,
          placement: "bottomRight",
          duration: 3,
          key: "websocket-waiting"
        });

        message.success({
          content: "Données actualisées avec succès",
          key: "refreshMessage",
          duration: 2,
        });
      }
    } catch (error) {
      setWsStatus(prev => ({ ...prev, updating: false }));

      message.error({
        content: `Erreur lors de l'actualisation: ${error.message}`,
        key: "refreshMessage",
        duration: 3,
      });
    }
  }

  // Fonction pour obtenir la couleur en fonction de l'état de la machine
  const getStatusColor = (_, machine) => {
    // Si la machine n'existe pas, retourner gris
    if (!machine) {
      return "#d9d9d9"
    }

    // Si la machine est allumée (Etat === "on"), toujours retourner vert
    if (machine.Etat === "on") {
      return "#52c41a" // Vert pour toutes les machines actives
    }

    // Si la machine est éteinte (Etat === "off"), retourner gris
    return "#d9d9d9" // Gris pour les machines inactives
  }

  // Fonction pour déterminer le quart actuel en fonction de l'heure
  const getCurrentShift = () => {
    const now = new Date();
    const hour = now.getHours();

    if (hour >= 6 && hour < 14) {
      return "Matin";
    } else if (hour >= 14 && hour < 22) {
      return "Après-midi";
    } else {
      return "Nuit";
    }
  };

  // Helper function to check if a session belongs to a specific shift
  const isSessionInShift = (session, shift) => {
    const sessionDate = new Date(session.session_start);
    const hour = sessionDate.getHours();

    if (shift === "Matin" && hour >= 6 && hour < 14) {
      return true;
    } else if (shift === "Après-midi" && hour >= 14 && hour < 22) {
      return true;
    } else if (shift === "Nuit" && (hour >= 22 || hour < 6)) {
      return true;
    }
    return false;
  };



  // Options de base pour les graphiques
  const baseOptions = getBaseChartOptions(darkMode)


  // Fonction pour vérifier si une session est active
  const isSessionActive = (session) => {
    return !session.session_end
  }

  // Colonnes pour le tableau d'historique des sessions
  const historyColumns = [
    {
      title: "Statut",
      key: "status",
      render: (_, record) => (
        <Tag color={isSessionActive(record) ? "processing" : "default"}>
          {isSessionActive(record) ? "Active" : "Terminée"}
        </Tag>
      ),
      width: 100,
    },
    {
      title: "Début de session",
      dataIndex: "session_start",
      key: "session_start",
      render: (value) => new Date(value).toLocaleString(),
      sorter: (a, b) => new Date(b.session_start) - new Date(a.session_start),
    },
    {
      title: "Fin de session",
      dataIndex: "session_end",
      key: "session_end",
      render: (value) => (value ? new Date(value).toLocaleString() : "En cours"),
    },
    {
      title: "Durée",
      key: "duration",
      render: (_, record) => {
        const start = new Date(record.session_start)
        const end = record.session_end ? new Date(record.session_end) : new Date()
        const durationMs = end - start
        const minutes = Math.floor(durationMs / 60000)
        const hours = Math.floor(minutes / 60)
        return `${hours}h ${minutes % 60}m`
      },
    },
    {
      title: "Quantité bonne",
      dataIndex: "Quantite_Bon",
      key: "Quantite_Bon",
    },
    {
      title: "Quantité rejetée",
      dataIndex: "Quantite_Rejet",
      key: "Quantite_Rejet",
    },
    {
      title: "TRS",
      dataIndex: "TRS",
      key: "TRS",
      render: (value) => <Tag color={value > 80 ? "success" : value > 60 ? "warning" : "error"}>{value}%</Tag>,
    },
    {
      title: "Cycle",
      dataIndex: "cycle",
      key: "cycle",
    },
  ]

  // Colonnes pour le tableau des machines
  const machineColumns = [
    {
      title: "Machine",
      dataIndex: "Machine_Name",
      key: "Machine_Name",
      render: (text) => <strong>{text}</strong>,
    },
    {
      title: "TRS",
      dataIndex: "TRS",
      key: "TRS",
      render: (value) => (
        <Tag color={safeParseFloat(value) > 80 ? "success" : safeParseFloat(value) > 60 ? "warning" : "error"}>
          {safeParseFloat(value).toFixed(1)}%
        </Tag>
      ),
    },
    {
      title: "Planifié",
      dataIndex: "Quantite_Planifier",
      key: "Quantite_Planifier",
      render: (value) => safeParseFloat(value),
    },
    {
      title: "Produit",
      dataIndex: "Quantite_Bon",
      key: "Quantite_Bon",
      render: (value) => safeParseFloat(value),
    },
    {
      title: "Rejeté",
      dataIndex: "Quantite_Rejet",
      key: "Quantite_Rejet",
      render: (value) => safeParseFloat(value),
    },
    {
      title: "Progression",
      dataIndex: "progress",
      key: "progress",
      render: (value) => (
        <Progress
          percent={Number.parseFloat(value.toFixed(1))}
          size="small"
          status={value > 90 ? "success" : value > 70 ? "normal" : "exception"}
        />
      ),
    },
    {
      title: "Session",
      key: "session",
      render: (_, record) => (
        <Tag color={record.Etat === "on" ? "processing" : "default"}>
          {record.Etat === "on" ? "Active" : "Inactive"}
        </Tag>
      ),
    },
  ]

  // Rendu du contenu modal
  const modalContent = () => {
    if (state.historyLoading) {
      return <Spin size="large" style={{ display: "block", margin: "40px auto" }} />
    }

    if (state.historyError) {
      return <Alert type="error" message="Erreur de chargement" description={state.historyError} showIcon />
    }

    // Vérifier si l'historique est vide
    if (!state.machineHistory || state.machineHistory.length === 0) {
      return (
        <Empty
          description={
            <>
              <p>Aucune session trouvée pour cette machine</p>
              <p>La table machine_sessions est vide ou aucune donnée n'est disponible</p>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={() => fetchMachineHistory(state.selectedMachine)}
              >
                Rafraîchir
              </Button>
            </>
          }
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )
    }

    // Préparer les données pour les graphiques d'historique des sessions
    const historyTrsChartData = {
      labels: state.machineHistory.map((h) => {
        const date = new Date(h.session_start)
        return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
      }),
      datasets: [
        {
          label: "TRS (%)",
          data: state.machineHistory.map((h) => Number.parseFloat(h.TRS) || 0),
          backgroundColor: "rgba(153, 102, 255, 0.2)",
          borderColor: "rgba(153, 102, 255, 1)",
          borderWidth: 2,
          fill: true,
        },
      ],
    }

    const historyProductionChartData = {
      labels: state.machineHistory.map((h) => {
        const date = new Date(h.session_start)
        return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
      }),
      datasets: [
        {
          label: "Quantité bonne",
          data: state.machineHistory.map((h) => Number.parseFloat(h.Quantite_Bon) || 0),
          backgroundColor: "rgba(75, 192, 192, 0.6)",
          borderColor: "rgba(75, 192, 192, 1)",
          borderWidth: 1,
        },
      ],
    }

    const historyRejectChartData = {
      labels: state.machineHistory.map((h) => {
        const date = new Date(h.session_start)
        return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
      }),
      datasets: [
        {
          label: "Quantité rejetée",
          data: state.machineHistory.map((h) => Number.parseFloat(h.Quantite_Rejet) || 0),
          backgroundColor: "rgba(255, 99, 132, 0.6)",
          borderColor: "rgba(255, 99, 132, 1)",
          borderWidth: 1,
        },
      ],
    }

    const historyDurationChartData = {
      labels: state.machineHistory.map((h) => {
        const date = new Date(h.session_start)
        return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
      }),
      datasets: [
        {
          label: "Durée de session (min)",
          data: state.machineHistory.map((h) => {
            const start = new Date(h.session_start)
            const end = h.session_end ? new Date(h.session_end) : new Date()
            return Math.round((end - start) / 60000) // Convertir ms en minutes
          }),
          backgroundColor: "rgba(255, 159, 64, 0.6)",
          borderColor: "rgba(255, 159, 64, 1)",
          borderWidth: 1,
        },
      ],
    }

    return (
      <Tabs defaultActiveKey="1" className={darkMode ? "dark-mode" : ""}>
        <TabPane tab="Sessions" key="1">
          <Table
            columns={historyColumns}
            dataSource={state.machineHistory.map((item, index) => ({ ...item, key: index }))}
            pagination={{ pageSize: 5 }}
            scroll={{ x: true }}
          />
        </TabPane>
        <TabPane tab="Graphique" key="2">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <div className="chart-container">
                <h3 className="chart-title">
                  <LineChartOutlined /> TRS (%)
                </h3>
                <div style={{ height: 200 }}>
                  <Line
                    data={historyTrsChartData}
                    options={{
                      ...baseOptions,
                      scales: {
                        ...baseOptions.scales,
                        y: {
                          ...baseOptions.scales.y,
                          beginAtZero: true,
                          max: 100,
                          grid: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                        x: {
                          ...baseOptions.scales.x,
                          grid: {
                            display: false,
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                      plugins: {
                        ...baseOptions.plugins,
                        legend: {
                          ...baseOptions.plugins.legend,
                          labels: {
                            ...baseOptions.plugins.legend.labels,
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="chart-container">
                <h3 className="chart-title">
                  <CheckCircleOutlined /> Production (pcs)
                </h3>
                <div style={{ height: 200 }}>
                  <Bar
                    data={historyProductionChartData}
                    options={{
                      ...baseOptions,
                      scales: {
                        ...baseOptions.scales,
                        y: {
                          ...baseOptions.scales.y,
                          beginAtZero: true,
                          grid: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                        x: {
                          ...baseOptions.scales.x,
                          grid: {
                            display: false,
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                      plugins: {
                        ...baseOptions.plugins,
                        legend: {
                          ...baseOptions.plugins.legend,
                          labels: {
                            ...baseOptions.plugins.legend.labels,
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="chart-container">
                <h3 className="chart-title">
                  <CloseCircleOutlined /> Rejets (pcs)
                </h3>
                <div style={{ height: 200 }}>
                  <Bar
                    data={historyRejectChartData}
                    options={{
                      ...baseOptions,
                      scales: {
                        ...baseOptions.scales,
                        y: {
                          ...baseOptions.scales.y,
                          beginAtZero: true,
                          grid: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                        x: {
                          ...baseOptions.scales.x,
                          grid: {
                            display: false,
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                      plugins: {
                        ...baseOptions.plugins,
                        legend: {
                          ...baseOptions.plugins.legend,
                          labels: {
                            ...baseOptions.plugins.legend.labels,
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="chart-container">
                <h3 className="chart-title">
                  <ClockCircleOutlined /> Durée des sessions (min)
                </h3>
                <div style={{ height: 200 }}>
                  <Bar
                    data={historyDurationChartData}
                    options={{
                      ...baseOptions,
                      scales: {
                        ...baseOptions.scales,
                        y: {
                          ...baseOptions.scales.y,
                          beginAtZero: true,
                          grid: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                        x: {
                          ...baseOptions.scales.x,
                          grid: {
                            display: false,
                            color: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                          },
                          ticks: {
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                      plugins: {
                        ...baseOptions.plugins,
                        legend: {
                          ...baseOptions.plugins.legend,
                          labels: {
                            ...baseOptions.plugins.legend.labels,
                            color: darkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </TabPane>
        <TabPane
          tab={
            <span>
              <InfoCircleOutlined style={{ marginRight: 8 }} />
              Informations
            </span>
          }
          key="3"
        >
          <div style={{ padding: "16px 0" }}>
            <Row gutter={[24, 24]}>
              {/* Machine Info Card */}
              <Col xs={24} md={12}>
                <Card
                  title={
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <DashboardOutlined style={{ color: "#1890ff", marginRight: 8 }} />
                      <span>Détails de la machine</span>
                    </div>
                  }
                  bordered
                  style={{ height: "100%" }}
                >
                  <div style={{ display: "flex", alignItems: "center", marginBottom: 16 }}>
                    <div
                      style={{
                        width: 64,
                        height: 64,
                        borderRadius: 8,
                        background: "rgba(24, 144, 255, 0.1)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        marginRight: 16,
                      }}
                    >
                      <DashboardOutlined style={{ fontSize: 32, color: "#1890ff" }} />
                    </div>
                    <div>
                      <Title level={4} style={{ margin: 0 }}>
                        {state.selectedMachine}
                      </Title>
                      <Text type="secondary">
                        {state.machineHistory.length > 0 && state.machineHistory[0].Ordre_Fabrication
                          ? `OF: ${state.machineHistory[0].Ordre_Fabrication}`
                          : "Aucun ordre de fabrication"}
                      </Text>
                    </div>
                  </div>

                  <Divider style={{ margin: "16px 0" }} />

                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Statistic
                        title={<Text style={{ fontSize: 14 }}>Sessions {getCurrentShift()}</Text>}
                        value={state.machineHistory.filter(session => isSessionInShift(session, getCurrentShift())).length}
                        prefix={<HistoryOutlined />}
                        valueStyle={{ color: "#1890ff", fontSize: 20 }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title={<Text style={{ fontSize: 14 }}>Sessions actives {getCurrentShift()}</Text>}
                        value={state.machineHistory.filter(s => !s.session_end && isSessionInShift(s, getCurrentShift())).length}
                        prefix={<PlayCircleOutlined />}
                        valueStyle={{
                          color: state.machineHistory.filter(s => !s.session_end && isSessionInShift(s, getCurrentShift())).length > 0 ? "#52c41a" : "#8c8c8c",
                          fontSize: 20,
                        }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>

              {/* Session Info Card */}
              <Col xs={24} md={12}>
                <Card
                  title={
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <HistoryOutlined style={{ color: "#1890ff", marginRight: 8 }} />
                      <span>Historique des sessions</span>
                    </div>
                  }
                  bordered
                  style={{ height: "100%" }}
                >
                  {state.machineHistory.length > 0 ? (
                    <>
                      <div style={{ marginBottom: 16 }}>
                        <Text strong>Dernière session:</Text>
                        <div
                          style={{
                            background: "rgba(0,0,0,0.02)",
                            padding: "12px",
                            borderRadius: "8px",
                            marginTop: "8px",
                          }}
                        >
                          <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 8 }}>
                            <Text>Début:</Text>
                            <Text strong>{new Date(state.machineHistory[0].session_start).toLocaleString()}</Text>
                          </div>
                          <div style={{ display: "flex", justifyContent: "space-between" }}>
                            <Text>Fin:</Text>
                            <Text strong>
                              {state.machineHistory[0].session_end ? (
                                new Date(state.machineHistory[0].session_end).toLocaleString()
                              ) : (
                                <Tag color="processing">En cours</Tag>
                              )}
                            </Text>
                          </div>
                        </div>
                      </div>

                      <Divider style={{ margin: "16px 0" }} />

                      <Row gutter={[16, 16]}>
                        <Col span={8}>
                          <Statistic
                            title={<Text style={{ fontSize: 14 }}>TRS moyen</Text>}
                            value={(() => {
                              const trsValues = state.machineHistory
                                .map((s) => Number(s.TRS || 0))
                                .filter((v) => !isNaN(v))
                              return trsValues.length
                                ? (trsValues.reduce((a, b) => a + b, 0) / trsValues.length).toFixed(1)
                                : "N/A"
                            })()}
                            suffix="%"
                            valueStyle={{ fontSize: 18 }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title={<Text style={{ fontSize: 14 }}>Pièces bonnes</Text>}
                            value={state.machineHistory.reduce((sum, s) => sum + Number(s.Quantite_Bon || 0), 0)}
                            valueStyle={{ color: "#52c41a", fontSize: 18 }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title={<Text style={{ fontSize: 14 }}>Pièces rejetées</Text>}
                            value={state.machineHistory.reduce((sum, s) => sum + Number(s.Quantite_Rejet || 0), 0)}
                            valueStyle={{ color: "#ff4d4f", fontSize: 18 }}
                          />
                        </Col>
                      </Row>
                    </>
                  ) : (
                    <Empty description="Aucune donnée de session disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </Card>
              </Col>

              {/* Performance Metrics */}
              <Col xs={24}>
                <Card
                  title={
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <AreaChartOutlined style={{ color: "#1890ff", marginRight: 8 }} />
                      <span>Métriques de performance</span>
                    </div>
                  }
                  bordered
                >
                  {state.machineHistory.length > 0 ? (
                    <Row gutter={[24, 24]}>
                      <Col xs={24} md={8}>
                        <Card style={{ background: "rgba(0,0,0,0.02)" }}>
                          <Statistic
                            title="Durée moyenne des sessions"
                            value={(() => {
                              const durations = state.machineHistory.map((s) => {
                                const start = new Date(s.session_start)
                                const end = s.session_end ? new Date(s.session_end) : new Date()
                                return end - start
                              })
                              const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length
                              const hours = Math.floor(avgDuration / 3600000)
                              const minutes = Math.floor((avgDuration % 3600000) / 60000)
                              return `${hours}h ${minutes}m`
                            })()}
                            prefix={<ClockCircleOutlined />}
                          />
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card style={{ background: "rgba(0,0,0,0.02)" }}>
                          <Statistic
                            title="Taux de rejet moyen"
                            value={(() => {
                              const totalBon = state.machineHistory.reduce(
                                (sum, s) => sum + Number(s.Quantite_Bon || 0),
                                0,
                              )
                              const totalRejet = state.machineHistory.reduce(
                                (sum, s) => sum + Number(s.Quantite_Rejet || 0),
                                0,
                              )
                              return totalBon + totalRejet > 0
                                ? ((totalRejet / (totalBon + totalRejet)) * 100).toFixed(1)
                                : "0.0"
                            })()}
                            suffix="%"
                            prefix={<WarningOutlined />}
                            valueStyle={{ color: "#faad14" }}
                          />
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card style={{ background: "rgba(0,0,0,0.02)" }}>
                          <Statistic
                            title="Productivité"
                            value={(() => {
                              const totalBon = state.machineHistory.reduce(
                                (sum, s) => sum + Number(s.Quantite_Bon || 0),
                                0,
                              )
                              const totalDuration = state.machineHistory.reduce((sum, s) => {
                                const start = new Date(s.session_start)
                                const end = s.session_end ? new Date(s.session_end) : new Date()
                                return sum + (end - start)
                              }, 0)
                              const hours = totalDuration / 3600000
                              return hours > 0 ? Math.round(totalBon / hours) : 0
                            })()}
                            suffix="pcs/h"
                            prefix={<RiseOutlined />}
                            valueStyle={{ color: "#52c41a" }}
                          />
                        </Card>
                      </Col>
                    </Row>
                  ) : (
                    <Empty description="Aucune donnée de performance disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </Card>
              </Col>
            </Row>
          </div>
        </TabPane>
      </Tabs>
    )
  }

  // Connect to WebSocket for notifications
  useEffect(() => {
    if (!isAuthenticated || !user?.id) return

    const wsProtocol = window.location.protocol === "https:" ? "wss:" : "ws:"
    const wsUrl = `${wsProtocol}//${window.location.host}/api/notifications`

    const newSocket = new WebSocket(wsUrl)

    newSocket.onopen = () => {
      if (user?.id) {
        newSocket.send(JSON.stringify({ type: "auth", userId: user.id }))
      }
    }

    newSocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)

        if (data.type === "notification") {
          const notif = data.notification

          // Show notification based on category
          if (notif.category === "alert") {
            notification.error({
              message: notif.title,
              description: notif.message,
              icon: <AlertOutlined style={{ color: "#ff4d4f" }} />,
              placement: "topRight",
              duration: 5,
            })
          } else if (notif.category === "maintenance") {
            notification.warning({
              message: notif.title,
              description: notif.message,
              icon: <ToolOutlined style={{ color: "#faad14" }} />,
              placement: "topRight",
              duration: 5,
            })
          } else if (notif.category === "update") {
            notification.info({
              message: notif.title,
              description: notif.message,
              icon: <InfoCircleOutlined style={{ color: "#1890ff" }} />,
              placement: "topRight",
              duration: 4,
            })
          } else {
            notification.success({
              message: notif.title,
              description: notif.message,
              icon: <InfoCircleOutlined style={{ color: "#52c41a" }} />,
              placement: "topRight",
              duration: 4,
            })
          }
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error)
      }
    }

    newSocket.onerror = () => {
      message.error("Erreur de connexion aux notifications")
    }

    newSocket.onclose = () => {
      // Attempt to reconnect after 3 seconds
      setTimeout(() => {
        setSocket(null)
      }, 3000)
    }

    setSocket(newSocket)

    return () => {
      if (newSocket) {
        newSocket.close()
      }
    }
  }, [user?.id, isAuthenticated])


  return (
    <div style={{ padding: "24px" }}>
      {/* En-tête avec date et bouton de rafraîchissement */}
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: 24 }}>
        <div>
          <Title level={2}> Performance Temps Réel des Machines</Title>
          <Text type="secondary">{currentDate}</Text>
        </div>
        <Space>
          <Button type="primary" icon={<ReloadOutlined />} onClick={handleRefresh}>
            Actualiser
          </Button>
        </Space>
      </div>

      {/* Alerte d'erreur */}
      {state.error && (
        <Alert
          type="error"
          message="Erreur de connexion"
          description={`Dernière erreur: ${state.error} | Mise à jour: ${state.lastUpdate.toLocaleTimeString()}`}
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      <Divider />

      {/* Filtres et vues */}
      <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
        <Radio.Group value={selectedView} onChange={handleViewChange} buttonStyle="solid">
          <Radio.Button value="machines">
            <DashboardOutlined /> Machines
          </Radio.Button>
        </Radio.Group>

        <Space>
          <Tooltip title="Filtrer les données">
            <Button icon={<FilterOutlined />}>Filtres</Button>
          </Tooltip>
          <Tooltip title="Exporter les données">
            <Button icon={<FileTextOutlined />}>Exporter</Button>
          </Tooltip>
        </Space>
      </div>

      {/* Connection status and refresh button */}
      <Row gutter={[16, 16]} style={{ marginBottom: "16px" }}>
        <Col span={24}>
          <Card>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
              <div>
                <Text strong>Dernière mise à jour: {state.lastUpdate.toLocaleTimeString()}</Text>
                {wsStatus.connected ? (
                  <Tag color="success" className="ws-status-tag" style={{ marginLeft: "10px" }}>
                    <CheckCircleOutlined /> Connecté en temps réel
                  </Tag>
                ) : wsStatus.connecting ? (
                  <Tag color="processing" className="ws-status-tag" style={{ marginLeft: "10px" }}>
                    <ReloadOutlined spin /> Connexion en cours...
                  </Tag>
                ) : (
                  <Tag color="warning" className="ws-status-tag" style={{ marginLeft: "10px" }}>
                    <WarningOutlined /> Mode de secours
                  </Tag>
                )}
                {wsStatus.updating && (
                  <Tag color="blue" className="ws-status-tag ws-status-updating" style={{ marginLeft: "10px" }}>
                    <ReloadOutlined spin /> Mise à jour en cours
                  </Tag>
                )}
              </div>
              <Button
                type="primary"
                icon={<ReloadOutlined spin={wsStatus.updating} />}
                onClick={refreshData}
                loading={!wsStatus.connected && state.loading}
                disabled={wsStatus.updating}
              >
                Rafraîchir les données
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Contenu principal - Vue des machines */}
      {selectedView === "machines" && (
        <Row gutter={[18, 18]}>
          {/* Statistiques des machines */}
          <Col xs={24} lg={24}>
            <Card
              title={
                <div style={{ display: "flex", alignItems: "center" }}>
                  <DashboardOutlined style={{ fontSize: 20, marginRight: 8 }} />
                  <span>Statistiques des machines</span>
                </div>
              }
              extra={<Badge count={state.machineData.length} style={{ backgroundColor: "#1890ff" }} />}
            >
              {state.loading && !wsStatus.connected ? (
                <div style={{ textAlign: "center", padding: "40px 0" }}>
                  <Spin size="large" />
                  <div style={{ marginTop: 16 }}>Chargement des données...</div>
                </div>
              ) : wsStatus.connecting ? (
                <div style={{ textAlign: "center", padding: "10px 0" }}>
                  <Badge status="processing" text="Établissement de la connexion en temps réel..." style={{ color: "#1890ff" }} />
                </div>
              ) : wsStatus.updating ? (
                <div style={{ textAlign: "center", padding: "10px 0" }}>
                  <Badge status="processing" text="Mise à jour en temps réel..." style={{ color: "#1890ff" }} />
                </div>
              ) : (
                <Row gutter={[16, 16]}>
                  {state.machineData.slice(0, 4).map((machine, index) => (
                    <Col key={index} xs={24} sm={24} md={12}>
                      <div className="machine-card-container" style={{ position: "relative" }}>
                        <EnhancedMachineCard
                          machine={machine}
                          handleMachineClick={handleMachineClick}
                          getStatusColor={getStatusColor}
                        />

                        {/* Apply "Soon..." overlay to all machines except ID 1 */}
                        {machine.id !== 1 && (
                          <div
                            style={{
                              position: "absolute",
                              top: 0,
                              left: 0,
                              width: "100%",
                              height: "100%",
                              backdropFilter: "blur(2px)",
                              backgroundColor: "rgba(0, 0, 0, 0.05)",
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "center",
                              justifyContent: "center",
                              borderRadius: "8px",
                              zIndex: 10,
                              border: "2px dashed #1890ff",
                            }}
                          >
                            <div
                              style={{
                                fontSize: "26px",
                                fontWeight: "bold",
                                color: "#1890ff",
                                textShadow: "2px 2px 4px rgba(0,0,0,0.2)",
                                marginBottom: "10px",
                              }}
                            >
                              En cours de développement ...
                            </div>
                            <Button type="primary" ghost size="small" icon={<SettingOutlined />}>
                              Configuration requise
                            </Button>
                          </div>
                        )}

                        {/* Special overlay for machines without ID */}
                        {!machine.id && (
                          <div
                            style={{
                              position: "absolute",
                              top: 0,
                              left: 0,
                              width: "100%",
                              height: "100%",
                              backdropFilter: "blur(2px)",
                              backgroundColor: "rgba(0, 0, 0, 0.05)",
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "center",
                              justifyContent: "center",
                              borderRadius: "8px",
                              zIndex: 10,
                              border: "2px dashed #1890ff",
                            }}
                          >
                            <div
                              style={{
                                fontSize: "28px",
                                fontWeight: "bold",
                                color: "#1890ff",
                                textShadow: "2px 2px 4px rgba(0,0,0,0.2)",
                                marginBottom: "10px",
                              }}
                            >
                              En cours de développement ...
                            </div>
                            <Button type="default" size="small">
                              Configuration requise
                            </Button>
                          </div>
                        )}
                      </div>
                    </Col>
                  ))}
                </Row>
              )}
            </Card>
          </Col>
        </Row>
      )}

      <Divider />

      {/* Tableau détaillé des machines */}
      <Card
        title={
          <div style={{ display: "flex", alignItems: "center" }}>
            <SettingOutlined style={{ fontSize: 20, marginRight: 8 }} />
            <span>Détails des machines</span>
          </div>
        }
        extra={
          <Space>
            <Button type="primary" icon={<ReloadOutlined />} onClick={handleRefresh} size="small">
              Actualiser
            </Button>
         {/*   <ShiftReportButton
              machineId={state.selectedMachine || state.machineData[0]?.Machine_Name}
              machineName={state.selectedMachine || state.machineData[0]?.Machine_Name}
              shift={selectedShift !== "all" ? selectedShift : null}
            />
            <TestShiftReportButton />*/}
            <Tag color="processing">{state.machineData.filter((m) => m.Etat === "on").length} sessions actives</Tag>
          </Space>
        }
      >
        <Tabs defaultActiveKey="1" className={darkMode ? "dark-mode" : ""}>
          <TabPane tab="Tableau" key="1">
            <Table
              columns={machineColumns}
              dataSource={state.machineData.map((item, index) => ({ ...item, key: index }))}
              pagination={{ pageSize: 10 }}
              scroll={{ x: true }}
              onRow={(record) => ({
                onClick: () => handleMachineClick(record),
              })}
            />
          </TabPane>
          <TabPane tab="Cartes" key="2">
            <Row gutter={[16, 16]}>
              {state.machineData.map((machine, index) => (
                <Col key={index} xs={24} sm={12} md={8} lg={6}>
                  <div style={{ position: "relative" }}>
                    <Card
                      hoverable={!!machine.id}
                      onClick={() => machine.id && handleMachineClick(machine)}
                      style={{
                        borderTop: `2px solid ${getStatusColor(machine.status, machine)}`,
                      }}
                    >
                      <div style={{ textAlign: "center" }}>
                        <Title level={4}>{machine.Machine_Name || "Machine"}</Title>
                        <Progress
                          type="dashboard"
                          percent={safeParseFloat(machine.TRS || "0")}
                          status={
                            safeParseFloat(machine.TRS) > 80
                              ? "success"
                              : safeParseFloat(machine.TRS) > 60
                                ? "normal"
                                : "exception"
                          }
                        />
                        {machine.Etat === "on" && (
                          <Badge status="processing" text="Session active" style={{ marginTop: 8 }} />
                        )}
                        <div style={{ marginTop: 8 }}>
                          <Text>Production: {safeParseFloat(machine.Quantite_Bon || 0)}</Text>
                        </div>
                      </div>
                    </Card>

                    {/* Apply "Soon..." overlay to all machines except ID 1 */}
                    {machine.id !== 1 && (
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          width: "100%",
                          height: "100%",
                          backdropFilter: "blur(2px)",
                          backgroundColor: "rgba(0, 0, 0, 0.05)",
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                          borderRadius: "8px",
                          zIndex: 10,
                          border: "2px dashed #1890ff",
                        }}
                      >
                        <div
                          style={{
                            fontSize: "24px",
                            fontWeight: "bold",
                            color: "#1890ff",
                            textShadow: "2px 2px 4px rgba(0,0,0,0.2)",
                            marginBottom: "8px",
                          }}
                        >
                          En cours de développement ...
                        </div>
                        <Button type="default" size="small">
                          Configuration requise
                        </Button>
                      </div>
                    )}

                    {/* Special overlay for machines without ID */}
                    {!machine.id && (
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          width: "100%",
                          height: "100%",
                          backdropFilter: "blur(2px)",
                          backgroundColor: "rgba(0, 0, 0, 0.05)",
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                          borderRadius: "8px",
                          zIndex: 10,
                          border: "2px dashed #1890ff",
                        }}
                      >
                        <div
                          style={{
                            fontSize: "24px",
                            fontWeight: "bold",
                            color: "#1890ff",
                            textShadow: "2px 2px 4px rgba(0,0,0,0.2)",
                            marginBottom: "8px",
                          }}
                        >
                          En cours de développement ...
                        </div>
                        <Button type="default" size="small">
                          Configuration requise
                        </Button>
                      </div>
                    )}
                  </div>
                </Col>
              ))}
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      {/* Aide et outils */}
      <div style={{ position: "fixed", bottom: 20, right: 20, zIndex: 1000 }}>
        <Popover
          content={
            <div style={{ width: 250 }}>
              <p>
                <strong>Outils disponibles:</strong>
              </p>
              <ul>
                <li>Vue des machines</li>
                <li>Analyse détaillée des performances</li>
                <li>Export des données</li>
              </ul>
              <Button type="primary" block>
                Guide d'utilisation
              </Button>
            </div>
          }
          title="Aide et outils"
          trigger="click"
          placement="topRight"
        >
          <Button
            type="primary"
            shape="circle"
            icon={<InfoCircleOutlined />}
            size="large"
            style={{ boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)" }}
          />
        </Popover>
      </div>

      {/* Modal pour l'historique des machines */}
      <Modal
        title={`Sessions de ${state.selectedMachine}`}
        open={state.visible}
        width={800}
        onCancel={() =>
          setState((prev) => ({
            ...prev,
            visible: false,
            historyError: null,
          }))
        }
        footer={[
          <Button key="close" onClick={() => setState((prev) => ({ ...prev, visible: false, historyError: null }))}>
            Fermer
          </Button>,

          <Button key="allSessions" type="primary" onClick={() => window.open("/sessions-report", "_blank")}>
            Voir toutes les sessions
        </Button>,
        ]}
        destroyOnClose
      >
        {modalContent()}
      </Modal>
    </div>
  )
}

export default DailyPerformanceDashboard
