/**
 * Verification script to confirm the database schema fix is working
 * Tests the version column can store "enhanced-react" without authentication
 */

import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASS || 'root',
  database: process.env.DB_NAME || 'Testingarea51'
};

async function verifyDatabaseFix() {
  let connection;
  
  try {
    console.log('🔍 Verifying Database Schema Fix');
    console.log('===============================\n');

    // Connect to database
    console.log('📡 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection established\n');

    // 1. Check current schema
    console.log('1️⃣ Checking reports table schema...');
    const [schema] = await connection.execute(`
      SHOW COLUMNS FROM reports LIKE 'version'
    `);

    if (schema.length === 0) {
      console.log('❌ Version column not found');
      return false;
    }

    const column = schema[0];
    console.log('📊 Version column details:', {
      type: column.Type,
      null: column.Null,
      default: column.Default
    });

    // Check if fix was applied
    const isFixed = column.Type.includes('varchar(50)') || 
                   column.Type.includes('varchar(255)') ||
                   parseInt(column.Type.match(/varchar\((\d+)\)/)?.[1] || '0') >= 50;

    if (!isFixed) {
      console.log('❌ Column still too small for "enhanced-react"');
      console.log(`   Current: ${column.Type}`);
      console.log(`   Required: varchar(50) or larger`);
      return false;
    }

    console.log('✅ Column size is sufficient for "enhanced-react"');

    // 2. Test actual insertion
    console.log('\n2️⃣ Testing "enhanced-react" insertion...');
    
    const testData = {
      type: 'verification',
      title: 'Database Fix Verification',
      description: 'Testing enhanced-react version storage',
      date: new Date().toISOString().split('T')[0],
      status: 'completed',
      version: 'enhanced-react'
    };

    const [insertResult] = await connection.execute(`
      INSERT INTO reports (type, title, description, date, status, generated_at, version)
      VALUES (?, ?, ?, ?, ?, NOW(), ?)
    `, [testData.type, testData.title, testData.description, testData.date, testData.status, testData.version]);

    const testId = insertResult.insertId;
    console.log(`✅ Test record inserted successfully (ID: ${testId})`);

    // 3. Verify the inserted data
    console.log('\n3️⃣ Verifying inserted data...');
    const [verifyResult] = await connection.execute(`
      SELECT id, version, LENGTH(version) as version_length, created_at
      FROM reports 
      WHERE id = ?
    `, [testId]);

    const record = verifyResult[0];
    console.log('📊 Inserted record:', {
      id: record.id,
      version: record.version,
      length: record.version_length,
      created: record.created_at
    });

    // Verify it matches what we expected
    if (record.version === 'enhanced-react' && record.version_length === 13) {
      console.log('✅ Data stored correctly - no truncation occurred');
    } else {
      console.log('❌ Data may have been truncated or modified');
      return false;
    }

    // 4. Clean up test record
    console.log('\n4️⃣ Cleaning up test data...');
    await connection.execute(`DELETE FROM reports WHERE id = ?`, [testId]);
    console.log('✅ Test record removed');

    // 5. Show current version statistics
    console.log('\n5️⃣ Current version statistics...');
    const [stats] = await connection.execute(`
      SELECT 
        version,
        COUNT(*) as count,
        MAX(LENGTH(version)) as max_length
      FROM reports 
      GROUP BY version 
      ORDER BY count DESC
    `);

    console.log('📊 Version distribution:');
    stats.forEach(stat => {
      console.log(`  - ${stat.version}: ${stat.count} reports (${stat.max_length} chars)`);
    });

    // 6. Final assessment
    console.log('\n🎯 Final Assessment:');
    console.log('==================');
    console.log('✅ Database schema fix is working correctly');
    console.log('✅ "enhanced-react" version can be stored without errors');
    console.log('✅ No data truncation occurs');
    console.log('✅ Existing data is preserved');
    console.log('✅ PDF generation system should work end-to-end');

    console.log('\n🚀 System Status: READY FOR PRODUCTION');
    
    return true;

  } catch (error) {
    console.error('\n❌ Verification failed:', error.message);
    
    if (error.code === 'ER_DATA_TOO_LONG') {
      console.error('💡 The database schema fix was not applied correctly');
      console.error('   Run: node backend/scripts/fixReportsVersionColumn.js');
    } else if (error.code === 'ER_NO_SUCH_TABLE') {
      console.error('💡 The reports table does not exist');
      console.error('   Start the application to create the table first');
    } else {
      console.error('📋 Full error:', error);
    }
    
    return false;
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n📡 Database connection closed');
    }
  }
}

// Load environment variables
const configPath = path.join(__dirname, './backend/config.env');
if (fs.existsSync(configPath)) {
  const envContent = fs.readFileSync(configPath, 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
}

// Run verification
verifyDatabaseFix().then(success => {
  if (success) {
    console.log('\n🎉 Database schema fix verification PASSED');
    process.exit(0);
  } else {
    console.log('\n❌ Database schema fix verification FAILED');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Verification script failed:', error);
  process.exit(1);
});
