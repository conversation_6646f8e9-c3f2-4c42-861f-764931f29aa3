import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { Spin, Empty, Typography, Space, Button, Row, Col } from 'antd';
import { Bar<PERSON><PERSON>Outlined, ClockCircleOutlined } from '@ant-design/icons';
import SOMIPEM_COLORS from '../../../styles/brand-colors';

const { Text } = Typography;

const CHART_COLORS = {
  primary: SOMIPEM_COLORS.PRIMARY_BLUE,
  secondary: SOMIPEM_COLORS.SECONDARY_BLUE, 
  success: SOMIPEM_COLORS.PRIMARY_BLUE, // Updated to SOMIPEM Primary Blue
  warning: "#faad14",
  danger: "#f5222d",
  purple: SOMIPEM_COLORS.DARK_GRAY,
  pink: SOMIPEM_COLORS.LIGHT_GRAY,
  orange: SOMIPEM_COLORS.SECONDARY_BLUE,
  cyan: <PERSON>OM<PERSON><PERSON>_COLORS.SECONDARY_BLUE,
  lime: SOMIPEM_COLORS.PRIMARY_BLUE,
}

const ArretDualBarChart = ({ data = [], loading = false, title = "Comparaison par Machine" }) => {
  const [viewMode, setViewMode] = useState('stops'); // 'stops' or 'duration' or 'both'

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <Text type="secondary">Chargement de la comparaison par machine...</Text>
      </div>
    );
  }  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);

  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Empty 
          description="Aucune donnée de machine disponible"
          style={{ color: '#8c8c8c' }}
        />
      </div>
    );
  }

  // Process and sanitize data - be more forgiving
  const processedData = safeData
    .map(item => {
      // More flexible machine name extraction
      const machineName = item.Machine_Name || 
                         item.machine || 
                         item.nom_machine || 
                         item.name || 
                         item.machineName ||
                         String(item.machine_id || item.id || "Unknown");
      
      // More flexible numeric value extraction
      const stops = Number.parseInt(
        item.stops || 
        item.totalStops ||            // Backend provides totalStops
        item.nombre_arrets || 
        item.Total_Stops || 
        item.count || 
        item.frequency || 
        item.incidents ||             // Another backend field
        0
      );
      
      const totalDuration = Number.parseFloat(
        item.totalDuration || 
        item.duree_totale || 
        item.Total_Duration || 
        item.duration || 
        item.total_time ||
        0
      );
      
      const avgDuration = Number.parseFloat(
        item.avgDuration || 
        item.duree_moyenne || 
        item.average_duration ||
        item.avg_time ||
        (totalDuration > 0 && stops > 0 ? totalDuration / stops : 0)
      );

      return {
        machine: machineName,
        stops: stops,
        totalDuration: totalDuration,
        avgDuration: avgDuration
      };
    })
    .filter(item => 
      item.machine && 
      item.machine !== "N/A" && 
      (item.stops >= 0 || item.totalDuration >= 0) // Allow zero values, don't filter out unknown
    );
  // Use processed data if available, otherwise show empty state
  const finalData = processedData.length > 0 ? processedData : [];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: '#fff',
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          padding: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold', color: '#262626' }}>
            {`Machine: ${label}`}
          </p>
          {payload.map((entry, index) => (
            <p key={index} style={{ margin: '4px 0', color: entry.color }}>
              {`${entry.name}: ${entry.value}${entry.dataKey === 'totalDuration' || entry.dataKey === 'avgDuration' ? ' min' : ''}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };
  const renderChart = () => {
    if (viewMode === 'stops') {
      return (
        <Bar 
          dataKey="stops" 
          fill={CHART_COLORS.primary} 
          name="Nombre d'arrêts"
          radius={[4, 4, 0, 0]}
        />
      );
    } else if (viewMode === 'duration') {
      return (
        <Bar 
          dataKey="totalDuration" 
          fill={CHART_COLORS.secondary} 
          name="Durée totale (min)"
          radius={[4, 4, 0, 0]}
        />
      );
    } else {
      // For 'both' mode, we'll render two separate charts
      return null;
    }
  };  const renderSingleChart = (dataKey, color, title) => (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart 
        data={finalData} 
        margin={{ top: 5, right: 15, left: 15, bottom: 35 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="machine" 
          angle={-45}
          textAnchor="end"
          height={45}
          stroke="#666"
          fontSize={10}
        />
        <YAxis stroke="#666" fontSize={10} />
        <Tooltip content={<CustomTooltip />} />
        <Bar 
          dataKey={dataKey} 
          fill={color} 
          name={title}
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );

  return (
    <div style={{ height: '100%', width: '100%' }}>      {/* View Mode Toggle */}
      <Row style={{ marginBottom: '12px' }}>
        <Col span={24}>
          <Space size="small" style={{ width: '100%', justifyContent: 'center' }}>
            <Button
              type={viewMode === 'stops' ? 'primary' : 'default'}
              icon={<BarChartOutlined />}
              onClick={() => setViewMode('stops')}
              size="small"
            >
              Arrêts
            </Button>
            <Button
              type={viewMode === 'duration' ? 'primary' : 'default'}
              icon={<ClockCircleOutlined />}
              onClick={() => setViewMode('duration')}
              size="small"
            >
              Durée
            </Button>
            <Button
              type={viewMode === 'both' ? 'primary' : 'default'}
              onClick={() => setViewMode('both')}
              size="small"
            >
              Les deux
            </Button>
          </Space>
        </Col>
      </Row>{/* Chart(s) */}
      {viewMode === 'both' ? (
        /* Two separate charts stacked vertically */
        <div style={{ height: 'calc(100% - 70px)', display: 'flex', flexDirection: 'column' }}>
          <div style={{ flex: '1', minHeight: '0', marginBottom: '12px' }}>
            <h4 style={{ 
              textAlign: 'center', 
              margin: '0 0 6px 0',
              color: CHART_COLORS.primary,
              fontSize: '13px',
              fontWeight: 'bold'
            }}>
              Nombre d'arrêts
            </h4>
            <div style={{ height: 'calc(100% - 20px)' }}>
              {renderSingleChart('stops', CHART_COLORS.primary, 'Nombre d\'arrêts')}
            </div>
          </div>
          <div style={{ flex: '1', minHeight: '0' }}>
            <h4 style={{ 
              textAlign: 'center', 
              margin: '0 0 6px 0',
              color: CHART_COLORS.secondary,
              fontSize: '13px',
              fontWeight: 'bold'
            }}>
              Durée totale (min)
            </h4>
            <div style={{ height: 'calc(100% - 20px)' }}>
              {renderSingleChart('totalDuration', CHART_COLORS.secondary, 'Durée totale (min)')}
            </div>
          </div>
        </div>
      ) : (        /* Single chart */
        <div style={{ height: 'calc(100% - 70px)' }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart 
              data={finalData} 
              margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="machine" 
                angle={-45}
                textAnchor="end"
                height={80}
                stroke="#666"
              />
              <YAxis stroke="#666" />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              {renderChart()}
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  );
};

export default ArretDualBarChart;
