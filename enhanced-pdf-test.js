// Enhanced PDF Test Script for Browser Console
// Run this in the browser console after logging in

console.log('🧪 Testing Enhanced PDF Generation...');

// Test function to generate enhanced vs standard reports
async function testEnhancedPDF() {
  try {
    // Test Standard Report
    console.log('📝 Testing Standard PDF Generation...');
    const standardResponse = await fetch('/api/shift-reports/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('token')
      },
      body: JSON.stringify({
        machineId: 'IPS01',
        date: '2025-07-14',
        shift: 'Matin'
      })
    });
    
    const standardResult = await standardResponse.json();
    console.log('✅ Standard PDF Result:', standardResult);
    
    // Test Enhanced Report
    console.log('📝 Testing Enhanced PDF Generation...');
    const enhancedResponse = await fetch('/api/shift-reports/generate-enhanced', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('token')
      },
      body: JSON.stringify({
        machineId: 'IPS01', 
        date: '2025-07-14',
        shift: 'Matin'
      })
    });
    
    const enhancedResult = await enhancedResponse.json();
    console.log('✅ Enhanced PDF Result:', enhancedResult);
    
    // Compare results
    console.log('\n📊 Comparison:');
    console.log('Standard file size:', standardResult.fileSize || 'Not available');
    console.log('Enhanced file size:', enhancedResult.fileSize || 'Not available');
    console.log('Enhanced version:', enhancedResult.version || 'Not available');
    console.log('Performance data:', enhancedResult.reportData?.performance || 'Not available');
    
    return { standard: standardResult, enhanced: enhancedResult };
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return null;
  }
}

// Instructions for testing
console.log(`
🎯 Testing Instructions:

1. Log in to the application first
2. Navigate to the Reports page 
3. Run testEnhancedPDF() in this console
4. Or use the UI toggle to switch between Standard/Enhanced
5. Generate reports and compare results

📋 What to look for:
- Enhanced reports should have larger file sizes
- Enhanced reports should include performance data
- Enhanced reports should have SOMIPEM branding
- Enhanced reports should show French number formatting
- Enhanced reports should include recommendations

🚀 To run the test, type: testEnhancedPDF()
`);

// Make the function available globally
window.testEnhancedPDF = testEnhancedPDF;
