// Test the data transformation logic
const testData = [
  {"Stop_Date":"2025-05-02","Total_Stops":1},
  {"Stop_Date":"2025-05-01","Total_Stops":3},
  {"Stop_Date":"2025-04-30","Total_Stops":11}
];

const transformedChartData = testData.map(item => {
  // Create a more user-friendly date format
  const date = new Date(item.Stop_Date);
  const displayDate = date.toLocaleDateString('fr-FR', { 
    day: '2-digit', 
    month: '2-digit' 
  });
  
  return {
    date: item.Stop_Date,
    displayDate: displayDate,
    stops: item.Total_Stops,
    duration: 0
  };
}).sort((a, b) => new Date(a.date) - new Date(b.date));

console.log('Transformed data:', transformedChartData);
