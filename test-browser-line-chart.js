/**
 * Browser Test: Verify ArretLineChart Data Reception
 * 
 * This script can be run in the browser console to verify that
 * ArretLineChart.jsx receives the exact data we expect from our tests.
 */

// Function to monitor ArretLineChart data reception
function monitorArretLineChartData() {
  console.log('🎯 Monitoring ArretLineChart data reception...');
  
  // Store original console.log to capture ArretLineChart debug logs
  const originalLog = console.log;
  const capturedLogs = [];
  
  console.log = function(...args) {
    // Capture ArretLineChart specific logs
    if (args[0] && args[0].includes && args[0].includes('📈 ArretLineChart')) {
      capturedLogs.push({
        timestamp: new Date().toISOString(),
        message: args[0],
        data: args[1] || null
      });
      
      // Also show the log normally
      originalLog.apply(console, args);
      
      // If this is the main data reception log, analyze it
      if (args[0].includes('received data')) {
        analyzeLineChartData(args[1]);
      }
    } else {
      // Normal log behavior for other messages
      originalLog.apply(console, args);
    }
  };
  
  // Set up a timer to restore console and show results
  setTimeout(() => {
    console.log = originalLog;
    showMonitoringResults(capturedLogs);
  }, 10000); // Monitor for 10 seconds
  
  console.log('✅ Monitoring active for 10 seconds. Trigger a filter change or page refresh to see data flow...');
}

// Analyze the data received by ArretLineChart
function analyzeLineChartData(data) {
  console.log('\n🔍 ANALYZING ARRETLINECHART DATA:');
  console.log('================================');
  
  if (!data) {
    console.log('❌ No data received');
    return;
  }
  
  if (!Array.isArray(data)) {
    console.log('❌ Data is not an array:', typeof data);
    return;
  }
  
  console.log(`📊 Chart receives ${data.length} data points`);
  
  if (data.length > 0) {
    console.log('\n📈 Data Structure Analysis:');
    const firstItem = data[0];
    console.log('- First item keys:', Object.keys(firstItem));
    console.log('- First item:', firstItem);
    
    console.log('\n📅 Date Analysis:');
    data.forEach((item, index) => {
      console.log(`  ${index + 1}. ${item.displayDate || item.date}: ${item.stops} stops, ${item.duration || 0}min`);
    });
    
    console.log('\n📊 Summary:');
    const totalStops = data.reduce((sum, item) => sum + (item.stops || 0), 0);
    const totalDuration = data.reduce((sum, item) => sum + (item.duration || 0), 0);
    const daysWithStops = data.filter(item => (item.stops || 0) > 0).length;
    
    console.log(`- Total stops: ${totalStops}`);
    console.log(`- Total duration: ${totalDuration}min`);
    console.log(`- Days with stops: ${daysWithStops}/${data.length}`);
    console.log(`- Average stops/day: ${(totalStops / data.length).toFixed(1)}`);
    
    if (totalDuration === 0) {
      console.log('⚠️ WARNING: All durations are 0');
    }
    
    // Check data quality
    const hasDisplayDate = data.every(item => item.displayDate);
    const hasStops = data.every(item => typeof item.stops === 'number');
    
    console.log('\n✅ Data Quality Check:');
    console.log(`- Has displayDate: ${hasDisplayDate ? '✅' : '❌'}`);
    console.log(`- Has stops count: ${hasStops ? '✅' : '❌'}`);
    console.log(`- Ready for chart: ${hasDisplayDate && hasStops ? '✅' : '❌'}`);
    
  } else {
    console.log('❌ Empty data array - chart will show "No data available"');
  }
}

// Show monitoring results
function showMonitoringResults(logs) {
  console.log('\n📋 MONITORING RESULTS:');
  console.log('=====================');
  
  if (logs.length === 0) {
    console.log('❌ No ArretLineChart logs captured during monitoring period');
    console.log('💡 Try refreshing the page or changing filters to trigger data loading');
    return;
  }
  
  console.log(`✅ Captured ${logs.length} ArretLineChart logs:`);
  logs.forEach((log, index) => {
    console.log(`\n${index + 1}. [${log.timestamp}] ${log.message}`);
    if (log.data) {
      console.log('   Data:', log.data);
    }
  });
}

// Test current ArretLineChart data (if available)
function testCurrentArretLineChartData() {
  console.log('🔍 Checking current ArretLineChart data...');
  
  // Try to find the ArretLineChart component in the React dev tools
  if (window.React && window.React.version) {
    console.log('✅ React detected:', window.React.version);
  }
  
  // Look for ArretLineChart elements
  const lineChartElements = document.querySelectorAll('[class*="LineChart"], [class*="line-chart"]');
  console.log(`📊 Found ${lineChartElements.length} potential line chart elements`);
  
  // Check for Recharts LineChart specifically
  const rechartsElements = document.querySelectorAll('.recharts-wrapper');
  console.log(`📈 Found ${rechartsElements.length} Recharts elements`);
  
  if (rechartsElements.length > 0) {
    console.log('✅ Recharts library detected - ArretLineChart should be functional');
  }
  
  console.log('\n💡 To monitor live data, run: monitorArretLineChartData()');
}

// Export functions for browser console use
window.testArretLineChart = {
  monitor: monitorArretLineChartData,
  testCurrent: testCurrentArretLineChartData,
  analyze: analyzeLineChartData
};

console.log('🎯 ArretLineChart Test Functions Loaded!');
console.log('Usage:');
console.log('  testArretLineChart.monitor()     - Monitor data for 10 seconds');
console.log('  testArretLineChart.testCurrent() - Check current state');
console.log('  testArretLineChart.analyze(data) - Analyze specific data');

// Run initial test
testCurrentArretLineChartData();
