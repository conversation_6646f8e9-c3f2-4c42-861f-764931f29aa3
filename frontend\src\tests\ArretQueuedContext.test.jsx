// Test script to verify ArretQueuedContext and GraphQL fixes
import { render, screen, waitFor } from '@testing-library/react';
import { ArretQueuedProvider } from '../context/arret/ArretQueuedContext';

// Mock fetch for testing
global.fetch = jest.fn();

describe('ArretQueuedContext GraphQL Fixes', () => {
  beforeEach(() => {
    fetch.mockClear();
  });

  test('should handle corrected GraphQL field names', async () => {
    // Mock successful GraphQL responses with correct field names
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          getStopSidecards: {
            Arret_Totale: 42,
            Arret_Totale_nondeclare: 12
          }
        }
      })
    });

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          getStopMachineNames: [
            { Machine_Name: 'IPS01' },
            { Machine_Name: 'CCM24SC' }
          ]
        }
      })
    });

    const TestComponent = () => (
      <ArretQueuedProvider>
        <div>Test Component</div>
      </ArretQueuedProvider>
    );

    render(<TestComponent />);

    // Wait for the component to finish loading
    await waitFor(() => {
      expect(screen.getByText('Test Component')).toBeInTheDocument();
    });

    // Verify that the correct GraphQL queries were called
    expect(fetch).toHaveBeenCalledWith('/api/graphql', expect.objectContaining({
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: expect.stringContaining('getStopSidecards')
    }));

    expect(fetch).toHaveBeenCalledWith('/api/graphql', expect.objectContaining({
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: expect.stringContaining('getStopMachineNames')
    }));

    // Verify the queries use correct field names
    const calls = fetch.mock.calls;
    const sidecardsCall = calls.find(call => 
      call[1].body.includes('getStopSidecards')
    );
    const machineNamesCall = calls.find(call => 
      call[1].body.includes('getStopMachineNames')
    );

    // Check that the queries use the correct field names
    expect(sidecardsCall[1].body).toContain('Arret_Totale');
    expect(sidecardsCall[1].body).toContain('Arret_Totale_nondeclare');
    expect(sidecardsCall[1].body).not.toContain('title');
    expect(sidecardsCall[1].body).not.toContain('value');

    expect(machineNamesCall[1].body).toContain('Machine_Name');
    expect(machineNamesCall[1].body).not.toContain('name');
  });

  test('should not reference undefined graphQL variable', () => {
    // This test ensures that there are no references to the old graphQL variable
    // If there were, it would throw a ReferenceError during compilation/runtime
    expect(() => {
      const TestComponent = () => (
        <ArretQueuedProvider>
          <div>Test Component</div>
        </ArretQueuedProvider>
      );
      render(<TestComponent />);
    }).not.toThrow();
  });
});
