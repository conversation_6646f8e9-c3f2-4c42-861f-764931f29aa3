import dayjs from "dayjs";

/**
 * Data sampling and optimization utilities for chart performance
 */

/**
 * Sample data points for large datasets to improve chart performance
 * @param {Array} data - Original dataset
 * @param {number} maxPoints - Maximum number of points to display
 * @param {string} dateKey - Key for date field in data objects
 * @returns {Array} Sampled dataset
 */
export const sampleDataForChart = (data, maxPoints = 100, dateKey = 'date') => {
  if (!data || data.length <= maxPoints) {
    return data;
  }

  // Sort data by date to ensure proper sampling
  const sortedData = [...data].sort((a, b) => {
    const dateA = dayjs(a[dateKey]);
    const dateB = dayjs(b[dateKey]);
    return dateA.diff(dateB);
  });

  const step = Math.ceil(sortedData.length / maxPoints);
  const sampledData = [];

  for (let i = 0; i < sortedData.length; i += step) {
    sampledData.push(sortedData[i]);
  }

  // Always include the last data point
  if (sampledData[sampledData.length - 1] !== sortedData[sortedData.length - 1]) {
    sampledData.push(sortedData[sortedData.length - 1]);
  }

  return sampledData;
};

/**
 * Aggregate data by time periods for better performance
 * @param {Array} data - Original dataset
 * @param {string} aggregationType - 'hour', 'day', 'week', 'month'
 * @param {string} dateKey - Key for date field
 * @returns {Array} Aggregated dataset
 */
export const aggregateDataByTime = (data, aggregationType = 'day', dateKey = 'date') => {
  if (!data || data.length === 0) return data;

  const aggregatedMap = new Map();

  data.forEach(item => {
    const date = dayjs(item[dateKey]);
    let periodKey;

    switch (aggregationType) {
      case 'hour':
        periodKey = date.format('YYYY-MM-DD HH:00');
        break;
      case 'day':
        periodKey = date.format('YYYY-MM-DD');
        break;
      case 'week':
        periodKey = date.startOf('week').format('YYYY-MM-DD');
        break;
      case 'month':
        periodKey = date.format('YYYY-MM');
        break;
      default:
        periodKey = date.format('YYYY-MM-DD');
    }

    if (!aggregatedMap.has(periodKey)) {
      aggregatedMap.set(periodKey, {
        [dateKey]: periodKey,
        good: 0,
        reject: 0,
        oee: 0,
        speed: 0,
        count: 0,
        Machine_Name: item.Machine_Name,
        Shift: item.Shift,
      });
    }

    const existing = aggregatedMap.get(periodKey);
    existing.good += Number(item.good) || 0;
    existing.reject += Number(item.reject) || 0;
    existing.oee += Number(item.oee) || 0;
    existing.speed += Number(item.speed) || 0;
    existing.count += 1;
  });

  // Calculate averages for percentage values
  return Array.from(aggregatedMap.values()).map(item => ({
    ...item,
    oee: item.count > 0 ? item.oee / item.count : 0,
    speed: item.count > 0 ? item.speed / item.count : 0,
  }));
};

/**
 * Optimize data for chart display based on view mode
 * @param {Array} data - Original dataset
 * @param {boolean} isExpanded - Whether chart is in expanded mode
 * @param {string} chartType - Type of chart ('bar', 'line', 'pie')
 * @returns {Object} Optimized data and configuration
 */
export const optimizeChartData = (data, isExpanded = false, chartType = 'bar') => {
  if (!data || data.length === 0) {
    return { data: [], config: {} };
  }

  let optimizedData = data;
  let config = {
    showAllLabels: false,
    labelInterval: 'preserveStartEnd',
    maxBarSize: 40,
    strokeWidth: 2,
    dotSize: 4,
  };

  if (isExpanded) {
    // In expanded mode, allow more data points but still optimize for performance
    const maxPoints = chartType === 'line' ? 200 : 150;
    
    if (data.length > maxPoints) {
      optimizedData = sampleDataForChart(data, maxPoints);
    }

    config = {
      showAllLabels: true,
      labelInterval: data.length > 50 ? Math.ceil(data.length / 20) : 0,
      maxBarSize: 60,
      strokeWidth: 3,
      dotSize: 6,
    };
  } else {
    // In normal mode, be more aggressive with sampling
    const maxPoints = chartType === 'line' ? 50 : 30;
    
    if (data.length > maxPoints) {
      optimizedData = sampleDataForChart(data, maxPoints);
    }

    config = {
      showAllLabels: false,
      labelInterval: 'preserveStartEnd',
      maxBarSize: 40,
      strokeWidth: 2,
      dotSize: 4,
    };
  }

  return { data: optimizedData, config };
};

/**
 * Format date labels for better readability
 * @param {string} date - Date string
 * @param {boolean} isExpanded - Whether chart is expanded
 * @param {number} dataLength - Total number of data points
 * @returns {string} Formatted date string
 */
export const formatDateLabel = (date, isExpanded = false, dataLength = 0) => {
  if (!date) return 'N/A';

  try {
    const dateObj = dayjs(date);
    if (!dateObj.isValid()) return 'N/A';

    if (isExpanded) {
      // In expanded mode, show more detailed dates
      if (dataLength > 100) {
        return dateObj.format('MM/DD');
      } else if (dataLength > 50) {
        return dateObj.format('MM/DD/YY');
      } else {
        return dateObj.format('DD/MM/YYYY');
      }
    } else {
      // In normal mode, use shorter format
      if (dataLength > 30) {
        return dateObj.format('MM/DD');
      } else {
        return dateObj.format('DD/MM');
      }
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'N/A';
  }
};

/**
 * Calculate optimal chart dimensions based on data and view mode
 * @param {Array} data - Chart data
 * @param {boolean} isExpanded - Whether chart is expanded
 * @param {string} chartType - Type of chart
 * @returns {Object} Optimal dimensions and margins
 */
export const calculateChartDimensions = (data, isExpanded = false, chartType = 'bar') => {
  const dataLength = data?.length || 0;

  if (isExpanded) {
    return {
      height: '70vh',
      margin: { top: 30, right: 50, left: 50, bottom: 100 },
      fontSize: 14,
      labelAngle: dataLength > 20 ? -45 : 0,
      labelHeight: dataLength > 20 ? 100 : 60,
    };
  } else {
    return {
      height: 300,
      margin: { top: 16, right: 24, left: 24, bottom: 60 },
      fontSize: 12,
      labelAngle: dataLength > 10 ? -45 : 0,
      labelHeight: dataLength > 10 ? 80 : 40,
    };
  }
};

/**
 * Debounce function for performance optimization
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};
