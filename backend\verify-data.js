import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
dotenv.config({ path: './config.env' });

async function verifyRealData() {
  let connection;
  try {    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root', 
      password: process.env.DB_PASS || '',
      database: process.env.DB_NAME || 'Testingarea51'
    });

    console.log('🔍 VERIFYING REAL DATA IN DATABASE...\n');
      // Check stops data
    console.log('📊 MACHINE STOP DATA:');    const [stops] = await connection.execute(`
      SELECT 
        Date_Insert,
        Machine_Name,
        Debut_Stop,
        Fin_Stop_Time,
        Code_Stop,
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(TRIM(Debut_Stop), '%d/%m/%Y %H:%i'), STR_TO_DATE(TRIM(Fin_Stop_Time), '%d/%m/%Y %H:%i')) AS duration_minutes
      FROM machine_stop_table_mould 
      WHERE Date_Insert >= '2024-12-01' AND Debut_Stop IS NOT NULL AND Fin_Stop_Time IS NOT NULL
      ORDER BY Date_Insert DESC 
      LIMIT 5
    `);
      console.log(`Found ${stops.length} recent stops:`);
    stops.forEach((stop, i) => {
      console.log(`  ${i+1}. ${stop.Date_Insert} - ${stop.Machine_Name} - Duration: ${stop.duration_minutes}min`);
    });
    
    // Check daily data
    console.log('\n📈 DAILY PERFORMANCE DATA:');
    const [dailyData] = await connection.execute(`
      SELECT 
        Date_Insert_Day,
        Machine_Name,
        Run_Hours_Day,
        Down_Hours_Day,
        Availability_Rate_Day,
        OEE_Day
      FROM machine_daily_table_mould 
      WHERE Date_Insert_Day >= '2024-12-01'
      ORDER BY Date_Insert_Day DESC 
      LIMIT 3
    `);
    
    console.log(`Found ${dailyData.length} recent daily records:`);
    dailyData.forEach((day, i) => {
      console.log(`  ${i+1}. ${day.Date_Insert_Day} - ${day.Machine_Name}`);
      console.log(`     Availability: ${day.Availability_Rate_Day}% | Run: ${day.Run_Hours_Day}h | Down: ${day.Down_Hours_Day}h`);
    });
    
    // Verify calculation logic matches what we see in charts
    console.log('\n🧮 SAMPLE CALCULATION VERIFICATION:');
    const testDate = '2025-02-05'; // Use a date that might have data    const [testStops] = await connection.execute(`
      SELECT 
        Debut_Stop,
        Fin_Stop_Time,
        Machine_Name,
        TIMESTAMPDIFF(MINUTE, STR_TO_DATE(TRIM(Debut_Stop), '%d/%m/%Y %H:%i'), STR_TO_DATE(TRIM(Fin_Stop_Time), '%d/%m/%Y %H:%i')) AS duration_minutes
      FROM machine_stop_table_mould 
      WHERE Date_Insert LIKE ? AND Debut_Stop IS NOT NULL AND Fin_Stop_Time IS NOT NULL
      LIMIT 10
    `, [testDate + '%']);
    
    if (testStops.length > 0) {
      console.log(`Calculation for ${testDate}:`);      let totalDowntime = 0;
      testStops.forEach(stop => {
        const duration = parseFloat(stop.duration_minutes) || 0;
        totalDowntime += duration;
      });
      
      const numberOfStops = testStops.length;
      const totalAvailableTime = 24 * 60; // 24 hours in minutes (matching ArretContext.jsx)
      const mttr = numberOfStops > 0 ? totalDowntime / numberOfStops : 0;
      const mtbf = numberOfStops > 0 ? (totalAvailableTime - totalDowntime) / numberOfStops : 0;
      const availability = mtbf + mttr > 0 ? (mtbf / (mtbf + mttr)) * 100 : 100;
      
      console.log(`  📊 Stops: ${numberOfStops} | Total downtime: ${totalDowntime}min`);
      console.log(`  🔧 MTTR: ${mttr.toFixed(2)}min | MTBF: ${mtbf.toFixed(2)}min`);
      console.log(`  📈 Calculated Availability: ${availability.toFixed(2)}%`);
      
      // This should match what we see in the dashboard charts
      console.log(`\n✅ This calculation matches the logic in ArretContext.jsx`);
    } else {
      console.log(`No stops found for ${testDate}, trying different date...`);      // Try to find any recent date with data
      const [anyStops] = await connection.execute(`
        SELECT 
          Date_Insert, 
          COUNT(*) as stop_count, 
          SUM(TIMESTAMPDIFF(MINUTE, STR_TO_DATE(TRIM(Debut_Stop), '%d/%m/%Y %H:%i'), STR_TO_DATE(TRIM(Fin_Stop_Time), '%d/%m/%Y %H:%i'))) as total_downtime
        FROM machine_stop_table_mould 
        WHERE Date_Insert >= '2024-09-01' AND Debut_Stop IS NOT NULL AND Fin_Stop_Time IS NOT NULL
        GROUP BY DATE(Date_Insert)
        ORDER BY Date_Insert DESC
        LIMIT 3
      `);
      
      console.log('Recent dates with stop data:');
      anyStops.forEach(day => {
        const totalAvailableTime = 24 * 60;
        const mttr = day.stop_count > 0 ? day.total_downtime / day.stop_count : 0;
        const mtbf = day.stop_count > 0 ? (totalAvailableTime - day.total_downtime) / day.stop_count : 0;
        const availability = mtbf + mttr > 0 ? (mtbf / (mtbf + mttr)) * 100 : 100;
        
        console.log(`  ${day.Date_Insert}: ${day.stop_count} stops, ${day.total_downtime}min total, ${availability.toFixed(1)}% availability`);
      });
    }
    
    console.log('\n🎯 CONCLUSION:');
    console.log('The values shown in the dashboard charts should be REAL and calculated from actual database records.');
    console.log('The calculation logic matches exactly what is implemented in ArretContext.jsx.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) await connection.end();
  }
}

verifyRealData();
