/**
 * Test the main comprehensive GraphQL query
 */

import fetch from 'node-fetch';

async function testComprehensiveQuery() {
  console.log('🧪 Testing comprehensive stop data query...');
  
  const query = `
    query GetComprehensiveStopData($filters: OptimizedStopFilterInput) {
      getComprehensiveStopData(filters: $filters) {
        queryInfo {
          executionTime
          recordCount
          cacheStatus
        }
        
        statistics {
          totalStops
          nonDeclaredStops
          totalDuration
          avgDuration
          uniqueMachines
          uniqueOperators
        }
        
        rawStops {
          Machine_Name
          Date_Insert
          Code_Stop
          duration_minutes
        }
        
        topStops {
          stopName
          count
        }
        
        machineComparison {
          Machine_Name
          stops
          totalDuration
        }
        
        operatorStats {
          operator
          interventions
          totalDuration
        }
      }
    }
  `;

  const variables = {
    filters: {
      limit: 100
    }
  };

  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, variables })
    });

    if (!response.ok) {
      console.error(`❌ HTTP error! status: ${response.status}`);
      return;
    }

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return;
    }

    const data = result.data.getComprehensiveStopData;
    console.log('✅ Success! Comprehensive data retrieved:');
    console.log(`   📊 Query Time: ${data.queryInfo.executionTime}ms`);
    console.log(`   📈 Total Records: ${data.queryInfo.recordCount}`);
    console.log(`   💾 Cache Status: ${data.queryInfo.cacheStatus}`);
    console.log(`   🛑 Total Stops: ${data.statistics.totalStops}`);
    console.log(`   ⚠️  Non-declared: ${data.statistics.nonDeclaredStops}`);
    console.log(`   🏭 Unique Machines: ${data.statistics.uniqueMachines}`);
    console.log(`   👷 Unique Operators: ${data.statistics.uniqueOperators}`);
    console.log(`   🔝 Top Stops: ${data.topStops.length} categories`);
    console.log(`   🔧 Machine Comparisons: ${data.machineComparison.length} machines`);
    console.log(`   👤 Operator Stats: ${data.operatorStats.length} operators`);
    console.log(`   📋 Raw Stops: ${data.rawStops.length} records`);
    
    // Show sample data
    console.log('\n📋 Sample Raw Stops:');
    data.rawStops.slice(0, 3).forEach((stop, i) => {
      console.log(`   ${i+1}. ${stop.Machine_Name} - ${stop.Code_Stop} (${stop.duration_minutes}min)`);
    });
    
    console.log('\n🔝 Top Stop Reasons:');
    data.topStops.slice(0, 3).forEach((stop, i) => {
      console.log(`   ${i+1}. ${stop.stopName}: ${stop.count} occurrences`);
    });

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testComprehensiveQuery();
