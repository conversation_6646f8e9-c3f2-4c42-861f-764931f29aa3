/**
 * Test the top5Stops GraphQL query to see what it returns
 */

const testTop5StopsQuery = async () => {
  try {
    console.log('🔍 Testing getTop5Stops GraphQL query...');
    
    const query = `
      query GetTop5Stops($filters: StopFilterInput) {
        getTop5Stops(filters: $filters) {
          stopName
          count
        }
      }
    `;
    
    // Test with IPS01 + April 2025 filter
    const variables = {
      filters: {
        machine: "IPS01",
        startDate: "2025-04-01",
        endDate: "2025-04-30"
      }
    };
    
    console.log('📤 Query variables:', variables);
    
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query,
        variables
      })
    });
    
    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL errors:', result.errors);
      return;
    }
    
    const topStops = result.data?.getTop5Stops || [];
    console.log('📊 Top 5 Stops result:', topStops);
    
    if (topStops.length > 0) {
      console.log('✅ Top stops data received:');
      topStops.forEach((stop, index) => {
        console.log(`  ${index + 1}. ${stop.stopName}: ${stop.count} occurrences`);
      });
    } else {
      console.log('❌ No top stops data returned');
    }
    
    // Also test without filters
    console.log('\n🔍 Testing without filters...');
    
    const noFilterResponse = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query,
        variables: { filters: {} }
      })
    });
    
    const noFilterResult = await noFilterResponse.json();
    const allTopStops = noFilterResult.data?.getTop5Stops || [];
    
    console.log('📊 Top 5 Stops (no filter):', allTopStops);
    
  } catch (error) {
    console.error('❌ Error testing top5Stops:', error);
  }
};

testTop5StopsQuery();
