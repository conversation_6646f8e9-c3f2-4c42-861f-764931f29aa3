@echo off
echo Starting SOMIPEM PDF Generation System Test
echo ==========================================

echo.
echo 1. Starting Frontend (React)...
cd frontend
start "Frontend" cmd /k "npm run dev"

echo.
echo 2. Waiting for frontend to start...
timeout /t 10 /nobreak

echo.
echo 3. Starting Backend (Express)...
cd ..\backend
start "Backend" cmd /k "npm run dev"

echo.
echo 4. Waiting for backend to start...
timeout /t 15 /nobreak

echo.
echo 5. Running PDF Generation Test...
node test-pdf-generation.js

echo.
echo Test completed! Check the test-output folder for generated PDF.
pause
