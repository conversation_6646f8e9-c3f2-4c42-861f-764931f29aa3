-- Migration script to enhance notifications table with priority and machine alert features
-- Run this script to upgrade existing Somipem installations

-- Add new columns to notifications table
ALTER TABLE notifications 
ADD COLUMN priority ENUM('low','medium','high','critical') NOT NULL DEFAULT 'medium' AFTER category,
ADD COLUMN severity ENUM('info','warning','error','critical') NOT NULL DEFAULT 'info' AFTER priority,
ADD COLUMN source VARCHAR(100) DEFAULT 'system' AFTER severity,
ADD COLUMN machine_id INT DEFAULT NULL AFTER source,
ADD COLUMN acknowledged TINYINT(1) NOT NULL DEFAULT '0' AFTER `read`,
ADD COLUMN acknowledged_by INT DEFAULT NULL AFTER acknowledged,
ADD COLUMN acknowledged_at DATETIME DEFAULT NULL AFTER acknowledged_by;

-- Update category enum to include new types
ALTER TABLE notifications 
MODIFY COLUMN category ENUM('alert','maintenance','update','info','machine_alert','production','quality') NOT NULL;

-- Add new indexes for better performance
ALTER TABLE notifications 
ADD KEY idx_notifications_priority (priority),
ADD KEY idx_notifications_machine_id (machine_id),
ADD KEY idx_notifications_category (category),
ADD KEY idx_notifications_source (source),
ADD KEY idx_notifications_acknowledged (acknowledged);

-- Add foreign key constraint for acknowledged_by
ALTER TABLE notifications 
ADD CONSTRAINT notifications_ibfk_2 FOREIGN KEY (acknowledged_by) REFERENCES users (id) ON DELETE SET NULL;

-- Update existing notifications to have default values
UPDATE notifications 
SET priority = 'medium', 
    severity = 'info', 
    source = 'system' 
WHERE priority IS NULL OR severity IS NULL OR source IS NULL;

-- Create a view for critical notifications
CREATE OR REPLACE VIEW critical_notifications AS
SELECT 
    n.*,
    u.username as acknowledged_by_username,
    m.Machine_Name as machine_name
FROM notifications n
LEFT JOIN users u ON n.acknowledged_by = u.id
LEFT JOIN real_time_table m ON n.machine_id = m.id
WHERE n.priority = 'critical' AND n.acknowledged = FALSE
ORDER BY n.timestamp DESC;

-- Create a view for machine alerts
CREATE OR REPLACE VIEW machine_alerts AS
SELECT 
    n.*,
    m.Machine_Name as machine_name,
    m.Etat as machine_status,
    m.TRS as machine_trs
FROM notifications n
LEFT JOIN real_time_table m ON n.machine_id = m.id
WHERE n.category = 'machine_alert'
ORDER BY n.timestamp DESC;

-- Insert sample machine alert thresholds configuration
CREATE TABLE IF NOT EXISTS machine_alert_thresholds (
    id INT AUTO_INCREMENT PRIMARY KEY,
    threshold_type ENUM('trs_warning', 'trs_critical', 'reject_rate_warning', 'reject_rate_critical', 'downtime_warning', 'downtime_critical') NOT NULL,
    threshold_value DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_threshold_type (threshold_type)
);

-- Insert default threshold values
INSERT INTO machine_alert_thresholds (threshold_type, threshold_value, unit, description) VALUES
('trs_warning', 70.00, 'percent', 'TRS below this value triggers warning alert'),
('trs_critical', 50.00, 'percent', 'TRS below this value triggers critical alert'),
('reject_rate_warning', 5.00, 'percent', 'Reject rate above this value triggers warning alert'),
('reject_rate_critical', 10.00, 'percent', 'Reject rate above this value triggers critical alert'),
('downtime_warning', 5.00, 'minutes', 'Machine downtime above this value triggers warning alert'),
('downtime_critical', 15.00, 'minutes', 'Machine downtime above this value triggers critical alert')
ON DUPLICATE KEY UPDATE 
    threshold_value = VALUES(threshold_value),
    description = VALUES(description);

-- Create notification statistics view
CREATE OR REPLACE VIEW notification_stats AS
SELECT 
    COUNT(*) as total_notifications,
    SUM(CASE WHEN `read` = FALSE THEN 1 ELSE 0 END) as unread_count,
    SUM(CASE WHEN priority = 'critical' THEN 1 ELSE 0 END) as critical_count,
    SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_count,
    SUM(CASE WHEN category = 'machine_alert' THEN 1 ELSE 0 END) as machine_alert_count,
    SUM(CASE WHEN acknowledged = TRUE THEN 1 ELSE 0 END) as acknowledged_count,
    AVG(TIMESTAMPDIFF(MINUTE, timestamp, 
        CASE WHEN `read` = TRUE THEN timestamp ELSE NOW() END)) as avg_response_time_minutes
FROM notifications
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- Create indexes for better query performance
CREATE INDEX idx_notifications_composite_priority_read ON notifications (priority, `read`, timestamp);
CREATE INDEX idx_notifications_composite_category_machine ON notifications (category, machine_id, timestamp);
CREATE INDEX idx_notifications_composite_user_unread ON notifications (user_id, `read`, timestamp);

-- Add a trigger to automatically set acknowledged_at when acknowledged is set to true
DELIMITER //
CREATE TRIGGER tr_notifications_acknowledged_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
BEGIN
    IF NEW.acknowledged = TRUE AND OLD.acknowledged = FALSE THEN
        SET NEW.acknowledged_at = NOW();
    END IF;
END//
DELIMITER ;

-- Create a stored procedure to clean up old notifications
DELIMITER //
CREATE PROCEDURE CleanupOldNotifications(IN days_to_keep INT)
BEGIN
    DECLARE notification_count INT;
    
    -- Count notifications to be deleted
    SELECT COUNT(*) INTO notification_count
    FROM notifications 
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
    AND `read` = TRUE 
    AND acknowledged = TRUE;
    
    -- Delete old notifications
    DELETE FROM notifications 
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
    AND `read` = TRUE 
    AND acknowledged = TRUE;
    
    -- Log the cleanup
    INSERT INTO notifications (title, message, category, priority, source, timestamp, `read`)
    VALUES (
        'Notification Cleanup',
        CONCAT('Cleaned up ', notification_count, ' old notifications older than ', days_to_keep, ' days'),
        'info',
        'low',
        'system',
        NOW(),
        FALSE
    );
END//
DELIMITER ;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON notifications TO 'somipem_user'@'localhost';
-- GRANT SELECT ON critical_notifications TO 'somipem_user'@'localhost';
-- GRANT SELECT ON machine_alerts TO 'somipem_user'@'localhost';
-- GRANT SELECT ON notification_stats TO 'somipem_user'@'localhost';

-- Migration completed successfully
SELECT 'Notification system enhancement migration completed successfully' as status;
