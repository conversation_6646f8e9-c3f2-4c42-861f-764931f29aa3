// Mock GraphQL hook for debugging
export function createMockGraphQLHook() {
  return {
    getMachineNames: async () => {
      return [
        { Machine_Name: 'Machine1-IPS', model: 'IPS' },
        { Machine_Name: 'Machine2-IPS', model: 'IPS' },
        { Machine_Name: 'Machine3-AKROS', model: 'AKROS' }
      ];
    },
    getMachineModels: async () => {
      return ['IPS', 'AKROS', 'ML', 'FCS'];
    }
  };
}
