/**
 * Smart Skeleton Hook for Context-Aware Loading States
 * Provides intelligent skeleton management based on data state and complexity
 */

import { useState, useEffect, useMemo } from 'react';

const useSmartSkeleton = (
  loading, 
  data, 
  filters = {}, 
  options = {}
) => {
  const {
    minDisplayTime = 500, // Minimum time to show skeleton (prevent flashing)
    maxDisplayTime = 10000, // Maximum time before showing error state
    complexityThreshold = 3, // Number of filters that make query "complex"
    adaptiveTimings = true
  } = options;

  const [skeletonState, setSkeletonState] = useState({
    show: loading,
    type: 'normal',
    message: 'Chargement...',
    startTime: null
  });

  // Analyze filter complexity for adaptive skeleton behavior
  const filterComplexity = useMemo(() => {
    const filterCount = Object.keys(filters).filter(key => 
      filters[key] !== null && 
      filters[key] !== undefined && 
      filters[key] !== ''
    ).length;

    return {
      level: filterCount >= complexityThreshold ? 'high' : 
             filterCount >= 2 ? 'medium' : 'low',
      count: filterCount,
      isComplex: filterCount >= complexityThreshold
    };
  }, [filters, complexityThreshold]);

  // Determine skeleton type and timing based on complexity
  const skeletonConfig = useMemo(() => {
    const baseConfig = {
      type: 'normal',
      estimatedTime: 2000,
      message: 'Chargement des données...'
    };

    if (!adaptiveTimings) return baseConfig;

    switch (filterComplexity.level) {
      case 'high':
        return {
          type: 'complex',
          estimatedTime: 5000,
          message: 'Traitement des filtres complexes...'
        };
      case 'medium':
        return {
          type: 'medium',
          estimatedTime: 3000,
          message: 'Chargement avec filtres...'
        };
      default:
        return baseConfig;
    }
  }, [filterComplexity, adaptiveTimings]);

  // Manage skeleton display timing
  useEffect(() => {
    if (loading) {
      const startTime = Date.now();
      setSkeletonState(prev => ({
        ...prev,
        show: true,
        type: skeletonConfig.type,
        message: skeletonConfig.message,
        startTime
      }));

      // Set timeout for error state if loading takes too long
      const errorTimeout = setTimeout(() => {
        setSkeletonState(prev => ({
          ...prev,
          type: 'error',
          message: 'Le chargement prend plus de temps que prévu...'
        }));
      }, maxDisplayTime);

      return () => clearTimeout(errorTimeout);
    } else if (data !== null && data !== undefined) {
      // Data loaded successfully
      const currentTime = Date.now();
      const elapsedTime = skeletonState.startTime ? 
        currentTime - skeletonState.startTime : 0;

      if (elapsedTime < minDisplayTime) {
        // Show skeleton for minimum time to prevent flashing
        setTimeout(() => {
          setSkeletonState(prev => ({ ...prev, show: false }));
        }, minDisplayTime - elapsedTime);
      } else {
        setSkeletonState(prev => ({ ...prev, show: false }));
      }
    }
  }, [loading, data, skeletonConfig, minDisplayTime, maxDisplayTime]);

  return {
    showSkeleton: skeletonState.show,
    skeletonType: skeletonState.type,
    skeletonMessage: skeletonState.message,
    filterComplexity,
    isComplexQuery: filterComplexity.isComplex,
    estimatedTime: skeletonConfig.estimatedTime
  };
};

export default useSmartSkeleton;
