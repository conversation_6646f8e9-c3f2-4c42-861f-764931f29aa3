/**
 * Test database connection and basic query
 */

import fetch from 'node-fetch';

async function testDatabaseConnection() {
  console.log('🧪 Testing database connection via existing resolver...');
  
  // Test with the working original resolver
  const query = `
    query {
      getAllMachineStops(filters: { limit: 5 }) {
        Machine_Name
        Date_Insert
        Code_Stop
        duration_minutes
      }
    }
  `;

  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query })
    });

    if (!response.ok) {
      console.error(`❌ HTTP error! status: ${response.status}`);
      return;
    }

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return;
    }

    console.log('✅ Database connection working!');
    console.log('📊 Sample data:', result.data.getAllMachineStops.length, 'records');
    console.log('🔍 First record:', result.data.getAllMachineStops[0]);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDatabaseConnection();
