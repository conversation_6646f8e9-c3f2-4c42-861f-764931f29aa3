/**
 * Test chart data flow specifically for ArretQueuedContext
 * This will help debug why the chart shows "Aucune donnée d'évolution disponible"
 */

// Test the queuedDataManager specifically
console.log('🧪 Testing ArretQueuedContext chart data flow...');

// Simulate a backend response with the format we know works
const mockBackendResponse = {
  getAllMachineStops: [
    {
      ID_Stop: 1,
      Date_Insert: "16/04/2025 10:30:00",
      Machine_Name: "IPS01",
      duration_minutes: 90,
      Debut_Stop: "16/04/2025 10:30:00",
      Fin_Stop_Time: "16/04/2025 12:00:00"
    },
    {
      ID_Stop: 2,
      Date_Insert: "16/04/2025 14:45:00", 
      Machine_Name: "IPS01",
      duration_minutes: 45,
      Debut_Stop: "16/04/2025 14:45:00", 
      Fin_Stop_Time: "16/04/2025 15:30:00"
    },
    {
      ID_Stop: 3,
      Date_Insert: "17/04/2025 08:15:00",
      Machine_Name: "IPS01", 
      duration_minutes: 135,
      Debut_Stop: "17/04/2025 08:15:00",
      Fin_Stop_Time: "17/04/2025 10:30:00"
    }
  ]
};

// Test the date parsing function from queuedDataManager
const parseDate = (dateStr) => {
  if (!dateStr) return null;
  
  try {
    const str = String(dateStr).trim();
    
    // Handle DD/MM/YYYY HH:MM:SS format (most common from GraphQL)
    if (str.includes('/')) {
      const parts = str.split(' ');
      const datePart = parts[0]; // "DD/MM/YYYY"
      const timePart = parts[1] || '00:00:00'; // "HH:MM:SS" or default
      
      const [day, month, year] = datePart.split('/');
      
      if (day && month && year && 
          day.length <= 2 && month.length <= 2 && year.length === 4) {
        
        const paddedDay = day.padStart(2, '0');
        const paddedMonth = month.padStart(2, '0');
        
        // Create ISO format: YYYY-MM-DDTHH:MM:SS
        const isoString = `${year}-${paddedMonth}-${paddedDay}T${timePart}`;
        
        const date = new Date(isoString);
        if (!isNaN(date.getTime())) {
          return {
            toISOString: () => date.toISOString(),
            format: (fmt) => {
              if (fmt === 'YYYY-MM-DD') {
                return `${year}-${paddedMonth}-${paddedDay}`;
              }
              if (fmt === 'DD/MM/YYYY') {
                return `${paddedDay}/${paddedMonth}/${year}`;
              }
              return date.toISOString();
            },
            isValid: () => true
          };
        }
      }
    }
    
    return null;
  } catch (error) {
    console.warn('Date parsing error:', error, 'for date:', dateStr);
    return null;
  }
};

// Test chart data transformation like queuedDataManager does
const transformToChartData = (stopsData, filters = {}) => {
  console.log('📊 Transforming stops data to chart format...');
  console.log('Input data:', stopsData?.length || 0, 'stops');
  
  if (!stopsData || stopsData.length === 0) {
    console.log('❌ No stops data to transform');
    return [];
  }

  // Group by date
  const dailyStats = {};
  
  stopsData.forEach(stop => {
    const dateField = stop.Date_Insert;
    const parsedDate = parseDate(dateField);
    
    if (parsedDate && parsedDate.isValid()) {
      const dateKey = parsedDate.format('YYYY-MM-DD');
      
      if (!dailyStats[dateKey]) {
        dailyStats[dateKey] = {
          date: parsedDate.format('DD/MM/YYYY'),
          dateKey: dateKey,
          stops: 0,
          totalDuration: 0,
          details: []
        };
      }
      
      dailyStats[dateKey].stops += 1;
      dailyStats[dateKey].totalDuration += stop.duration_minutes || 0;
      dailyStats[dateKey].details.push(stop);
    } else {
      console.warn('⚠️ Failed to parse date:', dateField);
    }
  });
  
  // Convert to array and sort
  const chartData = Object.values(dailyStats).sort((a, b) => {
    return new Date(a.dateKey) - new Date(b.dateKey);
  });
  
  console.log('📈 Generated chart data:', chartData);
  return chartData;
};

// Test the transformation
const chartData = transformToChartData(mockBackendResponse.getAllMachineStops);

console.log('🎯 Final result:');
console.log('Chart data length:', chartData.length);
console.log('Chart data preview:', chartData.slice(0, 2));

if (chartData.length > 0) {
  console.log('✅ Chart data transformation successful!');
  console.log('First data point:', {
    date: chartData[0].date,
    stops: chartData[0].stops,
    totalDuration: chartData[0].totalDuration
  });
} else {
  console.log('❌ Chart data transformation failed - no data generated');
}

// Test what the ArretLineChart component expects
console.log('\n🖼️ Testing ArretLineChart data format:');
console.log('Expected format: Array of objects with { date, stops, totalDuration }');
console.log('Actual format:', chartData.length > 0 ? Object.keys(chartData[0]) : 'N/A');

export { parseDate, transformToChartData };
