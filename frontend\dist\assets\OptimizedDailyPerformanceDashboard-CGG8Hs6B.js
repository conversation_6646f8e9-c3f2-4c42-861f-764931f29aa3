import{a as e,r as t,j as s,b as n,u as i}from"./index-Nnj1g72A.js";import{r as a,R as r}from"./react-vendor-tYPmozCJ.js";import{w as o,l,E as c,r as d}from"./chart-config-DFN0skhq.js";import{a as g,R as u,a0 as h,S as x,c as p,f as m,a1 as j,a2 as y,a3 as b,e as f,M as v,E as S,a4 as R,a5 as _,a6 as w,a7 as E,x as C,a8 as D,a9 as k,w as T,T as N,O as A,Y as I,aa as M,m as F,ab as B,ac as L,ad as z,ae as P,B as Q,af as U,A as W,ag as H,u as O,s as q}from"./antd-vendor-4OvKHZ_k.js";import{L as G,B as $}from"./chart-vendor-CazprKWL.js";import"./utils-vendor-BlNwBmLj.js";const Y=e=>{const t=parseFloat(e);return isNaN(t)?0:t},K=({wsStatus:e,onRefresh:t})=>s.jsxs(x,{children:[e.connected?s.jsx(p,{title:"Data is being updated in real-time",children:s.jsxs(m,{className:"ws-status-tag",color:"success",children:[s.jsx(j,{})," Real-time connected"]})}):e.connecting?s.jsx(p,{title:"Attempting to establish real-time connection",children:s.jsxs(m,{className:"ws-status-tag",color:"processing",children:[s.jsx(y,{spin:!0})," Connecting..."]})}):e.reconnecting?s.jsx(p,{title:"Connection lost, attempting to reconnect",children:s.jsxs(m,{className:"ws-status-tag",color:"warning",children:[s.jsx(y,{spin:!0})," Reconnecting..."]})}):s.jsx(p,{title:"WebSocket connection lost - click Refresh to reconnect",children:s.jsxs(m,{className:"ws-status-tag",color:"error",children:[s.jsx(b,{})," Disconnected"]})}),e.updating?s.jsx(p,{title:"Receiving new data from server",children:s.jsxs(m,{className:"ws-status-tag ws-status-updating",color:"processing",children:[s.jsx(y,{spin:!0})," Updating"]})}):null,(()=>{const n=!e.connected&&e.connecting,i=e.updating;let a="Refresh data from server",r="Refresh Data";return n?a="Connection in progress...":i?a="Data is currently being updated":e.connected||e.reconnecting?e.reconnecting&&(a="Reconnection in progress...",r="Reconnecting..."):(a="Click to attempt WebSocket reconnection",r="Reconnect"),s.jsx(p,{title:a,children:s.jsx(f,{type:e.connected||e.connecting||e.reconnecting?"primary":"danger",icon:s.jsx(u,{spin:e.updating||e.reconnecting}),onClick:t,size:"small",loading:n,disabled:i||e.reconnecting,children:r})})})()]});l.throttle(((e,t)=>{e&&e.data&&(e.data=t,e.update("none"))}),500);const V=window.innerWidth<768,{Title:Z,Text:X}=N,{TabPane:J}=R,ee=({visible:e,machine:t,machineHistory:i,loading:a,error:r,onClose:o,onRefresh:l,darkMode:c})=>{if(!t)return null;const d=[{title:"Start Time",dataIndex:"session_start",key:"session_start",render:e=>new Date(e).toLocaleString(),sorter:(e,t)=>new Date(t.session_start)-new Date(e.session_start)},{title:"End Time",dataIndex:"session_end",key:"session_end",render:e=>e?new Date(e).toLocaleString():s.jsx(m,{color:"processing",children:"Active"})},{title:"Duration",key:"duration",render:(e,t)=>{const s=new Date(t.session_start),n=(t.session_end?new Date(t.session_end):new Date)-s;return`${Math.floor(n/36e5)}h ${Math.floor(n%36e5/6e4)}m`}},{title:"TRS",dataIndex:"TRS",key:"TRS",render:e=>{const t=parseFloat(e||0);let n="red";return t>80?n="green":t>60&&(n="orange"),s.jsxs("span",{style:{color:n},children:[t.toFixed(1),"%"]})},sorter:(e,t)=>parseFloat(e.TRS||0)-parseFloat(t.TRS||0)},{title:"Production",dataIndex:"Quantite_Bon",key:"Quantite_Bon",sorter:(e,t)=>parseFloat(e.Quantite_Bon||0)-parseFloat(t.Quantite_Bon||0)},{title:"Rejects",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",sorter:(e,t)=>parseFloat(e.Quantite_Rejet||0)-parseFloat(t.Quantite_Rejet||0)},{title:"Status",key:"status",render:(e,t)=>s.jsx(m,{color:t.session_end?"default":"processing",children:t.session_end?"Completed":"Active"})}],g=(e=>{const t=e?n.DARK.BORDER:n.ACCENT_BORDER,s=e?n.DARK.TEXT_SECONDARY:n.LIGHT_GRAY,i=e?n.DARK.TEXT:n.DARK_GRAY,a=e?n.DARK.BACKGROUND:n.WHITE,r=e?n.DARK.BORDER:n.ACCENT_BORDER,o=[n.PRIMARY_BLUE,n.SECONDARY_BLUE,n.CHART_TERTIARY,n.CHART_QUATERNARY,"#60A5FA","#1D4ED8","#3730A3"],l=e?"0 4px 12px rgba(0, 0, 0, 0.5)":"0 4px 12px rgba(0, 0, 0, 0.1)";return{responsive:!0,maintainAspectRatio:!1,animation:!1,devicePixelRatio:1,plugins:{legend:{position:"top",align:"center",labels:{color:s,padding:15,usePointStyle:!0,pointStyle:"circle",boxWidth:10,font:{weight:500}},display:!V},tooltip:{enabled:!V||window.innerWidth>480,backgroundColor:a,titleColor:e?"rgba(255, 255, 255, 0.95)":"rgba(0, 0, 0, 0.95)",bodyColor:s,borderColor:r,borderWidth:1,padding:10,cornerRadius:6,boxPadding:5,displayColors:!0,boxShadow:l,callbacks:{label:function(e){let t=e.dataset.label||"";return t&&(t+=": "),t+=Math.round(100*e.parsed.y)/100,t},title:function(e){return e[0].label}}},title:{display:!1,color:i,font:{weight:600,size:16}}},scales:{x:{grid:{display:!1,color:t,z:-1},border:{color:e?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:s,maxRotation:0,autoSkipPadding:10,maxTicksLimit:V?5:10,padding:8,font:{size:V?10:12}},title:{display:!1,color:i,font:{weight:500}}},y:{beginAtZero:!0,grid:{color:t,z:-1,lineWidth:1,drawBorder:!0},border:{color:e?"rgba(255, 255, 255, 0.2)":"rgba(0, 0, 0, 0.2)"},ticks:{color:s,precision:0,maxTicksLimit:V?5:8,padding:8,font:{size:V?10:12}},title:{display:!1,color:i,font:{weight:500}}}},elements:{point:{radius:V?0:3,hoverRadius:V?3:6,backgroundColor:function(e){const t=e.datasetIndex%o.length;return o[t]},borderColor:e?"#141414":"white",borderWidth:2,hoverBorderWidth:2,hoverBorderColor:e?"rgba(255, 255, 255, 0.5)":"rgba(0, 0, 0, 0.5)"},line:{borderWidth:V?2:3,tension:.2,fill:!1,borderColor:function(e){const t=e.datasetIndex%o.length;return o[t]},borderCapStyle:"round"},bar:{backgroundColor:function(e){const t=e.datasetIndex%o.length;return o[t]},borderWidth:0,borderRadius:4,hoverBackgroundColor:function(t){return t.datasetIndex,e?n.DARK.SECONDARY_BLUE:n.SECONDARY_BLUE}}}}})(c),x=()=>{const e=(new Date).getHours();return e>=6&&e<14?"morning":e>=14&&e<22?"afternoon":"night"},p=(e,t)=>{if("all"===t)return!0;const s=new Date(e.session_start).getHours();return"morning"===t&&s>=6&&s<14||("afternoon"===t&&s>=14&&s<22||"night"===t&&(s>=22||s<6))},y=e=>{if(null==e||""===e)return 0;const t=String(e).replace(/,/g,"."),s=Number.parseFloat(t);return isNaN(s)?0:s},b=(e,t=1)=>{if(null==e||""===e)return"0";return y(e).toFixed(t).replace(/\./g,",")},N=e=>{if(null==e||""===e)return"0";return y(e).toLocaleString("en-US").replace(/,/g,".")};return s.jsx(v,{title:`Sessions de ${t.Machine_Name}`,open:e,width:800,onCancel:o,footer:[s.jsx(f,{onClick:o,children:"Fermer"},"close"),s.jsx(f,{type:"primary",onClick:()=>window.open("/sessions-report","_blank"),children:"Voir toutes les sessions"},"allSessions")],destroyOnClose:!0,children:(()=>{if(a)return s.jsxs("div",{style:{textAlign:"center",padding:"40px 0"},children:[s.jsx("div",{className:"ant-spin ant-spin-lg ant-spin-spinning",children:s.jsxs("span",{className:"ant-spin-dot ant-spin-dot-spin",children:[s.jsx("i",{className:"ant-spin-dot-item"}),s.jsx("i",{className:"ant-spin-dot-item"}),s.jsx("i",{className:"ant-spin-dot-item"}),s.jsx("i",{className:"ant-spin-dot-item"})]})}),s.jsx("div",{style:{marginTop:16},children:"Chargement de l'historique..."})]});if(r)return s.jsx(S,{description:s.jsxs(s.Fragment,{children:[s.jsxs("p",{children:["Erreur de chargement: ",r]}),s.jsx(f,{type:"primary",icon:s.jsx(u,{}),onClick:l,children:"Réessayer"})]})});if(!i||0===i.length)return s.jsx(S,{description:s.jsxs(s.Fragment,{children:[s.jsx("p",{children:"Aucune session trouvée pour cette machine"}),s.jsx("p",{children:"La table machine_sessions est vide ou aucune donnée n'est disponible"}),s.jsx(f,{type:"primary",icon:s.jsx(u,{}),onClick:l,children:"Rafraîchir"})]}),image:S.PRESENTED_IMAGE_SIMPLE});const e={labels:i.map((e=>{const t=new Date(e.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})),datasets:[{label:"TRS (%)",data:i.map((e=>parseFloat(e.TRS)||0)),backgroundColor:"rgba(153, 102, 255, 0.2)",borderColor:"rgba(153, 102, 255, 1)",borderWidth:2,fill:!0}]},n={labels:i.map((e=>{const t=new Date(e.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})),datasets:[{label:"Good Production",data:i.map((e=>parseFloat(e.Quantite_Bon)||0)),backgroundColor:"rgba(75, 192, 192, 0.6)",borderColor:"rgba(75, 192, 192, 1)",borderWidth:1}]},o={labels:i.map((e=>{const t=new Date(e.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})),datasets:[{label:"Rejected Production",data:i.map((e=>parseFloat(e.Quantite_Rejet)||0)),backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:1}]},v={labels:i.map((e=>{const t=new Date(e.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})),datasets:[{label:"Session Duration (min)",data:i.map((e=>{const t=new Date(e.session_start),s=e.session_end?new Date(e.session_end):new Date;return Math.round((s-t)/6e4)})),backgroundColor:"rgba(255, 159, 64, 0.6)",borderColor:"rgba(255, 159, 64, 1)",borderWidth:1}]};return s.jsxs(R,{defaultActiveKey:"1",className:c?"dark-mode":"",children:[s.jsx(J,{tab:"Sessions",children:s.jsx(_,{columns:d,dataSource:i.map(((e,t)=>({...e,key:t}))),pagination:{pageSize:5},scroll:{x:!0}})},"1"),s.jsx(J,{tab:"Charts",children:s.jsxs(w,{gutter:[16,16],children:[s.jsx(E,{xs:24,md:12,children:s.jsxs("div",{className:"chart-container",children:[s.jsxs("h3",{className:"chart-title",children:[s.jsx(C,{})," TRS (%)"]}),s.jsx("div",{style:{height:200},children:s.jsx(G,{data:e,options:{...g,scales:{...g.scales,y:{...g.scales.y,beginAtZero:!0,max:100,grid:{color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...g.scales.x,grid:{display:!1,color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...g.plugins,legend:{...g.plugins.legend,labels:{...g.plugins.legend.labels,color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})})]})}),s.jsx(E,{xs:24,md:12,children:s.jsxs("div",{className:"chart-container",children:[s.jsxs("h3",{className:"chart-title",children:[s.jsx(j,{})," Production (pcs)"]}),s.jsx("div",{style:{height:200},children:s.jsx($,{data:n,options:{...g,scales:{...g.scales,y:{...g.scales.y,beginAtZero:!0,grid:{color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...g.scales.x,grid:{display:!1,color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...g.plugins,legend:{...g.plugins.legend,labels:{...g.plugins.legend.labels,color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})})]})}),s.jsx(E,{xs:24,md:12,children:s.jsxs("div",{className:"chart-container",children:[s.jsxs("h3",{className:"chart-title",children:[s.jsx(h,{})," Rejects (pcs)"]}),s.jsx("div",{style:{height:200},children:s.jsx($,{data:o,options:{...g,scales:{...g.scales,y:{...g.scales.y,beginAtZero:!0,grid:{color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...g.scales.x,grid:{display:!1,color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...g.plugins,legend:{...g.plugins.legend,labels:{...g.plugins.legend.labels,color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})})]})}),s.jsx(E,{xs:24,md:12,children:s.jsxs("div",{className:"chart-container",children:[s.jsxs("h3",{className:"chart-title",children:[s.jsx(D,{})," Session Duration (min)"]}),s.jsx("div",{style:{height:200},children:s.jsx($,{data:v,options:{...g,scales:{...g.scales,y:{...g.scales.y,beginAtZero:!0,grid:{color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...g.scales.x,grid:{display:!1,color:c?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...g.plugins,legend:{...g.plugins.legend,labels:{...g.plugins.legend.labels,color:c?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})})]})})]})},"2"),s.jsx(J,{tab:s.jsxs("span",{children:[s.jsx(F,{style:{marginRight:8}}),"Informations"]}),children:s.jsx("div",{style:{padding:"16px 0"},children:s.jsxs(w,{gutter:[24,24],children:[s.jsx(E,{xs:24,md:12,children:s.jsxs(k,{title:s.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx(T,{style:{color:"#1890ff",marginRight:8}}),s.jsx("span",{children:"Détails de la machine"})]}),bordered:!0,style:{height:"100%"},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:16},children:[s.jsx("div",{style:{width:64,height:64,borderRadius:8,background:"rgba(24, 144, 255, 0.1)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:16},children:s.jsx(T,{style:{fontSize:32,color:"#1890ff"}})}),s.jsxs("div",{children:[s.jsx(Z,{level:4,style:{margin:0},children:t.Machine_Name}),s.jsx(X,{type:"secondary",children:i.length>0&&i[0].Ordre_Fabrication?`OF : ${i[0].Ordre_Fabrication}`:"Aucun ordre de fabrication"})]})]}),s.jsx(A,{style:{margin:"16px 0"}}),s.jsxs(w,{gutter:[16,16],children:[s.jsx(E,{span:12,children:s.jsx(I,{title:s.jsxs(X,{style:{fontSize:14},children:["Sessions ","morning"===x()?"matin":"afternoon"===x()?"après-midi":"nuit"]}),value:N(i.filter((e=>p(e,x()))).length),prefix:s.jsx(M,{}),valueStyle:{color:"#1890ff",fontSize:20}})}),s.jsx(E,{span:12,children:s.jsx(I,{title:s.jsxs(X,{style:{fontSize:14},children:["Sessions actives ","morning"===x()?"matin":"afternoon"===x()?"après-midi":"nuit"]}),value:N(i.filter((e=>!e.session_end&&p(e,x()))).length),prefix:s.jsx(D,{}),valueStyle:{color:i.filter((e=>!e.session_end&&p(e,x()))).length>0?"#52c41a":"#8c8c8c",fontSize:20}})})]})]})}),s.jsx(E,{xs:24,md:12,children:s.jsx(k,{title:s.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx(M,{style:{color:"#1890ff",marginRight:8}}),s.jsx("span",{children:"Historique des sessions"})]}),bordered:!0,style:{height:"100%"},children:i.length>0?s.jsxs(s.Fragment,{children:[s.jsxs("div",{style:{marginBottom:16},children:[s.jsx(X,{strong:!0,children:"Dernière session :"}),s.jsxs("div",{style:{background:"rgba(0,0,0,0.02)",padding:"12px",borderRadius:"8px",marginTop:"8px"},children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8},children:[s.jsx(X,{children:"Début :"}),s.jsx(X,{strong:!0,children:i[0].session_start?new Date(i[0].session_start).toLocaleString("fr-FR"):""})]}),s.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[s.jsx(X,{children:"Fin :"}),s.jsx(X,{strong:!0,children:i[0].session_end?new Date(i[0].session_end).toLocaleString("fr-FR"):s.jsx(m,{color:"processing",children:"En cours"})})]})]})]}),s.jsx(A,{style:{margin:"16px 0"}}),s.jsxs(w,{gutter:[16,16],children:[s.jsx(E,{span:8,children:s.jsx(I,{title:s.jsx(X,{style:{fontSize:14},children:"TRS moyen"}),value:(()=>{const e=i.map((e=>y(e.TRS||0))).filter((e=>!isNaN(e)));return e.length?b(e.reduce(((e,t)=>e+t),0)/e.length):"N/A"})(),suffix:"%",valueStyle:{fontSize:18}})}),s.jsx(E,{span:8,children:s.jsx(I,{title:s.jsx(X,{style:{fontSize:14},children:"Bons produits"}),value:N(i.reduce(((e,t)=>e+y(t.Quantite_Bon||0)),0)),valueStyle:{color:"#52c41a",fontSize:18}})}),s.jsx(E,{span:8,children:s.jsx(I,{title:s.jsx(X,{style:{fontSize:14},children:"Produits rejetés"}),value:N(i.reduce(((e,t)=>e+y(t.Quantite_Rejet||0)),0)),valueStyle:{color:"#ff4d4f",fontSize:18}})})]})]}):s.jsx(S,{description:"Aucune donnée de session disponible",image:S.PRESENTED_IMAGE_SIMPLE})})}),s.jsx(E,{xs:24,children:s.jsx(k,{title:s.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx(C,{style:{color:"#1890ff",marginRight:8}}),s.jsx("span",{children:"Métriques de performance"})]}),bordered:!0,children:i.length>0?s.jsxs(w,{gutter:[24,24],children:[s.jsx(E,{xs:24,md:8,children:s.jsx(k,{style:{background:"rgba(0,0,0,0.02)"},children:s.jsx(I,{title:"Durée moyenne des sessions",value:(()=>{const e=i.map((e=>{const t=new Date(e.session_start);return(e.session_end?new Date(e.session_end):new Date)-t})),t=e.reduce(((e,t)=>e+t),0)/e.length;return`${Math.floor(t/36e5)}h ${Math.floor(t%36e5/6e4)}m`})(),prefix:s.jsx(D,{})})})}),s.jsx(E,{xs:24,md:8,children:s.jsx(k,{style:{background:"rgba(0,0,0,0.02)"},children:s.jsx(I,{title:"Taux de rejet moyen",value:(()=>{const e=i.reduce(((e,t)=>e+y(t.Quantite_Bon||0)),0),t=i.reduce(((e,t)=>e+y(t.Quantite_Rejet||0)),0);return e+t>0?b(t/(e+t)*100):"0,0"})(),suffix:"%",prefix:s.jsx(h,{}),valueStyle:{color:"#ff4d4f"}})})}),s.jsx(E,{xs:24,md:8,children:s.jsx(k,{style:{background:"rgba(0,0,0,0.02)"},children:s.jsx(I,{title:"Production totale",value:N(i.reduce(((e,t)=>e+y(t.Quantite_Bon||0)),0)),prefix:s.jsx(j,{}),valueStyle:{color:"#52c41a"}})})})]}):s.jsx(S,{description:"Aucune donnée de performance disponible",image:S.PRESENTED_IMAGE_SIMPLE})})})]})})},"3")]})})()})},{Title:te,Text:se}=N,{TabPane:ne}=R,ie={primary:n.PRIMARY_BLUE,success:n.SUCCESS,warning:n.WARNING,error:n.ERROR,gray:n.LIGHT_GRAY},ae=()=>{const{darkMode:n}=i(),[l,d]=a.useState(!1),[y,b]=a.useState("machines"),{machineData:v,selectedMachine:S,machineHistory:C,loading:D,error:N,lastUpdate:I,wsStatus:M,historyLoading:G,historyError:$,setSelectedMachine:V,handleRefresh:Z,fetchMachineHistory:X}=(()=>{const{isAuthenticated:s}=e(),[n,i]=a.useState([]),[l,c]=a.useState([]),[d,x]=a.useState({}),[p,m]=a.useState([]),[j,y]=a.useState(null),[b,f]=a.useState([]),[v,S]=a.useState(!0),[R,_]=a.useState(null),[w,E]=a.useState(new Date),[C,D]=a.useState(!1),[k,T]=a.useState(null),[N,A]=a.useState(!1),[I,M]=a.useState({connected:!1,connecting:!1,updating:!1,reconnecting:!1}),[F,B]=a.useState({}),[L,z]=a.useState([]),[P,Q]=a.useState(null),[U,W]=a.useState(new Date),[H,O]=a.useState("all"),[q,G]=a.useState("machines"),$=a.useCallback((e=>e.map((e=>{const t=Y(e.TRS||"0"),s=t>80?"success":t>60?"warning":"error",n=Y(e.Quantite_Bon||"0")/(Y(e.Quantite_Planifier||"0")||1)*100;return{...e,status:s,progress:n}}))),[]),K=a.useCallback((async(e,s)=>{try{const n=(await t.get("/api/activeSessions").retry(2)).body,i={};n.forEach((e=>{i[e.machine_id]=e}));const a={};s.forEach((e=>{e.id&&(a[e.id]=e)}));for(const s of e){if(!s.id)continue;const e={...s,Regleur_Prenom:s.Regleur_Prenom||"0",Quantite_Planifier:s.Quantite_Planifier||"0",Quantite_Bon:s.Quantite_Bon||"0",Quantite_Rejet:s.Quantite_Rejet||"0",TRS:s.TRS||"0",Poid_unitaire:s.Poid_unitaire||"0",cycle_theorique:s.cycle_theorique||"0",empreint:s.empreint||"0",Etat:s.Etat||"off",Code_arret:s.Code_arret||""},n=!!i[s.id];"on"!==s.Etat||n?"on"===s.Etat&&n?(await t.post("/api/updateSession").send({machineId:s.id,machineData:e}).retry(2),B((e=>({...e,[s.id]:{...e[s.id],lastUpdate:new Date}})))):"off"===s.Etat&&n&&(await t.post("/api/stopSession").send({machineId:s.id}).retry(2),B((e=>{const t={...e};return delete t[s.id],t}))):(await t.post("/api/createSession").send({machineId:s.id,machineData:e}).retry(2),B((e=>({...e,[s.id]:{active:!0,startTime:new Date,lastUpdate:new Date}}))))}}catch(n){}}),[]),V=a.useCallback((async()=>{try{S(!0);const e=await Promise.all([t.get("/api/RealTimeTable").retry(2),t.get("/api/MachineCard").retry(2),t.get("/api/sidecards").retry(2),t.get("/api/dailyStats").retry(2)]),[s,a,r,o]=e;c([...n]);const l=$(a.body);i(l),x(r.body[0]||{}),m(o.body),_(null),S(!1),E(new Date),await K(l,n)}catch(e){_(e.message||"Failed to fetch data"),S(!1),E(new Date)}}),[n,$,K]),Z=a.useCallback((async e=>{if(e)try{D(!0),T(null);const s=await t.get(`/api/machineSessions/${e}`).retry(2);if(Array.isArray(s.body)&&s.body.length>0){const e=s.body.map((e=>({...e,timestamp:new Date(e.session_start),isActive:!e.session_end,highlight:!e.session_end})));f(e)}else f([]),T("No sessions found for this machine");D(!1)}catch(s){T("No history data available for this machine"),D(!1),f([])}}),[]),X=a.useCallback((()=>{I.connected?o.requestUpdate():I.connecting||I.reconnecting||(M((e=>({...e,connecting:!0}))),g.info({message:"Reconnecting",description:"Attempting to establish WebSocket connection",icon:r.createElement(u,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:2,key:"websocket-reconnecting"}),o.connect())}),[I.connected,I.connecting,I.reconnecting]),J=a.useCallback((async()=>{try{const e=await t.get("/api/operator-stats").retry(2);z(e.body)}catch(e){}}),[]),ee=a.useCallback((async()=>{try{const e=await t.get("/api/production-stats").retry(2);Q(e.body)}catch(e){}}),[]);return a.useEffect((()=>{o.isConnected?(M((e=>({...e,connected:!0,connecting:!1}))),o.requestUpdate()):(M((e=>({...e,connecting:!0}))),o.connect());const e=o.addEventListener("initialData",(e=>{M((e=>({...e,connecting:!1,updating:!1})));const t=$(e.machineData),s={};e.activeSessions.forEach((e=>{s[e.machine_id]={active:!0,startTime:new Date(e.session_start),lastUpdate:new Date(e.last_updated),sessionId:e.id}})),B(s),i(t),c([...t]),x(e.sideCardData||{}),m(e.dailyStats||[]),_(null),S(!1),E(new Date)})),t=o.addEventListener("update",(e=>{M((e=>({...e,updating:!0}))),setTimeout((()=>{M((e=>({...e,updating:!1})))}),500),c([...n]),e.data.changedMachines;const t=e.data.fullData||[],s=$(t);i(s),E(new Date),K(s,n)})),s=o.addEventListener("sessionUpdate",(e=>{const{sessionData:t,updateType:s}=e,n=t.machine_id;"created"===s||"updated"===s?B((e=>({...e,[n]:{active:!0,startTime:new Date(t.session_start),lastUpdate:new Date(t.last_updated),sessionId:t.id}}))):"stopped"===s&&B((e=>{const t={...e};return delete t[n],t}))})),a=o.addEventListener("connect",(()=>{M((e=>({...e,connected:!0,connecting:!1}))),o.requestUpdate()})),l=o.addEventListener("disconnect",(()=>{M((e=>({...e,connected:!1,connecting:!1}))),"visible"===document.visibilityState&&g.warning({message:"Connexion perdue",description:"La connexion WebSocket a été interrompue",icon:r.createElement(u,{style:{color:"#faad14"}}),placement:"bottomRight",duration:4,key:"websocket-disconnected"})})),d=o.addEventListener("error",(e=>{M((e=>({...e,connected:!1,connecting:!1}))),g.error({message:"Erreur de connexion",description:"Impossible de se connecter au service de données en temps réel. Utilisation du mode de secours.",icon:r.createElement(h,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4,key:"websocket-error"}),_("Erreur de connexion WebSocket")})),p=setTimeout((()=>{o.isConnected||M((e=>({...e,connecting:!1})))}),1e4);return()=>{clearTimeout(p),e(),t(),s(),a(),l(),d(),window.addEventListener("beforeunload",(()=>{o.disconnect()}),{once:!0})}}),[$,K,V,n]),a.useEffect((()=>{J(),ee()}),[J,ee]),{machineData:n,previousMachineData:l,sideCardData:d,dailyStats:p,selectedMachine:j,machineHistory:b,operatorStats:L,productionStats:P,sessionStatus:F,loading:v,error:R,lastUpdate:w,historyLoading:C,historyError:k,wsStatus:I,isHistoricalView:N,selectedDate:U,selectedShift:H,selectedView:q,setSelectedMachine:y,fetchMachineHistory:Z,handleRefresh:X,setSelectedDate:W,setSelectedShift:O,setSelectedView:G,formatMachineData:$}})(),J=()=>I.toLocaleTimeString(),ae=e=>{e.id?(V(e),X(e.id),d(!0)):q.info("Cette machine n'est pas encore configurée")},re=(e,t)=>t&&1!==t.id?ie.gray:t&&"off"===t.Etat?ie.error:t&&"on"===t.Etat||"success"===e?ie.success:"warning"===e?ie.warning:ie.error,oe=[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",sorter:(e,t)=>e.Machine_Name.localeCompare(t.Machine_Name)},{title:"Statut",dataIndex:"Etat",key:"Etat",render:e=>s.jsx(m,{color:"on"===e?"success":"error",children:"on"===e?"En ligne":"Hors ligne"}),filters:[{text:"En ligne",value:"on"},{text:"Hors ligne",value:"off"}],onFilter:(e,t)=>t.Etat===e},{title:"TRS",dataIndex:"TRS",key:"TRS",sorter:(e,t)=>parseFloat(e.TRS||0)-parseFloat(t.TRS||0),render:e=>{const t=parseFloat(e||0);let n="red";return t>80?n="green":t>60&&(n="orange"),s.jsxs("span",{style:{color:n},children:[t.toFixed(1),"%"]})}},{title:"Production",dataIndex:"Quantite_Bon",key:"Quantite_Bon",sorter:(e,t)=>parseFloat(e.Quantite_Bon||0)-parseFloat(t.Quantite_Bon||0)},{title:"Rejets",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",sorter:(e,t)=>parseFloat(e.Quantite_Rejet||0)-parseFloat(t.Quantite_Rejet||0)},{title:"Opérateur",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:e=>e||"Non assigné"},{title:"Actions",key:"actions",render:(e,t)=>s.jsx(f,{type:"primary",size:"small",disabled:"off"===t.Etat,onClick:e=>{e.stopPropagation(),"off"!==t.Etat&&ae(t)},title:"off"===t.Etat?"Machine hors ligne. Détails non disponibles.":"Voir les détails de la machine",children:"Détails"})}];return s.jsxs("div",{style:{padding:"24px"},children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24},children:[s.jsxs("div",{children:[s.jsxs(te,{level:2,children:[s.jsx(T,{})," Tableau de Bord"]}),s.jsx(se,{type:"secondary",children:(new Date).toLocaleDateString()})]}),s.jsxs(x,{children:[(e=>{let t="Connecté",n=ie.success,i=s.jsx(j,{});return e.fallbackMode?(t="Mode de secours",n=ie.warning,i=s.jsx(F,{})):e.reconnecting?(t="Reconnexion...",n=ie.warning,i=s.jsx(u,{spin:!0})):e.connecting?(t="Connexion...",n=ie.primary,i=s.jsx(u,{spin:!0})):e.connected||(t="Déconnecté",n=ie.error,i=s.jsx(h,{})),s.jsx("div",{style:{display:"flex",alignItems:"center",marginRight:"16px"},children:s.jsx(Q,{status:e.connected?"success":e.fallbackMode?"warning":e.reconnecting?"processing":"error",text:s.jsx(p,{title:e.fallbackMode?"Utilisation des mises à jour périodiques au lieu de la connexion en temps réel":"",children:s.jsxs("span",{style:{color:n,display:"flex",alignItems:"center"},children:[s.jsx("span",{style:{marginRight:"4px"},children:i}),t]})})})})})(M),s.jsx(f,{type:"primary",icon:s.jsx(u,{}),onClick:Z,children:"Actualiser"})]})]}),N&&s.jsx(B,{type:"error",message:"Erreur de connexion",description:`Dernière erreur: ${N} | Dernière mise à jour: ${J()}`,showIcon:!0,closable:!0,style:{marginBottom:16}}),s.jsx(A,{}),s.jsxs("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s.jsx(L.Group,{value:y,onChange:e=>{b(e.target.value)},buttonStyle:"solid",children:s.jsxs(L.Button,{value:"machines",children:[s.jsx(T,{})," Machines"]})}),s.jsxs(x,{children:[s.jsx(p,{title:"Filtrer les données",children:s.jsx(f,{icon:s.jsx(z,{}),children:"Filtres"})}),s.jsx(p,{title:"Exporter les données",children:s.jsx(f,{icon:s.jsx(P,{}),children:"Exporter"})})]})]}),s.jsx(w,{gutter:[16,16],style:{marginBottom:"16px"},children:s.jsx(E,{span:24,children:s.jsx(k,{children:s.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:s.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[s.jsxs(se,{strong:!0,style:{marginRight:"10px"},children:["Dernière mise à jour: ",J()]}),s.jsx(K,{wsStatus:M,onRefresh:Z})]})})})})}),"machines"===y&&s.jsx(w,{gutter:[18,18],children:s.jsx(E,{xs:24,lg:24,children:s.jsx(k,{title:s.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx(T,{style:{fontSize:20,marginRight:8}}),s.jsx("span",{children:"Statistiques des machines"})]}),extra:s.jsx(Q,{count:v.length,style:{backgroundColor:"#1890ff"}}),children:!D||M.connected||v.length?N&&!v.length?s.jsx(B,{message:"Erreur de connexion",description:s.jsxs(s.Fragment,{children:[N,M.fallbackMode&&s.jsxs("div",{style:{marginTop:"10px"},children:[s.jsx("strong",{children:"Mode de secours activé:"})," Les données seront actualisées périodiquement au lieu des mises à jour en temps réel."]}),!M.fallbackMode&&s.jsxs("div",{style:{marginTop:"10px"},children:[s.jsx("strong",{children:"Dépannage:"}),s.jsxs("ul",{children:[s.jsx("li",{children:"Vérifiez votre connexion réseau"}),s.jsx("li",{children:"Le serveur peut être temporairement indisponible"}),s.jsx("li",{children:"Essayez de rafraîchir la page"})]})]})]}),type:"error",showIcon:!0,action:s.jsx(f,{type:"primary",onClick:Z,children:"Réessayer"})}):v&&0!==v.length?s.jsx(s.Fragment,{children:s.jsx(w,{gutter:[16,16],children:v.map((e=>s.jsx(E,{xs:24,sm:24,md:12,children:s.jsxs("div",{className:"machine-card-container",style:{position:"relative"},children:[s.jsx(c,{machine:e,handleMachineClick:ae,getStatusColor:re}),1!==e.id&&s.jsxs("div",{className:"soon-overlay",children:[s.jsx("div",{className:"soon-text",children:"En développement..."}),s.jsx(f,{type:"primary",ghost:!0,size:"small",icon:s.jsx(W,{}),children:"Configuration requise"})]}),!e.id&&s.jsxs("div",{className:"soon-overlay",children:[s.jsx("div",{className:"soon-text",children:"En développement..."}),s.jsx(f,{type:"default",size:"small",children:"Configuration requise"})]})]})},e.id||e.Machine_Name)))})}):s.jsx(B,{message:"Aucune donnée",description:"Aucune donnée de machine disponible",type:"info",showIcon:!0}):s.jsxs("div",{style:{textAlign:"center",padding:"40px 0"},children:[s.jsx(O,{size:"large"}),s.jsx("div",{style:{marginTop:16},children:"Chargement des données..."})]})})})}),s.jsx(A,{}),s.jsx(k,{title:s.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx(W,{style:{fontSize:20,marginRight:8}}),s.jsx("span",{children:"Détails des machines"})]}),extra:s.jsxs(x,{children:[s.jsx(f,{type:"primary",icon:s.jsx(u,{}),onClick:Z,size:"small",children:"Actualiser"}),s.jsxs(m,{color:"processing",children:[v.filter((e=>"on"===e.Etat)).length," sessions actives"]})]}),children:s.jsxs(R,{defaultActiveKey:"1",className:n?"dark-mode":"",children:[s.jsx(ne,{tab:"Tableau",children:s.jsx(_,{columns:oe,dataSource:v.map(((e,t)=>({...e,key:t}))),pagination:{pageSize:10},scroll:{x:!0},onRow:e=>({onClick:()=>"off"!==e.Etat&&ae(e),style:{cursor:"off"===e.Etat?"not-allowed":"pointer",opacity:"off"===e.Etat?.7:1}})})},"1"),s.jsx(ne,{tab:"Cartes",children:s.jsx(w,{gutter:[16,16],children:v.map(((e,t)=>s.jsx(E,{xs:24,sm:12,md:8,lg:6,children:s.jsxs("div",{style:{position:"relative"},children:[s.jsx(k,{hoverable:!!e.id&&"off"!==e.Etat,onClick:()=>e.id&&"off"!==e.Etat&&ae(e),style:{borderTop:`2px solid ${re(e.status,e)}`,opacity:"off"===e.Etat?.7:1,cursor:"off"===e.Etat?"not-allowed":"pointer"},title:"off"===e.Etat?"Machine hors ligne. Détails non disponibles.":"",children:s.jsxs("div",{style:{textAlign:"center"},children:[s.jsx(te,{level:4,children:e.Machine_Name||"Machine"}),s.jsx(U,{type:"dashboard",percent:parseFloat(e.TRS||"0"),status:parseFloat(e.TRS)>80?"success":parseFloat(e.TRS)>60?"normal":"exception"}),"on"===e.Etat&&s.jsx(Q,{status:"processing",text:"Session active",style:{marginTop:8}}),s.jsx("div",{style:{marginTop:8},children:s.jsxs(se,{children:["Production: ",parseFloat(e.Quantite_Bon||0)]})})]})}),1!==e.id&&s.jsxs("div",{className:"soon-overlay",children:[s.jsx("div",{className:"soon-text",children:"En développement..."}),s.jsx(f,{type:"default",size:"small",children:"Configuration requise"})]}),!e.id&&s.jsxs("div",{className:"soon-overlay",children:[s.jsx("div",{className:"soon-text",children:"En développement..."}),s.jsx(f,{type:"default",size:"small",children:"Configuration requise"})]})]})},t)))})},"2")]})}),s.jsx("div",{style:{position:"fixed",bottom:20,right:20,zIndex:1e3},children:s.jsx(H,{content:s.jsxs("div",{style:{width:250},children:[s.jsx("p",{children:s.jsx("strong",{children:"Available tools:"})}),s.jsxs("ul",{children:[s.jsx("li",{children:"Machine view"}),s.jsx("li",{children:"Detailed performance analysis"}),s.jsx("li",{children:"Data export"})]}),s.jsx(f,{type:"primary",block:!0,children:"User Guide"})]}),title:"Help and tools",trigger:"click",placement:"topRight",children:s.jsx(f,{type:"primary",shape:"circle",icon:s.jsx(F,{}),size:"large",style:{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"}})})}),s.jsx(ee,{visible:l,machine:S,machineHistory:C,loading:G,error:$,onClose:()=>d(!1),onRefresh:()=>X(null==S?void 0:S.id),darkMode:n})]})};"undefined"!=typeof window&&d();const re=()=>{e(),a.useEffect((()=>{o.connect();const e=()=>{"visible"===document.visibilityState&&(o.isConnected||o.ensureConnection())};return document.addEventListener("visibilitychange",e),()=>{document.removeEventListener("visibilitychange",e),o.disconnect()}}),[]);return s.jsxs("div",{className:"optimized-dashboard-wrapper",children:[s.jsx("div",{style:{position:"absolute",top:"10px",right:"10px",zIndex:2e3},children:s.jsx(f,{type:"primary",onClick:async()=>{var e,s,n;try{q.loading("Creating test notification...",1);const e=await t.post("/api/notifications/test-sse").send({}).withCredentials().timeout(1e4).retry(2);if(e.body.success){const{notification:t,broadcast_result:s,database_saved:n,database_error:i}=e.body;n?q.success(`✅ Test notification created and saved! ${t.title}`,4):q.warning(`⚠️ Notification broadcast but not saved to database: ${i}. Will disappear on reload.`,6)}else q.warning("Test notification created but may not have been broadcast properly",3)}catch(i){"ECONNABORTED"===i.code?q.error("Test notification timed out - server may be busy",3):401===(null==(e=i.response)?void 0:e.status)?q.error("Authentication required - please login again",3):q.error(`Failed to create test notification: ${(null==(n=null==(s=i.response)?void 0:s.data)?void 0:n.message)||i.message}`,3)}},style:{backgroundColor:"#52c41a",borderColor:"#52c41a",fontWeight:"bold"},children:"🧪 Test SSE Notification"})}),s.jsx(ae,{})]})};export{re as default};
