/**
 * Test the simple comprehensive GraphQL query
 */

import fetch from 'node-fetch';

async function testSimpleComprehensiveQuery() {
  console.log('🧪 Testing SIMPLE comprehensive stop data query...');
  
  const query = `
    query GetSimpleComprehensiveStopData($filters: SimpleStopFilterInput) {
      getSimpleComprehensiveStopData(filters: $filters) {
        totalRecords
        queryExecutionTime
        
        allStops {
          Machine_Name
          Date_Insert
          Code_Stop
          Debut_Stop
          Fin_Stop_Time
          Regleur_Prenom
          duration_minutes
        }
      }
    }
  `;

  const variables = {
    filters: {
      limit: 10
    }
  };

  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, variables })
    });

    if (!response.ok) {
      console.error(`❌ HTTP error! status: ${response.status}`);
      return;
    }

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return;
    }

    const data = result.data.getSimpleComprehensiveStopData;
    console.log('✅ Success! Simple comprehensive data retrieved:');
    console.log(`   📊 Query Time: ${data.queryExecutionTime}ms`);
    console.log(`   📈 Total Records: ${data.totalRecords}`);
    console.log(`   📋 Stops Retrieved: ${data.allStops.length} records`);
    
    // Show sample data
    console.log('\n📋 Sample Stop Data:');
    data.allStops.slice(0, 3).forEach((stop, i) => {
      console.log(`   ${i+1}. Machine: ${stop.Machine_Name}`);
      console.log(`      Code: ${stop.Code_Stop}`);
      console.log(`      Duration: ${stop.duration_minutes} minutes`);
      console.log(`      Operator: ${stop.Regleur_Prenom || 'N/A'}`);
      console.log(`      Date: ${stop.Date_Insert}`);
      console.log('');
    });

    return data.allStops.length > 0;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Test with different filters
async function testWithFilters() {
  console.log('\n🔍 Testing with IPS machine filter...');
  
  const query = `
    query GetSimpleComprehensiveStopData($filters: SimpleStopFilterInput) {
      getSimpleComprehensiveStopData(filters: $filters) {
        totalRecords
        queryExecutionTime
        allStops {
          Machine_Name
          Code_Stop
          duration_minutes
        }
      }
    }
  `;

  const variables = {
    filters: {
      model: 'IPS',
      limit: 5
    }
  };

  try {
    const response = await fetch('http://localhost:5000/api/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, variables })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL Errors:', result.errors);
      return false;
    }

    const data = result.data.getSimpleComprehensiveStopData;
    console.log(`✅ IPS Filter Success! ${data.totalRecords} records in ${data.queryExecutionTime}ms`);
    
    // Verify all machines start with IPS
    const allIPS = data.allStops.every(stop => stop.Machine_Name.startsWith('IPS'));
    console.log(`   🔍 All machines are IPS: ${allIPS ? '✅' : '❌'}`);
    
    return allIPS;

  } catch (error) {
    console.error('❌ Filter test failed:', error.message);
    return false;
  }
}

async function runSimpleTests() {
  console.log('🚀 Starting Simple Optimized GraphQL Tests');
  console.log('='.repeat(50));

  const basicTest = await testSimpleComprehensiveQuery();
  const filterTest = await testWithFilters();

  console.log('\n📊 Test Results:');
  console.log(`   Basic Query: ${basicTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Filter Query: ${filterTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (basicTest && filterTest) {
    console.log('\n🎉 All tests passed! The simple optimized resolver is working correctly.');
    console.log('💡 Next step: Enhance with aggregations and multiple data sets.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.');
  }
}

runSimpleTests();
