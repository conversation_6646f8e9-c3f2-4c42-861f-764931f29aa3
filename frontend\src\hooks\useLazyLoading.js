/**
 * Custom hook for lazy loading components
 * Prevents heavy components from rendering simultaneously
 */

import { useState, useEffect, useRef } from 'react';

const useLazyLoading = (priority = 0, delay = 0) => {
  const [shouldRender, setShouldRender] = useState(priority === 0);
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef(null);
  const timeoutRef = useRef(null);

  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // For high priority components (0), render immediately
    if (priority === 0) {
      setShouldRender(true);
      return;
    }

    // For lower priority components, add progressive delay
    const calculatedDelay = delay + (priority * 200); // 200ms per priority level
    
    timeoutRef.current = setTimeout(() => {
      setShouldRender(true);
    }, calculatedDelay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [priority, delay]);

  // Intersection Observer for viewport-based lazy loading
  useEffect(() => {
    if (!shouldRender || !elementRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    observer.observe(elementRef.current);

    return () => observer.disconnect();
  }, [shouldRender]);

  return {
    shouldRender,
    isVisible,
    elementRef
  };
};

export default useLazyLoading;
