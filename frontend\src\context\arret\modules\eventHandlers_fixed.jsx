import { useCallback } from 'react'
import { message } from 'antd'

/**
 * Event Handlers Module
 * Handles all user interactions and filter changes for the Arret dashboard
 * Updated to work with queued data fetching system
 */
export const useEventHandlers = (state, setState, queuedDataManager, skeletonManager) => {

  /**
   * Handle date range type change (day, week, month)
   */
  const handleDateRangeTypeChange = useCallback((value) => {
    console.log('📅 Date range type changed:', value);
    setState(prev => ({ ...prev, dateRangeType: value }));
  }, [setState]);

  /**
   * Handle date selection change
   */
  const handleDateChange = useCallback((date) => {
    console.log('📅 Date changed:', date?.format?.('YYYY-MM-DD') || 'null');
    setState(prev => ({ 
      ...prev, 
      selectedDate: date,
      // Properly set dateFilterActive based on whether date is null or not
      dateFilterActive: date !== null
    }));
  }, [setState]);

  /**
   * Handle machine model selection change
   */
  const handleMachineModelChange = useCallback((value) => {
    console.log('🏭 Machine model changed:', value);
    setState(prev => ({ 
      ...prev, 
      selectedMachineModel: value,
      selectedMachine: "" // Reset machine selection when model changes
    }));
  }, [setState]);

  /**
   * Handle machine selection change
   */
  const handleMachineChange = useCallback((value) => {
    console.log('🔧 Machine changed:', value);
    setState(prev => ({ ...prev, selectedMachine: value }));
  }, [setState]);

  /**
   * Reset all filters to default state
   */
  const resetFilters = useCallback(() => {
    console.log('🔄 Resetting all filters');
    
    setState(prev => ({
      ...prev,
      selectedMachineModel: "IPS",
      selectedMachine: "",
      selectedDate: null,
      dateRangeType: "month",
      dateFilterActive: false,
      dateRangeDescription: "",
      // Clear data arrays
      arretStats: [],
      topStopsData: [],
      arretsByRange: [],
      stopsData: [],
      durationTrend: [],
      // Reset performance metrics
      mttr: 0,
      mtbf: 0,
      doper: 0,
      showPerformanceMetrics: false
    }));

    // Clear skeletons
    skeletonManager?.stopSkeletonLoading?.();
    
    message.success('Filters reset successfully');
  }, [setState, skeletonManager]);

  /**
   * Force refresh data using queued system
   */
  const handleRefresh = useCallback(async () => {
    console.log('🔄 Force refreshing data using queued system');
    
    try {
      // Start refresh skeleton
      skeletonManager?.startSkeletonLoading?.(['dataRefresh']);
      
      await queuedDataManager.fetchDataInQueue(true); // Force refresh with queued system
      
      skeletonManager?.stopSkeletonLoading?.(['dataRefresh']);
      message.success('Data refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing data:', error);
      skeletonManager?.stopSkeletonLoading?.(['dataRefresh']);
      message.error('Failed to refresh data');
    }
  }, [queuedDataManager, skeletonManager]);

  /**
   * Reset date filter specifically
   */
  const resetDateFilter = useCallback(() => {
    console.log('🗑️ Resetting date filter...');
    setState(prev => ({ 
      ...prev, 
      selectedDate: null,
      dateFilterActive: false,
      dateRangeDescription: ""
    }));
  }, [setState]);

  /**
   * Reset machine selection specifically
   */
  const handleResetMachineSelection = useCallback(() => {
    console.log('🗑️ Resetting machine selection...');
    setState(prev => ({ 
      ...prev, 
      selectedMachineModel: "",
      selectedMachine: ""
    }));
  }, [setState]);

  return {
    handleDateRangeTypeChange,
    handleDateChange,
    handleMachineModelChange,
    handleMachineChange,
    resetFilters,
    handleRefresh,
    resetDateFilter,
    handleResetMachineSelection
  };
};
