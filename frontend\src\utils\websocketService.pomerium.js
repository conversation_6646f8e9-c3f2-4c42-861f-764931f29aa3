// WebSocket service for real-time data synchronization with Pomerium
// This is a Pomerium-compatible version of the WebSocket service

class PomeriumWebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectTimeout = null;
    this.pingInterval = null;
    this.connectionTimeout = null;

    // Set default WebSocket URL for Pomerium
    this.defaultWsUrl = "wss://ws.adapted-osprey-5307.pomerium.app";
    this.listeners = {
      initialData: [],
      update: [],
      sessionUpdate: [],
      connect: [],
      disconnect: [],
      error: []
    };

    // Add network status event listeners
    this._setupNetworkListeners();
  }

  // Set up network status event listeners
  _setupNetworkListeners() {
    // Handle browser going offline
    window.addEventListener('offline', () => {
      console.warn('Browser went offline. WebSocket connections may be interrupted.');
      this._notifyListeners('error', { type: 'network', message: 'Network connection lost' });
    });

    // Handle browser coming back online
    window.addEventListener('online', () => {
      console.log('Browser back online. Checking WebSocket connection...');

      // If socket is closed or closing and we were previously connected, try to reconnect
      if (this.socket &&
          (this.socket.readyState === WebSocket.CLOSED ||
           this.socket.readyState === WebSocket.CLOSING)) {
        console.log('Reconnecting WebSocket after network recovery...');
        this.connect();
      }
    });
  }

  // Connect to the WebSocket server through Pomerium
  connect() {
    // Check if already connected or connecting
    if (this.socket) {
      if (this.socket.readyState === WebSocket.OPEN) {
        console.log('WebSocket already connected');
        this._notifyListeners('connect');
        return;
      }

      if (this.socket.readyState === WebSocket.CONNECTING) {
        console.log('WebSocket already connecting');
        return;
      }

      // If socket exists but is closing or closed, clean it up first
      if (this.socket.readyState === WebSocket.CLOSING || this.socket.readyState === WebSocket.CLOSED) {
        console.log('Cleaning up existing WebSocket before reconnecting');
        this.socket = null;
      }
    }

    // Clear any existing reconnect timeout
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    // Clear any existing connection timeout
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }

    // Use Pomerium WebSocket URL
    let wsBaseURL = this.defaultWsUrl;

    // Support environment variable override for different environments
    if (import.meta.env.VITE_WS_URL) {
      wsBaseURL = import.meta.env.VITE_WS_URL;
    }

    // Construct the full WebSocket URL
    const wsUrl = `${wsBaseURL}/api/machine-data-ws`;
    console.log(`Attempting WebSocket connection to ${wsUrl} via Pomerium`);

    try {
      // Create a new WebSocket with timeout handling
      this.socket = new WebSocket(wsUrl);

      // Set a connection timeout
      this.connectionTimeout = setTimeout(() => {
        if (this.socket && this.socket.readyState !== WebSocket.OPEN) {
          console.warn('WebSocket connection timeout - closing socket');
          try {
            console.log(`WebSocket state before timeout close: ${this.getState()}`);
            this.socket.close();
            this.socket = null;
            this.isConnected = false;
          } catch (e) {
            console.error('Error closing timed out socket:', e);
          }
          this._handleConnectionFailure('Connection timeout');

          this._notifyListeners('error', {
            type: 'timeout',
            message: 'WebSocket connection timed out after 15 seconds'
          });
        }
        this.connectionTimeout = null;
      }, 15000); // 15 second timeout

      this.socket.onopen = () => {
        // Clear the connection timeout
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        console.log('WebSocket connection established successfully via Pomerium');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this._notifyListeners('connect');

        // Send a ping every 30 seconds to keep the connection alive
        this.pingInterval = setInterval(() => {
          if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.send({ type: 'ping' });
          }
        }, 30000);
      };

      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          // Don't log ping/pong messages to avoid console clutter
          if (data.type !== 'ping' && data.type !== 'pong') {
            console.log('WebSocket message received via Pomerium:', data.type);
          }

          // Handle pong responses internally
          if (data.type === 'pong') {
            return;
          }

          // Notify appropriate listeners based on message type
          if (data.type && this.listeners[data.type]) {
            this._notifyListeners(data.type, data);
          }
        } catch (error) {
          console.error('Error processing WebSocket message:', error, event.data);
        }
      };

      this.socket.onclose = (event) => {
        // Clear the connection timeout
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        this._clearPingInterval();
        this.isConnected = false;

        const reason = event.reason ? ` - ${event.reason}` : '';
        console.log(`WebSocket connection closed via Pomerium: Code ${event.code}${reason}`);

        // Notify listeners about the disconnect
        this._notifyListeners('disconnect', event);

        // Only attempt to reconnect if it's a network error (not a clean close)
        // and the page is visible (user is actively using the app)
        if (!event.wasClean &&
            document.visibilityState === 'visible' &&
            event.code !== 1000 && // Not a normal closure
            event.code !== 1001) { // Not a going away (page unload)
          console.log('Unexpected connection close. Will attempt to reconnect if needed.');
        }
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error occurred via Pomerium:', error);

        // Clear the connection timeout if it exists
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        this.isConnected = false;
        this._notifyListeners('error', error);

        console.log('WebSocket error - application will handle reconnection if needed');
      };

    } catch (error) {
      console.error('Error creating WebSocket connection via Pomerium:', error);
      this._notifyListeners('error', error);
      this._handleConnectionFailure('Failed to create WebSocket');
    }
  }

  // Helper method to handle connection failures
  _handleConnectionFailure(reason) {
    this.isConnected = false;
    console.log(`Connection failed: ${reason}. Application will handle reconnection if needed.`);
  }

  // Clear the ping interval
  _clearPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  // Disconnect from the WebSocket server
  disconnect() {
    this._clearPingInterval();

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.socket) {
      try {
        if (this.socket.readyState !== WebSocket.CLOSED && this.socket.readyState !== WebSocket.CLOSING) {
          this.socket.close(1000, "Disconnected by user");
        }
      } catch (error) {
        console.error('Error closing WebSocket:', error);
      } finally {
        this.socket = null;
      }
    }

    this.isConnected = false;
    console.log('WebSocket disconnected from Pomerium');
  }

  // Send a message to the WebSocket server
  send(message) {
    if (!this.socket) {
      console.warn('Cannot send message, WebSocket instance does not exist');
      return false;
    }

    switch (this.socket.readyState) {
      case WebSocket.CONNECTING:
        console.warn('Cannot send message, WebSocket is still connecting');
        return false;

      case WebSocket.OPEN:
        try {
          const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
          this.socket.send(messageStr);
          return true;
        } catch (error) {
          console.error('Error sending WebSocket message:', error);
          return false;
        }

      case WebSocket.CLOSING:
        console.warn('Cannot send message, WebSocket is closing');
        return false;

      case WebSocket.CLOSED:
        console.warn('Cannot send message, WebSocket is closed');
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          console.log('Attempting to reconnect...');
          this.connect();
        }
        return false;

      default:
        console.error('Unknown WebSocket state:', this.socket.readyState);
        return false;
    }
  }

  // Request a data update from the server
  requestUpdate() {
    this.ensureConnection();
    return this.send({ type: 'requestUpdate' });
  }

  // Check connection status and reconnect if needed
  ensureConnection() {
    if (navigator.onLine === false) {
      console.warn('Cannot ensure connection - browser reports offline status');
      return false;
    }

    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return true;
    }

    if (this.socket && this.socket.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket is currently connecting...');
      return false;
    }

    if (!this.socket ||
        this.socket.readyState === WebSocket.CLOSED ||
        this.socket.readyState === WebSocket.CLOSING) {
      console.log('WebSocket not connected, attempting to connect via Pomerium...');
      this.connect();
      return false;
    }

    return true;
  }

  // Get current connection state
  getState() {
    if (!this.socket) {
      return 'DISCONNECTED';
    }

    switch (this.socket.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'CONNECTED';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'DISCONNECTED';
      default: return 'UNKNOWN';
    }
  }

  // Add an event listener
  addEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    } else {
      console.warn(`Unknown event type: ${event}`);
    }
    return () => this.removeEventListener(event, callback);
  }

  // Remove an event listener
  removeEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }

  // Private method to notify all listeners of an event
  _notifyListeners(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }

  // Set a new default WebSocket URL
  setDefaultUrl(url) {
    if (!url) return;

    if (!url.startsWith('ws:') && !url.startsWith('wss:')) {
      url = `wss://${url}`;
    }

    this.defaultWsUrl = url;
    console.log(`WebSocket default URL set to: ${url}`);

    return this.defaultWsUrl;
  }
}

// Create and export a singleton instance
const pomeriumWebSocketService = new PomeriumWebSocketService();
export default pomeriumWebSocketService;
