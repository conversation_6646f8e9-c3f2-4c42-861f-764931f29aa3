import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Form, Input, Button, Card, message, Typography, Result, Spin } from "antd";
import { LockOutlined, CheckCircleOutlined } from "@ant-design/icons";
import { useAuth } from "../hooks/useAuth";
import { useTheme } from "../theme-context";
import "./login.css";

const { Title, Text } = Typography;

const ResetPassword = () => {
  const [form] = Form.useForm();
  const { token } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(true);
  const [tokenValid, setTokenValid] = useState(null);
  const [resetComplete, setResetComplete] = useState(false);
  const { resetPassword, verifyResetToken } = useAuth();
  const { darkMode } = useTheme();

  useEffect(() => {
    // Verify token validity
    const verifyToken = async () => {
      setVerifying(true);
      try {
        const result = await verifyResetToken(token);
        setTokenValid(result.success);
      } catch (error) {
        console.error("Error verifying token:", error);
        setTokenValid(false);
      } finally {
        setVerifying(false);
      }
    };

    if (token) {
      verifyToken();
    }
  }, [token, verifyResetToken]);

  const onFinish = async (values) => {
    setLoading(true);
    try {
      
      const result = await resetPassword(token, values.password);
      if (result.success) {
        setResetComplete(true);
      }
    } finally {
      setLoading(false);
    }
  };

  // Styles adaptés au mode sombre
  const darkModeStyles = {
    container: {
      background: darkMode
        ? "linear-gradient(135deg, #1f1f1f 0%, #141414 100%)"
        : "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
    },
    card: {
      backgroundColor: darkMode ? "#1f1f1f" : "#ffffff",
      boxShadow: darkMode ? "0 12px 40px rgba(0, 0, 0, 0.5)" : "0 12px 40px rgba(0, 0, 0, 0.15)",
    },
    title: {
      color: darkMode ? "rgba(255, 255, 255, 0.85)" : "#2c3e50",
    },
    input: {
      backgroundColor: darkMode ? "#141414" : "#ffffff",
      borderColor: darkMode ? "#434343" : "#e8e8e8",
      color: darkMode ? "rgba(255, 255, 255, 0.85)" : "rgba(0, 0, 0, 0.85)",
    },
  };

  // If still verifying token
  if (verifying) {
    return (
      <div className={`login-container ${darkMode ? "dark" : "light"}`} style={darkModeStyles.container}>
        <div className="centered-wrapper" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
          <Spin size="large" tip="Vérification du lien de réinitialisation..." />
        </div>
      </div>
    );
  }

  // If reset is complete, show success message
  if (resetComplete) {
    return (
      <div className={`login-container ${darkMode ? "dark" : "light"}`} style={darkModeStyles.container}>
        <div className="centered-wrapper">
          <Result
            status="success"
            title="Réinitialisation du mot de passe réussie!"
            subTitle="Vous pouvez maintenant vous connecter avec votre nouveau mot de passe."
            extra={[
              <Button type="primary" key="login" onClick={() => navigate("/login")}>
                Aller à la page de connexion
              </Button>
            ]}
          />
        </div>
      </div>
    );
  }

  // If token is invalid, show error message
  if (tokenValid === false) {
    return (
      <div className={`login-container ${darkMode ? "dark" : "light"}`} style={darkModeStyles.container}>
        <div className="centered-wrapper">
          <Result
            status="error"
            title="Lien invalide ou expiré"
            subTitle="Le lien de réinitialisation du mot de passe est invalide ou a expiré."
            extra={[
              <Button type="primary" key="login" onClick={() => navigate("/login")}>
                Retour à la page de connexion
              </Button>
            ]}
          />
        </div>
      </div>
    );
  }

  // Show reset password form
  return (
    <div className={`login-container ${darkMode ? "dark" : "light"}`} style={darkModeStyles.container}>
      <div className="centered-wrapper">
        <Card className="login-card" style={darkModeStyles.card} hoverable>
          <div className="decorative-line" />
          <Title level={3} style={darkModeStyles.title}>
            Réinitialisation du mot de passe
          </Title>
          <Text type="secondary" style={{ display: "block", marginBottom: 24 }}>
            Veuillez entrer votre nouveau mot de passe
          </Text>

          <Form
            form={form}
            name="resetPassword"
            onFinish={onFinish}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="password"
              rules={[
                { required: true, message: "Veuillez entrer votre nouveau mot de passe" },
                { min: 8, message: "Le mot de passe doit contenir au moins 8 caractères" }
              ]}
              hasFeedback
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Nouveau mot de passe"
                style={darkModeStyles.input}
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              dependencies={["password"]}
              hasFeedback
              rules={[
                { required: true, message: "Veuillez confirmer votre mot de passe" },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue("password") === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error("Les deux mots de passe ne correspondent pas"));
                  }
                })
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Confirmer le mot de passe"
                style={darkModeStyles.input}
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                block
                loading={loading}
              >
                Réinitialiser le mot de passe
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default ResetPassword;