import React, { memo } from 'react';
import { <PERSON>sponsive<PERSON><PERSON>r, LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ReferenceLine } from 'recharts';
import { Empty, Spin } from 'antd';

const CumulativeImpactChart = memo(({ data = [], loading = false }) => {
  console.log('🔧 CumulativeImpactChart received data:', {
    dataType: typeof data,
    isArray: Array.isArray(data),
    dataLength: data?.length || 0,
    sampleData: data?.slice(0, 3) || []
  });

  if (loading) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  // Ensure data is an array - handle both direct arrays and response objects
  const safeData = Array.isArray(data) ? data : (data?.data || []);
  if (!safeData || safeData.length === 0) {
    return (
      <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Empty description="Aucune donnée d'impact cumulé disponible" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }
  // Process data to focus on cumulative impact
  const sortedData = safeData
    .map(item => ({
      reason: item.reason || item.Code_Stop || item.stopName || 'N/A',
      value: parseFloat(item.value || item.duration || item.count || 0),
    }))
    .sort((a, b) => b.value - a.value);

  // Calculate cumulative percentage
  const totalValue = sortedData.reduce((sum, item) => sum + item.value, 0);
  let cumulativeSum = 0;
  
  const cumulativeData = sortedData.map((item, index) => {
    cumulativeSum += item.value;
    const cumulativePercentage = (cumulativeSum / totalValue) * 100;
    
    return {
      index: index + 1,
      reason: item.reason.length > 12 ? item.reason.substring(0, 10) + '...' : item.reason,
      fullReason: item.reason,
      value: item.value,
      cumulativePercentage: cumulativePercentage,
    };
  }).slice(0, 10); // Show only top 10 for better visualization
  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={cumulativeData} margin={{ top: 5, right: 5, left: 0, bottom: 45 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" strokeWidth={0.5} />
        <XAxis
          dataKey="reason"
          tick={{ fill: "#333", fontSize: 9, fontWeight: '500' }}
          angle={-25}
          textAnchor="end"
          height={50}
          interval={0}
          axisLine={{ strokeWidth: 1 }}
        />
        <YAxis
          tick={{ fill: "#333", fontSize: 10, fontWeight: '500' }}
          width={25}
          domain={[0, 100]}
          tickCount={5}
          tickFormatter={(value) => `${value}%`}
          axisLine={{ strokeWidth: 1 }}
        />
        
        {/* Reference lines for 80-20 rule */}        <ReferenceLine 
          y={80} 
          stroke="#f5222d" 
          strokeDasharray="5 3" 
          strokeWidth={1.5}
          label={{ value: "80%", position: "right", style: { fill: "#f5222d", fontSize: "10px", fontWeight: 'bold' } }}
        />
          <Tooltip
          contentStyle={{
            backgroundColor: "#fff",
            border: "1px solid #722ed1",
            borderRadius: 6,
            boxShadow: "0 4px 12px rgba(114, 46, 209, 0.15)",
            fontSize: "12px",
            fontWeight: '500',
            padding: '8px 10px'
          }}
          formatter={(value, name, props) => {
            return [`${value.toFixed(0)}%`, 'Cumul'];
          }}
          labelFormatter={(reason, payload) => {
            if (payload && payload[0]) {
              return payload[0].payload.fullReason;
            }
            return reason;
          }}
        />          <Line
          type="monotone"
          dataKey="cumulativePercentage"
          stroke="#722ed1"
          strokeWidth={5}
          dot={{ 
            fill: "#722ed1", 
            strokeWidth: 2, 
            r: 6,
            stroke: "#fff"
          }}
          activeDot={{ 
            r: 9, 
            fill: "#fff", 
            stroke: "#722ed1", 
            strokeWidth: 3
          }}
          name="Impact Cumulé"
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

CumulativeImpactChart.displayName = 'CumulativeImpactChart';

export default CumulativeImpactChart;
