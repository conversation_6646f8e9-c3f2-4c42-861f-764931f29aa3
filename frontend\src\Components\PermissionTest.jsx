import React, { useState, useEffect } from 'react';
import { Card, Typography, Divider, List, Tag, Button, Space, Alert, Tabs, Table, Collapse, Badge, message } from 'antd';
import { usePermission } from '../hooks/usePermission';
import { usePermissionCheck } from '../hooks/usePermissionCheck';
import { routePermissions, actionPermissions } from '../config/permissionConfig';
import PermissionButton from './PermissionButton';
import PermissionGuard from './PermissionGuard';
import { useAuth } from '../hooks/useAuth';
import superagent from 'superagent';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

/**
 * Component for testing permission functionality
 */
const PermissionTest = () => {
  const { user } = useAuth();
  const { hasPermission, hasRole, hasDepartmentAccess } = usePermission();
  const { canPerformAction } = usePermissionCheck();
  const [roleHierarchy, setRoleHierarchy] = useState(null);
  const [loading, setLoading] = useState(false);

  // Parse permissions from user object
  const parseUserPermissions = () => {
    // Check if permissions are stored as a string (JSON)
    if (user?.permissions && typeof user.permissions === 'string') {
      try {
        return JSON.parse(user.permissions);
      } catch (e) {
        console.error('Error parsing user permissions:', e);
        return [];
      }
    }

    // If it's already an array, return it
    if (Array.isArray(user?.permissions)) {
      return user.permissions;
    }

    return [];
  };

  // Get user permissions and roles
  const directPermissions = parseUserPermissions();
  const rolePermissions = Array.isArray(user?.role_permissions) ? user.role_permissions : [];
  const hierarchyPermissions = Array.isArray(user?.hierarchy_permissions) ? user.hierarchy_permissions : [];
  const allPermissions = Array.isArray(user?.all_permissions) ? user.all_permissions : [];

  // Combine all permissions (use all_permissions if available, otherwise combine manually)
  const userPermissions = allPermissions.length > 0
    ? allPermissions
    : [...new Set([...directPermissions, ...rolePermissions, ...hierarchyPermissions])];

  // Fetch role hierarchy data from backend
  useEffect(() => {
    const fetchRoleHierarchy = async () => {
      setLoading(true);
      try {
        // Try to get the role permissions from the backend
        const response = await superagent.get('/api/role-hierarchy/user-permissions').withCredentials();
        if (response.data && response.data.success) {
          setRoleHierarchy(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching role hierarchy:', error);

        // Create a fallback hierarchy object if the API fails
        // This will use the permissions from the user object directly
        setRoleHierarchy({
          roleName: user?.role || 'Unknown',
          departmentId: user?.department_id || null,
          departmentName: 'Unknown',
          permissions: userPermissions
        });

        // Try to get the role permissions from the role hierarchy directly
        try {
          const roleResponse = await superagent.get('/api/role-hierarchy/hierarchy').withCredentials();
          if (roleResponse.data && roleResponse.data.success) {
            const roleData = roleResponse.data.data;
            if (user?.role && roleData.rolePermissions && roleData.rolePermissions[user.role]) {
              // Update the role hierarchy with the permissions from the role
              setRoleHierarchy(prevState => ({
                ...prevState,
                permissions: [
                  ...userPermissions,
                  ...roleData.rolePermissions[user.role]
                ]
              }));
            }
          }
        } catch (roleError) {
          console.error('Error fetching role hierarchy data:', roleError);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRoleHierarchy();
  }, []);

  // Get route permissions
  const routes = Object.entries(routePermissions).map(([path, config]) => ({
    path,
    ...config,
    hasAccess: (
      (!config.permissions || hasPermission(config.permissions)) &&
      (!config.roles || hasRole(config.roles)) &&
      (!config.departments || hasDepartmentAccess(config.departments))
    )
  }));

  // Get action permissions
  const actions = Object.entries(actionPermissions).map(([key, config]) => ({
    key,
    ...config,
    hasAccess: canPerformAction(key)
  }));

  // Helper function to group permissions by namespace
  const groupPermissionsByNamespace = (permissions) => {
    const grouped = {};

    permissions.forEach(permission => {
      // Split permission into namespace and action
      const parts = permission.split(':');
      const namespace = parts.length > 1 ? parts[0] : 'other';

      // Initialize namespace array if it doesn't exist
      if (!grouped[namespace]) {
        grouped[namespace] = [];
      }

      // Add permission to namespace
      grouped[namespace].push(permission);
    });

    return grouped;
  };

  return (
    <div>
      <Title level={2}>Test des permissions</Title>

      <Alert
        message="Ceci est une page de test pour vérifier le fonctionnement des permissions"
        description="Cette page affiche les permissions de l'utilisateur actuel et les routes et actions auxquelles il a accès."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Card title="Informations utilisateur" style={{ marginBottom: 24 }}>
        <Paragraph>
          <Text strong>Nom d'utilisateur:</Text> {user?.username || 'Non connecté'}
        </Paragraph>
        <Paragraph>
          <Text strong>Rôle:</Text> {user?.role || 'Aucun'}
        </Paragraph>
        <Paragraph>
          <Text strong>Département:</Text> {user?.department_id || 'Aucun'}
        </Paragraph>

        <Divider />

        <div style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            onClick={async () => {
              try {
                const response = await superagent.post('/api/users/set-test-permissions').withCredentials();
                if (response.data && response.data.success) {
                  message.success('Permissions de test appliquées avec succès. Actualisation de la page...');
                  // Reload the page after a short delay
                  setTimeout(() => window.location.reload(), 1500);
                } else {
                  message.error('Erreur lors de l\'application des permissions de test');
                }
              } catch (error) {
                console.error('Error setting test permissions:', error);
                message.error('Erreur lors de l\'application des permissions de test');
              }
            }}
          >
            Appliquer les permissions de test
          </Button>
          <Text type="secondary" style={{ marginLeft: 8 }}>
            (Cela va mettre à jour les permissions de l'utilisateur pour les tests)
          </Text>
        </div>

        <Collapse style={{ marginTop: 16 }}>
          <Panel header="Données brutes de l'utilisateur (pour débogage)" key="1">
            <pre style={{ overflow: 'auto', maxHeight: 300 }}>
              {JSON.stringify(user, null, 2)}
            </pre>
          </Panel>
        </Collapse>
      </Card>

      <Card title="Détail des permissions utilisateur" style={{ marginBottom: 24 }}>
        <Tabs defaultActiveKey="all">
          <TabPane tab="Toutes les permissions" key="all">
            <Alert
              message="Permissions consolidées"
              description="Cette liste montre toutes les permissions de l'utilisateur, incluant celles héritées du rôle et les permissions directes."
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            {userPermissions.length > 0 ? (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>Nombre total de permissions: </Text>
                  <Badge count={userPermissions.length} style={{ backgroundColor: '#1890ff' }} />
                </div>

                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginBottom: 16 }}>
                  {userPermissions.map(permission => (
                    <Tag color="blue" key={permission} style={{ margin: '0 8px 8px 0', fontSize: '14px' }}>
                      {permission}
                    </Tag>
                  ))}
                </div>

                <Collapse>
                  <Panel header="Permissions par namespace" key="namespaces">
                    {Object.entries(groupPermissionsByNamespace(userPermissions)).map(([namespace, perms]) => (
                      <div key={namespace} style={{ marginBottom: 16 }}>
                        <Title level={5}>{namespace}</Title>
                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                          {perms.map(perm => (
                            <Tag color="green" key={perm} style={{ margin: '4px' }}>
                              {perm.replace(`${namespace}:`, '')}
                            </Tag>
                          ))}
                        </div>
                      </div>
                    ))}
                  </Panel>
                </Collapse>
              </div>
            ) : (
              <Text type="secondary">Aucune permission</Text>
            )}
          </TabPane>

          <TabPane tab="Permissions directes" key="direct">
            <Alert
              message="Permissions directes"
              description="Cette liste montre uniquement les permissions assignées directement à l'utilisateur, sans inclure celles héritées du rôle."
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            {directPermissions.length > 0 ? (
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                {directPermissions.map(permission => (
                  <Tag color="purple" key={permission} style={{ margin: '0 8px 8px 0', fontSize: '14px' }}>
                    {permission}
                  </Tag>
                ))}
              </div>
            ) : (
              <Text type="secondary">Aucune permission directe</Text>
            )}

            {user?.permissions && typeof user.permissions === 'string' && (
              <Alert
                message="Format des permissions"
                description={`Les permissions sont stockées sous forme de chaîne JSON: ${user.permissions}`}
                type="warning"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </TabPane>

          <TabPane tab="Permissions du rôle" key="role">
            <Alert
              message="Permissions du rôle"
              description={`Cette liste montre les permissions directement associées au rôle "${user?.role || 'Aucun'}" de l'utilisateur dans la base de données.`}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            {rolePermissions.length > 0 ? (
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                {rolePermissions.map(permission => (
                  <Tag color="orange" key={permission} style={{ margin: '0 8px 8px 0', fontSize: '14px' }}>
                    {permission}
                  </Tag>
                ))}
              </div>
            ) : (
              <div>
                <Text type="secondary">Aucune permission directe de rôle trouvée dans la base de données</Text>
              </div>
            )}

            <Collapse style={{ marginTop: 24 }}>
              <Panel header="Données brutes du rôle (pour débogage)" key="1">
                <div style={{ padding: 16, background: '#f5f5f5', borderRadius: 4 }}>
                  <Text strong>Nom du rôle:</Text>
                  <pre style={{ marginTop: 8, overflow: 'auto', maxHeight: 50 }}>
                    {JSON.stringify(user?.role, null, 2)}
                  </pre>

                  <Text strong>ID du rôle:</Text>
                  <pre style={{ marginTop: 8, overflow: 'auto', maxHeight: 50 }}>
                    {JSON.stringify(user?.role_id, null, 2)}
                  </pre>

                  <Text strong>Permissions du rôle (brutes):</Text>
                  <pre style={{ marginTop: 8, overflow: 'auto', maxHeight: 100 }}>
                    {JSON.stringify(user?.role_permissions, null, 2)}
                  </pre>
                </div>
              </Panel>
            </Collapse>
          </TabPane>

          <TabPane tab="Permissions de la hiérarchie" key="hierarchy">
            <Alert
              message="Permissions de la hiérarchie de rôles"
              description={`Cette liste montre les permissions héritées du rôle "${user?.role || 'Aucun'}" selon la hiérarchie de rôles configurée dans l'application.`}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            {hierarchyPermissions.length > 0 ? (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>Nombre de permissions de la hiérarchie: </Text>
                  <Badge count={hierarchyPermissions.length} style={{ backgroundColor: '#52c41a' }} />
                </div>

                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginBottom: 16 }}>
                  {hierarchyPermissions.map(permission => (
                    <Tag color="green" key={permission} style={{ margin: '0 8px 8px 0', fontSize: '14px' }}>
                      {permission}
                    </Tag>
                  ))}
                </div>

                <Collapse>
                  <Panel header="Permissions par namespace" key="namespaces">
                    {Object.entries(groupPermissionsByNamespace(hierarchyPermissions)).map(([namespace, perms]) => (
                      <div key={namespace} style={{ marginBottom: 16 }}>
                        <Title level={5}>{namespace}</Title>
                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                          {perms.map(perm => (
                            <Tag color="cyan" key={perm} style={{ margin: '4px' }}>
                              {perm.replace(`${namespace}:`, '')}
                            </Tag>
                          ))}
                        </div>
                      </div>
                    ))}
                  </Panel>
                </Collapse>
              </div>
            ) : (
              <div>
                <Text type="secondary">Aucune permission de hiérarchie trouvée</Text>
                <Alert
                  message="Problème de hiérarchie de rôles"
                  description={`Le rôle "${user?.role || 'Aucun'}" n'a pas de permissions définies dans la hiérarchie de rôles ou n'existe pas dans la configuration.`}
                  type="warning"
                  showIcon
                  style={{ marginTop: 16 }}
                />
              </div>
            )}

            {/* Display expected permissions based on role */}
            <div style={{ marginTop: 24 }}>
              <Alert
                message="Permissions attendues pour ce rôle"
                description={`Cette section montre les permissions que l'utilisateur devrait avoir selon la configuration du rôle "${user?.role || 'Aucun'}".`}
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />

              {roleHierarchy?.permissions && roleHierarchy.permissions.length > 0 ? (
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                  {roleHierarchy.permissions.map(permission => (
                    <Tag
                      color={userPermissions.includes(permission) ? "green" : "red"}
                      key={permission}
                      style={{ margin: '0 8px 8px 0', fontSize: '14px' }}
                    >
                      {permission}
                    </Tag>
                  ))}
                </div>
              ) : (
                <Text type="secondary">Aucune permission attendue trouvée pour ce rôle</Text>
              )}
            </div>

            {/* Display raw role data for debugging */}
            <Collapse style={{ marginTop: 24 }}>
              <Panel header="Données brutes de la hiérarchie (pour débogage)" key="1">
                <div style={{ padding: 16, background: '#f5f5f5', borderRadius: 4 }}>
                  <Text strong>Nom du rôle:</Text>
                  <pre style={{ marginTop: 8, overflow: 'auto', maxHeight: 50 }}>
                    {JSON.stringify(user?.role, null, 2)}
                  </pre>

                  <Text strong>Permissions de la hiérarchie:</Text>
                  <pre style={{ marginTop: 8, overflow: 'auto', maxHeight: 200 }}>
                    {JSON.stringify(hierarchyPermissions, null, 2)}
                  </pre>

                  <Text strong>Données du rôle depuis la hiérarchie:</Text>
                  <pre style={{ marginTop: 8, overflow: 'auto', maxHeight: 200 }}>
                    {JSON.stringify(roleHierarchy, null, 2)}
                  </pre>
                </div>
              </Panel>
            </Collapse>
          </TabPane>

          <TabPane tab="Comparaison avec routes" key="comparison">
            <Alert
              message="Comparaison avec les routes"
              description="Cette section compare les permissions de l'utilisateur avec celles requises pour chaque route."
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Table
              dataSource={routes.map(route => ({
                ...route,
                key: route.path,
                requiredPermissions: Array.isArray(route.permissions)
                  ? route.permissions.join(', ')
                  : route.permissions || 'Aucune',
                requiredRoles: Array.isArray(route.roles)
                  ? route.roles.join(', ')
                  : route.roles || 'Aucun'
              }))}
              columns={[
                {
                  title: 'Route',
                  dataIndex: 'path',
                  key: 'path',
                  render: text => <Text code>{text}</Text>,
                  sorter: (a, b) => a.path.localeCompare(b.path),
                },
                {
                  title: 'Nom',
                  dataIndex: 'label',
                  key: 'label',
                  sorter: (a, b) => (a.label || '').localeCompare(b.label || ''),
                },
                {
                  title: 'Permissions requises',
                  dataIndex: 'requiredPermissions',
                  key: 'requiredPermissions',
                  render: (text, record) => (
                    record.permissions ? (
                      <div>
                        {Array.isArray(record.permissions) ? record.permissions.map(p => {
                          const hasPermission = userPermissions.includes(p);
                          return (
                            <Tag
                              key={p}
                              color={hasPermission ? 'green' : 'red'}
                              style={{
                                textDecoration: hasPermission ? 'none' : 'line-through',
                                opacity: hasPermission ? 1 : 0.7
                              }}
                            >
                              {p}
                            </Tag>
                          );
                        }) : (
                          <Tag
                            color={userPermissions.includes(record.permissions) ? 'green' : 'red'}
                            style={{
                              textDecoration: userPermissions.includes(record.permissions) ? 'none' : 'line-through',
                              opacity: userPermissions.includes(record.permissions) ? 1 : 0.7
                            }}
                          >
                            {record.permissions}
                          </Tag>
                        )}
                      </div>
                    ) : 'Aucune'
                  ),
                  filters: [
                    { text: 'Avec permission', value: 'has' },
                    { text: 'Sans permission', value: 'missing' }
                  ],
                  onFilter: (value, record) => {
                    if (!record.permissions) return value === 'has';

                    const permissions = Array.isArray(record.permissions)
                      ? record.permissions
                      : [record.permissions];

                    if (value === 'has') {
                      return permissions.every(p => userPermissions.includes(p));
                    }
                    return permissions.some(p => !userPermissions.includes(p));
                  },
                },
                {
                  title: 'Accès',
                  key: 'access',
                  render: (_, record) => (
                    <Tag color={record.hasAccess ? 'success' : 'error'}>
                      {record.hasAccess ? 'Autorisé' : 'Refusé'}
                    </Tag>
                  ),
                  filters: [
                    { text: 'Autorisé', value: true },
                    { text: 'Refusé', value: false }
                  ],
                  onFilter: (value, record) => record.hasAccess === value,
                  sorter: (a, b) => (a.hasAccess ? 1 : 0) - (b.hasAccess ? 1 : 0),
                }
              ]}
              pagination={{ pageSize: 10 }}
              size="small"
              bordered
            />

            <div style={{ marginTop: 24 }}>
              <Alert
                message="Permissions manquantes"
                description="Cette section montre les permissions requises pour les routes auxquelles l'utilisateur n'a pas accès."
                type="warning"
                showIcon
                style={{ marginBottom: 16 }}
              />

              {routes.filter(route => !route.hasAccess).length > 0 ? (
                <List
                  size="small"
                  bordered
                  dataSource={routes.filter(route => !route.hasAccess)}
                  renderItem={route => (
                    <List.Item>
                      <List.Item.Meta
                        title={<span><Text code>{route.path}</Text> {route.label}</span>}
                        description={
                          <div>
                            <Text>Permissions requises: </Text>
                            {Array.isArray(route.permissions) ? route.permissions.map(p => (
                              <Tag
                                color={userPermissions.includes(p) ? "green" : "red"}
                                key={p}
                              >
                                {p}
                              </Tag>
                            )) : route.permissions ? (
                              <Tag
                                color={userPermissions.includes(route.permissions) ? "green" : "red"}
                              >
                                {route.permissions}
                              </Tag>
                            ) : (
                              <Text type="secondary">Aucune permission requise</Text>
                            )}
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <Text type="success">L'utilisateur a accès à toutes les routes.</Text>
              )}
            </div>
          </TabPane>
        </Tabs>
      </Card>

      <Card title="Accès aux routes" style={{ marginBottom: 24 }}>
        <List
          dataSource={routes}
          renderItem={route => (
            <List.Item
              actions={[
                <Tag color={route.hasAccess ? 'success' : 'error'}>
                  {route.hasAccess ? 'Accès autorisé' : 'Accès refusé'}
                </Tag>
              ]}
            >
              <List.Item.Meta
                title={<Text code>{route.path}</Text>}
                description={
                  <Space direction="vertical">
                    <Text>{route.label || 'Sans titre'}</Text>
                    {route.permissions && (
                      <div>
                        <Text type="secondary">Permissions requises: </Text>
                        {Array.isArray(route.permissions) ? route.permissions.map(p => (
                          <Tag key={p} color={hasPermission(p) ? 'green' : 'red'}>{p}</Tag>
                        )) : (
                          <Tag color={hasPermission(route.permissions) ? 'green' : 'red'}>
                            {route.permissions}
                          </Tag>
                        )}
                      </div>
                    )}
                    {route.roles && (
                      <div>
                        <Text type="secondary">Rôles requis: </Text>
                        {Array.isArray(route.roles) ? route.roles.map(r => (
                          <Tag key={r} color={hasRole(r) ? 'green' : 'red'}>{r}</Tag>
                        )) : (
                          <Tag color={hasRole(route.roles) ? 'green' : 'red'}>
                            {route.roles}
                          </Tag>
                        )}
                      </div>
                    )}
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      <Card title="Test des composants de permission" style={{ marginBottom: 24 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Divider orientation="left">PermissionButton</Divider>

          <Space wrap>
            <PermissionButton type="primary" permissions={['view_dashboard']}>
              Bouton avec permission view_dashboard
            </PermissionButton>

            <PermissionButton type="primary" permissions={['edit_production']}>
              Bouton avec permission edit_production
            </PermissionButton>

            <PermissionButton type="primary" roles={['admin']}>
              Bouton pour admin uniquement
            </PermissionButton>

            <PermissionButton
              type="primary"
              permissions={['invalid_permission']}
              hideIfUnauthorized
            >
              Ce bouton est caché si non autorisé
            </PermissionButton>
          </Space>

          <Divider orientation="left">PermissionGuard</Divider>

          <PermissionGuard permissions={['view_dashboard']}>
            <Alert message="Contenu visible avec permission view_dashboard" type="success" />
          </PermissionGuard>

          <PermissionGuard permissions={['edit_production']}>
            <Alert message="Contenu visible avec permission edit_production" type="success" />
          </PermissionGuard>

          <PermissionGuard
            permissions={['invalid_permission']}
            fallback={<Alert message="Fallback pour permission invalide" type="warning" />}
          >
            <Alert message="Contenu avec permission invalide" type="success" />
          </PermissionGuard>
        </Space>
      </Card>

      <Card title="Pages spécifiques" style={{ marginBottom: 24 }}>
        <Alert
          message="Vérification des accès aux pages demandées"
          description="Cette section vérifie spécifiquement l'accès aux pages ProductionDashboard et Reports."
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Collapse defaultActiveKey={['1', '2']}>
          <Panel header="Page ProductionDashboard" key="1">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Route: </Text>
                <Text code>/production</Text>
              </div>

              <div>
                <Text strong>Permissions requises: </Text>
                {routePermissions['/production']?.permissions ? (
                  Array.isArray(routePermissions['/production'].permissions) ?
                    routePermissions['/production'].permissions.map(p => (
                      <Tag key={p} color={hasPermission(p) ? 'green' : 'red'}>
                        {p}
                      </Tag>
                    )) : (
                      <Tag color={hasPermission(routePermissions['/production'].permissions) ? 'green' : 'red'}>
                        {routePermissions['/production'].permissions}
                      </Tag>
                    )
                ) : (
                  <Text type="secondary">Aucune permission requise</Text>
                )}
              </div>

              <div>
                <Text strong>Accès: </Text>
                {hasPermission(routePermissions['/production']?.permissions) ? (
                  <Tag color="success">Autorisé</Tag>
                ) : (
                  <Tag color="error">Refusé</Tag>
                )}
              </div>

              <Divider />

              <div>
                <Text strong>Vos permissions correspondantes: </Text>
                {userPermissions.filter(p => p.includes('production')).map(p => (
                  <Tag key={p} color="blue" style={{ margin: '0 4px 4px 0' }}>{p}</Tag>
                ))}
              </div>

              <Alert
                message={hasPermission(routePermissions['/production']?.permissions)
                  ? "Vous avez accès à la page ProductionDashboard"
                  : "Vous n'avez pas accès à la page ProductionDashboard"}
                type={hasPermission(routePermissions['/production']?.permissions) ? "success" : "error"}
                showIcon
              />
            </Space>
          </Panel>

          <Panel header="Page Reports" key="2">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Route: </Text>
                <Text code>/reports</Text>
              </div>

              <div>
                <Text strong>Permissions requises: </Text>
                {routePermissions['/reports']?.permissions ? (
                  Array.isArray(routePermissions['/reports'].permissions) ?
                    routePermissions['/reports'].permissions.map(p => (
                      <Tag key={p} color={hasPermission(p) ? 'green' : 'red'}>
                        {p}
                      </Tag>
                    )) : (
                      <Tag color={hasPermission(routePermissions['/reports'].permissions) ? 'green' : 'red'}>
                        {routePermissions['/reports'].permissions}
                      </Tag>
                    )
                ) : (
                  <Text type="secondary">Aucune permission requise</Text>
                )}
              </div>

              <div>
                <Text strong>Accès: </Text>
                {hasPermission(routePermissions['/reports']?.permissions) ? (
                  <Tag color="success">Autorisé</Tag>
                ) : (
                  <Tag color="error">Refusé</Tag>
                )}
              </div>

              <Divider />

              <div>
                <Text strong>Vos permissions correspondantes: </Text>
                {userPermissions.filter(p => p.includes('report')).map(p => (
                  <Tag key={p} color="blue" style={{ margin: '0 4px 4px 0' }}>{p}</Tag>
                ))}
              </div>

              <Alert
                message={hasPermission(routePermissions['/reports']?.permissions)
                  ? "Vous avez accès à la page Reports"
                  : "Vous n'avez pas accès à la page Reports"}
                type={hasPermission(routePermissions['/reports']?.permissions) ? "success" : "error"}
                showIcon
              />
            </Space>
          </Panel>
        </Collapse>
      </Card>

      <Card title="Actions disponibles">
        <List
          dataSource={actions}
          renderItem={action => (
            <List.Item
              actions={[
                <Tag color={action.hasAccess ? 'success' : 'error'}>
                  {action.hasAccess ? 'Autorisé' : 'Refusé'}
                </Tag>
              ]}
            >
              <List.Item.Meta
                title={<Text code>{action.key}</Text>}
                description={
                  <Space direction="vertical">
                    {action.permissions && (
                      <div>
                        <Text type="secondary">Permissions requises: </Text>
                        {Array.isArray(action.permissions) ? action.permissions.map(p => (
                          <Tag key={p} color={hasPermission(p) ? 'green' : 'red'}>{p}</Tag>
                        )) : (
                          <Tag color={hasPermission(action.permissions) ? 'green' : 'red'}>
                            {action.permissions}
                          </Tag>
                        )}
                      </div>
                    )}
                    {action.roles && (
                      <div>
                        <Text type="secondary">Rôles requis: </Text>
                        {Array.isArray(action.roles) ? action.roles.map(r => (
                          <Tag key={r} color={hasRole(r) ? 'green' : 'red'}>{r}</Tag>
                        )) : (
                          <Tag color={hasRole(action.roles) ? 'green' : 'red'}>
                            {action.roles}
                          </Tag>
                        )}
                      </div>
                    )}
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default PermissionTest;
