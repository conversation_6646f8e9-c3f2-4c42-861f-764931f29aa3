import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react'
import request from 'superagent'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

import { isAbortError } from '../../utils/error_handler'

// Import modular components
import { 
  CHART_COLORS, 
  INITIAL_SKELETON_STATE
} from './modules/constants.jsx'
import { useSkeletonManager } from './modules/skeletonManager.jsx'
import { useComputedValues } from './modules/computedValues.jsx'
import { useDataManager } from './modules/dataManager.jsx'
import { useEventHandlers } from './modules/eventHandlers.jsx'

// Extend dayjs with required plugins
dayjs.extend(isoWeek)
dayjs.extend(isSameOrBefore)

/**
 * ArretQueuedContext - Fully modular architecture
 * 
 * This context uses the modular architecture from /modules to separate concerns:
 * - dataManager: handles all data fetching and processing
 * - eventHandlers: handles all user interactions
 * - skeletonManager: manages loading states
 * - computedValues: handles calculated values
 */

const ArretQueuedContext = createContext()

export const useArretQueuedContext = () => {
  const context = useContext(ArretQueuedContext)
  if (!context) {
    console.error('⚠️  useArretQueuedContext: Context not found!')
    return null
  }
  return context
}

export const ArretQueuedProvider = ({ children }) => {
  console.log('🚀 ArretQueuedProvider: Initializing with modular architecture...');

  // Combined state object for better module integration
  const [state, setState] = useState({
    // Machine selection state
    machineModels: [],
    machineNames: [],
    selectedMachineModel: "",
    selectedMachine: "",
    filteredMachineNames: [],
    
    // Date filtering state
    dateRangeType: "month",
    selectedDate: null,
    dateFilterActive: false,
    dateRangeDescription: "",
    
    // Data arrays
    arretStats: [],
    stopsData: [],
    topStopsData: [],
    durationTrend: [],
    machineComparison: [],
    operatorStats: [],
    stopReasons: [],
    
    // Loading and error states
    loading: false,
    error: null,
    
    // UI state
    isChartModalVisible: false,
    chartModalContent: null,
    
    // Performance metrics
    mttr: 0,
    mtbf: 0,
    doper: 0,
    showPerformanceMetrics: false
  });

  // Initialize skeleton manager
  const skeletonManager = useSkeletonManager();
  
  // Initialize computed values
  const computedValues = useComputedValues(state.stopsData);
  
  // Refs for optimization
  const isMounted = useRef(true);
  const initialLoadComplete = useRef(false);
  
  // 🔒 SECURITY: GraphQL fetch function using SuperAgent with HTTP-only cookies
  const fetchGraphQL = useCallback(async (query, variables = {}) => {
    const baseURL = process.env.NODE_ENV === "production"
      ? "https://charming-hermit-intense.ngrok-free.app"
      : "http://localhost:5000";

    const response = await request.post(`${baseURL}/api/graphql`)
      .send({ query, variables })
      .set('Content-Type', 'application/json')
      .withCredentials()
      .timeout(30000)
      .retry(2);

    const result = response.body;

    if (result.errors) {
      throw new Error(result.errors[0].message);
    }

    return result.data;
  }, []);

  // GraphQL interface for the modules
  const graphQLInterface = {
    async getComprehensiveStopData(filters) {
      console.log('📊 getComprehensiveStopData called with filters:', filters);
      
      try {
        // Fetch all data in parallel
        const [sidecardsData, tableData, topStopsData] = await Promise.all([
          fetchGraphQL(`
            query($filters: StopFilterInput) {
              getStopSidecards(filters: $filters) {
                Arret_Totale
                Arret_Totale_nondeclare
              }
            }
          `, { filters }),
          
          fetchGraphQL(`
            query($filters: StopFilterInput) {
              getAllMachineStops(filters: $filters) {
                Date_Insert
                Machine_Name
                Part_NO
                Code_Stop
                Debut_Stop
                Fin_Stop_Time
                Regleur_Prenom
                duration_minutes
              }
            }
          `, { filters }),
          
          fetchGraphQL(`
            query($filters: StopFilterInput) {
              getTop5Stops(filters: $filters) {
                stopName
                count
              }
            }
          `, { filters })
        ]);

        return {
          sidecards: sidecardsData?.getStopSidecards,
          stopsData: tableData?.getAllMachineStops || [],
          topStops: topStopsData?.getTop5Stops || []
        };
      } catch (error) {
        console.error('❌ Error in getComprehensiveStopData:', error);
        throw error;
      }
    },

    async getMachineModels() {
      const data = await fetchGraphQL(`
        query {
          getStopMachineModels {
            model
          }
        }
      `);
      return data?.getStopMachineModels || [];
    },

    async getMachineNames() {
      const data = await fetchGraphQL(`
        query {
          getStopMachineNames {
            Machine_Name
          }
        }
      `);
      return data?.getStopMachineNames || [];
    }
  };

  // Initialize data manager
  const dataManager = useDataManager(graphQLInterface, state, setState, skeletonManager);
  
  // Initialize event handlers
  const eventHandlers = useEventHandlers(state, setState, dataManager, skeletonManager);

  // Machine name transformation logic
  const transformMachineNames = useCallback((machineNames) => {
    return machineNames.map(item => {
      const machineName = item.Machine_Name;
      // Extract model from machine name (e.g., "IPS01" -> "IPS", "CCM24SB" -> "CCM")
      let model = '';
      if (machineName.startsWith('IPSO')) {
        model = 'IPSO';
      } else if (machineName.startsWith('IPS')) {
        model = 'IPS';
      } else if (machineName.startsWith('CCM')) {
        model = 'CCM';
      } else {
        // Fallback: extract letters from the beginning
        model = machineName.match(/^[A-Za-z]+/)?.[0] || 'UNKNOWN';
      }
      
      return {
        name: machineName,
        model: model
      };
    });
  }, []);

  // Helper function to format date range for display
  const formatDateRange = useCallback((date, rangeType) => {
    if (!date) return { short: "", full: "" };

    const formattedDate = dayjs(date);

    if (rangeType === "day") {
      return {
        short: formattedDate.format("DD/MM"),
        full: formattedDate.format("DD/MM/YYYY")
      };
    } else if (rangeType === "week") {
      const start = formattedDate.startOf('isoWeek');
      const end = formattedDate.endOf('isoWeek');
      return {
        short: `${start.format("DD/MM")} - ${end.format("DD/MM")}`,
        full: `Semaine du ${start.format("DD/MM/YYYY")} au ${end.format("DD/MM/YYYY")}`
      };
    } else if (rangeType === "month") {
      return {
        short: formattedDate.format("MM/YYYY"),
        full: formattedDate.format("MMMM YYYY")
      };
    }

    return { short: "", full: "" };
  }, []);

  // Effect to filter machines by selected model
  useEffect(() => {
    if (state.selectedMachineModel) {
      const filtered = state.machineNames.filter(name => 
        name.model === state.selectedMachineModel || 
        (typeof name === 'string' && name.includes(state.selectedMachineModel))
      );
      console.log('✅ Filtered machine names:', filtered);
      setState(prev => ({ ...prev, filteredMachineNames: filtered }));
      
      // Clear machine selection if it's no longer valid
      if (state.selectedMachine && !filtered.find(m => 
        (typeof m === 'string' ? m : m.name) === state.selectedMachine
      )) {
        setState(prev => ({ ...prev, selectedMachine: "" }));
      }
    } else {
      setState(prev => ({ ...prev, filteredMachineNames: [] }));
    }
  }, [state.selectedMachineModel, state.machineNames, state.selectedMachine]);

  // Effect to update stats with computed values
  useEffect(() => {
    if (computedValues.totalDuration || computedValues.averageDuration || computedValues.totalInterventions) {
      console.log('📊 Updating stats with computed values:', {
        totalDuration: computedValues.totalDuration,
        averageDuration: computedValues.averageDuration,
        totalInterventions: computedValues.totalInterventions
      });

      setState(prev => ({
        ...prev,
        arretStats: prev.arretStats.map(stat => {
          if (stat.title === 'Durée Totale') {
            return { ...stat, value: computedValues.totalDuration };
          } else if (stat.title === 'Durée Moyenne') {
            return { ...stat, value: computedValues.averageDuration };
          } else if (stat.title === 'Interventions') {
            return { ...stat, value: computedValues.totalInterventions };
          }
          return stat;
        })
      }));
    }
  }, [computedValues.totalDuration, computedValues.averageDuration, computedValues.totalInterventions]);

  // Initial data fetch
  useEffect(() => {
    if (initialLoadComplete.current) {
      console.log('⚠️ Initial load already completed, skipping...');
      return;
    }

    const initialize = async () => {
      try {
        console.log('🎯 Starting initial data load...');
        
        // Fetch machine models and names
        const models = await graphQLInterface.getMachineModels();
        const names = await graphQLInterface.getMachineNames();
        
        if (isMounted.current) {
          setState(prev => ({
            ...prev,
            machineModels: models,
            machineNames: transformMachineNames(names),
            selectedMachineModel: "IPS" // Default selection
          }));
        }
        
        // Fetch initial data
        await dataManager.fetchData();
        
        initialLoadComplete.current = true;
        console.log('✅ Initial data load complete');
        
      } catch (error) {
        console.error('❌ Error during initial load:', error);
        if (isMounted.current) {
          setState(prev => ({ ...prev, error: error.message }));
        }
      }
    };

    initialize();
  }, []);

  // Effect to trigger data fetch when filters change
  useEffect(() => {
    if (initialLoadComplete.current) {
      console.log('🔄 Filters changed, fetching data...');
      dataManager.fetchData();
    }
  }, [state.selectedMachineModel, state.selectedMachine, state.selectedDate, state.dateRangeType]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Context value
  const contextValue = {
    // State
    ...state,
    
    // Computed values
    computedValues,
    
    // Utility functions
    formatDateRange,
    
    // Event handlers from modules
    ...eventHandlers,
    
    // Data manager methods
    refreshData: () => dataManager.fetchData(true),
    
    // Skeleton manager
    skeletonManager,
    
    // Chart modal controls
    showChartModal: (content) => {
      setState(prev => ({
        ...prev,
        isChartModalVisible: true,
        chartModalContent: content
      }));
    },
    hideChartModal: () => {
      setState(prev => ({
        ...prev,
        isChartModalVisible: false,
        chartModalContent: null
      }));
    }
  };

  return (
    <ArretQueuedContext.Provider value={contextValue}>
      {children}
    </ArretQueuedContext.Provider>
  );
};

export default ArretQueuedContext;
