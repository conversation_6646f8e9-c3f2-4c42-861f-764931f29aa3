import { useMemo } from 'react'
import { extractTopStops } from './dataProcessing.jsx'

/**
 * Computed Values Module
 * Handles all computed/derived values and data transformations
 * for charts, statistics, and UI components
 */
export const useComputedValues = (state) => {
  
  /**
   * Computed chart data with memoization for performance
   */
  const computedChartData = useMemo(() => {
    
    const result = {
      topStopsData: [],
      chartData: [],
      stopReasons: []
    };

    try {
      // Extract top stops data from current stops
      if (state.stopsData && state.stopsData.length > 0) {
        result.topStopsData = extractTopStops(state.stopsData, 5);
        
        // Extract unique stop reasons for filtering
        result.stopReasons = [...new Set(
          state.stopsData
            .map(stop => stop.Code_Stop || stop.type_arret || stop.raison_arret)
            .filter(reason => reason && reason.trim() !== '')
        )].map((reason, index) => ({
          id: index + 1,
          name: reason,
          count: state.stopsData.filter(stop => 
            (stop.Code_Stop || stop.type_arret || stop.raison_arret) === reason
          ).length
        }));
      }

      // Use chart data from state (already processed)
      result.chartData = state.rawChartData || [];


    } catch (error) {
      console.error('❌ Error computing chart data:', error);
    }

    return result;
  }, [state.stopsData, state.rawChartData]);

  /**
   * Filtered stops data based on current filters
   */
  const filteredStopsData = useMemo(() => {
    if (!state.stopsData || state.stopsData.length === 0) {
      return [];
    }

    let filtered = [...state.stopsData];

    // Apply machine filter if selected
    if (state.selectedMachine) {
      filtered = filtered.filter(stop => 
        stop.Machine_Name === state.selectedMachine
      );
    }

    // Apply machine model filter if selected and no specific machine
    if (state.selectedMachineModel && !state.selectedMachine) {
      filtered = filtered.filter(stop => {
        // Extract model from Machine_Name (e.g., "IPS01" -> "IPS")
        const machineName = stop.Machine_Name || '';
        let model = '';
        if (machineName.startsWith('IPSO')) {
          model = 'IPSO';
        } else if (machineName.startsWith('IPS')) {
          model = 'IPS';
        } else if (machineName.startsWith('CCM')) {
          model = 'CCM';
        } else {
          model = machineName.match(/^[A-Za-z]+/)?.[0] || '';
        }
        return model === state.selectedMachineModel;
      });
    }

    // Apply date filter if selected
    if (state.selectedDate) {
      const targetDate = state.selectedDate.format('YYYY-MM-DD');
      filtered = filtered.filter(stop => {
        if (!stop.Date_Insert) return false;
        
        // Handle different date formats
        let stopDate;
        if (stop.Date_Insert.includes('/')) {
          // DD/MM/YYYY format
          const [day, month, year] = stop.Date_Insert.split('/');
          stopDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        } else {
          // ISO format
          stopDate = stop.Date_Insert.split('T')[0];
        }
        
        return stopDate === targetDate;
      });
    }


    return filtered;
  }, [
    state.stopsData, 
    state.selectedMachine, 
    state.selectedMachineModel, 
    state.selectedDate
  ]);

  /**
   * Chart data calculations for various visualizations
   * NOTE: This uses filtered data for charts, but we need separate global calculations for stats cards
   */
  const chartDataCalculations = useMemo(() => {
    
    const calculations = {
      totalStops: 0,
      totalDuration: 0,
      averageDuration: 0,
      topReasons: [],
      machineDistribution: [],
      timeDistribution: []
    };

    try {
      if (filteredStopsData && filteredStopsData.length > 0) {
        calculations.totalStops = filteredStopsData.length;
        
        // Calculate total duration in minutes
        calculations.totalDuration = filteredStopsData.reduce((total, stop) => {
          // Handle both GraphQL format (duration_minutes) and legacy format (duree_arret)
          if (stop.duration_minutes && stop.duration_minutes > 0) {
            return total + (parseFloat(stop.duration_minutes) || 0);
          } else if (stop.duree_arret) {
            const parts = stop.duree_arret.split(':');
            const hours = parseInt(parts[0]) || 0;
            const minutes = parseInt(parts[1]) || 0;
            return total + (hours * 60) + minutes;
          } else if (stop.Debut_Stop && stop.Fin_Stop_Time) {
            // Calculate duration from start and end times
            try {
              // Parse date format: DD/MM/YYYY HH:mm
              const parseDateTime = (dateStr) => {
                const [datePart, timePart] = dateStr.split(' ');
                const [day, month, year] = datePart.split('/');
                const [hours, minutes] = timePart.split(':');
                return new Date(year, month - 1, day, hours, minutes);
              };
              
              const startTime = parseDateTime(stop.Debut_Stop);
              const endTime = parseDateTime(stop.Fin_Stop_Time);
              
              if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
                const durationMs = endTime - startTime;
                const durationMinutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
                return total + durationMinutes;
              }
            } catch (error) {
              console.warn('Error calculating duration for stop:', stop, error);
              return total;
            }
          }
          return total;
        }, 0);

        calculations.averageDuration = calculations.totalStops > 0 
          ? calculations.totalDuration / calculations.totalStops 
          : 0;

        // Top reasons distribution
        const reasonCounts = {};
        filteredStopsData.forEach(stop => {
          const reason = stop.Code_Stop || stop.type_arret || stop.raison_arret || 'Unknown';
          reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
        });

        calculations.topReasons = Object.entries(reasonCounts)
          .map(([reason, count]) => ({ reason, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);

        // Machine distribution
        const machineCounts = {};
        filteredStopsData.forEach(stop => {
          const machine = stop.Machine_Name || 'Unknown';
          machineCounts[machine] = (machineCounts[machine] || 0) + 1;
        });

        calculations.machineDistribution = Object.entries(machineCounts)
          .map(([machine, count]) => ({ machine, count }))
          .sort((a, b) => b.count - a.count);

        // Time distribution (by hour)
        const hourCounts = new Array(24).fill(0);
        filteredStopsData.forEach(stop => {
          if (stop.Debut_Stop) {
            const hour = parseInt(stop.Debut_Stop.split(':')[0]) || 0;
            if (hour >= 0 && hour < 24) {
              hourCounts[hour]++;
            }
          }
        });

        calculations.timeDistribution = hourCounts.map((count, hour) => ({
          hour: `${hour.toString().padStart(2, '0')}:00`,
          count
        }));
      }


    } catch (error) {
      console.error('❌ Error computing chart calculations:', error);
    }

    return calculations;
  }, [filteredStopsData]);

  /**
   * Global data calculations for stats cards (should NOT be affected by filters)
   */
  const globalDataCalculations = useMemo(() => {
    
    const calculations = {
      totalStops: 0,
      totalDuration: 0,
      averageDuration: 0,
      totalMachines: 0,
      criticalStops: 0,
      declaredStops: 0,
      undeclaredStops: 0
    };

    try {
      if (state.stopsData && state.stopsData.length > 0) {
        calculations.totalStops = state.stopsData.length;
        
        // Count declared stops - FIXED: exclude "Arrêt non déclaré" entries
        calculations.declaredStops = state.stopsData.filter(stop => {
          const codeStop = stop.Code_Stop || stop.type_arret || stop.raison_arret;
          // A stop is declared if it has a code/reason AND it's not "Arrêt non déclaré"
          return codeStop && 
                 codeStop !== 'Arrêt non déclaré' && 
                 codeStop !== 'Non déclaré' &&
                 codeStop !== 'Undeclared';
        }).length;
        
        calculations.undeclaredStops = calculations.totalStops - calculations.declaredStops;
        
        // Calculate total duration in minutes from ALL data
        calculations.totalDuration = state.stopsData.reduce((total, stop) => {
          // Handle both GraphQL format (duration_minutes) and legacy format (duree_arret)
          if (stop.duration_minutes && stop.duration_minutes > 0) {
            return total + (parseFloat(stop.duration_minutes) || 0);
          } else if (stop.duree_arret) {
            const parts = stop.duree_arret.split(':');
            const hours = parseInt(parts[0]) || 0;
            const minutes = parseInt(parts[1]) || 0;
            return total + (hours * 60) + minutes;
          } else if (stop.Debut_Stop && stop.Fin_Stop_Time) {
            // Calculate duration from start and end times
            try {
              // Parse date format: DD/MM/YYYY HH:mm
              const parseDateTime = (dateStr) => {
                const [datePart, timePart] = dateStr.split(' ');
                const [day, month, year] = datePart.split('/');
                const [hours, minutes] = timePart.split(':');
                return new Date(year, month - 1, day, hours, minutes);
              };
              
              const startTime = parseDateTime(stop.Debut_Stop);
              const endTime = parseDateTime(stop.Fin_Stop_Time);
              
              if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
                const durationMs = endTime - startTime;
                const durationMinutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
                return total + durationMinutes;
              }
            } catch (error) {
              console.warn('Error calculating duration for stop:', stop, error);
              return total;
            }
          }
          return total;
        }, 0);

        calculations.averageDuration = calculations.totalStops > 0 
          ? calculations.totalDuration / calculations.totalStops 
          : 0;

        // Count unique machines
        const uniqueMachines = new Set(
          state.stopsData.map(stop => stop.Machine_Name).filter(Boolean)
        );
        calculations.totalMachines = uniqueMachines.size;

        // Critical stops (duration > 60 minutes)
        calculations.criticalStops = state.stopsData.filter(stop => {
          if (stop.duration_minutes && stop.duration_minutes > 0) {
            return parseFloat(stop.duration_minutes) > 60;
          } else if (stop.duree_arret) {
            const parts = stop.duree_arret.split(':');
            const hours = parseInt(parts[0]) || 0;
            return hours >= 1;
          } else if (stop.Debut_Stop && stop.Fin_Stop_Time) {
            try {
              // Parse date format: DD/MM/YYYY HH:mm
              const parseDateTime = (dateStr) => {
                const [datePart, timePart] = dateStr.split(' ');
                const [day, month, year] = datePart.split('/');
                const [hours, minutes] = timePart.split(':');
                return new Date(year, month - 1, day, hours, minutes);
              };
              
              const startTime = parseDateTime(stop.Debut_Stop);
              const endTime = parseDateTime(stop.Fin_Stop_Time);
              
              if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
                const durationMs = endTime - startTime;
                const durationMinutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
                return durationMinutes > 60;
              }
            } catch (error) {
              return false;
            }
          }
          return false;
        }).length;
      }


    } catch (error) {
      console.error('❌ Error computing global calculations:', error);
    }

    return calculations;
  }, [state.stopsData]);

  /**
   * Sidebar statistics for the dashboard - format as array for component compatibility
   */
  const sidebarStats = useMemo(() => {
    const stats = {
      totalMachines: 0,
      activeMachines: 0,
      totalStops: 0,
      criticalStops: 0,
      averageDowntime: 0,
      availability: 0
    };

    try {
      if (state.stopsData && state.stopsData.length > 0) {
        // Count unique machines
        const uniqueMachines = new Set(
          state.stopsData.map(stop => stop.Machine_Name).filter(Boolean)
        );
        stats.totalMachines = uniqueMachines.size;
        stats.activeMachines = uniqueMachines.size; // Assume all machines with stops are active

        stats.totalStops = state.stopsData.length;

        // Critical stops (duration > 60 minutes)
        stats.criticalStops = state.stopsData.filter(stop => {
          if (stop.duration_minutes && stop.duration_minutes > 0) {
            return parseFloat(stop.duration_minutes) > 60;
          } else if (stop.duree_arret) {
            const parts = stop.duree_arret.split(':');
            const hours = parseInt(parts[0]) || 0;
            return hours >= 1;
          } else if (stop.Debut_Stop && stop.Fin_Stop_Time) {
            try {
              // Parse date format: DD/MM/YYYY HH:mm
              const parseDateTime = (dateStr) => {
                const [datePart, timePart] = dateStr.split(' ');
                const [day, month, year] = datePart.split('/');
                const [hours, minutes] = timePart.split(':');
                return new Date(year, month - 1, day, hours, minutes);
              };
              
              const startTime = parseDateTime(stop.Debut_Stop);
              const endTime = parseDateTime(stop.Fin_Stop_Time);
              
              if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
                const durationMs = endTime - startTime;
                const durationMinutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
                return durationMinutes > 60;
              }
            } catch (error) {
              return false;
            }
          }
          return false;
        }).length;

        // Average downtime in minutes
        const totalDuration = state.stopsData.reduce((total, stop) => {
          if (stop.duration_minutes && stop.duration_minutes > 0) {
            return total + (parseFloat(stop.duration_minutes) || 0);
          } else if (stop.duree_arret) {
            const parts = stop.duree_arret.split(':');
            const hours = parseInt(parts[0]) || 0;
            const minutes = parseInt(parts[1]) || 0;
            return total + (hours * 60) + minutes;
          } else if (stop.Debut_Stop && stop.Fin_Stop_Time) {
            try {
              // Parse date format: DD/MM/YYYY HH:mm
              const parseDateTime = (dateStr) => {
                const [datePart, timePart] = dateStr.split(' ');
                const [day, month, year] = datePart.split('/');
                const [hours, minutes] = timePart.split(':');
                return new Date(year, month - 1, day, hours, minutes);
              };
              
              const startTime = parseDateTime(stop.Debut_Stop);
              const endTime = parseDateTime(stop.Fin_Stop_Time);
              
              if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
                const durationMs = endTime - startTime;
                const durationMinutes = Math.max(0, Math.floor(durationMs / (1000 * 60)));
                return total + durationMinutes;
              }
            } catch (error) {
              return total;
            }
          }
          return total;
        }, 0);

        stats.averageDowntime = stats.totalStops > 0 ? totalDuration / stats.totalStops : 0;
        stats.availability = state.doper || 0;
      }


    } catch (error) {
      console.error('❌ Error computing sidebar stats:', error);
    }

    // Convert to array format expected by components
    return [
      {
        title: "Total Arrêts",
        value: stats.totalStops,
        suffix: "arrêts",
        icon: "AlertOutlined",
        color: "#f5222d"
      },
      {
        title: "Arrêts Non Déclarés", 
        value: stats.totalStops - (state.stopsData?.filter(stop => stop.Code_Stop || stop.type_arret || stop.raison_arret).length || 0),
        suffix: "arrêts",
        icon: "WarningOutlined", 
        color: "#faad14"
      },
      {
        title: "Machines Concernées",
        value: stats.totalMachines,
        suffix: "machines",
        icon: "ToolOutlined",
        color: "#1890ff"
      },
      {
        title: "Arrêts Critiques",
        value: stats.criticalStops,
        suffix: "arrêts",
        icon: "ExclamationCircleOutlined",
        color: "#f5222d"
      },
      {
        title: "Temps Moyen d'Arrêt",
        value: Math.round(stats.averageDowntime),
        suffix: "min",
        icon: "ClockCircleOutlined",
        color: "#722ed1"
      },
      {
        title: "Disponibilité",
        value: Math.round(stats.availability * 100) / 100,
        suffix: "%",
        icon: "CheckCircleOutlined", 
        color: "#52c41a"
      }
    ];
  }, [state.stopsData, state.doper]);

  /**
   * Chart options for various chart types
   */
  const chartOptions = useMemo(() => {
    return {
      // Common chart styling
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false,
        },
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
          },
        },
        y: {
          display: true,
          title: {
            display: true,
          },
        },
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false,
      },
    };
  }, []);

  return {
    computedChartData,
    filteredStopsData,
    chartDataCalculations, // For charts (uses filtered data)
    globalDataCalculations, // For stats cards (uses all data)
    sidebarStats,
    chartOptions,
    
    // Individual properties for dashboard compatibility
    // Global stats (always show total regardless of filters) - FIXED to use global calculations
    totalStops: globalDataCalculations.totalStops,
    totalStopsGlobal: globalDataCalculations.totalStops,
    undeclaredStops: globalDataCalculations.undeclaredStops, // Use pre-calculated value from global
    // Filtered stats (based on current filters)
    totalStopsFiltered: filteredStopsData?.length || 0,
    undeclaredStopsFiltered: (() => {
      const totalFiltered = filteredStopsData?.length || 0;
      const declaredFiltered = filteredStopsData?.filter(stop => {
        const codeStop = stop.Code_Stop || stop.type_arret || stop.raison_arret;
        // A stop is declared if it has a code/reason AND it's not "Arrêt non déclaré"
        return codeStop && 
               codeStop !== 'Arrêt non déclaré' && 
               codeStop !== 'Non déclaré' &&
               codeStop !== 'Undeclared';
      }).length || 0;
      const undeclaredFiltered = totalFiltered - declaredFiltered;
      console.log('🔍 Undeclared stops calculation (FILTERED):', {
        totalFiltered,
        declaredFiltered,
        undeclaredFiltered,
        hasFilters: !!(state.selectedMachine || state.selectedMachineModel || state.selectedDate)
      });
      return undeclaredFiltered;
    })(),
    // Duration stats - FIXED to use global calculations for consistency
    avgDuration: globalDataCalculations.averageDuration,
    totalDuration: globalDataCalculations.totalDuration
  };
}
