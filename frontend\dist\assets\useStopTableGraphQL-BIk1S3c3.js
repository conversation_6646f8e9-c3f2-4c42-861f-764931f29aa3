import{r as e}from"./react-vendor-tYPmozCJ.js";import{r as t}from"./index-Nnj1g72A.js";const a=e=>!!e&&(e.isAbortError||"AbortError"===e.name||e.message&&(e.message.includes("aborted")||e.message.includes("cancelled"))),r=3e4,n=1e4,s=()=>{const[a,s]=e.useState(!1),[o,c]=e.useState(null),l=e.useRef(new Map),i=e.useRef(0),u=e.useRef(new Map),m=e.useRef({models:{data:null,timestamp:null},names:new Map}),p=e.useRef(new Map),d=e.useRef({cacheHits:0,cacheMisses:0,totalRequests:0,avgResponseTime:0,lastCleanup:Date.now()});e.useEffect((()=>{const e=()=>{const e=Date.now();for(const[t,a]of u.current.entries())e-a.timestamp>r&&u.current.delete(t);m.current.models.timestamp&&e-m.current.models.timestamp>6e4&&(m.current.models={data:null,timestamp:null});for(const[t,a]of m.current.names.entries())e-a.timestamp>r&&m.current.names.delete(t);if(u.current.size>10){const e=u.current.keys().next().value;u.current.delete(e)}d.current.lastCleanup=e};e();const t=setInterval(e,r);return()=>{clearInterval(t),l.current.forEach(((e,t)=>{try{e.abort("Component unmounted")}catch(a){e.abort()}})),l.current.clear(),p.current.clear()}}),[]);const h=e.useCallback((e=>{const t={model:(null==e?void 0:e.model)||"",machine:(null==e?void 0:e.machine)||"",startDate:(null==e?void 0:e.startDate)||"",endDate:(null==e?void 0:e.endDate)||"",date:(null==e?void 0:e.date)||"",limit:(null==e?void 0:e.limit)||1e3};return JSON.stringify(t)}),[]),g=e.useCallback((e=>{const t=h(e),a=u.current.get(t);if(!a)return null;return Date.now()-a.timestamp>r?(u.current.delete(t),null):(d.current.cacheHits++,a.data)}),[h]),S=e.useCallback(((e,t)=>{const a=h(t);u.current.set(a,{data:e,filters:{...t},timestamp:Date.now(),size:JSON.stringify(e).length}),d.current.cacheMisses++}),[h]),w=e.useCallback((async(e,t={})=>{const a=JSON.stringify({query:e,variables:t});if(p.current.has(a))return p.current.get(a);const r=y(e,t);p.current.set(a,r);try{return await r}finally{p.current.delete(a)}}),[]),y=e.useCallback((async(e,a={})=>{const r=Date.now();s(!0),c(null);const o=++i.current,u=new AbortController;d.current.totalRequests++;try{l.current.set(o,u);const c=new Promise(((e,t)=>setTimeout((()=>{u.abort(),t(new Error("Request timeout after 10000ms"))}),n))),i=await Promise.race([t.post("/api/graphql").send({query:e,variables:a}).timeout(n).retry(2),c]);if(l.current.delete(o),i.status>=400)throw new Error(`HTTP ${i.status}: ${i.text||"Request failed"}`);const m=i.body;if(m.errors)throw new Error(`GraphQL: ${m.errors.map((e=>e.message)).join(", ")}`);const p=Date.now()-r,h=d.current;return h.avgResponseTime=(h.avgResponseTime*(h.totalRequests-1)+p)/h.totalRequests,s(!1),m.data}catch(m){l.current.delete(o);const e=((e,t,a)=>{var r;const n=Date.now()-a;return"AbortError"===e.name||e.message&&e.message.includes("aborted")?{isAbortError:!0,message:"Request was cancelled",requestId:t,responseTime:n}:e.message&&(e.message.includes("Failed to fetch")||e.message.includes("Network"))?{isNetworkError:!0,message:"Network error - please check your connection and try again",requestId:t,responseTime:n}:e.message&&e.message.includes("timeout")?{isTimeoutError:!0,message:"Request timed out - server may be experiencing high load",requestId:t,responseTime:n}:e.graphQLErrors?{isGraphQLError:!0,message:(null==(r=e.graphQLErrors[0])?void 0:r.message)||"GraphQL error",errors:e.graphQLErrors,requestId:t,responseTime:n}:{isGeneralError:!0,message:e.message||"Unknown error occurred",originalError:e,requestId:t,responseTime:n}})(m,o,r);throw e.isAbortError?c(null):c(e.message),e}finally{s(!1)}}),[]),f=e.useCallback((()=>{const e=d.current,t=e.totalRequests>0?e.cacheHits/(e.cacheHits+e.cacheMisses)*100:0;return{cacheHitRate:Math.round(t),totalRequests:e.totalRequests,avgResponseTime:Math.round(e.avgResponseTime),cacheEntries:u.current.size,lastCleanup:new Date(e.lastCleanup).toLocaleTimeString()}}),[]),C=e.useCallback(((e=null)=>{if(e){const t=h(e);u.current.delete(t)}else u.current.clear(),m.current.models={data:null,timestamp:null},m.current.names.clear()}),[h]),b=e.useCallback(((e="User cancelled")=>{l.current.forEach(((t,a)=>{try{t.abort(e)}catch(r){t.abort()}})),l.current.clear(),p.current.clear(),c(null),C()}),[C]),M=e.useCallback((async(e={})=>{const t=g(e);if(t)return t;try{const t=(await w("\n      query GetFinalComprehensiveStopData($filters: FinalOptimizedStopFilterInput) {\n        getFinalComprehensiveStopData(filters: $filters) {\n          # Raw stop data\n          allStops {\n            Machine_Name\n            Date_Insert\n            Part_NO\n            Code_Stop\n            Debut_Stop\n            Fin_Stop_Time\n            Regleur_Prenom\n            duration_minutes\n            Cause\n            Raison_Arret\n            Operateur\n          }\n          \n          # Pre-computed analytics\n          topStops {\n            stopName\n            count\n          }\n          \n          stopReasons {\n            reason\n            count\n          }\n          \n          machineComparison {\n            Machine_Name\n            stops\n            totalDuration\n          }\n          \n          operatorStats {\n            operator\n            interventions\n            totalDuration\n          }\n          \n          durationTrend {\n            hour\n            avgDuration\n          }\n          \n          stopStats {\n            Stop_Date\n            Total_Stops\n          }\n          \n          # Summary statistics\n          sidecards {\n            Arret_Totale\n            Arret_Totale_nondeclare\n          }\n          \n          # Metadata\n          totalRecords\n          queryExecutionTime\n          cacheHit\n        }\n      }\n    ",{filters:e})).getFinalComprehensiveStopData;return S(t,e),t}catch(a){throw a}}),[g,S,w]),R=e.useCallback((async()=>{const e=m.current.models;if(e.data&&e.timestamp&&Date.now()-e.timestamp<6e4)return d.current.cacheHits++,e.data;try{const e=(await w("\n      query {\n        getFinalStopMachineModels {\n          model\n        }\n      }\n    ")).getFinalStopMachineModels.map((e=>e.model))||[];return m.current.models={data:e,timestamp:Date.now()},d.current.cacheMisses++,e}catch(t){const e=[{model:"IPS"},{model:"AKROS"},{model:"ML"},{model:"FCS"}];return m.current.models={data:e,timestamp:Date.now()},e.map((e=>e.model))}}),[w]),D=e.useCallback((async(e={})=>{const t=e.model||"all",a=m.current.names.get(t);if(a&&Date.now()-a.timestamp<r)return d.current.cacheHits++,a.data;try{const a=await w("\n      query GetFinalStopMachineNames($filters: FinalOptimizedStopFilterInput) {\n        getFinalStopMachineNames(filters: $filters) {\n          Machine_Name\n        }\n      }\n    ",{filters:e}),r=(a.getFinalStopMachineNames||[]).map((t=>({...t,model:e.model||null})));return m.current.names.set(t,{data:r,timestamp:Date.now()}),d.current.cacheMisses++,r}catch(n){return[]}}),[w]),T=e.useCallback((async(e={})=>({getAllMachineStops:(await M(e)).allStops})),[M]),v=e.useCallback((async(e={})=>({getTop5Stops:(await M(e)).topStops.slice(0,5)})),[M]),k=e.useCallback((async(e={})=>({getStopStats:(await M(e)).stopStats})),[M]),q=e.useCallback((async(e={})=>({getStopSidecards:(await M(e)).sidecards})),[M]),E=e.useCallback((async(e,t={})=>{const a={...t};e&&(a.date=e);return{getMachineStopComparison:(await M(a)).machineComparison}}),[M]),N=e.useCallback((async()=>({getStopMachineModels:await R()})),[R]),_=e.useCallback((async(e={})=>({getStopMachineNames:await D(e)})),[D]),F=e.useCallback((async(e={})=>{try{const t=await M(e);return{allStops:t.allStops,topStops:t.topStops,sidecards:t.sidecards,stopComparison:t.machineComparison,stopReasons:t.stopReasons,operatorStats:t.operatorStats,durationTrend:t.durationTrend,stopStats:t.stopStats,totalRecords:t.totalRecords,queryExecutionTime:t.queryExecutionTime}}catch(t){throw t}}),[M]),A=e.useCallback((async(e={})=>{try{const t=await M(e);return{durationTrend:t.durationTrend,operatorStats:t.operatorStats,stopReasons:t.stopReasons,stopStats:t.stopStats}}catch(t){throw t}}),[M]);return{loading:a,error:o,getComprehensiveStopData:M,getMachineModels:R,getMachineNames:D,getStopDashboardData:F,getStopsAnalysisData:A,getAllMachineStops:T,getTop5Stops:v,getStopStats:k,getStopSidecards:q,getMachineStopComparison:E,getStopMachineModels:N,getStopMachineNames:_,invalidateCache:C,getCacheStats:f,executeQueryWithDeduplication:w,cancelAllRequests:b}};export{a as i,s as u};
