/**
 * NODE.JS BACKEND TEST SCRIPT - THREE FILTERS FREEZE FIX
 * 
 * This script tests the backend GraphQL resolver optimizations
 * Run with: node test-backend-freeze-fix.js
 */

import fetch from 'node-fetch';

// Test configuration
const testConfig = {
  backendURL: 'http://localhost:5000', // Adjust if your backend runs on different port
  graphqlEndpoint: '/graphql',
  testQueries: {
    singleFilter: {
      name: "Single Filter (Machine Model)",
      query: `
        query GetStopTableData($filters: StopTableFilters!) {
          stopTableData(filters: $filters) {
            totalCount
            data {
              id
              machine_name
              start_time
              end_time
              duration_minutes
            }
          }
        }
      `,
      variables: {
        filters: {
          machineModel: "IPS",
          limit: 100
        }
      }
    },
    doubleFilter: {
      name: "Double Filter (Model + Machine)",
      query: `
        query GetStopTableData($filters: StopTableFilters!) {
          stopTableData(filters: $filters) {
            totalCount
            data {
              id
              machine_name
              start_time
              end_time
              duration_minutes
            }
          }
        }
      `,
      variables: {
        filters: {
          machineModel: "IPS",
          machineName: "IPS01",
          limit: 100
        }
      }
    },
    tripleFilter: {
      name: "Triple Filter (Model + Machine + Date) - The Problem Case",
      query: `
        query GetStopTableData($filters: StopTableFilters!) {
          stopTableData(filters: $filters) {
            totalCount
            data {
              id
              machine_name
              start_time
              end_time
              duration_minutes
            }
          }
        }
      `,
      variables: {
        filters: {
          machineModel: "IPS",
          machineName: "IPS01",
          startDate: "2025-04-01",
          endDate: "2025-04-30",
          limit: 100
        }
      }
    }
  }
};

// Performance monitoring utilities
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
  }
  
  start(label) {
    this.metrics[label] = { start: Date.now() };
    console.log(`⏱️ Starting ${label}...`);
  }
  
  end(label) {
    if (this.metrics[label]) {
      this.metrics[label].end = Date.now();
      this.metrics[label].duration = this.metrics[label].end - this.metrics[label].start;
      console.log(`✅ ${label} completed in ${this.metrics[label].duration}ms`);
    }
  }
  
  report() {
    console.log('\n📊 Performance Report:');
    Object.entries(this.metrics).forEach(([label, data]) => {
      console.log(`  ${label}: ${data.duration}ms`);
    });
    return this.metrics;
  }
}

// GraphQL test client
class GraphQLTestClient {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.endpoint = baseURL + testConfig.graphqlEndpoint;
  }
  
  async query(query, variables = {}, timeout = 10000) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      const response = await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, variables }),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.errors) {
        throw new Error(`GraphQL Errors: ${JSON.stringify(result.errors)}`);
      }
      
      return result.data;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('Query timeout - potential freeze detected');
      }
      throw error;
    }
  }
}

// Test execution engine
class TestEngine {
  constructor() {
    this.monitor = new PerformanceMonitor();
    this.client = new GraphQLTestClient(testConfig.backendURL);
  }
  
  async testBackendConnection() {
    console.log('🔌 Testing backend connection...');
    try {
      const response = await fetch(testConfig.backendURL + '/health', {
        timeout: 5000
      }).catch(() => {
        // If health endpoint doesn't exist, try GraphQL endpoint with a simple query
        return this.client.query('{ __typename }');
      });
      
      console.log('✅ Backend connection successful');
      return true;
    } catch (error) {
      console.error('❌ Backend connection failed:', error.message);
      console.log('💡 Make sure your backend server is running on', testConfig.backendURL);
      return false;
    }
  }
  
  async executeQuery(testName, queryConfig) {
    console.log(`\n🧪 Testing: ${queryConfig.name}`);
    this.monitor.start(testName);
    
    try {
      const startTime = Date.now();
      const result = await this.client.query(
        queryConfig.query,
        queryConfig.variables,
        15000 // 15 second timeout
      );
      const endTime = Date.now();
      
      this.monitor.end(testName);
      
      // Analyze results
      const analysis = this.analyzeQueryResult(result, endTime - startTime);
      console.log('📊 Query Analysis:', analysis);
      
      return {
        success: true,
        duration: endTime - startTime,
        recordCount: result.stopTableData?.totalCount || 0,
        dataPoints: result.stopTableData?.data?.length || 0,
        analysis
      };
      
    } catch (error) {
      this.monitor.end(testName);
      console.error(`❌ Query failed: ${error.message}`);
      
      return {
        success: false,
        error: error.message,
        analysis: this.analyzeError(error)
      };
    }
  }
  
  analyzeQueryResult(result, duration) {
    const analysis = {
      performance: 'good',
      warnings: []
    };
    
    // Performance analysis
    if (duration > 10000) {
      analysis.performance = 'poor';
      analysis.warnings.push('Query took longer than 10 seconds');
    } else if (duration > 5000) {
      analysis.performance = 'slow';
      analysis.warnings.push('Query took longer than 5 seconds');
    }
    
    // Data volume analysis
    const dataPoints = result.stopTableData?.data?.length || 0;
    if (dataPoints > 1000) {
      analysis.warnings.push('Large dataset returned - may cause frontend issues');
    }
    
    return analysis;
  }
  
  analyzeError(error) {
    if (error.message.includes('timeout')) {
      return {
        type: 'timeout',
        severity: 'high',
        recommendation: 'Potential freeze scenario - query too complex or slow'
      };
    }
    
    if (error.message.includes('ECONNREFUSED')) {
      return {
        type: 'connection',
        severity: 'high',
        recommendation: 'Backend server not running'
      };
    }
    
    return {
      type: 'unknown',
      severity: 'medium',
      recommendation: 'Check backend logs for more details'
    };
  }
}

// Main test runner
async function runBackendTests() {
  console.log('🧪 STARTING BACKEND THREE FILTERS FREEZE TESTS');
  console.log('===============================================');
  
  const engine = new TestEngine();
  const results = {};
  
  // Test backend connection first
  const connectionOk = await engine.testBackendConnection();
  if (!connectionOk) {
    console.log('\n❌ Cannot proceed without backend connection');
    console.log('💡 Start your backend server with: npm run dev (in backend directory)');
    return false;
  }
  
  // Run all test queries
  for (const [testName, queryConfig] of Object.entries(testConfig.testQueries)) {
    results[testName] = await engine.executeQuery(testName, queryConfig);
    
    // Brief pause between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Generate comprehensive report
  console.log('\n📊 BACKEND TEST RESULTS');
  console.log('========================');
  
  let allPassed = true;
  Object.entries(results).forEach(([testName, result]) => {
    const status = result.success ? '✅' : '❌';
    const duration = result.duration ? `(${result.duration}ms)` : '';
    const recordInfo = result.recordCount ? `[${result.recordCount} records]` : '';
    
    console.log(`${status} ${testName} ${duration} ${recordInfo}`);
    
    if (result.analysis?.warnings?.length > 0) {
      result.analysis.warnings.forEach(warning => {
        console.log(`   ⚠️ ${warning}`);
      });
    }
    
    if (!result.success) {
      allPassed = false;
      if (result.analysis?.recommendation) {
        console.log(`   💡 ${result.analysis.recommendation}`);
      }
    }
  });
  
  // Performance summary
  engine.monitor.report();
  
  // Final assessment
  console.log(`\n🏆 Overall Backend Status: ${allPassed ? 'HEALTHY' : 'ISSUES DETECTED'}`);
  
  if (allPassed) {
    console.log('✅ All backend queries executed successfully');
    console.log('✅ No timeout/freeze issues detected');
    console.log('💡 You can now test the frontend by opening the dashboard in your browser');
  } else {
    console.log('❌ Some backend issues detected');
    console.log('💡 Check the warnings above and fix any issues before testing frontend');
  }
  
  return { results, success: allPassed, performance: engine.monitor.metrics };
}

// Error handling for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Promise Rejection:', reason);
});

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runBackendTests().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

export { runBackendTests, TestEngine, PerformanceMonitor };
