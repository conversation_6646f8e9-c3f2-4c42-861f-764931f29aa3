import{g as t,r as e}from"./react-vendor-tYPmozCJ.js";import{d as a,s as r}from"./antd-vendor-4OvKHZ_k.js";var n,s={exports:{}};const o=t(n?s.exports:(n=1,s.exports=function(t,e){e.prototype.isSameOrBefore=function(t,e){return this.isSame(t,e)||this.isBefore(t,e)}})),i={primary:"#1890ff",secondary:"#13c2c2",success:"#52c41a",warning:"#faad14",danger:"#f5222d",purple:"#722ed1",pink:"#eb2f96",orange:"#fa8c16",cyan:"#13c2c2",lime:"#a0d911"},l="stats",c="charts",u="table",d="performance",p="topStopsChart",h="durationTrendChart",m="machineComparisonChart",f="paretoChart",D="mttrHeatmap",_="availabilityChart",S="filters",g="header",M="sidebar",y="pagination",I="initialLoad",b="dataRefresh",C="filterChange",w=[{sections:[l],delay:200},{sections:[d],delay:400},{sections:[p,h],delay:600},{sections:[m,f],delay:800},{sections:[u,y],delay:1e3},{sections:[D,_],delay:1200},{sections:[I,b,C],delay:1400}],T=60,v=24,N=7,k=30,F={SIMPLE:200,MEDIUM:400,COMPLEX:800},O={THRESHOLD:5},A={[l]:!1,[c]:!1,[u]:!1,[d]:!1,[p]:!1,[h]:!1,[m]:!1,[f]:!1,[D]:!1,[_]:!1,[S]:!1,[g]:!1,[M]:!1,[y]:!1,[I]:!1,[b]:!1,[C]:!1},x={machineModels:[],machineNames:[],selectedMachineModel:"IPS",selectedMachine:"",filteredMachineNames:[],dateRangeType:"month",selectedDate:null,dateRangeDescription:"",dateFilterActive:!1,dateOptions:[],loading:!1,error:null,essentialLoading:!1,detailedLoading:!1,complexFilterLoading:!1,arretStats:[],topStopsData:[],arretsByRange:[],arretNonDeclareStats:[],filteredArretsByRange:[],stopsData:[],rawChartData:[],durationTrend:[],machineComparison:[],operatorStats:[],stopReasons:[],mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1,isSearchModalVisible:!1,isAutoMode:!0,searchResults:[],searchLoading:!1},R=(t,a)=>{const r=e.useCallback(((t=[])=>{0===t.length&&(t=[l,c,u,I]);const e={};t.forEach((t=>{e[t]=!0})),a((t=>({...t,...e})))}),[a]),n=e.useCallback(((t=[])=>{if(0===t.length)return void a(A);const e={};t.forEach((t=>{e[t]=!1})),a((t=>({...t,...e})))}),[a]),s=e.useCallback(((t=null)=>{(t||w).forEach((({sections:t,delay:e})=>{setTimeout((()=>{n(t)}),e)}))}),[n]),o=e.useCallback(((t,e,a)=>{const n=[l];return e&&n.push(d,m),a&&n.push(h,D),(t||e)&&n.push(p,f),t&&e&&a&&n.push(u,_,C),r(n),n}),[r]),i=e.useCallback((e=>t[e]||!1),[t]),S=e.useCallback((e=>e.some((e=>t[e]))),[t]),g=e.useCallback((()=>Object.keys(t).filter((e=>t[e]))),[t]),M=e.useCallback((()=>{s([{sections:[l],delay:100},{sections:[d],delay:200},{sections:[p,h],delay:300},{sections:[u,c],delay:400},{sections:[I,b],delay:500}])}),[s]);return{startSkeletonLoading:r,stopSkeletonLoading:n,progressiveSkeletonClear:s,smartSkeletonForFilters:o,isSkeletonActive:i,areSkeletonsActive:S,getActiveSkeletons:g,fastSkeletonClear:M}},Y=(t=[],e=[])=>{if(!t||0===t.length)return[];try{return t.map((t=>{const r={...t};if(e&&e.length>0){const n=t.Machine_Name||t.nom_machine,s=t.Date_Insert||t.date_arret,o=e.find((t=>t.nom_machine===n&&a(t.date_arret).format("YYYY-MM-DD")===a(s).format("YYYY-MM-DD")));o&&(r.dailyContext=o,r.productionTarget=o.objectif_production,r.actualProduction=o.production_reelle,r.efficiency=o.rendement)}return r}))}catch(r){return t}},$=(t=[])=>{if(!t||0===t.length)return{};try{return t.reduce(((t,e)=>{const r=e.Date_Insert||e.date_arret;let n="unknown";if(r){const t=(t=>{if(!t)return null;try{const e=String(t).trim();if(e.includes("/")){const t=e.split(" "),r=t[0],n=t[1]||"00:00:00",[s,o,i]=r.split("/");if(s&&o&&i&&s.length<=2&&o.length<=2&&4===i.length){const t=s.padStart(2,"0"),e=o.padStart(2,"0"),r=a(`${i}-${e}-${t}T${n}`);if(r.isValid())return r}}if(e.includes("-")&&e.includes(" ")){const t=e.indexOf(" "),r=e.substring(0,t),n=e.substring(t+1);if(n.includes("-")){const t=n.lastIndexOf("-"),e=n.substring(0,t),s=n.substring(t+1);if(s.includes("-")){const[t,n]=s.split("-");if(r&&t&&n&&e){const s=`${r}-${t.padStart(2,"0")}-${n.padStart(2,"0")}T${e}`,o=a(s);if(o.isValid())return o}}}}const r=a(e);return r.isValid()?r:null}catch(e){return null}})(r);t&&t.isValid()&&(n=t.format("YYYY-MM-DD"))}return t[n]||(t[n]=[]),t[n].push(e),t}),{})}catch(e){return{}}},E=t=>{if(!t)return{sidecards:[],stopsData:[],chartData:[],groupedData:{},isEmpty:!0};try{const{sidecards:e={},allStops:r=[]}=t,n=((t={})=>{if(!t||"object"!=typeof t)return[];try{return[{id:1,title:"Total Arrêts",value:parseInt(t.Arret_Totale)||0,subtitle:"",icon:"AlertOutlined",color:"#f5222d"},{id:2,title:"Arrêts Non Déclarés",value:parseInt(t.Arret_Totale_nondeclare)||0,subtitle:"",icon:"WarningOutlined",color:"#faad14"}]}catch(e){return[]}})(e),s=$(r),o=((t={})=>{try{return Object.entries(t).map((([t,e])=>({date:a(t).format("DD/MM/YYYY"),dateKey:t,stops:e.length,totalDuration:e.reduce(((t,e)=>{if(e.duree_arret){const a=e.duree_arret.split(":");return t+60*(parseInt(a[0])||0)+(parseInt(a[1])||0)}return t}),0),details:e}))).sort(((t,e)=>a(t.dateKey).diff(a(e.dateKey))))}catch(e){return[]}})(s);return{sidecards:n,stopsData:r,chartData:o,groupedData:s,isEmpty:0===r.length}}catch(e){return{sidecards:[],stopsData:[],chartData:[],groupedData:{},isEmpty:!0}}},L=(t=[],e=5)=>{if(!t||0===t.length)return[];try{const a=t.reduce(((t,e)=>{const a=e.type_arret||e.raison_arret||"Unknown";if(t[a]||(t[a]={reason:a,count:0,totalDuration:0,stops:[]}),t[a].count+=1,t[a].stops.push(e),e.duree_arret){const r=e.duree_arret.split(":"),n=parseInt(r[0])||0,s=parseInt(r[1])||0;t[a].totalDuration+=60*n+s}return t}),{});return Object.values(a).sort(((t,e)=>e.count-t.count)).slice(0,e).map(((e,a)=>({id:a+1,reason:e.reason,count:e.count,totalDuration:e.totalDuration,percentage:Math.round(e.count/t.length*100),stops:e.stops})))}catch(a){return[]}},j=t=>{const a=e.useMemo((()=>{const e={topStopsData:[],chartData:[],stopReasons:[]};try{t.stopsData&&t.stopsData.length>0&&(e.topStopsData=L(t.stopsData,5),e.stopReasons=[...new Set(t.stopsData.map((t=>t.Code_Stop||t.type_arret||t.raison_arret)).filter((t=>t&&""!==t.trim())))].map(((e,a)=>({id:a+1,name:e,count:t.stopsData.filter((t=>(t.Code_Stop||t.type_arret||t.raison_arret)===e)).length})))),e.chartData=t.rawChartData||[]}catch(a){}return e}),[t.stopsData,t.rawChartData]),r=e.useMemo((()=>{if(!t.stopsData||0===t.stopsData.length)return[];let e=[...t.stopsData];if(t.selectedMachine&&(e=e.filter((e=>e.Machine_Name===t.selectedMachine))),t.selectedMachineModel&&!t.selectedMachine&&(e=e.filter((e=>{var a;const r=e.Machine_Name||"";let n="";return n=r.startsWith("IPSO")?"IPSO":r.startsWith("IPS")?"IPS":r.startsWith("CCM")?"CCM":(null==(a=r.match(/^[A-Za-z]+/))?void 0:a[0])||"",n===t.selectedMachineModel}))),t.selectedDate){const a=t.selectedDate.format("YYYY-MM-DD");e=e.filter((t=>{if(!t.Date_Insert)return!1;let e;if(t.Date_Insert.includes("/")){const[a,r,n]=t.Date_Insert.split("/");e=`${n}-${r.padStart(2,"0")}-${a.padStart(2,"0")}`}else e=t.Date_Insert.split("T")[0];return e===a}))}return e}),[t.stopsData,t.selectedMachine,t.selectedMachineModel,t.selectedDate]),n=e.useMemo((()=>{const t={totalStops:0,totalDuration:0,averageDuration:0,topReasons:[],machineDistribution:[],timeDistribution:[]};try{if(r&&r.length>0){t.totalStops=r.length,t.totalDuration=r.reduce(((t,e)=>{if(e.duration_minutes&&e.duration_minutes>0)return t+(parseFloat(e.duration_minutes)||0);if(e.duree_arret){const a=e.duree_arret.split(":");return t+60*(parseInt(a[0])||0)+(parseInt(a[1])||0)}if(e.Debut_Stop&&e.Fin_Stop_Time)try{const a=t=>{const[e,a]=t.split(" "),[r,n,s]=e.split("/"),[o,i]=a.split(":");return new Date(s,n-1,r,o,i)},r=a(e.Debut_Stop),n=a(e.Fin_Stop_Time);if(!isNaN(r.getTime())&&!isNaN(n.getTime())){const e=n-r;return t+Math.max(0,Math.floor(e/6e4))}}catch(a){return t}return t}),0),t.averageDuration=t.totalStops>0?t.totalDuration/t.totalStops:0;const e={};r.forEach((t=>{const a=t.Code_Stop||t.type_arret||t.raison_arret||"Unknown";e[a]=(e[a]||0)+1})),t.topReasons=Object.entries(e).map((([t,e])=>({reason:t,count:e}))).sort(((t,e)=>e.count-t.count)).slice(0,5);const a={};r.forEach((t=>{const e=t.Machine_Name||"Unknown";a[e]=(a[e]||0)+1})),t.machineDistribution=Object.entries(a).map((([t,e])=>({machine:t,count:e}))).sort(((t,e)=>e.count-t.count));const n=new Array(24).fill(0);r.forEach((t=>{if(t.Debut_Stop){const e=parseInt(t.Debut_Stop.split(":")[0])||0;e>=0&&e<24&&n[e]++}})),t.timeDistribution=n.map(((t,e)=>({hour:`${e.toString().padStart(2,"0")}:00`,count:t})))}}catch(e){}return t}),[r]),s=e.useMemo((()=>{const e={totalStops:0,totalDuration:0,averageDuration:0,totalMachines:0,criticalStops:0,declaredStops:0,undeclaredStops:0};try{if(t.stopsData&&t.stopsData.length>0){e.totalStops=t.stopsData.length,e.declaredStops=t.stopsData.filter((t=>{const e=t.Code_Stop||t.type_arret||t.raison_arret;return e&&"Arrêt non déclaré"!==e&&"Non déclaré"!==e&&"Undeclared"!==e})).length,e.undeclaredStops=e.totalStops-e.declaredStops,e.totalDuration=t.stopsData.reduce(((t,e)=>{if(e.duration_minutes&&e.duration_minutes>0)return t+(parseFloat(e.duration_minutes)||0);if(e.duree_arret){const a=e.duree_arret.split(":");return t+60*(parseInt(a[0])||0)+(parseInt(a[1])||0)}if(e.Debut_Stop&&e.Fin_Stop_Time)try{const a=t=>{const[e,a]=t.split(" "),[r,n,s]=e.split("/"),[o,i]=a.split(":");return new Date(s,n-1,r,o,i)},r=a(e.Debut_Stop),n=a(e.Fin_Stop_Time);if(!isNaN(r.getTime())&&!isNaN(n.getTime())){const e=n-r;return t+Math.max(0,Math.floor(e/6e4))}}catch(a){return t}return t}),0),e.averageDuration=e.totalStops>0?e.totalDuration/e.totalStops:0;const a=new Set(t.stopsData.map((t=>t.Machine_Name)).filter(Boolean));e.totalMachines=a.size,e.criticalStops=t.stopsData.filter((t=>{if(t.duration_minutes&&t.duration_minutes>0)return parseFloat(t.duration_minutes)>60;if(t.duree_arret){const e=t.duree_arret.split(":");return(parseInt(e[0])||0)>=1}if(t.Debut_Stop&&t.Fin_Stop_Time)try{const e=t=>{const[e,a]=t.split(" "),[r,n,s]=e.split("/"),[o,i]=a.split(":");return new Date(s,n-1,r,o,i)},a=e(t.Debut_Stop),r=e(t.Fin_Stop_Time);if(!isNaN(a.getTime())&&!isNaN(r.getTime())){const t=r-a;return Math.max(0,Math.floor(t/6e4))>60}}catch(e){return!1}return!1})).length}}catch(a){}return e}),[t.stopsData]),o=e.useMemo((()=>{var e;const a={totalMachines:0,activeMachines:0,totalStops:0,criticalStops:0,averageDowntime:0,availability:0};try{if(t.stopsData&&t.stopsData.length>0){const e=new Set(t.stopsData.map((t=>t.Machine_Name)).filter(Boolean));a.totalMachines=e.size,a.activeMachines=e.size,a.totalStops=t.stopsData.length,a.criticalStops=t.stopsData.filter((t=>{if(t.duration_minutes&&t.duration_minutes>0)return parseFloat(t.duration_minutes)>60;if(t.duree_arret){const e=t.duree_arret.split(":");return(parseInt(e[0])||0)>=1}if(t.Debut_Stop&&t.Fin_Stop_Time)try{const e=t=>{const[e,a]=t.split(" "),[r,n,s]=e.split("/"),[o,i]=a.split(":");return new Date(s,n-1,r,o,i)},a=e(t.Debut_Stop),r=e(t.Fin_Stop_Time);if(!isNaN(a.getTime())&&!isNaN(r.getTime())){const t=r-a;return Math.max(0,Math.floor(t/6e4))>60}}catch(e){return!1}return!1})).length;const r=t.stopsData.reduce(((t,e)=>{if(e.duration_minutes&&e.duration_minutes>0)return t+(parseFloat(e.duration_minutes)||0);if(e.duree_arret){const a=e.duree_arret.split(":");return t+60*(parseInt(a[0])||0)+(parseInt(a[1])||0)}if(e.Debut_Stop&&e.Fin_Stop_Time)try{const a=t=>{const[e,a]=t.split(" "),[r,n,s]=e.split("/"),[o,i]=a.split(":");return new Date(s,n-1,r,o,i)},r=a(e.Debut_Stop),n=a(e.Fin_Stop_Time);if(!isNaN(r.getTime())&&!isNaN(n.getTime())){const e=n-r;return t+Math.max(0,Math.floor(e/6e4))}}catch(a){return t}return t}),0);a.averageDowntime=a.totalStops>0?r/a.totalStops:0,a.availability=t.doper||0}}catch(r){}return[{title:"Total Arrêts",value:a.totalStops,suffix:"arrêts",icon:"AlertOutlined",color:"#f5222d"},{title:"Arrêts Non Déclarés",value:a.totalStops-((null==(e=t.stopsData)?void 0:e.filter((t=>t.Code_Stop||t.type_arret||t.raison_arret)).length)||0),suffix:"arrêts",icon:"WarningOutlined",color:"#faad14"},{title:"Machines Concernées",value:a.totalMachines,suffix:"machines",icon:"ToolOutlined",color:"#1890ff"},{title:"Arrêts Critiques",value:a.criticalStops,suffix:"arrêts",icon:"ExclamationCircleOutlined",color:"#f5222d"},{title:"Temps Moyen d'Arrêt",value:Math.round(a.averageDowntime),suffix:"min",icon:"ClockCircleOutlined",color:"#722ed1"},{title:"Disponibilité",value:Math.round(100*a.availability)/100,suffix:"%",icon:"CheckCircleOutlined",color:"#52c41a"}]}),[t.stopsData,t.doper]),i=e.useMemo((()=>({responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{x:{display:!0,title:{display:!0}},y:{display:!0,title:{display:!0}}},interaction:{mode:"nearest",axis:"x",intersect:!1}})),[]);return{computedChartData:a,filteredStopsData:r,chartDataCalculations:n,globalDataCalculations:s,sidebarStats:o,chartOptions:i,totalStops:s.totalStops,totalStopsGlobal:s.totalStops,undeclaredStops:s.undeclaredStops,totalStopsFiltered:(null==r?void 0:r.length)||0,undeclaredStopsFiltered:((null==r?void 0:r.length)||0)-((null==r?void 0:r.filter((t=>{const e=t.Code_Stop||t.type_arret||t.raison_arret;return e&&"Arrêt non déclaré"!==e&&"Non déclaré"!==e&&"Undeclared"!==e})).length)||0),avgDuration:s.averageDuration,totalDuration:s.totalDuration}},P=(t=[],e="day",a=null)=>{try{const a=t.reduce(((t,e)=>{if(e.duree_arret){const a=e.duree_arret.split(":");if(a.length>=3){const e=parseInt(a[0])||0,r=parseInt(a[1])||0,n=parseInt(a[2])||0;return t+e*T+r+n/T}if(2===a.length){return t+(parseInt(a[0])||0)+(parseInt(a[1])||0)/T}}return t}),0),r=t.length;let n=0;switch(e){case"day":n=v*T;break;case"week":n=N*v*T;break;case"month":n=k*v*T}const s=r>0?a/r:0,o=r>0?(n-a)/r:0,i=n>0?(n-a)/n*100:100;return{mttr:Math.round(100*s)/100,mtbf:Math.round(100*o)/100,doper:Math.round(100*i)/100}}catch(r){return{mttr:0,mtbf:0,doper:0}}},B=(t=[],e="day")=>{try{if(!t||0===t.length)return[];const e=t=>{if(!t||"string"!=typeof t)return null;try{const e=t.trim();let a=e.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);if(a){const[t,e,r,n,s,o,i]=a,l=new Date(parseInt(n),parseInt(r)-1,parseInt(e),parseInt(s),parseInt(o),parseInt(i));if(!isNaN(l.getTime()))return l}if(a=e.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/),a){const[t,e,r,n,s,o,i]=a;return new Date(parseInt(e),parseInt(o)-1,parseInt(i),parseInt(r),parseInt(n),parseInt(s))}const r=new Date(e);return isNaN(r.getTime())?null:r}catch(e){return null}},a={};t.forEach((t=>{const r=e(t.Debut_Stop||t.debut_stop||t.startTime);if(!r)return;const n=r.toISOString().split("T")[0];a[n]||(a[n]={date:n,stops:[],totalDowntime:0,stopCount:0}),a[n].stops.push(t),a[n].stopCount++;const s=e(t.Fin_Stop_Time||t.fin_stop_time||t.endTime||t.Fin_Stop);if(s&&r){const t=(s-r)/6e4;t>0&&(a[n].totalDowntime+=t)}}));const r=Object.values(a).map((t=>{const e=v*T,a=e>0?(e-t.totalDowntime)/e*100:100;return{date:t.date,disponibilite:Math.round(100*a)/100,downtime:Math.round(100*t.totalDowntime)/100,stopCount:t.stopCount}}));return r.sort(((t,e)=>new Date(t.date)-new Date(e.date))),r}catch(a){return[]}},U=(t=[])=>{try{if(!t||0===t.length)return[];const e=t=>{if(!t||"string"!=typeof t)return null;try{const e=t.trim();let a=e.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);if(a){const[t,e,r,n,s,o,i]=a,l=new Date(parseInt(n),parseInt(r)-1,parseInt(e),parseInt(s),parseInt(o),parseInt(i));if(!isNaN(l.getTime()))return l}if(a=e.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/),a){const[t,e,r,n,s,o,i]=a;return new Date(parseInt(e),parseInt(o)-1,parseInt(i),parseInt(r),parseInt(n),parseInt(s))}const r=new Date(e);return isNaN(r.getTime())?null:r}catch(e){return null}},a={};t.forEach((t=>{const r=e(t.Debut_Stop||t.debut_stop||t.startTime),n=e(t.Fin_Stop_Time||t.fin_stop_time||t.endTime||t.Fin_Stop);if(!r||!n)return;const s=r.toISOString().split("T")[0],o=(n-r)/6e4;o>0&&(a[s]||(a[s]={date:s,totalDowntime:0,stopCount:0}),a[s].totalDowntime+=o,a[s].stopCount++)}));const r=Object.values(a).map((t=>{const e=t.stopCount>0?t.totalDowntime/t.stopCount:0;return{date:t.date,mttr:Math.round(100*e)/100,stopCount:t.stopCount,totalDowntime:Math.round(100*t.totalDowntime)/100}}));return r.sort(((t,e)=>new Date(t.date)-new Date(e.date))),r}catch(e){return[]}},V=(t=[])=>{try{if(!t||0===t.length)return[];const e=t=>{if(!t||"string"!=typeof t)return null;try{const e=t.trim();let a=e.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/);if(a){const[t,e,r,n,s,o,i]=a,l=new Date(parseInt(n),parseInt(r)-1,parseInt(e),parseInt(s),parseInt(o),parseInt(i));if(!isNaN(l.getTime()))return l}if(a=e.match(/^(\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})-(\d{1,2})-\s*(\d{1,2})$/),a){const[t,e,r,n,s,o,i]=a,l=new Date(parseInt(e),parseInt(o)-1,parseInt(i),parseInt(r),parseInt(n),parseInt(s));if(!isNaN(l.getTime()))return l}const r=new Date(e);return isNaN(r.getTime())?null:r}catch(e){return null}},a={};let r=0,n=0;t.forEach(((t,s)=>{const o=e(t.Debut_Stop||t.debut_stop||t.startTime),i=e(t.Fin_Stop_Time||t.fin_stop_time||t.endTime||t.Fin_Stop);let l=t.Code_Stop||t.code_stop||t.stopCode||t.reason||t.cause;if(l&&null!==l&&"null"!==l&&""!==l||(l=t.Type_Arret||t.type_arret),l&&null!==l&&"null"!==l&&""!==l||(l="Arrêt non déclaré"),!o||!i)return void n++;const c=(i-o)/6e4;c>0&&(r++,a[l]||(a[l]={reason:l,totalDowntime:0,stopCount:0}),a[l].totalDowntime+=c,a[l].stopCount++)}));const s=Object.values(a).sort(((t,e)=>e.totalDowntime-t.totalDowntime)),o=s.reduce(((t,e)=>t+e.totalDowntime),0);let i=0;return s.map((t=>{i+=t.totalDowntime;const e=o>0?t.totalDowntime/o*100:0,a=o>0?i/o*100:0;return{reason:t.reason,value:Math.round(100*t.totalDowntime)/100,totalDowntime:Math.round(100*t.totalDowntime)/100,stopCount:t.stopCount,percentage:Math.round(100*e)/100,cumulativePercentage:Math.round(100*a)/100}}))}catch(e){return[]}},W=(t,a,n,s)=>({handleDateRangeTypeChange:e.useCallback((t=>{a((e=>({...e,dateRangeType:t})))}),[a]),handleDateChange:e.useCallback((t=>{a((e=>({...e,selectedDate:t,dateFilterActive:null!==t})))}),[a]),handleMachineModelChange:e.useCallback((t=>{a((e=>({...e,selectedMachineModel:t,selectedMachine:""})))}),[a]),handleMachineChange:e.useCallback((t=>{a((e=>({...e,selectedMachine:t})))}),[a]),resetFilters:e.useCallback((()=>{var t;a((t=>({...t,selectedMachineModel:"IPS",selectedMachine:"",selectedDate:null,dateRangeType:"month",dateFilterActive:!1,dateRangeDescription:"",arretStats:[],topStopsData:[],arretsByRange:[],stopsData:[],durationTrend:[],mttr:0,mtbf:0,doper:0,showPerformanceMetrics:!1}))),null==(t=null==s?void 0:s.stopSkeletonLoading)||t.call(s),r.success("Filters reset successfully")}),[a,s]),handleRefresh:e.useCallback((async()=>{var t,e,a;try{null==(t=null==s?void 0:s.startSkeletonLoading)||t.call(s,["dataRefresh"]),await n.fetchDataInQueue(!0),null==(e=null==s?void 0:s.stopSkeletonLoading)||e.call(s,["dataRefresh"]),r.success("Data refreshed successfully")}catch(o){null==(a=null==s?void 0:s.stopSkeletonLoading)||a.call(s,["dataRefresh"]),r.error("Failed to refresh data")}}),[n,s]),resetDateFilter:e.useCallback((()=>{a((t=>({...t,selectedDate:null,dateFilterActive:!1,dateRangeDescription:""})))}),[a]),handleResetMachineSelection:e.useCallback((()=>{a((t=>({...t,selectedMachineModel:"",selectedMachine:""})))}),[a])});export{O as C,F as D,A as I,U as a,V as b,B as c,j as d,W as e,Y as f,L as g,P as h,o as i,x as j,i as k,E as p,R as u};
