import{j as e}from"./index-Nnj1g72A.js";import{r as t}from"./react-vendor-tYPmozCJ.js";import{E as r,a,b as n}from"./EnhancedChartComponents-C30-oOvy.js";import{T as s,a6 as o,a7 as i,a9 as d,S as l,b9 as c,e as h,ah as m,R as x,aG as p,Y as j,ab as g,d as f}from"./antd-vendor-4OvKHZ_k.js";import"./chart-vendor-CazprKWL.js";const{Title:u,Text:y}=s,T=()=>{const[s,T]=t.useState(100),[M,v]=t.useState(!1),[b,C]=t.useState([]),[S,w]=t.useState({generationTime:0,renderTime:0,memoryUsage:0}),[z,D]=t.useState(!0);t.useEffect((()=>{if(b.length>0){const e=performance.now();requestAnimationFrame((()=>{const t=performance.now()-e;w((e=>({...e,renderTime:Math.round(t)})))}))}}),[b]),t.useEffect((()=>{performance.memory&&w((e=>({...e,memoryUsage:Math.round(performance.memory.usedJSHeapSize/1024/1024)})))}),[b]);const E=(e,t)=>{switch(e){case"generation":return t<100?"green":t<500?"orange":"red";case"render":return t<50?"green":t<200?"orange":"red";case"memory":return t<50?"green":t<100?"orange":"red";default:return"blue"}};return e.jsxs("div",{style:{padding:"24px"},children:[e.jsx(u,{level:2,children:"Chart Performance Test"}),e.jsx(y,{type:"secondary",children:"Test the performance improvements for chart expansion with large datasets"}),e.jsxs(o,{gutter:[24,24],style:{marginTop:"24px"},children:[e.jsx(i,{span:24,children:e.jsx(d,{title:"Test Controls",children:e.jsxs(l,{direction:"vertical",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsxs(y,{strong:!0,children:["Data Size: ",s," points"]}),e.jsx(c,{min:10,max:1e3,step:10,value:s,onChange:e=>{T(e)},marks:{10:"10",100:"100",500:"500",1e3:"1000"}})]}),e.jsxs(l,{children:[e.jsx(h,{type:"primary",icon:e.jsx(m,{}),onClick:()=>{(async e=>{v(!0);const t=performance.now(),r=[],a=f().subtract(e,"days");for(let s=0;s<e;s++){const e=a.add(s,"day").format("YYYY-MM-DD");r.push({date:e,good:Math.floor(1e3*Math.random())+500,reject:Math.floor(100*Math.random())+10,oee:100*Math.random(),speed:60*Math.random()+20,Machine_Name:`Machine_${Math.floor(s/10)+1}`,Shift:s%3==0?"Morning":s%3==1?"Afternoon":"Night"})}const n=performance.now()-t;C(r),w((e=>({...e,generationTime:Math.round(n)}))),v(!1)})(s)},loading:M,children:"Generate Test Data"}),e.jsx(h,{icon:e.jsx(x,{}),onClick:()=>C([]),children:"Clear Data"})]}),e.jsxs("div",{children:[e.jsx(y,{strong:!0,children:"Enable Optimizations: "}),e.jsx(p,{checked:z,onChange:D,checkedChildren:"ON",unCheckedChildren:"OFF"})]})]})})}),e.jsx(i,{span:24,children:e.jsxs(d,{title:"Performance Metrics",children:[e.jsxs(o,{gutter:16,children:[e.jsx(i,{span:8,children:e.jsx(j,{title:"Data Generation",value:S.generationTime,suffix:"ms",valueStyle:{color:E("generation",S.generationTime)}})}),e.jsx(i,{span:8,children:e.jsx(j,{title:"Render Time",value:S.renderTime,suffix:"ms",valueStyle:{color:E("render",S.renderTime)}})}),e.jsx(i,{span:8,children:e.jsx(j,{title:"Memory Usage",value:S.memoryUsage,suffix:"MB",valueStyle:{color:E("memory",S.memoryUsage)}})})]}),b.length>500&&e.jsx(g,{message:"Large Dataset Detected",description:"Performance optimizations are automatically applied for datasets over 500 points.",type:"info",showIcon:!0,style:{marginTop:"16px"}})]})}),b.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(i,{span:12,children:e.jsx(r,{title:`Quantity Chart (${b.length} points)`,data:b,chartType:"bar",expandMode:"modal",exportEnabled:!0,zoomEnabled:!0,children:e.jsx(a,{data:b,title:"Test Quantity Data",dataKey:"good",color:"#1890ff",tooltipLabel:"Quantité bonne"})})}),e.jsx(i,{span:12,children:e.jsx(r,{title:`TRS Chart (${b.length} points)`,data:b,chartType:"line",expandMode:"modal",exportEnabled:!0,zoomEnabled:!0,children:e.jsx(n,{data:b,color:"#52c41a"})})})]}),e.jsx(i,{span:24,children:e.jsx(d,{title:"Test Instructions",children:e.jsxs(l,{direction:"vertical",children:[e.jsxs(y,{children:[e.jsx("strong",{children:"1."})," Adjust the data size slider to test with different dataset sizes"]}),e.jsxs(y,{children:[e.jsx("strong",{children:"2."}),' Click "Generate Test Data" to create sample data']}),e.jsxs(y,{children:[e.jsx("strong",{children:"3."})," Click the expand button on charts to test modal performance"]}),e.jsxs(y,{children:[e.jsx("strong",{children:"4."})," Monitor the performance metrics above"]}),e.jsxs(y,{children:[e.jsx("strong",{children:"5."})," Toggle optimizations to see the difference"]}),e.jsxs(y,{type:"secondary",children:[e.jsx("strong",{children:"Expected Results:"})," With optimizations enabled, large datasets should render smoothly with proper date label formatting and no sidebar interference in modal mode."]})]})})})]})]})};export{T as default};
