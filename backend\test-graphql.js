const query = `{
  getAllMachineStops(filters: {model: "IPS"}) {
    Machine_Name
    Date_Insert
  }
}`;

const body = JSON.stringify({ query });

console.log('Testing GraphQL query...');
fetch('http://localhost:5000/api/graphql', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body
})
.then(r => r.json())
.then(result => {
  console.log('Success:', JSON.stringify(result, null, 2));
})
.catch(error => {
  console.error('Error:', error);
});
